package com.timeflow.app.receiver;

import com.timeflow.app.utils.NotificationHelper;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class HabitAlarmReceiver_MembersInjector implements MembersInjector<HabitAlarmReceiver> {
  private final Provider<NotificationHelper> notificationHelperProvider;

  public HabitAlarmReceiver_MembersInjector(
      Provider<NotificationHelper> notificationHelperProvider) {
    this.notificationHelperProvider = notificationHelperProvider;
  }

  public static MembersInjector<HabitAlarmReceiver> create(
      Provider<NotificationHelper> notificationHelperProvider) {
    return new HabitAlarmReceiver_MembersInjector(notificationHelperProvider);
  }

  @Override
  public void injectMembers(HabitAlarmReceiver instance) {
    injectNotificationHelper(instance, notificationHelperProvider.get());
  }

  @InjectedFieldSignature("com.timeflow.app.receiver.HabitAlarmReceiver.notificationHelper")
  public static void injectNotificationHelper(HabitAlarmReceiver instance,
      NotificationHelper notificationHelper) {
    instance.notificationHelper = notificationHelper;
  }
}
