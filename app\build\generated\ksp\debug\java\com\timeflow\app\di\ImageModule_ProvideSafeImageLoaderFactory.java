package com.timeflow.app.di;

import android.content.Context;
import com.timeflow.app.utils.SafeImageLoader;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ImageModule_ProvideSafeImageLoaderFactory implements Factory<SafeImageLoader> {
  private final Provider<Context> contextProvider;

  public ImageModule_ProvideSafeImageLoaderFactory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public SafeImageLoader get() {
    return provideSafeImageLoader(contextProvider.get());
  }

  public static ImageModule_ProvideSafeImageLoaderFactory create(
      Provider<Context> contextProvider) {
    return new ImageModule_ProvideSafeImageLoaderFactory(contextProvider);
  }

  public static SafeImageLoader provideSafeImageLoader(Context context) {
    return Preconditions.checkNotNullFromProvides(ImageModule.INSTANCE.provideSafeImageLoader(context));
  }
}
