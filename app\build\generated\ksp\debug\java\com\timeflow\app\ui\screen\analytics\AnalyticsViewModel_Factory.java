package com.timeflow.app.ui.screen.analytics;

import com.timeflow.app.data.repository.TaskRepository;
import com.timeflow.app.data.repository.TimeAnalyticsRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AnalyticsViewModel_Factory implements Factory<AnalyticsViewModel> {
  private final Provider<AnalyticsDataService> analyticsDataServiceProvider;

  private final Provider<TimeAnalyticsRepository> timeAnalyticsRepositoryProvider;

  private final Provider<TaskRepository> taskRepositoryProvider;

  private final Provider<AnalyticsInsightService> analyticsInsightServiceProvider;

  public AnalyticsViewModel_Factory(Provider<AnalyticsDataService> analyticsDataServiceProvider,
      Provider<TimeAnalyticsRepository> timeAnalyticsRepositoryProvider,
      Provider<TaskRepository> taskRepositoryProvider,
      Provider<AnalyticsInsightService> analyticsInsightServiceProvider) {
    this.analyticsDataServiceProvider = analyticsDataServiceProvider;
    this.timeAnalyticsRepositoryProvider = timeAnalyticsRepositoryProvider;
    this.taskRepositoryProvider = taskRepositoryProvider;
    this.analyticsInsightServiceProvider = analyticsInsightServiceProvider;
  }

  @Override
  public AnalyticsViewModel get() {
    return newInstance(analyticsDataServiceProvider.get(), timeAnalyticsRepositoryProvider.get(), taskRepositoryProvider.get(), analyticsInsightServiceProvider.get());
  }

  public static AnalyticsViewModel_Factory create(
      Provider<AnalyticsDataService> analyticsDataServiceProvider,
      Provider<TimeAnalyticsRepository> timeAnalyticsRepositoryProvider,
      Provider<TaskRepository> taskRepositoryProvider,
      Provider<AnalyticsInsightService> analyticsInsightServiceProvider) {
    return new AnalyticsViewModel_Factory(analyticsDataServiceProvider, timeAnalyticsRepositoryProvider, taskRepositoryProvider, analyticsInsightServiceProvider);
  }

  public static AnalyticsViewModel newInstance(AnalyticsDataService analyticsDataService,
      TimeAnalyticsRepository timeAnalyticsRepository, TaskRepository taskRepository,
      AnalyticsInsightService analyticsInsightService) {
    return new AnalyticsViewModel(analyticsDataService, timeAnalyticsRepository, taskRepository, analyticsInsightService);
  }
}
