package com.timeflow.app.ui.screen.settings

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Check
import androidx.compose.material.icons.filled.ChevronRight
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material.icons.filled.Image
import androidx.compose.material.icons.filled.KeyboardArrowRight
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import com.timeflow.app.R
import androidx.navigation.NavController
import com.timeflow.app.data.ai.model.AiConfig
import com.timeflow.app.ui.navigation.AppDestinations
import com.timeflow.app.ui.theme.*
import com.timeflow.app.ui.viewmodel.AiConfigViewModel
import com.timeflow.app.ui.viewmodel.AiSettingsViewModel
import com.timeflow.app.utils.SystemBarManager
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.text.style.TextDecoration
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import android.net.Uri
import coil.compose.AsyncImage
import coil.request.ImageRequest
import java.io.File
import java.io.FileOutputStream
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.util.Log
import java.io.IOException
import java.util.UUID
import android.content.Context
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import androidx.compose.runtime.rememberCoroutineScope
import com.timeflow.app.ui.screen.calendar.CalendarViewModel
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll

// 添加缺少的颜色定义
private val PastelPink = Color(0xFFF5F0FF)
private val LightPurple = Color(0xFFA59AB0)

/**
 * AI设置屏幕
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AiSettingsScreen(
    navController: NavController,
    aiSettingsViewModel: AiSettingsViewModel = hiltViewModel(),
    aiConfigViewModel: AiConfigViewModel = hiltViewModel(),
    calendarViewModel: CalendarViewModel = hiltViewModel(),
    onBackClick: () -> Unit = { navController.popBackStack() }
) {
    val context = LocalContext.current
    val configurations by aiConfigViewModel.configurations.collectAsState()
    val selectedConfigId by aiConfigViewModel.selectedConfigId.collectAsState()
    
    // 加载AI设置
    val aiSettings by aiSettingsViewModel.aiSettings.collectAsState()
    val coroutineScope = rememberCoroutineScope()
    
    // 获取用户颜色偏好
    val userColorPreference by calendarViewModel.userColorPreference.collectAsState()
    val settingsBackgroundColor = remember(userColorPreference) {
        Color(userColorPreference.settingsPageBackground)
    }
    
    LaunchedEffect(Unit) {
        aiSettingsViewModel.loadAiSettings(context)
        aiConfigViewModel.loadConfigurations(context)
    }
    
    // 对话框状态
    var showAssistantNameDialog by remember { mutableStateOf(false) }
    var showAvatarDialog by remember { mutableStateOf(false) }
    var showEmojiDialog by remember { mutableStateOf(false) }
    var showAddConfigDialog by remember { mutableStateOf(false) }
    
    // 图片选择器
    val imagePickerLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.GetContent()
    ) { uri: Uri? ->
        uri?.let { selectedUri ->
            // 处理选择的图片，将其保存到应用内部存储并更新设置
            coroutineScope.launch {
                val savedUri = saveImageToInternalStorage(context, selectedUri)
                savedUri?.let {
                    aiSettingsViewModel.updateCustomAvatar(context, it)
                }
            }
        }
    }
    
    // 更新临时值
    LaunchedEffect(aiSettings) {
        // 保持原有逻辑
    }
    
    // AI助手名称对话框
    if (showAssistantNameDialog) {
        var name by remember { mutableStateOf(aiSettings.assistantName) }
        
        AlertDialog(
            onDismissRequest = { showAssistantNameDialog = false },
            title = {
                Text(
                    stringResource(R.string.set_ai_assistant_name),
                    fontSize = 16.sp // 🔧 缩小标题字体
                )
            },
            text = {
                OutlinedTextField(
                    value = name,
                    onValueChange = { name = it },
                    label = {
                        Text(
                            stringResource(R.string.ai_assistant_name),
                            fontSize = 12.sp // 🔧 缩小标签字体
                        )
                    },
                    placeholder = {
                        Text(
                            stringResource(R.string.ai_assistant_name_example),
                            fontSize = 12.sp // 🔧 缩小占位符字体
                        )
                    },
                    singleLine = true,
                    textStyle = MaterialTheme.typography.bodyMedium.copy(fontSize = 13.sp), // 🔧 缩小输入字体
                    colors = TextFieldDefaults.outlinedTextFieldColors(
                        focusedBorderColor = DustyLavender,
                        unfocusedBorderColor = Color.Gray.copy(alpha = 0.3f)
                    )
                )
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        aiSettingsViewModel.updateAssistantName(context, name)
                        showAssistantNameDialog = false
                    }
                ) {
                    Text(
                        stringResource(R.string.confirm),
                        fontSize = 12.sp // 🔧 缩小按钮字体
                    )
                }
            },
            dismissButton = {
                TextButton(
                    onClick = { showAssistantNameDialog = false }
                ) {
                    Text(
                        stringResource(R.string.cancel),
                        fontSize = 12.sp // 🔧 缩小按钮字体
                    )
                }
            }
        )
    }
    
    // 头像选择对话框
    if (showAvatarDialog) {
        // 自定义AlertDialog内容以包含图片上传选项
        AlertDialog(
            onDismissRequest = { showAvatarDialog = false },
            title = { 
                Text(
                    "选择AI助手头像",
                    fontSize = 16.sp // 🔧 缩小标题字体
                ) 
            },
            text = {
                Column {
                    // 自定义头像展示
                    if (aiSettings.customAvatarUri != null) {
                        Text(
                            text = "当前自定义头像",
                            style = MaterialTheme.typography.bodyMedium.copy(fontSize = 12.sp), // 🔧 缩小字体
                            modifier = Modifier.padding(bottom = 8.dp)
                        )
                        
                        Box(
                            modifier = Modifier
                                .padding(bottom = 16.dp)
                                .align(Alignment.CenterHorizontally)
                        ) {
                            AsyncImage(
                                model = ImageRequest.Builder(context)
                                    .data(aiSettings.customAvatarUri)
                                    .crossfade(true)
                                    .build(),
                                contentDescription = "自定义头像",
                                modifier = Modifier
                                    .size(64.dp) // 🔧 缩小头像预览大小
                                    .clip(CircleShape)
                                    .border(
                                        width = 2.dp,
                                        color = DustyLavender.copy(alpha = 0.5f),
                                        shape = CircleShape
                                    )
                            )
                        }
                    }
                    
                    // 上传图片按钮
                    Button(
                        onClick = { 
                            imagePickerLauncher.launch("image/jpeg") 
                            showAvatarDialog = false
                        },
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 8.dp),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = DustyLavender
                        )
                    ) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                imageVector = Icons.Default.Image,
                                contentDescription = "上传图片",
                                modifier = Modifier.size(16.dp) // 🔧 缩小图标
                            )
                            Spacer(modifier = Modifier.width(6.dp))
                            Text(
                                "上传JPG图片作为头像",
                                fontSize = 12.sp // 🔧 缩小按钮文字
                            )
                        }
                    }
                    
                    Divider(
                        modifier = Modifier.padding(vertical = 12.dp),
                        color = Color.LightGray.copy(alpha = 0.5f)
                    )
                    
                    Text(
                        text = "或选择默认图标",
                        style = MaterialTheme.typography.bodyMedium.copy(fontSize = 12.sp), // 🔧 缩小字体
                        modifier = Modifier.padding(bottom = 8.dp)
                    )
                    
                    // 默认头像列表
                    val avatars = listOf("🤖", "🧠", "👾", "👨‍💻", "🦾", "🤓", "😎", "🧙‍♂️", "🧚‍♀️", "🦄", "🐼", "🦊")
                    LazyRow(
                        horizontalArrangement = Arrangement.spacedBy(6.dp) // 🔧 缩小间距
                    ) {
                        items(items = avatars) { avatar ->
                            Box(
                                modifier = Modifier
                                    .size(40.dp) // 🔧 缩小头像大小
                                    .background(
                                        color = if (aiSettings.assistantAvatar == avatar && aiSettings.customAvatarUri == null) 
                                            LightPurple.copy(alpha = 0.2f) 
                                        else 
                                            Color.Transparent,
                                        shape = CircleShape
                                    )
                                    .border(
                                        width = 1.dp,
                                        color = if (aiSettings.assistantAvatar == avatar && aiSettings.customAvatarUri == null) 
                                            DustyLavender 
                                        else 
                                            Color.LightGray.copy(alpha = 0.3f),
                                        shape = CircleShape
                                    )
                                    .clickable {
                                        aiSettingsViewModel.updateAssistantAvatar(context, avatar)
                                        showAvatarDialog = false
                                    },
                                contentAlignment = Alignment.Center
                            ) {
                                Text(
                                    text = avatar,
                                    fontSize = 18.sp // 🔧 缩小emoji字体
                                )
                            }
                        }
                    }
                }
            },
            confirmButton = {
                TextButton(onClick = { showAvatarDialog = false }) {
                    Text(
                        "关闭",
                        fontSize = 12.sp // 🔧 缩小按钮字体
                    )
                }
            }
        )
    }
    
    // Emoji选择对话框
    if (showEmojiDialog) {
        val emojis = listOf("✨", "🌟", "💫", "⭐", "🚀", "🌈", "🔮", "💡", "🎯", "🎨", "🎭", "🎬")
        AlertDialog(
            onDismissRequest = { showEmojiDialog = false },
            title = { 
                Text(
                    "选择提示消息Emoji",
                    fontSize = 16.sp // 🔧 缩小标题字体
                ) 
            },
            text = {
                LazyRow(
                    horizontalArrangement = Arrangement.spacedBy(6.dp) // 🔧 缩小间距
                ) {
                    items(items = emojis) { emoji ->
                        Box(
                            modifier = Modifier
                                .size(40.dp) // 🔧 缩小大小
                                .background(
                                    color = if (aiSettings.promptEmoji == emoji) 
                                        LightPurple.copy(alpha = 0.2f) 
                                    else 
                                        Color.Transparent,
                                    shape = CircleShape
                                )
                                .border(
                                    width = 1.dp,
                                    color = if (aiSettings.promptEmoji == emoji) 
                                        DustyLavender 
                                    else 
                                        Color.LightGray.copy(alpha = 0.3f),
                                    shape = CircleShape
                                )
                                .clickable {
                                    aiSettingsViewModel.updatePromptEmoji(context, emoji)
                                    showEmojiDialog = false
                                },
                            contentAlignment = Alignment.Center
                        ) {
                            Text(
                                text = emoji,
                                fontSize = 18.sp // 🔧 缩小emoji字体
                            )
                        }
                    }
                }
            },
            confirmButton = {
                TextButton(onClick = { showEmojiDialog = false }) {
                    Text(
                        "关闭",
                        fontSize = 12.sp // 🔧 缩小按钮字体
                    )
                }
            }
        )
    }
    
    Scaffold(
        topBar = {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = SystemBarManager.getFixedStatusBarHeight())
            ) {
                TopAppBar(
                    title = {
                        Text(
                            text = "AI设置",
                            style = MaterialTheme.typography.titleMedium.copy(
                                fontWeight = FontWeight.Bold,
                                fontSize = 16.sp // 🔧 缩小标题字体
                            ),
                            color = TextPrimary
                        )
                    },
                    navigationIcon = {
                        IconButton(onClick = onBackClick) {
                            Icon(
                                imageVector = Icons.Default.ArrowBack,
                                contentDescription = "返回",
                                tint = Color.DarkGray,
                                modifier = Modifier.size(20.dp) // 🔧 缩小图标
                            )
                        }
                    },
                    colors = TopAppBarDefaults.topAppBarColors(
                        containerColor = Color.White,
                        titleContentColor = MaterialTheme.colorScheme.primary
                    )
                )
            }
        },
        containerColor = settingsBackgroundColor
    ) { innerPadding ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(innerPadding)
                .padding(14.dp) // 🔧 缩小内边距
                .verticalScroll(rememberScrollState())
        ) {
            // 使用说明部分
            Surface(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 12.dp), // 🔧 缩小间距
                shape = RoundedCornerShape(10.dp), // 🔧 缩小圆角
                color = Color.White,
                shadowElevation = 0.dp
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(12.dp) // 🔧 缩小内边距
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier.padding(bottom = 6.dp) // 🔧 缩小间距
                    ) {
                        Box(
                            modifier = Modifier
                                .size(28.dp) // 🔧 缩小图标容器
                                .background(
                                    color = Color.LightGray.copy(alpha = 0.1f),
                                    shape = CircleShape
                                ),
                            contentAlignment = Alignment.Center
                        ) {
                            Icon(
                                imageVector = Icons.Default.Settings,
                                contentDescription = null,
                                tint = DustyLavender,
                                modifier = Modifier.size(14.dp) // 🔧 缩小图标
                            )
                        }
                        Spacer(modifier = Modifier.width(6.dp))
                        Text(
                            text = "AI服务配置",
                            style = MaterialTheme.typography.titleMedium.copy(fontSize = 14.sp), // 🔧 缩小字体
                            color = TextPrimary
                        )
                    }
                    
                    Text(
                        text = "您可以配置自定义的AI服务提供商，包括API密钥、服务器地址等。系统将根据您的选择连接不同的AI模型。",
                        style = MaterialTheme.typography.bodyMedium.copy(fontSize = 11.sp), // 🔧 缩小字体
                        color = TextSecondary
                    )
                }
            }
            
            // 个性化设置区域
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 12.dp), // 🔧 缩小间距
                shape = RoundedCornerShape(10.dp), // 🔧 缩小圆角
                colors = CardDefaults.cardColors(
                    containerColor = Color.White
                )
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(12.dp) // 🔧 缩小内边距
                ) {
                    Text(
                        text = "AI助手个性化设置",
                        style = MaterialTheme.typography.titleMedium.copy(
                            fontWeight = FontWeight.Bold,
                            fontSize = 14.sp // 🔧 缩小字体
                        ),
                        color = TextPrimary,
                        modifier = Modifier.padding(bottom = 12.dp) // 🔧 缩小间距
                    )
                    
                    // AI助手名称设置
                    SettingItem(
                        icon = {
                            Box(
                                modifier = Modifier
                                    .size(32.dp) // 🔧 缩小图标容器
                                    .background(
                                        color = PastelPink.copy(alpha = 0.2f),
                                        shape = CircleShape
                                    ),
                                contentAlignment = Alignment.Center
                            ) {
                                Text(
                                    text = "📝",
                                    fontSize = 16.sp // 🔧 缩小emoji
                                )
                            }
                        },
                        title = "AI助手名称",
                        subtitle = aiSettings.assistantName,
                        onClick = { showAssistantNameDialog = true }
                    )
                    
                    // AI助手头像设置
                    SettingItem(
                        icon = {
                            Box(
                                modifier = Modifier
                                    .size(32.dp) // 🔧 缩小图标容器
                                    .background(
                                        color = PastelPink.copy(alpha = 0.2f),
                                        shape = CircleShape
                                    ),
                                contentAlignment = Alignment.Center
                            ) {
                                if (aiSettings.customAvatarUri != null) {
                                    // 显示自定义头像
                                    AsyncImage(
                                        model = ImageRequest.Builder(context)
                                            .data(aiSettings.customAvatarUri)
                                            .crossfade(true)
                                            .build(),
                                        contentDescription = "AI助手头像",
                                        modifier = Modifier
                                            .size(28.dp) // 🔧 缩小头像
                                            .clip(CircleShape)
                                    )
                                } else {
                                    // 显示Emoji头像
                                    Text(
                                        text = aiSettings.assistantAvatar,
                                        fontSize = 16.sp // 🔧 缩小emoji
                                    )
                                }
                            }
                        },
                        title = "AI助手头像",
                        subtitle = if (aiSettings.customAvatarUri != null) "使用自定义图片" else "修改AI头像图标",
                        onClick = { showAvatarDialog = true }
                    )
                    
                    // 提示消息Emoji设置
                    SettingItem(
                        icon = {
                            Box(
                                modifier = Modifier
                                    .size(32.dp) // 🔧 缩小图标容器
                                    .background(
                                        color = PastelPink.copy(alpha = 0.2f),
                                        shape = CircleShape
                                    ),
                                contentAlignment = Alignment.Center
                            ) {
                                Text(
                                    text = aiSettings.promptEmoji,
                                    fontSize = 16.sp // 🔧 缩小emoji
                                )
                            }
                        },
                        title = "提示消息Emoji",
                        subtitle = "修改提示消息前的Emoji表情",
                        onClick = { showEmojiDialog = true }
                    )
                }
            }
            
            // 配置列表
            Text(
                text = "我的AI配置",
                style = MaterialTheme.typography.titleMedium.copy(fontSize = 14.sp), // 🔧 缩小字体
                color = TextPrimary,
                modifier = Modifier.padding(vertical = 6.dp) // 🔧 缩小间距
            )
            
            if (configurations.isEmpty()) {
                // 空状态
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(160.dp), // 🔧 缩小高度
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "暂无配置，点击下方按钮添加",
                        style = MaterialTheme.typography.bodyMedium.copy(fontSize = 12.sp), // 🔧 缩小字体
                        color = TextSecondary
                    )
                }
            } else {
                // 配置列表 - 直接显示配置项，不使用嵌套的LazyColumn
                configurations.forEach { config ->
                    val isSelected = config.id == selectedConfigId
                    
                    Surface(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 3.dp) // 🔧 缩小间距
                            .clickable {
                                // 选择此配置
                                aiConfigViewModel.selectConfig(context, config.id)
                            },
                        shape = RoundedCornerShape(10.dp), // 🔧 缩小圆角
                        color = if (isSelected) Color.LightGray.copy(alpha = 0.1f) else Color.White
                    ) {
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(12.dp), // 🔧 缩小内边距
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            // 配置图标
                            Box(
                                modifier = Modifier
                                    .size(32.dp) // 🔧 缩小图标容器
                                    .background(
                                        color = Color.LightGray.copy(alpha = 0.1f),
                                        shape = CircleShape
                                    )
                                    .border(
                                        width = 1.dp,
                                        color = Color.LightGray.copy(alpha = 0.3f),
                                        shape = CircleShape
                                    ),
                                contentAlignment = Alignment.Center
                            ) {
                                Text(
                                    text = when (config.provider) {
                                        "DeepSeek" -> "🧠"
                                        "GPT-4" -> "🤖"
                                        "Claude" -> "👾"
                                        "Gemini" -> "✨"
                                        else -> "🔮"
                                    },
                                    fontSize = 14.sp // 🔧 缩小emoji
                                )
                            }
                            
                            // 配置信息
                            Column(
                                modifier = Modifier
                                    .weight(1f)
                                    .padding(horizontal = 10.dp) // 🔧 缩小间距
                            ) {
                                Text(
                                    text = config.name,
                                    style = MaterialTheme.typography.titleMedium.copy(fontSize = 13.sp), // 🔧 缩小字体
                                    color = TextPrimary
                                )
                                Spacer(modifier = Modifier.height(2.dp))
                                Text(
                                    text = "服务商: ${config.provider}",
                                    style = MaterialTheme.typography.bodyMedium.copy(fontSize = 11.sp), // 🔧 缩小字体
                                    color = TextSecondary,
                                    maxLines = 1,
                                    overflow = TextOverflow.Ellipsis
                                )
                            }
                            
                            // 已选中标记
                            if (isSelected) {
                                Box(
                                    modifier = Modifier
                                        .size(24.dp) // 🔧 缩小标记
                                        .background(
                                            color = DustyLavender,
                                            shape = CircleShape
                                        ),
                                    contentAlignment = Alignment.Center
                                ) {
                                    Icon(
                                        imageVector = Icons.Default.Check,
                                        contentDescription = "已选择",
                                        tint = Color.White,
                                        modifier = Modifier.size(12.dp) // 🔧 缩小图标
                                    )
                                }
                            }
                            
                            // 编辑按钮
                            IconButton(
                                onClick = {
                                    // 导航到编辑页面，并传递配置ID
                                    navController.navigate(AppDestinations.aiModelSettingsRoute(config.id))
                                },
                                modifier = Modifier.size(32.dp) // 🔧 缩小按钮
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Edit,
                                    contentDescription = "编辑",
                                    tint = TextSecondary,
                                    modifier = Modifier.size(16.dp) // 🔧 缩小图标
                                )
                            }
                            
                            // 删除按钮 (不允许删除默认配置)
                            if (!config.isDefault) {
                                IconButton(
                                    onClick = {
                                        aiConfigViewModel.deleteConfiguration(context, config.id)
                                    },
                                    modifier = Modifier.size(32.dp) // 🔧 缩小按钮
                                ) {
                                    Icon(
                                        imageVector = Icons.Default.Delete,
                                        contentDescription = "删除",
                                        tint = TextSecondary,
                                        modifier = Modifier.size(16.dp) // 🔧 缩小图标
                                    )
                                }
                            }
                        }
                    }
                }
            }
            
            // 添加按钮
            Button(
                onClick = {
                    // 导航到新增配置页面
                    navController.navigate(AppDestinations.AI_MODEL_SETTINGS_ROUTE)
                },
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 12.dp), // 🔧 缩小间距
                shape = RoundedCornerShape(40.dp), // 🔧 缩小圆角
                colors = ButtonDefaults.buttonColors(
                    containerColor = DustyLavender
                )
            ) {
                Icon(
                    imageVector = Icons.Default.Add,
                    contentDescription = "添加",
                    tint = Color.White,
                    modifier = Modifier.size(14.dp) // 🔧 缩小图标
                )
                Spacer(modifier = Modifier.width(6.dp))
                Text(
                    text = "添加新配置",
                    style = MaterialTheme.typography.bodyMedium.copy(fontSize = 12.sp) // 🔧 缩小字体
                )
            }
        }
    }
}

// 设置项组件
@Composable
private fun SettingItem(
    icon: @Composable () -> Unit,
    title: String,
    subtitle: String,
    onClick: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable(onClick = onClick)
            .padding(vertical = 8.dp), // 🔧 缩小内边距
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 图标
        icon()
        
        // 标题和子标题
        Column(
            modifier = Modifier
                .weight(1f)
                .padding(horizontal = 12.dp) // 🔧 缩小间距
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.bodyLarge.copy(fontSize = 13.sp), // 🔧 缩小字体
                color = TextPrimary
            )
            
            Text(
                text = subtitle,
                style = MaterialTheme.typography.bodyMedium.copy(fontSize = 11.sp), // 🔧 缩小字体
                color = TextSecondary
            )
        }
        
        // 右侧箭头
        Icon(
            imageVector = Icons.Default.KeyboardArrowRight,
            contentDescription = null,
            tint = TextSecondary,
            modifier = Modifier.size(20.dp) // 🔧 缩小图标
        )
    }
}

// 修复saveImageToInternalStorage函数
private suspend fun saveImageToInternalStorage(context: Context, imageUri: Uri): String? {
    return withContext(Dispatchers.IO) {
        try {
            // 创建唯一文件名
            val fileName = "avatar_${UUID.randomUUID()}.jpg"
            val directory = File(context.filesDir, "avatars")
            if (!directory.exists()) {
                directory.mkdirs()
            }
            
            val file = File(directory, fileName)
            
            // 读取输入流
            context.contentResolver.openInputStream(imageUri)?.use { inputStream ->
                // 解析为位图
                val bitmap = BitmapFactory.decodeStream(inputStream)
                
                // 压缩并保存为jpg
                FileOutputStream(file).use { outputStream ->
                    // 压缩为JPEG，质量80%
                    bitmap.compress(Bitmap.CompressFormat.JPEG, 80, outputStream)
                    outputStream.flush()
                }
                
                // 返回文件URI作为字符串
                file.absolutePath
            } ?: run {
                Log.e("AiSettingsScreen", "无法打开输入流")
                null
            }
        } catch (e: IOException) {
            Log.e("AiSettingsScreen", "保存图片失败", e)
            null
        }
    }
} 