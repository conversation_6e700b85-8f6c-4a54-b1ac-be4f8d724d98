package com.timeflow.app.data.dao;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.timeflow.app.data.converter.Converters;
import com.timeflow.app.data.converter.ListStringConverter;
import com.timeflow.app.data.model.TimeSession;
import java.lang.Class;
import java.lang.Exception;
import java.lang.IllegalStateException;
import java.lang.Integer;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class TimeSessionDao_Impl implements TimeSessionDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<TimeSession> __insertionAdapterOfTimeSession;

  private final Converters __converters = new Converters();

  private final ListStringConverter __listStringConverter = new ListStringConverter();

  private final EntityDeletionOrUpdateAdapter<TimeSession> __deletionAdapterOfTimeSession;

  private final EntityDeletionOrUpdateAdapter<TimeSession> __updateAdapterOfTimeSession;

  private final SharedSQLiteStatement __preparedStmtOfDeleteOldSessions;

  public TimeSessionDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfTimeSession = new EntityInsertionAdapter<TimeSession>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `time_sessions` (`id`,`taskId`,`taskName`,`startTime`,`endTime`,`duration`,`timerType`,`isCompleted`,`notes`,`createdAt`,`updatedAt`,`tags`,`focusRating`,`productivityRating`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final TimeSession entity) {
        statement.bindString(1, entity.getId());
        statement.bindString(2, entity.getTaskId());
        statement.bindString(3, entity.getTaskName());
        final Long _tmp = __converters.instantToTimestamp(entity.getStartTime());
        if (_tmp == null) {
          statement.bindNull(4);
        } else {
          statement.bindLong(4, _tmp);
        }
        final Long _tmp_1 = __converters.instantToTimestamp(entity.getEndTime());
        if (_tmp_1 == null) {
          statement.bindNull(5);
        } else {
          statement.bindLong(5, _tmp_1);
        }
        statement.bindLong(6, entity.getDuration());
        statement.bindString(7, entity.getTimerType());
        final int _tmp_2 = entity.isCompleted() ? 1 : 0;
        statement.bindLong(8, _tmp_2);
        statement.bindString(9, entity.getNotes());
        final Long _tmp_3 = __converters.instantToTimestamp(entity.getCreatedAt());
        if (_tmp_3 == null) {
          statement.bindNull(10);
        } else {
          statement.bindLong(10, _tmp_3);
        }
        final Long _tmp_4 = __converters.instantToTimestamp(entity.getUpdatedAt());
        if (_tmp_4 == null) {
          statement.bindNull(11);
        } else {
          statement.bindLong(11, _tmp_4);
        }
        final String _tmp_5 = __listStringConverter.fromList(entity.getTags());
        statement.bindString(12, _tmp_5);
        if (entity.getFocusRating() == null) {
          statement.bindNull(13);
        } else {
          statement.bindLong(13, entity.getFocusRating());
        }
        if (entity.getProductivityRating() == null) {
          statement.bindNull(14);
        } else {
          statement.bindLong(14, entity.getProductivityRating());
        }
      }
    };
    this.__deletionAdapterOfTimeSession = new EntityDeletionOrUpdateAdapter<TimeSession>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `time_sessions` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final TimeSession entity) {
        statement.bindString(1, entity.getId());
      }
    };
    this.__updateAdapterOfTimeSession = new EntityDeletionOrUpdateAdapter<TimeSession>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `time_sessions` SET `id` = ?,`taskId` = ?,`taskName` = ?,`startTime` = ?,`endTime` = ?,`duration` = ?,`timerType` = ?,`isCompleted` = ?,`notes` = ?,`createdAt` = ?,`updatedAt` = ?,`tags` = ?,`focusRating` = ?,`productivityRating` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final TimeSession entity) {
        statement.bindString(1, entity.getId());
        statement.bindString(2, entity.getTaskId());
        statement.bindString(3, entity.getTaskName());
        final Long _tmp = __converters.instantToTimestamp(entity.getStartTime());
        if (_tmp == null) {
          statement.bindNull(4);
        } else {
          statement.bindLong(4, _tmp);
        }
        final Long _tmp_1 = __converters.instantToTimestamp(entity.getEndTime());
        if (_tmp_1 == null) {
          statement.bindNull(5);
        } else {
          statement.bindLong(5, _tmp_1);
        }
        statement.bindLong(6, entity.getDuration());
        statement.bindString(7, entity.getTimerType());
        final int _tmp_2 = entity.isCompleted() ? 1 : 0;
        statement.bindLong(8, _tmp_2);
        statement.bindString(9, entity.getNotes());
        final Long _tmp_3 = __converters.instantToTimestamp(entity.getCreatedAt());
        if (_tmp_3 == null) {
          statement.bindNull(10);
        } else {
          statement.bindLong(10, _tmp_3);
        }
        final Long _tmp_4 = __converters.instantToTimestamp(entity.getUpdatedAt());
        if (_tmp_4 == null) {
          statement.bindNull(11);
        } else {
          statement.bindLong(11, _tmp_4);
        }
        final String _tmp_5 = __listStringConverter.fromList(entity.getTags());
        statement.bindString(12, _tmp_5);
        if (entity.getFocusRating() == null) {
          statement.bindNull(13);
        } else {
          statement.bindLong(13, entity.getFocusRating());
        }
        if (entity.getProductivityRating() == null) {
          statement.bindNull(14);
        } else {
          statement.bindLong(14, entity.getProductivityRating());
        }
        statement.bindString(15, entity.getId());
      }
    };
    this.__preparedStmtOfDeleteOldSessions = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM time_sessions WHERE startTime < ?";
        return _query;
      }
    };
  }

  @Override
  public Object insertSession(final TimeSession session,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfTimeSession.insert(session);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteSession(final TimeSession session,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfTimeSession.handle(session);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateSession(final TimeSession session,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfTimeSession.handle(session);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteOldSessions(final long cutoffTime,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteOldSessions.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, cutoffTime);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteOldSessions.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object getSessionById(final String sessionId,
      final Continuation<? super TimeSession> $completion) {
    final String _sql = "SELECT * FROM time_sessions WHERE id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, sessionId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<TimeSession>() {
      @Override
      @Nullable
      public TimeSession call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfTaskId = CursorUtil.getColumnIndexOrThrow(_cursor, "taskId");
          final int _cursorIndexOfTaskName = CursorUtil.getColumnIndexOrThrow(_cursor, "taskName");
          final int _cursorIndexOfStartTime = CursorUtil.getColumnIndexOrThrow(_cursor, "startTime");
          final int _cursorIndexOfEndTime = CursorUtil.getColumnIndexOrThrow(_cursor, "endTime");
          final int _cursorIndexOfDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "duration");
          final int _cursorIndexOfTimerType = CursorUtil.getColumnIndexOrThrow(_cursor, "timerType");
          final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "isCompleted");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfFocusRating = CursorUtil.getColumnIndexOrThrow(_cursor, "focusRating");
          final int _cursorIndexOfProductivityRating = CursorUtil.getColumnIndexOrThrow(_cursor, "productivityRating");
          final TimeSession _result;
          if (_cursor.moveToFirst()) {
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpTaskId;
            _tmpTaskId = _cursor.getString(_cursorIndexOfTaskId);
            final String _tmpTaskName;
            _tmpTaskName = _cursor.getString(_cursorIndexOfTaskName);
            final Instant _tmpStartTime;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfStartTime)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfStartTime);
            }
            final Instant _tmp_1 = __converters.fromTimestampToInstant(_tmp);
            if (_tmp_1 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.Instant', but it was NULL.");
            } else {
              _tmpStartTime = _tmp_1;
            }
            final Instant _tmpEndTime;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfEndTime)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfEndTime);
            }
            _tmpEndTime = __converters.fromTimestampToInstant(_tmp_2);
            final long _tmpDuration;
            _tmpDuration = _cursor.getLong(_cursorIndexOfDuration);
            final String _tmpTimerType;
            _tmpTimerType = _cursor.getString(_cursorIndexOfTimerType);
            final boolean _tmpIsCompleted;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfIsCompleted);
            _tmpIsCompleted = _tmp_3 != 0;
            final String _tmpNotes;
            _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            final Instant _tmpCreatedAt;
            final Long _tmp_4;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            final Instant _tmp_5 = __converters.fromTimestampToInstant(_tmp_4);
            if (_tmp_5 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.Instant', but it was NULL.");
            } else {
              _tmpCreatedAt = _tmp_5;
            }
            final Instant _tmpUpdatedAt;
            final Long _tmp_6;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmp_6 = null;
            } else {
              _tmp_6 = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            final Instant _tmp_7 = __converters.fromTimestampToInstant(_tmp_6);
            if (_tmp_7 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.Instant', but it was NULL.");
            } else {
              _tmpUpdatedAt = _tmp_7;
            }
            final List<String> _tmpTags;
            final String _tmp_8;
            _tmp_8 = _cursor.getString(_cursorIndexOfTags);
            _tmpTags = __listStringConverter.toList(_tmp_8);
            final Integer _tmpFocusRating;
            if (_cursor.isNull(_cursorIndexOfFocusRating)) {
              _tmpFocusRating = null;
            } else {
              _tmpFocusRating = _cursor.getInt(_cursorIndexOfFocusRating);
            }
            final Integer _tmpProductivityRating;
            if (_cursor.isNull(_cursorIndexOfProductivityRating)) {
              _tmpProductivityRating = null;
            } else {
              _tmpProductivityRating = _cursor.getInt(_cursorIndexOfProductivityRating);
            }
            _result = new TimeSession(_tmpId,_tmpTaskId,_tmpTaskName,_tmpStartTime,_tmpEndTime,_tmpDuration,_tmpTimerType,_tmpIsCompleted,_tmpNotes,_tmpCreatedAt,_tmpUpdatedAt,_tmpTags,_tmpFocusRating,_tmpProductivityRating);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<TimeSession>> getAllSessions() {
    final String _sql = "SELECT * FROM time_sessions ORDER BY startTime DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"time_sessions"}, new Callable<List<TimeSession>>() {
      @Override
      @NonNull
      public List<TimeSession> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfTaskId = CursorUtil.getColumnIndexOrThrow(_cursor, "taskId");
          final int _cursorIndexOfTaskName = CursorUtil.getColumnIndexOrThrow(_cursor, "taskName");
          final int _cursorIndexOfStartTime = CursorUtil.getColumnIndexOrThrow(_cursor, "startTime");
          final int _cursorIndexOfEndTime = CursorUtil.getColumnIndexOrThrow(_cursor, "endTime");
          final int _cursorIndexOfDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "duration");
          final int _cursorIndexOfTimerType = CursorUtil.getColumnIndexOrThrow(_cursor, "timerType");
          final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "isCompleted");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfFocusRating = CursorUtil.getColumnIndexOrThrow(_cursor, "focusRating");
          final int _cursorIndexOfProductivityRating = CursorUtil.getColumnIndexOrThrow(_cursor, "productivityRating");
          final List<TimeSession> _result = new ArrayList<TimeSession>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final TimeSession _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpTaskId;
            _tmpTaskId = _cursor.getString(_cursorIndexOfTaskId);
            final String _tmpTaskName;
            _tmpTaskName = _cursor.getString(_cursorIndexOfTaskName);
            final Instant _tmpStartTime;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfStartTime)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfStartTime);
            }
            final Instant _tmp_1 = __converters.fromTimestampToInstant(_tmp);
            if (_tmp_1 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.Instant', but it was NULL.");
            } else {
              _tmpStartTime = _tmp_1;
            }
            final Instant _tmpEndTime;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfEndTime)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfEndTime);
            }
            _tmpEndTime = __converters.fromTimestampToInstant(_tmp_2);
            final long _tmpDuration;
            _tmpDuration = _cursor.getLong(_cursorIndexOfDuration);
            final String _tmpTimerType;
            _tmpTimerType = _cursor.getString(_cursorIndexOfTimerType);
            final boolean _tmpIsCompleted;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfIsCompleted);
            _tmpIsCompleted = _tmp_3 != 0;
            final String _tmpNotes;
            _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            final Instant _tmpCreatedAt;
            final Long _tmp_4;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            final Instant _tmp_5 = __converters.fromTimestampToInstant(_tmp_4);
            if (_tmp_5 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.Instant', but it was NULL.");
            } else {
              _tmpCreatedAt = _tmp_5;
            }
            final Instant _tmpUpdatedAt;
            final Long _tmp_6;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmp_6 = null;
            } else {
              _tmp_6 = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            final Instant _tmp_7 = __converters.fromTimestampToInstant(_tmp_6);
            if (_tmp_7 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.Instant', but it was NULL.");
            } else {
              _tmpUpdatedAt = _tmp_7;
            }
            final List<String> _tmpTags;
            final String _tmp_8;
            _tmp_8 = _cursor.getString(_cursorIndexOfTags);
            _tmpTags = __listStringConverter.toList(_tmp_8);
            final Integer _tmpFocusRating;
            if (_cursor.isNull(_cursorIndexOfFocusRating)) {
              _tmpFocusRating = null;
            } else {
              _tmpFocusRating = _cursor.getInt(_cursorIndexOfFocusRating);
            }
            final Integer _tmpProductivityRating;
            if (_cursor.isNull(_cursorIndexOfProductivityRating)) {
              _tmpProductivityRating = null;
            } else {
              _tmpProductivityRating = _cursor.getInt(_cursorIndexOfProductivityRating);
            }
            _item = new TimeSession(_tmpId,_tmpTaskId,_tmpTaskName,_tmpStartTime,_tmpEndTime,_tmpDuration,_tmpTimerType,_tmpIsCompleted,_tmpNotes,_tmpCreatedAt,_tmpUpdatedAt,_tmpTags,_tmpFocusRating,_tmpProductivityRating);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<TimeSession>> getSessionsByDate(final String date) {
    final String _sql = "\n"
            + "        SELECT * FROM time_sessions \n"
            + "        WHERE date(startTime / 1000, 'unixepoch') = ? \n"
            + "        ORDER BY startTime DESC\n"
            + "    ";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, date);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"time_sessions"}, new Callable<List<TimeSession>>() {
      @Override
      @NonNull
      public List<TimeSession> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfTaskId = CursorUtil.getColumnIndexOrThrow(_cursor, "taskId");
          final int _cursorIndexOfTaskName = CursorUtil.getColumnIndexOrThrow(_cursor, "taskName");
          final int _cursorIndexOfStartTime = CursorUtil.getColumnIndexOrThrow(_cursor, "startTime");
          final int _cursorIndexOfEndTime = CursorUtil.getColumnIndexOrThrow(_cursor, "endTime");
          final int _cursorIndexOfDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "duration");
          final int _cursorIndexOfTimerType = CursorUtil.getColumnIndexOrThrow(_cursor, "timerType");
          final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "isCompleted");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfFocusRating = CursorUtil.getColumnIndexOrThrow(_cursor, "focusRating");
          final int _cursorIndexOfProductivityRating = CursorUtil.getColumnIndexOrThrow(_cursor, "productivityRating");
          final List<TimeSession> _result = new ArrayList<TimeSession>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final TimeSession _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpTaskId;
            _tmpTaskId = _cursor.getString(_cursorIndexOfTaskId);
            final String _tmpTaskName;
            _tmpTaskName = _cursor.getString(_cursorIndexOfTaskName);
            final Instant _tmpStartTime;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfStartTime)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfStartTime);
            }
            final Instant _tmp_1 = __converters.fromTimestampToInstant(_tmp);
            if (_tmp_1 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.Instant', but it was NULL.");
            } else {
              _tmpStartTime = _tmp_1;
            }
            final Instant _tmpEndTime;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfEndTime)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfEndTime);
            }
            _tmpEndTime = __converters.fromTimestampToInstant(_tmp_2);
            final long _tmpDuration;
            _tmpDuration = _cursor.getLong(_cursorIndexOfDuration);
            final String _tmpTimerType;
            _tmpTimerType = _cursor.getString(_cursorIndexOfTimerType);
            final boolean _tmpIsCompleted;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfIsCompleted);
            _tmpIsCompleted = _tmp_3 != 0;
            final String _tmpNotes;
            _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            final Instant _tmpCreatedAt;
            final Long _tmp_4;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            final Instant _tmp_5 = __converters.fromTimestampToInstant(_tmp_4);
            if (_tmp_5 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.Instant', but it was NULL.");
            } else {
              _tmpCreatedAt = _tmp_5;
            }
            final Instant _tmpUpdatedAt;
            final Long _tmp_6;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmp_6 = null;
            } else {
              _tmp_6 = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            final Instant _tmp_7 = __converters.fromTimestampToInstant(_tmp_6);
            if (_tmp_7 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.Instant', but it was NULL.");
            } else {
              _tmpUpdatedAt = _tmp_7;
            }
            final List<String> _tmpTags;
            final String _tmp_8;
            _tmp_8 = _cursor.getString(_cursorIndexOfTags);
            _tmpTags = __listStringConverter.toList(_tmp_8);
            final Integer _tmpFocusRating;
            if (_cursor.isNull(_cursorIndexOfFocusRating)) {
              _tmpFocusRating = null;
            } else {
              _tmpFocusRating = _cursor.getInt(_cursorIndexOfFocusRating);
            }
            final Integer _tmpProductivityRating;
            if (_cursor.isNull(_cursorIndexOfProductivityRating)) {
              _tmpProductivityRating = null;
            } else {
              _tmpProductivityRating = _cursor.getInt(_cursorIndexOfProductivityRating);
            }
            _item = new TimeSession(_tmpId,_tmpTaskId,_tmpTaskName,_tmpStartTime,_tmpEndTime,_tmpDuration,_tmpTimerType,_tmpIsCompleted,_tmpNotes,_tmpCreatedAt,_tmpUpdatedAt,_tmpTags,_tmpFocusRating,_tmpProductivityRating);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<TimeSession>> getSessionsByTaskId(final String taskId) {
    final String _sql = "\n"
            + "        SELECT * FROM time_sessions \n"
            + "        WHERE taskId = ? \n"
            + "        ORDER BY startTime DESC\n"
            + "    ";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, taskId);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"time_sessions"}, new Callable<List<TimeSession>>() {
      @Override
      @NonNull
      public List<TimeSession> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfTaskId = CursorUtil.getColumnIndexOrThrow(_cursor, "taskId");
          final int _cursorIndexOfTaskName = CursorUtil.getColumnIndexOrThrow(_cursor, "taskName");
          final int _cursorIndexOfStartTime = CursorUtil.getColumnIndexOrThrow(_cursor, "startTime");
          final int _cursorIndexOfEndTime = CursorUtil.getColumnIndexOrThrow(_cursor, "endTime");
          final int _cursorIndexOfDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "duration");
          final int _cursorIndexOfTimerType = CursorUtil.getColumnIndexOrThrow(_cursor, "timerType");
          final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "isCompleted");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfFocusRating = CursorUtil.getColumnIndexOrThrow(_cursor, "focusRating");
          final int _cursorIndexOfProductivityRating = CursorUtil.getColumnIndexOrThrow(_cursor, "productivityRating");
          final List<TimeSession> _result = new ArrayList<TimeSession>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final TimeSession _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpTaskId;
            _tmpTaskId = _cursor.getString(_cursorIndexOfTaskId);
            final String _tmpTaskName;
            _tmpTaskName = _cursor.getString(_cursorIndexOfTaskName);
            final Instant _tmpStartTime;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfStartTime)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfStartTime);
            }
            final Instant _tmp_1 = __converters.fromTimestampToInstant(_tmp);
            if (_tmp_1 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.Instant', but it was NULL.");
            } else {
              _tmpStartTime = _tmp_1;
            }
            final Instant _tmpEndTime;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfEndTime)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfEndTime);
            }
            _tmpEndTime = __converters.fromTimestampToInstant(_tmp_2);
            final long _tmpDuration;
            _tmpDuration = _cursor.getLong(_cursorIndexOfDuration);
            final String _tmpTimerType;
            _tmpTimerType = _cursor.getString(_cursorIndexOfTimerType);
            final boolean _tmpIsCompleted;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfIsCompleted);
            _tmpIsCompleted = _tmp_3 != 0;
            final String _tmpNotes;
            _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            final Instant _tmpCreatedAt;
            final Long _tmp_4;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            final Instant _tmp_5 = __converters.fromTimestampToInstant(_tmp_4);
            if (_tmp_5 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.Instant', but it was NULL.");
            } else {
              _tmpCreatedAt = _tmp_5;
            }
            final Instant _tmpUpdatedAt;
            final Long _tmp_6;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmp_6 = null;
            } else {
              _tmp_6 = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            final Instant _tmp_7 = __converters.fromTimestampToInstant(_tmp_6);
            if (_tmp_7 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.Instant', but it was NULL.");
            } else {
              _tmpUpdatedAt = _tmp_7;
            }
            final List<String> _tmpTags;
            final String _tmp_8;
            _tmp_8 = _cursor.getString(_cursorIndexOfTags);
            _tmpTags = __listStringConverter.toList(_tmp_8);
            final Integer _tmpFocusRating;
            if (_cursor.isNull(_cursorIndexOfFocusRating)) {
              _tmpFocusRating = null;
            } else {
              _tmpFocusRating = _cursor.getInt(_cursorIndexOfFocusRating);
            }
            final Integer _tmpProductivityRating;
            if (_cursor.isNull(_cursorIndexOfProductivityRating)) {
              _tmpProductivityRating = null;
            } else {
              _tmpProductivityRating = _cursor.getInt(_cursorIndexOfProductivityRating);
            }
            _item = new TimeSession(_tmpId,_tmpTaskId,_tmpTaskName,_tmpStartTime,_tmpEndTime,_tmpDuration,_tmpTimerType,_tmpIsCompleted,_tmpNotes,_tmpCreatedAt,_tmpUpdatedAt,_tmpTags,_tmpFocusRating,_tmpProductivityRating);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<Long> getTaskTotalTime(final String taskId) {
    final String _sql = "\n"
            + "        SELECT COALESCE(SUM(duration), 0) \n"
            + "        FROM time_sessions \n"
            + "        WHERE taskId = ? AND isCompleted = 1\n"
            + "    ";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, taskId);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"time_sessions"}, new Callable<Long>() {
      @Override
      @NonNull
      public Long call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Long _result;
          if (_cursor.moveToFirst()) {
            final long _tmp;
            _tmp = _cursor.getLong(0);
            _result = _tmp;
          } else {
            _result = 0L;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<Long> getTodayTotalTime() {
    final String _sql = "\n"
            + "        SELECT COALESCE(SUM(duration), 0) \n"
            + "        FROM time_sessions \n"
            + "        WHERE date(startTime / 1000, 'unixepoch') = date('now') \n"
            + "        AND isCompleted = 1\n"
            + "    ";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"time_sessions"}, new Callable<Long>() {
      @Override
      @NonNull
      public Long call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Long _result;
          if (_cursor.moveToFirst()) {
            final long _tmp;
            _tmp = _cursor.getLong(0);
            _result = _tmp;
          } else {
            _result = 0L;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<Long> getWeekTotalTime(final long weekStart, final long weekEnd) {
    final String _sql = "\n"
            + "        SELECT COALESCE(SUM(duration), 0) \n"
            + "        FROM time_sessions \n"
            + "        WHERE startTime >= ? AND startTime <= ?\n"
            + "        AND isCompleted = 1\n"
            + "    ";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, weekStart);
    _argIndex = 2;
    _statement.bindLong(_argIndex, weekEnd);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"time_sessions"}, new Callable<Long>() {
      @Override
      @NonNull
      public Long call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Long _result;
          if (_cursor.moveToFirst()) {
            final long _tmp;
            _tmp = _cursor.getLong(0);
            _result = _tmp;
          } else {
            _result = 0L;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<Long> getMonthTotalTime() {
    final String _sql = "\n"
            + "        SELECT COALESCE(SUM(duration), 0) \n"
            + "        FROM time_sessions \n"
            + "        WHERE strftime('%Y-%m', startTime / 1000, 'unixepoch') = strftime('%Y-%m', 'now')\n"
            + "        AND isCompleted = 1\n"
            + "    ";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"time_sessions"}, new Callable<Long>() {
      @Override
      @NonNull
      public Long call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Long _result;
          if (_cursor.moveToFirst()) {
            final long _tmp;
            _tmp = _cursor.getLong(0);
            _result = _tmp;
          } else {
            _result = 0L;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getActiveSession(final Continuation<? super TimeSession> $completion) {
    final String _sql = "\n"
            + "        SELECT * FROM time_sessions \n"
            + "        WHERE isCompleted = 0 AND endTime IS NULL\n"
            + "        ORDER BY startTime DESC \n"
            + "        LIMIT 1\n"
            + "    ";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<TimeSession>() {
      @Override
      @Nullable
      public TimeSession call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfTaskId = CursorUtil.getColumnIndexOrThrow(_cursor, "taskId");
          final int _cursorIndexOfTaskName = CursorUtil.getColumnIndexOrThrow(_cursor, "taskName");
          final int _cursorIndexOfStartTime = CursorUtil.getColumnIndexOrThrow(_cursor, "startTime");
          final int _cursorIndexOfEndTime = CursorUtil.getColumnIndexOrThrow(_cursor, "endTime");
          final int _cursorIndexOfDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "duration");
          final int _cursorIndexOfTimerType = CursorUtil.getColumnIndexOrThrow(_cursor, "timerType");
          final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "isCompleted");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfFocusRating = CursorUtil.getColumnIndexOrThrow(_cursor, "focusRating");
          final int _cursorIndexOfProductivityRating = CursorUtil.getColumnIndexOrThrow(_cursor, "productivityRating");
          final TimeSession _result;
          if (_cursor.moveToFirst()) {
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpTaskId;
            _tmpTaskId = _cursor.getString(_cursorIndexOfTaskId);
            final String _tmpTaskName;
            _tmpTaskName = _cursor.getString(_cursorIndexOfTaskName);
            final Instant _tmpStartTime;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfStartTime)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfStartTime);
            }
            final Instant _tmp_1 = __converters.fromTimestampToInstant(_tmp);
            if (_tmp_1 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.Instant', but it was NULL.");
            } else {
              _tmpStartTime = _tmp_1;
            }
            final Instant _tmpEndTime;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfEndTime)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfEndTime);
            }
            _tmpEndTime = __converters.fromTimestampToInstant(_tmp_2);
            final long _tmpDuration;
            _tmpDuration = _cursor.getLong(_cursorIndexOfDuration);
            final String _tmpTimerType;
            _tmpTimerType = _cursor.getString(_cursorIndexOfTimerType);
            final boolean _tmpIsCompleted;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfIsCompleted);
            _tmpIsCompleted = _tmp_3 != 0;
            final String _tmpNotes;
            _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            final Instant _tmpCreatedAt;
            final Long _tmp_4;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            final Instant _tmp_5 = __converters.fromTimestampToInstant(_tmp_4);
            if (_tmp_5 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.Instant', but it was NULL.");
            } else {
              _tmpCreatedAt = _tmp_5;
            }
            final Instant _tmpUpdatedAt;
            final Long _tmp_6;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmp_6 = null;
            } else {
              _tmp_6 = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            final Instant _tmp_7 = __converters.fromTimestampToInstant(_tmp_6);
            if (_tmp_7 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.Instant', but it was NULL.");
            } else {
              _tmpUpdatedAt = _tmp_7;
            }
            final List<String> _tmpTags;
            final String _tmp_8;
            _tmp_8 = _cursor.getString(_cursorIndexOfTags);
            _tmpTags = __listStringConverter.toList(_tmp_8);
            final Integer _tmpFocusRating;
            if (_cursor.isNull(_cursorIndexOfFocusRating)) {
              _tmpFocusRating = null;
            } else {
              _tmpFocusRating = _cursor.getInt(_cursorIndexOfFocusRating);
            }
            final Integer _tmpProductivityRating;
            if (_cursor.isNull(_cursorIndexOfProductivityRating)) {
              _tmpProductivityRating = null;
            } else {
              _tmpProductivityRating = _cursor.getInt(_cursorIndexOfProductivityRating);
            }
            _result = new TimeSession(_tmpId,_tmpTaskId,_tmpTaskName,_tmpStartTime,_tmpEndTime,_tmpDuration,_tmpTimerType,_tmpIsCompleted,_tmpNotes,_tmpCreatedAt,_tmpUpdatedAt,_tmpTags,_tmpFocusRating,_tmpProductivityRating);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getSessionStats(final long startTime, final long endTime,
      final Continuation<? super SessionStats> $completion) {
    final String _sql = "\n"
            + "        SELECT \n"
            + "            COUNT(*) as sessionCount,\n"
            + "            COALESCE(SUM(duration), 0) as totalDuration,\n"
            + "            AVG(duration) as avgDuration\n"
            + "        FROM time_sessions \n"
            + "        WHERE startTime >= ? AND startTime <= ?\n"
            + "        AND isCompleted = 1\n"
            + "    ";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, startTime);
    _argIndex = 2;
    _statement.bindLong(_argIndex, endTime);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<SessionStats>() {
      @Override
      @NonNull
      public SessionStats call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfSessionCount = 0;
          final int _cursorIndexOfTotalDuration = 1;
          final int _cursorIndexOfAvgDuration = 2;
          final SessionStats _result;
          if (_cursor.moveToFirst()) {
            final int _tmpSessionCount;
            _tmpSessionCount = _cursor.getInt(_cursorIndexOfSessionCount);
            final long _tmpTotalDuration;
            _tmpTotalDuration = _cursor.getLong(_cursorIndexOfTotalDuration);
            final double _tmpAvgDuration;
            _tmpAvgDuration = _cursor.getDouble(_cursorIndexOfAvgDuration);
            _result = new SessionStats(_tmpSessionCount,_tmpTotalDuration,_tmpAvgDuration);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<TaskTimeStats>> getTaskTimeStats(final long startTime, final long endTime) {
    final String _sql = "\n"
            + "        SELECT \n"
            + "            taskId,\n"
            + "            taskName,\n"
            + "            COUNT(*) as sessionCount,\n"
            + "            COALESCE(SUM(duration), 0) as totalDuration\n"
            + "        FROM time_sessions \n"
            + "        WHERE startTime >= ? AND startTime <= ?\n"
            + "        AND isCompleted = 1\n"
            + "        GROUP BY taskId, taskName\n"
            + "        ORDER BY totalDuration DESC\n"
            + "    ";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, startTime);
    _argIndex = 2;
    _statement.bindLong(_argIndex, endTime);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"time_sessions"}, new Callable<List<TaskTimeStats>>() {
      @Override
      @NonNull
      public List<TaskTimeStats> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfTaskId = 0;
          final int _cursorIndexOfTaskName = 1;
          final int _cursorIndexOfSessionCount = 2;
          final int _cursorIndexOfTotalDuration = 3;
          final List<TaskTimeStats> _result = new ArrayList<TaskTimeStats>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final TaskTimeStats _item;
            final String _tmpTaskId;
            _tmpTaskId = _cursor.getString(_cursorIndexOfTaskId);
            final String _tmpTaskName;
            _tmpTaskName = _cursor.getString(_cursorIndexOfTaskName);
            final int _tmpSessionCount;
            _tmpSessionCount = _cursor.getInt(_cursorIndexOfSessionCount);
            final long _tmpTotalDuration;
            _tmpTotalDuration = _cursor.getLong(_cursorIndexOfTotalDuration);
            _item = new TaskTimeStats(_tmpTaskId,_tmpTaskName,_tmpSessionCount,_tmpTotalDuration);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<TaskTimeStatsWithTags>> getTaskTimeStatsWithTags(final long startTime,
      final long endTime) {
    final String _sql = "\n"
            + "        SELECT \n"
            + "            ts.taskId,\n"
            + "            ts.taskName,\n"
            + "            COUNT(*) as sessionCount,\n"
            + "            COALESCE(SUM(ts.duration), 0) as totalDuration,\n"
            + "            COALESCE(t.tagIds, '') as taskTags\n"
            + "        FROM time_sessions ts\n"
            + "        LEFT JOIN tasks t ON ts.taskId = t.id\n"
            + "        WHERE ts.startTime >= ? AND ts.startTime <= ?\n"
            + "        AND ts.isCompleted = 1\n"
            + "        GROUP BY ts.taskId, ts.taskName, t.tagIds\n"
            + "        ORDER BY totalDuration DESC\n"
            + "    ";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, startTime);
    _argIndex = 2;
    _statement.bindLong(_argIndex, endTime);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"time_sessions",
        "tasks"}, new Callable<List<TaskTimeStatsWithTags>>() {
      @Override
      @NonNull
      public List<TaskTimeStatsWithTags> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfTaskId = 0;
          final int _cursorIndexOfTaskName = 1;
          final int _cursorIndexOfSessionCount = 2;
          final int _cursorIndexOfTotalDuration = 3;
          final int _cursorIndexOfTaskTags = 4;
          final List<TaskTimeStatsWithTags> _result = new ArrayList<TaskTimeStatsWithTags>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final TaskTimeStatsWithTags _item;
            final String _tmpTaskId;
            _tmpTaskId = _cursor.getString(_cursorIndexOfTaskId);
            final String _tmpTaskName;
            _tmpTaskName = _cursor.getString(_cursorIndexOfTaskName);
            final int _tmpSessionCount;
            _tmpSessionCount = _cursor.getInt(_cursorIndexOfSessionCount);
            final long _tmpTotalDuration;
            _tmpTotalDuration = _cursor.getLong(_cursorIndexOfTotalDuration);
            final String _tmpTaskTags;
            if (_cursor.isNull(_cursorIndexOfTaskTags)) {
              _tmpTaskTags = null;
            } else {
              _tmpTaskTags = _cursor.getString(_cursorIndexOfTaskTags);
            }
            _item = new TaskTimeStatsWithTags(_tmpTaskId,_tmpTaskName,_tmpSessionCount,_tmpTotalDuration,_tmpTaskTags);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<TimeSession>> getRecentSessions(final int limit) {
    final String _sql = "\n"
            + "        SELECT * FROM time_sessions \n"
            + "        WHERE isCompleted = 1\n"
            + "        ORDER BY startTime DESC \n"
            + "        LIMIT ?\n"
            + "    ";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, limit);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"time_sessions"}, new Callable<List<TimeSession>>() {
      @Override
      @NonNull
      public List<TimeSession> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfTaskId = CursorUtil.getColumnIndexOrThrow(_cursor, "taskId");
          final int _cursorIndexOfTaskName = CursorUtil.getColumnIndexOrThrow(_cursor, "taskName");
          final int _cursorIndexOfStartTime = CursorUtil.getColumnIndexOrThrow(_cursor, "startTime");
          final int _cursorIndexOfEndTime = CursorUtil.getColumnIndexOrThrow(_cursor, "endTime");
          final int _cursorIndexOfDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "duration");
          final int _cursorIndexOfTimerType = CursorUtil.getColumnIndexOrThrow(_cursor, "timerType");
          final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "isCompleted");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfFocusRating = CursorUtil.getColumnIndexOrThrow(_cursor, "focusRating");
          final int _cursorIndexOfProductivityRating = CursorUtil.getColumnIndexOrThrow(_cursor, "productivityRating");
          final List<TimeSession> _result = new ArrayList<TimeSession>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final TimeSession _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpTaskId;
            _tmpTaskId = _cursor.getString(_cursorIndexOfTaskId);
            final String _tmpTaskName;
            _tmpTaskName = _cursor.getString(_cursorIndexOfTaskName);
            final Instant _tmpStartTime;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfStartTime)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfStartTime);
            }
            final Instant _tmp_1 = __converters.fromTimestampToInstant(_tmp);
            if (_tmp_1 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.Instant', but it was NULL.");
            } else {
              _tmpStartTime = _tmp_1;
            }
            final Instant _tmpEndTime;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfEndTime)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfEndTime);
            }
            _tmpEndTime = __converters.fromTimestampToInstant(_tmp_2);
            final long _tmpDuration;
            _tmpDuration = _cursor.getLong(_cursorIndexOfDuration);
            final String _tmpTimerType;
            _tmpTimerType = _cursor.getString(_cursorIndexOfTimerType);
            final boolean _tmpIsCompleted;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfIsCompleted);
            _tmpIsCompleted = _tmp_3 != 0;
            final String _tmpNotes;
            _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            final Instant _tmpCreatedAt;
            final Long _tmp_4;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            final Instant _tmp_5 = __converters.fromTimestampToInstant(_tmp_4);
            if (_tmp_5 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.Instant', but it was NULL.");
            } else {
              _tmpCreatedAt = _tmp_5;
            }
            final Instant _tmpUpdatedAt;
            final Long _tmp_6;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmp_6 = null;
            } else {
              _tmp_6 = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            final Instant _tmp_7 = __converters.fromTimestampToInstant(_tmp_6);
            if (_tmp_7 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.Instant', but it was NULL.");
            } else {
              _tmpUpdatedAt = _tmp_7;
            }
            final List<String> _tmpTags;
            final String _tmp_8;
            _tmp_8 = _cursor.getString(_cursorIndexOfTags);
            _tmpTags = __listStringConverter.toList(_tmp_8);
            final Integer _tmpFocusRating;
            if (_cursor.isNull(_cursorIndexOfFocusRating)) {
              _tmpFocusRating = null;
            } else {
              _tmpFocusRating = _cursor.getInt(_cursorIndexOfFocusRating);
            }
            final Integer _tmpProductivityRating;
            if (_cursor.isNull(_cursorIndexOfProductivityRating)) {
              _tmpProductivityRating = null;
            } else {
              _tmpProductivityRating = _cursor.getInt(_cursorIndexOfProductivityRating);
            }
            _item = new TimeSession(_tmpId,_tmpTaskId,_tmpTaskName,_tmpStartTime,_tmpEndTime,_tmpDuration,_tmpTimerType,_tmpIsCompleted,_tmpNotes,_tmpCreatedAt,_tmpUpdatedAt,_tmpTags,_tmpFocusRating,_tmpProductivityRating);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
