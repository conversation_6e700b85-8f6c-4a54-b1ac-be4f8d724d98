package com.timeflow.app;

import androidx.datastore.core.DataStore;
import androidx.datastore.preferences.core.Preferences;
import com.timeflow.app.di.DataStoreModule;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@QualifierMetadata("com.timeflow.app.di.DataStoreModule.ThemeDataStore")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class MainActivity_MembersInjector implements MembersInjector<MainActivity> {
  private final Provider<DataStore<Preferences>> themeDataStoreProvider;

  public MainActivity_MembersInjector(Provider<DataStore<Preferences>> themeDataStoreProvider) {
    this.themeDataStoreProvider = themeDataStoreProvider;
  }

  public static MembersInjector<MainActivity> create(
      Provider<DataStore<Preferences>> themeDataStoreProvider) {
    return new MainActivity_MembersInjector(themeDataStoreProvider);
  }

  @Override
  public void injectMembers(MainActivity instance) {
    injectThemeDataStore(instance, themeDataStoreProvider.get());
  }

  @InjectedFieldSignature("com.timeflow.app.MainActivity.themeDataStore")
  @DataStoreModule.ThemeDataStore
  public static void injectThemeDataStore(MainActivity instance,
      DataStore<Preferences> themeDataStore) {
    instance.themeDataStore = themeDataStore;
  }
}
