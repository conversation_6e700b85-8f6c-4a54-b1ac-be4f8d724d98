package com.timeflow.app.worker;

import android.content.Context;
import androidx.work.WorkerParameters;
import com.timeflow.app.service.RecurringTaskManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class RecurringTaskWorker_Factory {
  private final Provider<RecurringTaskManager> recurringTaskManagerProvider;

  public RecurringTaskWorker_Factory(Provider<RecurringTaskManager> recurringTaskManagerProvider) {
    this.recurringTaskManagerProvider = recurringTaskManagerProvider;
  }

  public RecurringTaskWorker get(Context context, WorkerParameters workerParams) {
    return newInstance(context, workerParams, recurringTaskManagerProvider.get());
  }

  public static RecurringTaskWorker_Factory create(
      Provider<RecurringTaskManager> recurringTaskManagerProvider) {
    return new RecurringTaskWorker_Factory(recurringTaskManagerProvider);
  }

  public static RecurringTaskWorker newInstance(Context context, WorkerParameters workerParams,
      RecurringTaskManager recurringTaskManager) {
    return new RecurringTaskWorker(context, workerParams, recurringTaskManager);
  }
}
