package com.timeflow.app.data.repository;

import com.timeflow.app.data.dao.TimeSessionDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class TimeSessionRepository_Factory implements Factory<TimeSessionRepository> {
  private final Provider<TimeSessionDao> timeSessionDaoProvider;

  public TimeSessionRepository_Factory(Provider<TimeSessionDao> timeSessionDaoProvider) {
    this.timeSessionDaoProvider = timeSessionDaoProvider;
  }

  @Override
  public TimeSessionRepository get() {
    return newInstance(timeSessionDaoProvider.get());
  }

  public static TimeSessionRepository_Factory create(
      Provider<TimeSessionDao> timeSessionDaoProvider) {
    return new TimeSessionRepository_Factory(timeSessionDaoProvider);
  }

  public static TimeSessionRepository newInstance(TimeSessionDao timeSessionDao) {
    return new TimeSessionRepository(timeSessionDao);
  }
}
