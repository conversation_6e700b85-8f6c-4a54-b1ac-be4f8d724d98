package com.timeflow.app.ui.screen.reflection.data

import android.content.Context
import com.timeflow.app.R
import com.timeflow.app.ui.screen.reflection.SearchSuggestionService
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 搜索建议服务实现类
 */
@Singleton
class SearchSuggestionServiceImpl @Inject constructor(
    @ApplicationContext private val context: Context
) : SearchSuggestionService {

    // 获取本地化的搜索建议数据
    private fun getCommonSuggestions(): Map<String, List<String>> {
        return mapOf(
            "运" to listOf(
                context.getString(R.string.search_suggestion_exercise),
                context.getString(R.string.search_suggestion_exercise_effect),
                context.getString(R.string.search_suggestion_exercise_reflection),
                context.getString(R.string.search_suggestion_exercise_plan)
            ),
            "工" to listOf(
                context.getString(R.string.search_suggestion_work),
                context.getString(R.string.search_suggestion_work_pressure),
                context.getString(R.string.search_suggestion_work_progress),
                context.getString(R.string.search_suggestion_work_reflection),
                context.getString(R.string.search_suggestion_work_result)
            ),
            "学" to listOf(
                context.getString(R.string.search_suggestion_study),
                context.getString(R.string.search_suggestion_study_method),
                context.getString(R.string.search_suggestion_study_notes),
                context.getString(R.string.search_suggestion_study_progress),
                context.getString(R.string.search_suggestion_study_plan)
            ),
            "读" to listOf(
                context.getString(R.string.search_suggestion_reading),
                context.getString(R.string.search_suggestion_reading_notes),
                context.getString(R.string.search_suggestion_reading_thoughts),
                context.getString(R.string.search_suggestion_reading_plan)
            ),
            "冥" to listOf(
                context.getString(R.string.search_suggestion_meditation),
                context.getString(R.string.search_suggestion_meditation_experience),
                context.getString(R.string.search_suggestion_meditation_effect),
                context.getString(R.string.search_suggestion_meditation_method)
            )
        )
    }

    private fun getExtendedSuggestionsMap(): Map<String, List<String>> {
        return mapOf(
            context.getString(R.string.search_suggestion_exercise) to listOf(
                context.getString(R.string.search_suggestion_morning_run),
                context.getString(R.string.search_suggestion_fitness),
                context.getString(R.string.search_suggestion_swimming),
                context.getString(R.string.search_suggestion_basketball),
                context.getString(R.string.search_suggestion_running),
                context.getString(R.string.search_suggestion_yoga)
            ),
            context.getString(R.string.search_suggestion_work) to listOf(
                context.getString(R.string.search_suggestion_meeting),
                context.getString(R.string.search_suggestion_project),
                context.getString(R.string.search_suggestion_task),
                context.getString(R.string.search_suggestion_team),
                context.getString(R.string.search_suggestion_efficiency),
                context.getString(R.string.search_suggestion_pressure)
            ),
            context.getString(R.string.search_suggestion_study) to listOf(
                context.getString(R.string.search_suggestion_course),
                context.getString(R.string.search_suggestion_notes),
                context.getString(R.string.search_suggestion_exam),
                context.getString(R.string.search_suggestion_review),
                context.getString(R.string.search_suggestion_understand),
                context.getString(R.string.search_suggestion_master)
            ),
            context.getString(R.string.search_suggestion_reading) to listOf(
                context.getString(R.string.search_suggestion_novel),
                context.getString(R.string.search_suggestion_self_help),
                context.getString(R.string.search_suggestion_history),
                context.getString(R.string.search_suggestion_science),
                context.getString(R.string.search_suggestion_psychology),
                context.getString(R.string.search_suggestion_philosophy)
            ),
            context.getString(R.string.search_suggestion_meditation) to listOf(
                context.getString(R.string.search_suggestion_breathing),
                context.getString(R.string.search_suggestion_focus),
                context.getString(R.string.search_suggestion_relax),
                context.getString(R.string.search_suggestion_introspection),
                context.getString(R.string.search_suggestion_clarity)
            )
        )
    }

    override suspend fun getContextualSuggestions(query: String): List<String> {
        if (query.isEmpty()) return emptyList()

        val commonSuggestions = getCommonSuggestions()

        // 查找匹配的前缀建议
        val suggestions = commonSuggestions.entries
            .filter { it.key.startsWith(query) || query.startsWith(it.key) }
            .flatMap { it.value }
            .filter { it.contains(query, ignoreCase = true) }
            .toMutableList()

        // 如果找不到前缀匹配，尝试部分匹配
        if (suggestions.isEmpty()) {
            return commonSuggestions.values
                .flatten()
                .filter { it.contains(query, ignoreCase = true) }
                .take(5) // 限制数量
        }

        return suggestions.take(5) // 限制数量
    }

    override suspend fun getExtendedSuggestions(query: String): List<String> {
        if (query.isEmpty()) return emptyList()

        val commonSuggestions = getCommonSuggestions()
        val extendedSuggestions = getExtendedSuggestionsMap()

        // 为每个上下文关键词找到扩展建议
        val extendedResults = mutableListOf<String>()

        // 找到匹配query的关键词
        commonSuggestions.values
            .flatten()
            .filter { it.contains(query, ignoreCase = true) }
            .forEach { keyword ->
                // 获取该关键词的扩展建议
                val extensions = extendedSuggestions[keyword] ?: emptyList()
                extendedResults.addAll(extensions)
            }

        // 如果找不到任何扩展，尝试直接匹配query
        if (extendedResults.isEmpty()) {
            return extendedSuggestions.values
                .flatten()
                .filter { it.contains(query, ignoreCase = true) }
                .take(8) // 限制数量
        }

        return extendedResults.distinct().take(8) // 去重并限制数量
    }
} 