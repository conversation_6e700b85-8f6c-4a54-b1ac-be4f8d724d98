package com.timeflow.app.domain.usecase;

import com.timeflow.app.data.repository.TaskTimeRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class TaskTimeUseCase_Factory implements Factory<TaskTimeUseCase> {
  private final Provider<TaskTimeRepository> taskTimeRepositoryProvider;

  public TaskTimeUseCase_Factory(Provider<TaskTimeRepository> taskTimeRepositoryProvider) {
    this.taskTimeRepositoryProvider = taskTimeRepositoryProvider;
  }

  @Override
  public TaskTimeUseCase get() {
    return newInstance(taskTimeRepositoryProvider.get());
  }

  public static TaskTimeUseCase_Factory create(
      Provider<TaskTimeRepository> taskTimeRepositoryProvider) {
    return new TaskTimeUseCase_Factory(taskTimeRepositoryProvider);
  }

  public static TaskTimeUseCase newInstance(TaskTimeRepository taskTimeRepository) {
    return new TaskTimeUseCase(taskTimeRepository);
  }
}
