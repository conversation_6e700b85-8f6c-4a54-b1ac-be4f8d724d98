package com.timeflow.app.data.notification;

import android.content.Context;
import com.timeflow.app.data.repository.GoalRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class GoalNotificationScheduler_Factory implements Factory<GoalNotificationScheduler> {
  private final Provider<Context> contextProvider;

  private final Provider<GoalRepository> goalRepositoryProvider;

  private final Provider<GoalNotificationManager> notificationManagerProvider;

  public GoalNotificationScheduler_Factory(Provider<Context> contextProvider,
      Provider<GoalRepository> goalRepositoryProvider,
      Provider<GoalNotificationManager> notificationManagerProvider) {
    this.contextProvider = contextProvider;
    this.goalRepositoryProvider = goalRepositoryProvider;
    this.notificationManagerProvider = notificationManagerProvider;
  }

  @Override
  public GoalNotificationScheduler get() {
    return newInstance(contextProvider.get(), goalRepositoryProvider.get(), notificationManagerProvider.get());
  }

  public static GoalNotificationScheduler_Factory create(Provider<Context> contextProvider,
      Provider<GoalRepository> goalRepositoryProvider,
      Provider<GoalNotificationManager> notificationManagerProvider) {
    return new GoalNotificationScheduler_Factory(contextProvider, goalRepositoryProvider, notificationManagerProvider);
  }

  public static GoalNotificationScheduler newInstance(Context context,
      GoalRepository goalRepository, GoalNotificationManager notificationManager) {
    return new GoalNotificationScheduler(context, goalRepository, notificationManager);
  }
}
