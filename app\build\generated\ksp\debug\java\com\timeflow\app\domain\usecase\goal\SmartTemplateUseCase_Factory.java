package com.timeflow.app.domain.usecase.goal;

import com.timeflow.app.data.repository.GoalTemplateRepository;
import com.timeflow.app.data.repository.UserPreferenceRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SmartTemplateUseCase_Factory implements Factory<SmartTemplateUseCase> {
  private final Provider<GoalTemplateRepository> goalTemplateRepositoryProvider;

  private final Provider<UserPreferenceRepository> userPreferenceRepositoryProvider;

  public SmartTemplateUseCase_Factory(
      Provider<GoalTemplateRepository> goalTemplateRepositoryProvider,
      Provider<UserPreferenceRepository> userPreferenceRepositoryProvider) {
    this.goalTemplateRepositoryProvider = goalTemplateRepositoryProvider;
    this.userPreferenceRepositoryProvider = userPreferenceRepositoryProvider;
  }

  @Override
  public SmartTemplateUseCase get() {
    return newInstance(goalTemplateRepositoryProvider.get(), userPreferenceRepositoryProvider.get());
  }

  public static SmartTemplateUseCase_Factory create(
      Provider<GoalTemplateRepository> goalTemplateRepositoryProvider,
      Provider<UserPreferenceRepository> userPreferenceRepositoryProvider) {
    return new SmartTemplateUseCase_Factory(goalTemplateRepositoryProvider, userPreferenceRepositoryProvider);
  }

  public static SmartTemplateUseCase newInstance(GoalTemplateRepository goalTemplateRepository,
      UserPreferenceRepository userPreferenceRepository) {
    return new SmartTemplateUseCase(goalTemplateRepository, userPreferenceRepository);
  }
}
