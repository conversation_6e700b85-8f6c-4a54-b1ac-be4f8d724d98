package com.timeflow.app.ui.viewmodel;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AiSettingsViewModel_Factory implements Factory<AiSettingsViewModel> {
  @Override
  public AiSettingsViewModel get() {
    return newInstance();
  }

  public static AiSettingsViewModel_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static AiSettingsViewModel newInstance() {
    return new AiSettingsViewModel();
  }

  private static final class InstanceHolder {
    private static final AiSettingsViewModel_Factory INSTANCE = new AiSettingsViewModel_Factory();
  }
}
