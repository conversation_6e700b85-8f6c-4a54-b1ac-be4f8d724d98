package com.timeflow.app.utils;

import android.content.Context;
import com.timeflow.app.di.CrashReporter;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AppExceptionHandler_Factory implements Factory<AppExceptionHandler> {
  private final Provider<Context> contextProvider;

  private final Provider<CrashReporter> crashReporterProvider;

  public AppExceptionHandler_Factory(Provider<Context> contextProvider,
      Provider<CrashReporter> crashReporterProvider) {
    this.contextProvider = contextProvider;
    this.crashReporterProvider = crashReporterProvider;
  }

  @Override
  public AppExceptionHandler get() {
    return newInstance(contextProvider.get(), crashReporterProvider.get());
  }

  public static AppExceptionHandler_Factory create(Provider<Context> contextProvider,
      Provider<CrashReporter> crashReporterProvider) {
    return new AppExceptionHandler_Factory(contextProvider, crashReporterProvider);
  }

  public static AppExceptionHandler newInstance(Context context, CrashReporter crashReporter) {
    return new AppExceptionHandler(context, crashReporter);
  }
}
