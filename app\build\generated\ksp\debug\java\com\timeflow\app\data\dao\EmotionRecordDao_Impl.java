package com.timeflow.app.data.dao;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.timeflow.app.data.converter.Converters;
import com.timeflow.app.data.converter.LocalDateConverter;
import com.timeflow.app.data.converter.StringListConverter;
import com.timeflow.app.data.entity.EmotionRecordEntity;
import java.lang.Class;
import java.lang.Exception;
import java.lang.IllegalStateException;
import java.lang.Integer;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class EmotionRecordDao_Impl implements EmotionRecordDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<EmotionRecordEntity> __insertionAdapterOfEmotionRecordEntity;

  private final LocalDateConverter __localDateConverter = new LocalDateConverter();

  private final StringListConverter __stringListConverter = new StringListConverter();

  private final EntityDeletionOrUpdateAdapter<EmotionRecordEntity> __deletionAdapterOfEmotionRecordEntity;

  private final EntityDeletionOrUpdateAdapter<EmotionRecordEntity> __updateAdapterOfEmotionRecordEntity;

  private final SharedSQLiteStatement __preparedStmtOfDeleteEmotionRecordById;

  private final SharedSQLiteStatement __preparedStmtOfDeleteEmotionRecordByDate;

  private final Converters __converters = new Converters();

  private final SharedSQLiteStatement __preparedStmtOfDeleteAllEmotionRecords;

  public EmotionRecordDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfEmotionRecordEntity = new EntityInsertionAdapter<EmotionRecordEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `emotion_records` (`id`,`date`,`emotion_type`,`triggers`,`mindfulness_note`,`image_uri`,`audio_uri`,`is_detailed`,`created_at`,`updated_at`) VALUES (?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final EmotionRecordEntity entity) {
        statement.bindString(1, entity.getId());
        final String _tmp = __localDateConverter.fromLocalDate(entity.getDate());
        if (_tmp == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, _tmp);
        }
        statement.bindString(3, entity.getEmotionType());
        final String _tmp_1 = __stringListConverter.fromStringList(entity.getTriggers());
        statement.bindString(4, _tmp_1);
        statement.bindString(5, entity.getMindfulnessNote());
        if (entity.getImageUri() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getImageUri());
        }
        if (entity.getAudioUri() == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, entity.getAudioUri());
        }
        final int _tmp_2 = entity.isDetailed() ? 1 : 0;
        statement.bindLong(8, _tmp_2);
        statement.bindLong(9, entity.getCreatedAt());
        statement.bindLong(10, entity.getUpdatedAt());
      }
    };
    this.__deletionAdapterOfEmotionRecordEntity = new EntityDeletionOrUpdateAdapter<EmotionRecordEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `emotion_records` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final EmotionRecordEntity entity) {
        statement.bindString(1, entity.getId());
      }
    };
    this.__updateAdapterOfEmotionRecordEntity = new EntityDeletionOrUpdateAdapter<EmotionRecordEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `emotion_records` SET `id` = ?,`date` = ?,`emotion_type` = ?,`triggers` = ?,`mindfulness_note` = ?,`image_uri` = ?,`audio_uri` = ?,`is_detailed` = ?,`created_at` = ?,`updated_at` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final EmotionRecordEntity entity) {
        statement.bindString(1, entity.getId());
        final String _tmp = __localDateConverter.fromLocalDate(entity.getDate());
        if (_tmp == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, _tmp);
        }
        statement.bindString(3, entity.getEmotionType());
        final String _tmp_1 = __stringListConverter.fromStringList(entity.getTriggers());
        statement.bindString(4, _tmp_1);
        statement.bindString(5, entity.getMindfulnessNote());
        if (entity.getImageUri() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getImageUri());
        }
        if (entity.getAudioUri() == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, entity.getAudioUri());
        }
        final int _tmp_2 = entity.isDetailed() ? 1 : 0;
        statement.bindLong(8, _tmp_2);
        statement.bindLong(9, entity.getCreatedAt());
        statement.bindLong(10, entity.getUpdatedAt());
        statement.bindString(11, entity.getId());
      }
    };
    this.__preparedStmtOfDeleteEmotionRecordById = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM emotion_records WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteEmotionRecordByDate = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM emotion_records WHERE date = ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteAllEmotionRecords = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM emotion_records";
        return _query;
      }
    };
  }

  @Override
  public Object insertEmotionRecord(final EmotionRecordEntity record,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfEmotionRecordEntity.insert(record);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object insertEmotionRecords(final List<EmotionRecordEntity> records,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfEmotionRecordEntity.insert(records);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteEmotionRecord(final EmotionRecordEntity record,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfEmotionRecordEntity.handle(record);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateEmotionRecord(final EmotionRecordEntity record,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfEmotionRecordEntity.handle(record);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteEmotionRecordById(final String id,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteEmotionRecordById.acquire();
        int _argIndex = 1;
        _stmt.bindString(_argIndex, id);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteEmotionRecordById.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteEmotionRecordByDate(final LocalDate date,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteEmotionRecordByDate.acquire();
        int _argIndex = 1;
        final Long _tmp = __converters.dateToEpochDay(date);
        if (_tmp == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindLong(_argIndex, _tmp);
        }
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteEmotionRecordByDate.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteAllEmotionRecords(final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteAllEmotionRecords.acquire();
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteAllEmotionRecords.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<EmotionRecordEntity>> getAllEmotionRecords() {
    final String _sql = "SELECT * FROM emotion_records ORDER BY date DESC, created_at DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"emotion_records"}, new Callable<List<EmotionRecordEntity>>() {
      @Override
      @NonNull
      public List<EmotionRecordEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
          final int _cursorIndexOfEmotionType = CursorUtil.getColumnIndexOrThrow(_cursor, "emotion_type");
          final int _cursorIndexOfTriggers = CursorUtil.getColumnIndexOrThrow(_cursor, "triggers");
          final int _cursorIndexOfMindfulnessNote = CursorUtil.getColumnIndexOrThrow(_cursor, "mindfulness_note");
          final int _cursorIndexOfImageUri = CursorUtil.getColumnIndexOrThrow(_cursor, "image_uri");
          final int _cursorIndexOfAudioUri = CursorUtil.getColumnIndexOrThrow(_cursor, "audio_uri");
          final int _cursorIndexOfIsDetailed = CursorUtil.getColumnIndexOrThrow(_cursor, "is_detailed");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
          final List<EmotionRecordEntity> _result = new ArrayList<EmotionRecordEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final EmotionRecordEntity _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final LocalDate _tmpDate;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfDate);
            }
            final LocalDate _tmp_1 = __localDateConverter.toLocalDate(_tmp);
            if (_tmp_1 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDate', but it was NULL.");
            } else {
              _tmpDate = _tmp_1;
            }
            final String _tmpEmotionType;
            _tmpEmotionType = _cursor.getString(_cursorIndexOfEmotionType);
            final List<String> _tmpTriggers;
            final String _tmp_2;
            _tmp_2 = _cursor.getString(_cursorIndexOfTriggers);
            _tmpTriggers = __stringListConverter.toStringList(_tmp_2);
            final String _tmpMindfulnessNote;
            _tmpMindfulnessNote = _cursor.getString(_cursorIndexOfMindfulnessNote);
            final String _tmpImageUri;
            if (_cursor.isNull(_cursorIndexOfImageUri)) {
              _tmpImageUri = null;
            } else {
              _tmpImageUri = _cursor.getString(_cursorIndexOfImageUri);
            }
            final String _tmpAudioUri;
            if (_cursor.isNull(_cursorIndexOfAudioUri)) {
              _tmpAudioUri = null;
            } else {
              _tmpAudioUri = _cursor.getString(_cursorIndexOfAudioUri);
            }
            final boolean _tmpIsDetailed;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfIsDetailed);
            _tmpIsDetailed = _tmp_3 != 0;
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final long _tmpUpdatedAt;
            _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            _item = new EmotionRecordEntity(_tmpId,_tmpDate,_tmpEmotionType,_tmpTriggers,_tmpMindfulnessNote,_tmpImageUri,_tmpAudioUri,_tmpIsDetailed,_tmpCreatedAt,_tmpUpdatedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getEmotionRecordById(final String id,
      final Continuation<? super EmotionRecordEntity> $completion) {
    final String _sql = "SELECT * FROM emotion_records WHERE id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, id);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<EmotionRecordEntity>() {
      @Override
      @Nullable
      public EmotionRecordEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
          final int _cursorIndexOfEmotionType = CursorUtil.getColumnIndexOrThrow(_cursor, "emotion_type");
          final int _cursorIndexOfTriggers = CursorUtil.getColumnIndexOrThrow(_cursor, "triggers");
          final int _cursorIndexOfMindfulnessNote = CursorUtil.getColumnIndexOrThrow(_cursor, "mindfulness_note");
          final int _cursorIndexOfImageUri = CursorUtil.getColumnIndexOrThrow(_cursor, "image_uri");
          final int _cursorIndexOfAudioUri = CursorUtil.getColumnIndexOrThrow(_cursor, "audio_uri");
          final int _cursorIndexOfIsDetailed = CursorUtil.getColumnIndexOrThrow(_cursor, "is_detailed");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
          final EmotionRecordEntity _result;
          if (_cursor.moveToFirst()) {
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final LocalDate _tmpDate;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfDate);
            }
            final LocalDate _tmp_1 = __localDateConverter.toLocalDate(_tmp);
            if (_tmp_1 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDate', but it was NULL.");
            } else {
              _tmpDate = _tmp_1;
            }
            final String _tmpEmotionType;
            _tmpEmotionType = _cursor.getString(_cursorIndexOfEmotionType);
            final List<String> _tmpTriggers;
            final String _tmp_2;
            _tmp_2 = _cursor.getString(_cursorIndexOfTriggers);
            _tmpTriggers = __stringListConverter.toStringList(_tmp_2);
            final String _tmpMindfulnessNote;
            _tmpMindfulnessNote = _cursor.getString(_cursorIndexOfMindfulnessNote);
            final String _tmpImageUri;
            if (_cursor.isNull(_cursorIndexOfImageUri)) {
              _tmpImageUri = null;
            } else {
              _tmpImageUri = _cursor.getString(_cursorIndexOfImageUri);
            }
            final String _tmpAudioUri;
            if (_cursor.isNull(_cursorIndexOfAudioUri)) {
              _tmpAudioUri = null;
            } else {
              _tmpAudioUri = _cursor.getString(_cursorIndexOfAudioUri);
            }
            final boolean _tmpIsDetailed;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfIsDetailed);
            _tmpIsDetailed = _tmp_3 != 0;
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final long _tmpUpdatedAt;
            _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            _result = new EmotionRecordEntity(_tmpId,_tmpDate,_tmpEmotionType,_tmpTriggers,_tmpMindfulnessNote,_tmpImageUri,_tmpAudioUri,_tmpIsDetailed,_tmpCreatedAt,_tmpUpdatedAt);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getEmotionRecordByDate(final LocalDate date,
      final Continuation<? super EmotionRecordEntity> $completion) {
    final String _sql = "SELECT * FROM emotion_records WHERE date = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    final Long _tmp = __converters.dateToEpochDay(date);
    if (_tmp == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindLong(_argIndex, _tmp);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<EmotionRecordEntity>() {
      @Override
      @Nullable
      public EmotionRecordEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
          final int _cursorIndexOfEmotionType = CursorUtil.getColumnIndexOrThrow(_cursor, "emotion_type");
          final int _cursorIndexOfTriggers = CursorUtil.getColumnIndexOrThrow(_cursor, "triggers");
          final int _cursorIndexOfMindfulnessNote = CursorUtil.getColumnIndexOrThrow(_cursor, "mindfulness_note");
          final int _cursorIndexOfImageUri = CursorUtil.getColumnIndexOrThrow(_cursor, "image_uri");
          final int _cursorIndexOfAudioUri = CursorUtil.getColumnIndexOrThrow(_cursor, "audio_uri");
          final int _cursorIndexOfIsDetailed = CursorUtil.getColumnIndexOrThrow(_cursor, "is_detailed");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
          final EmotionRecordEntity _result;
          if (_cursor.moveToFirst()) {
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final LocalDate _tmpDate;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfDate)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfDate);
            }
            final LocalDate _tmp_2 = __localDateConverter.toLocalDate(_tmp_1);
            if (_tmp_2 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDate', but it was NULL.");
            } else {
              _tmpDate = _tmp_2;
            }
            final String _tmpEmotionType;
            _tmpEmotionType = _cursor.getString(_cursorIndexOfEmotionType);
            final List<String> _tmpTriggers;
            final String _tmp_3;
            _tmp_3 = _cursor.getString(_cursorIndexOfTriggers);
            _tmpTriggers = __stringListConverter.toStringList(_tmp_3);
            final String _tmpMindfulnessNote;
            _tmpMindfulnessNote = _cursor.getString(_cursorIndexOfMindfulnessNote);
            final String _tmpImageUri;
            if (_cursor.isNull(_cursorIndexOfImageUri)) {
              _tmpImageUri = null;
            } else {
              _tmpImageUri = _cursor.getString(_cursorIndexOfImageUri);
            }
            final String _tmpAudioUri;
            if (_cursor.isNull(_cursorIndexOfAudioUri)) {
              _tmpAudioUri = null;
            } else {
              _tmpAudioUri = _cursor.getString(_cursorIndexOfAudioUri);
            }
            final boolean _tmpIsDetailed;
            final int _tmp_4;
            _tmp_4 = _cursor.getInt(_cursorIndexOfIsDetailed);
            _tmpIsDetailed = _tmp_4 != 0;
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final long _tmpUpdatedAt;
            _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            _result = new EmotionRecordEntity(_tmpId,_tmpDate,_tmpEmotionType,_tmpTriggers,_tmpMindfulnessNote,_tmpImageUri,_tmpAudioUri,_tmpIsDetailed,_tmpCreatedAt,_tmpUpdatedAt);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<EmotionRecordEntity>> getEmotionRecordsByDateRange(final LocalDate startDate,
      final LocalDate endDate) {
    final String _sql = "SELECT * FROM emotion_records WHERE date BETWEEN ? AND ? ORDER BY date DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    final Long _tmp = __converters.dateToEpochDay(startDate);
    if (_tmp == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindLong(_argIndex, _tmp);
    }
    _argIndex = 2;
    final Long _tmp_1 = __converters.dateToEpochDay(endDate);
    if (_tmp_1 == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindLong(_argIndex, _tmp_1);
    }
    return CoroutinesRoom.createFlow(__db, false, new String[] {"emotion_records"}, new Callable<List<EmotionRecordEntity>>() {
      @Override
      @NonNull
      public List<EmotionRecordEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
          final int _cursorIndexOfEmotionType = CursorUtil.getColumnIndexOrThrow(_cursor, "emotion_type");
          final int _cursorIndexOfTriggers = CursorUtil.getColumnIndexOrThrow(_cursor, "triggers");
          final int _cursorIndexOfMindfulnessNote = CursorUtil.getColumnIndexOrThrow(_cursor, "mindfulness_note");
          final int _cursorIndexOfImageUri = CursorUtil.getColumnIndexOrThrow(_cursor, "image_uri");
          final int _cursorIndexOfAudioUri = CursorUtil.getColumnIndexOrThrow(_cursor, "audio_uri");
          final int _cursorIndexOfIsDetailed = CursorUtil.getColumnIndexOrThrow(_cursor, "is_detailed");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
          final List<EmotionRecordEntity> _result = new ArrayList<EmotionRecordEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final EmotionRecordEntity _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final LocalDate _tmpDate;
            final String _tmp_2;
            if (_cursor.isNull(_cursorIndexOfDate)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getString(_cursorIndexOfDate);
            }
            final LocalDate _tmp_3 = __localDateConverter.toLocalDate(_tmp_2);
            if (_tmp_3 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDate', but it was NULL.");
            } else {
              _tmpDate = _tmp_3;
            }
            final String _tmpEmotionType;
            _tmpEmotionType = _cursor.getString(_cursorIndexOfEmotionType);
            final List<String> _tmpTriggers;
            final String _tmp_4;
            _tmp_4 = _cursor.getString(_cursorIndexOfTriggers);
            _tmpTriggers = __stringListConverter.toStringList(_tmp_4);
            final String _tmpMindfulnessNote;
            _tmpMindfulnessNote = _cursor.getString(_cursorIndexOfMindfulnessNote);
            final String _tmpImageUri;
            if (_cursor.isNull(_cursorIndexOfImageUri)) {
              _tmpImageUri = null;
            } else {
              _tmpImageUri = _cursor.getString(_cursorIndexOfImageUri);
            }
            final String _tmpAudioUri;
            if (_cursor.isNull(_cursorIndexOfAudioUri)) {
              _tmpAudioUri = null;
            } else {
              _tmpAudioUri = _cursor.getString(_cursorIndexOfAudioUri);
            }
            final boolean _tmpIsDetailed;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfIsDetailed);
            _tmpIsDetailed = _tmp_5 != 0;
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final long _tmpUpdatedAt;
            _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            _item = new EmotionRecordEntity(_tmpId,_tmpDate,_tmpEmotionType,_tmpTriggers,_tmpMindfulnessNote,_tmpImageUri,_tmpAudioUri,_tmpIsDetailed,_tmpCreatedAt,_tmpUpdatedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<EmotionRecordEntity>> getEmotionRecordsByType(final String emotionType) {
    final String _sql = "SELECT * FROM emotion_records WHERE emotion_type = ? ORDER BY date DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, emotionType);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"emotion_records"}, new Callable<List<EmotionRecordEntity>>() {
      @Override
      @NonNull
      public List<EmotionRecordEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
          final int _cursorIndexOfEmotionType = CursorUtil.getColumnIndexOrThrow(_cursor, "emotion_type");
          final int _cursorIndexOfTriggers = CursorUtil.getColumnIndexOrThrow(_cursor, "triggers");
          final int _cursorIndexOfMindfulnessNote = CursorUtil.getColumnIndexOrThrow(_cursor, "mindfulness_note");
          final int _cursorIndexOfImageUri = CursorUtil.getColumnIndexOrThrow(_cursor, "image_uri");
          final int _cursorIndexOfAudioUri = CursorUtil.getColumnIndexOrThrow(_cursor, "audio_uri");
          final int _cursorIndexOfIsDetailed = CursorUtil.getColumnIndexOrThrow(_cursor, "is_detailed");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
          final List<EmotionRecordEntity> _result = new ArrayList<EmotionRecordEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final EmotionRecordEntity _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final LocalDate _tmpDate;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfDate);
            }
            final LocalDate _tmp_1 = __localDateConverter.toLocalDate(_tmp);
            if (_tmp_1 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDate', but it was NULL.");
            } else {
              _tmpDate = _tmp_1;
            }
            final String _tmpEmotionType;
            _tmpEmotionType = _cursor.getString(_cursorIndexOfEmotionType);
            final List<String> _tmpTriggers;
            final String _tmp_2;
            _tmp_2 = _cursor.getString(_cursorIndexOfTriggers);
            _tmpTriggers = __stringListConverter.toStringList(_tmp_2);
            final String _tmpMindfulnessNote;
            _tmpMindfulnessNote = _cursor.getString(_cursorIndexOfMindfulnessNote);
            final String _tmpImageUri;
            if (_cursor.isNull(_cursorIndexOfImageUri)) {
              _tmpImageUri = null;
            } else {
              _tmpImageUri = _cursor.getString(_cursorIndexOfImageUri);
            }
            final String _tmpAudioUri;
            if (_cursor.isNull(_cursorIndexOfAudioUri)) {
              _tmpAudioUri = null;
            } else {
              _tmpAudioUri = _cursor.getString(_cursorIndexOfAudioUri);
            }
            final boolean _tmpIsDetailed;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfIsDetailed);
            _tmpIsDetailed = _tmp_3 != 0;
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final long _tmpUpdatedAt;
            _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            _item = new EmotionRecordEntity(_tmpId,_tmpDate,_tmpEmotionType,_tmpTriggers,_tmpMindfulnessNote,_tmpImageUri,_tmpAudioUri,_tmpIsDetailed,_tmpCreatedAt,_tmpUpdatedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<EmotionRecordEntity>> getDetailedEmotionRecords() {
    final String _sql = "SELECT * FROM emotion_records WHERE is_detailed = 1 ORDER BY date DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"emotion_records"}, new Callable<List<EmotionRecordEntity>>() {
      @Override
      @NonNull
      public List<EmotionRecordEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
          final int _cursorIndexOfEmotionType = CursorUtil.getColumnIndexOrThrow(_cursor, "emotion_type");
          final int _cursorIndexOfTriggers = CursorUtil.getColumnIndexOrThrow(_cursor, "triggers");
          final int _cursorIndexOfMindfulnessNote = CursorUtil.getColumnIndexOrThrow(_cursor, "mindfulness_note");
          final int _cursorIndexOfImageUri = CursorUtil.getColumnIndexOrThrow(_cursor, "image_uri");
          final int _cursorIndexOfAudioUri = CursorUtil.getColumnIndexOrThrow(_cursor, "audio_uri");
          final int _cursorIndexOfIsDetailed = CursorUtil.getColumnIndexOrThrow(_cursor, "is_detailed");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
          final List<EmotionRecordEntity> _result = new ArrayList<EmotionRecordEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final EmotionRecordEntity _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final LocalDate _tmpDate;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfDate);
            }
            final LocalDate _tmp_1 = __localDateConverter.toLocalDate(_tmp);
            if (_tmp_1 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDate', but it was NULL.");
            } else {
              _tmpDate = _tmp_1;
            }
            final String _tmpEmotionType;
            _tmpEmotionType = _cursor.getString(_cursorIndexOfEmotionType);
            final List<String> _tmpTriggers;
            final String _tmp_2;
            _tmp_2 = _cursor.getString(_cursorIndexOfTriggers);
            _tmpTriggers = __stringListConverter.toStringList(_tmp_2);
            final String _tmpMindfulnessNote;
            _tmpMindfulnessNote = _cursor.getString(_cursorIndexOfMindfulnessNote);
            final String _tmpImageUri;
            if (_cursor.isNull(_cursorIndexOfImageUri)) {
              _tmpImageUri = null;
            } else {
              _tmpImageUri = _cursor.getString(_cursorIndexOfImageUri);
            }
            final String _tmpAudioUri;
            if (_cursor.isNull(_cursorIndexOfAudioUri)) {
              _tmpAudioUri = null;
            } else {
              _tmpAudioUri = _cursor.getString(_cursorIndexOfAudioUri);
            }
            final boolean _tmpIsDetailed;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfIsDetailed);
            _tmpIsDetailed = _tmp_3 != 0;
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final long _tmpUpdatedAt;
            _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            _item = new EmotionRecordEntity(_tmpId,_tmpDate,_tmpEmotionType,_tmpTriggers,_tmpMindfulnessNote,_tmpImageUri,_tmpAudioUri,_tmpIsDetailed,_tmpCreatedAt,_tmpUpdatedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<EmotionRecordEntity>> getRecentEmotionRecords(final int limit) {
    final String _sql = "SELECT * FROM emotion_records ORDER BY date DESC, created_at DESC LIMIT ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, limit);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"emotion_records"}, new Callable<List<EmotionRecordEntity>>() {
      @Override
      @NonNull
      public List<EmotionRecordEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
          final int _cursorIndexOfEmotionType = CursorUtil.getColumnIndexOrThrow(_cursor, "emotion_type");
          final int _cursorIndexOfTriggers = CursorUtil.getColumnIndexOrThrow(_cursor, "triggers");
          final int _cursorIndexOfMindfulnessNote = CursorUtil.getColumnIndexOrThrow(_cursor, "mindfulness_note");
          final int _cursorIndexOfImageUri = CursorUtil.getColumnIndexOrThrow(_cursor, "image_uri");
          final int _cursorIndexOfAudioUri = CursorUtil.getColumnIndexOrThrow(_cursor, "audio_uri");
          final int _cursorIndexOfIsDetailed = CursorUtil.getColumnIndexOrThrow(_cursor, "is_detailed");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
          final List<EmotionRecordEntity> _result = new ArrayList<EmotionRecordEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final EmotionRecordEntity _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final LocalDate _tmpDate;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfDate);
            }
            final LocalDate _tmp_1 = __localDateConverter.toLocalDate(_tmp);
            if (_tmp_1 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDate', but it was NULL.");
            } else {
              _tmpDate = _tmp_1;
            }
            final String _tmpEmotionType;
            _tmpEmotionType = _cursor.getString(_cursorIndexOfEmotionType);
            final List<String> _tmpTriggers;
            final String _tmp_2;
            _tmp_2 = _cursor.getString(_cursorIndexOfTriggers);
            _tmpTriggers = __stringListConverter.toStringList(_tmp_2);
            final String _tmpMindfulnessNote;
            _tmpMindfulnessNote = _cursor.getString(_cursorIndexOfMindfulnessNote);
            final String _tmpImageUri;
            if (_cursor.isNull(_cursorIndexOfImageUri)) {
              _tmpImageUri = null;
            } else {
              _tmpImageUri = _cursor.getString(_cursorIndexOfImageUri);
            }
            final String _tmpAudioUri;
            if (_cursor.isNull(_cursorIndexOfAudioUri)) {
              _tmpAudioUri = null;
            } else {
              _tmpAudioUri = _cursor.getString(_cursorIndexOfAudioUri);
            }
            final boolean _tmpIsDetailed;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfIsDetailed);
            _tmpIsDetailed = _tmp_3 != 0;
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final long _tmpUpdatedAt;
            _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            _item = new EmotionRecordEntity(_tmpId,_tmpDate,_tmpEmotionType,_tmpTriggers,_tmpMindfulnessNote,_tmpImageUri,_tmpAudioUri,_tmpIsDetailed,_tmpCreatedAt,_tmpUpdatedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getEmotionRecordCount(final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT COUNT(*) FROM emotion_records";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final int _tmp;
            _tmp = _cursor.getInt(0);
            _result = _tmp;
          } else {
            _result = 0;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getEmotionRecordCountByDateRange(final LocalDate startDate, final LocalDate endDate,
      final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT COUNT(*) FROM emotion_records WHERE date BETWEEN ? AND ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    final Long _tmp = __converters.dateToEpochDay(startDate);
    if (_tmp == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindLong(_argIndex, _tmp);
    }
    _argIndex = 2;
    final Long _tmp_1 = __converters.dateToEpochDay(endDate);
    if (_tmp_1 == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindLong(_argIndex, _tmp_1);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(0);
            _result = _tmp_2;
          } else {
            _result = 0;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
