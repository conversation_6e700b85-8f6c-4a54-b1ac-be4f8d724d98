package com.timeflow.app.di;

import com.timeflow.app.data.dao.TimeSessionDao;
import com.timeflow.app.data.db.AppDatabase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DatabaseModule_ProvideTimeSessionDaoFactory implements Factory<TimeSessionDao> {
  private final Provider<AppDatabase> databaseProvider;

  public DatabaseModule_ProvideTimeSessionDaoFactory(Provider<AppDatabase> databaseProvider) {
    this.databaseProvider = databaseProvider;
  }

  @Override
  public TimeSessionDao get() {
    return provideTimeSessionDao(databaseProvider.get());
  }

  public static DatabaseModule_ProvideTimeSessionDaoFactory create(
      Provider<AppDatabase> databaseProvider) {
    return new DatabaseModule_ProvideTimeSessionDaoFactory(databaseProvider);
  }

  public static TimeSessionDao provideTimeSessionDao(AppDatabase database) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.provideTimeSessionDao(database));
  }
}
