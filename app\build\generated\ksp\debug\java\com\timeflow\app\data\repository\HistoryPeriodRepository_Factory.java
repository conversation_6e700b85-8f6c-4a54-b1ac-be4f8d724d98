package com.timeflow.app.data.repository;

import com.timeflow.app.data.dao.CycleDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class HistoryPeriodRepository_Factory implements Factory<HistoryPeriodRepository> {
  private final Provider<CycleDao> cycleDaoProvider;

  public HistoryPeriodRepository_Factory(Provider<CycleDao> cycleDaoProvider) {
    this.cycleDaoProvider = cycleDaoProvider;
  }

  @Override
  public HistoryPeriodRepository get() {
    return newInstance(cycleDaoProvider.get());
  }

  public static HistoryPeriodRepository_Factory create(Provider<CycleDao> cycleDaoProvider) {
    return new HistoryPeriodRepository_Factory(cycleDaoProvider);
  }

  public static HistoryPeriodRepository newInstance(CycleDao cycleDao) {
    return new HistoryPeriodRepository(cycleDao);
  }
}
