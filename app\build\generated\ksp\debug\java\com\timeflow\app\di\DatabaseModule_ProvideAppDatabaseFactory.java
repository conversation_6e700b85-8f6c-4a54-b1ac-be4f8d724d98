package com.timeflow.app.di;

import android.content.Context;
import com.timeflow.app.data.db.AppDatabase;
import com.timeflow.app.utils.DatabaseBackupManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DatabaseModule_ProvideAppDatabaseFactory implements Factory<AppDatabase> {
  private final Provider<Context> contextProvider;

  private final Provider<DatabaseBackupManager> backupManagerProvider;

  public DatabaseModule_ProvideAppDatabaseFactory(Provider<Context> contextProvider,
      Provider<DatabaseBackupManager> backupManagerProvider) {
    this.contextProvider = contextProvider;
    this.backupManagerProvider = backupManagerProvider;
  }

  @Override
  public AppDatabase get() {
    return provideAppDatabase(contextProvider.get(), backupManagerProvider.get());
  }

  public static DatabaseModule_ProvideAppDatabaseFactory create(Provider<Context> contextProvider,
      Provider<DatabaseBackupManager> backupManagerProvider) {
    return new DatabaseModule_ProvideAppDatabaseFactory(contextProvider, backupManagerProvider);
  }

  public static AppDatabase provideAppDatabase(Context context,
      DatabaseBackupManager backupManager) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.provideAppDatabase(context, backupManager));
  }
}
