package com.timeflow.app.di;

import com.timeflow.app.data.dao.MedicationRecordDao;
import com.timeflow.app.data.repository.MedicationRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class MedicationModule_ProvideMedicationRepositoryFactory implements Factory<MedicationRepository> {
  private final Provider<MedicationRecordDao> medicationRecordDaoProvider;

  public MedicationModule_ProvideMedicationRepositoryFactory(
      Provider<MedicationRecordDao> medicationRecordDaoProvider) {
    this.medicationRecordDaoProvider = medicationRecordDaoProvider;
  }

  @Override
  public MedicationRepository get() {
    return provideMedicationRepository(medicationRecordDaoProvider.get());
  }

  public static MedicationModule_ProvideMedicationRepositoryFactory create(
      Provider<MedicationRecordDao> medicationRecordDaoProvider) {
    return new MedicationModule_ProvideMedicationRepositoryFactory(medicationRecordDaoProvider);
  }

  public static MedicationRepository provideMedicationRepository(
      MedicationRecordDao medicationRecordDao) {
    return Preconditions.checkNotNullFromProvides(MedicationModule.INSTANCE.provideMedicationRepository(medicationRecordDao));
  }
}
