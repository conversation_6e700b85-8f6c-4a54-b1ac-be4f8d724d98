package com.timeflow.app.di;

import com.timeflow.app.data.repository.TaskRepository;
import com.timeflow.app.data.repository.TaskRepositoryImpl;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class RepositoryModule_ProvideTaskRepositoryFactory implements Factory<TaskRepository> {
  private final Provider<TaskRepositoryImpl> taskRepositoryImplProvider;

  public RepositoryModule_ProvideTaskRepositoryFactory(
      Provider<TaskRepositoryImpl> taskRepositoryImplProvider) {
    this.taskRepositoryImplProvider = taskRepositoryImplProvider;
  }

  @Override
  public TaskRepository get() {
    return provideTaskRepository(taskRepositoryImplProvider.get());
  }

  public static RepositoryModule_ProvideTaskRepositoryFactory create(
      Provider<TaskRepositoryImpl> taskRepositoryImplProvider) {
    return new RepositoryModule_ProvideTaskRepositoryFactory(taskRepositoryImplProvider);
  }

  public static TaskRepository provideTaskRepository(TaskRepositoryImpl taskRepositoryImpl) {
    return Preconditions.checkNotNullFromProvides(RepositoryModule.INSTANCE.provideTaskRepository(taskRepositoryImpl));
  }
}
