package com.timeflow.app.receiver;

import com.timeflow.app.data.repository.GoalRepository;
import com.timeflow.app.data.repository.HabitRepository;
import com.timeflow.app.data.repository.TaskRepository;
import com.timeflow.app.data.repository.TimeSessionRepository;
import com.timeflow.app.ui.screen.reflection.ReflectionRepository;
import com.timeflow.app.utils.TimeFlowNotificationManager;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DailyReviewAlarmReceiver_MembersInjector implements MembersInjector<DailyReviewAlarmReceiver> {
  private final Provider<TimeFlowNotificationManager> notificationManagerProvider;

  private final Provider<TaskRepository> taskRepositoryProvider;

  private final Provider<HabitRepository> habitRepositoryProvider;

  private final Provider<TimeSessionRepository> timeSessionRepositoryProvider;

  private final Provider<ReflectionRepository> reflectionRepositoryProvider;

  private final Provider<GoalRepository> goalRepositoryProvider;

  public DailyReviewAlarmReceiver_MembersInjector(
      Provider<TimeFlowNotificationManager> notificationManagerProvider,
      Provider<TaskRepository> taskRepositoryProvider,
      Provider<HabitRepository> habitRepositoryProvider,
      Provider<TimeSessionRepository> timeSessionRepositoryProvider,
      Provider<ReflectionRepository> reflectionRepositoryProvider,
      Provider<GoalRepository> goalRepositoryProvider) {
    this.notificationManagerProvider = notificationManagerProvider;
    this.taskRepositoryProvider = taskRepositoryProvider;
    this.habitRepositoryProvider = habitRepositoryProvider;
    this.timeSessionRepositoryProvider = timeSessionRepositoryProvider;
    this.reflectionRepositoryProvider = reflectionRepositoryProvider;
    this.goalRepositoryProvider = goalRepositoryProvider;
  }

  public static MembersInjector<DailyReviewAlarmReceiver> create(
      Provider<TimeFlowNotificationManager> notificationManagerProvider,
      Provider<TaskRepository> taskRepositoryProvider,
      Provider<HabitRepository> habitRepositoryProvider,
      Provider<TimeSessionRepository> timeSessionRepositoryProvider,
      Provider<ReflectionRepository> reflectionRepositoryProvider,
      Provider<GoalRepository> goalRepositoryProvider) {
    return new DailyReviewAlarmReceiver_MembersInjector(notificationManagerProvider, taskRepositoryProvider, habitRepositoryProvider, timeSessionRepositoryProvider, reflectionRepositoryProvider, goalRepositoryProvider);
  }

  @Override
  public void injectMembers(DailyReviewAlarmReceiver instance) {
    injectNotificationManager(instance, notificationManagerProvider.get());
    injectTaskRepository(instance, taskRepositoryProvider.get());
    injectHabitRepository(instance, habitRepositoryProvider.get());
    injectTimeSessionRepository(instance, timeSessionRepositoryProvider.get());
    injectReflectionRepository(instance, reflectionRepositoryProvider.get());
    injectGoalRepository(instance, goalRepositoryProvider.get());
  }

  @InjectedFieldSignature("com.timeflow.app.receiver.DailyReviewAlarmReceiver.notificationManager")
  public static void injectNotificationManager(DailyReviewAlarmReceiver instance,
      TimeFlowNotificationManager notificationManager) {
    instance.notificationManager = notificationManager;
  }

  @InjectedFieldSignature("com.timeflow.app.receiver.DailyReviewAlarmReceiver.taskRepository")
  public static void injectTaskRepository(DailyReviewAlarmReceiver instance,
      TaskRepository taskRepository) {
    instance.taskRepository = taskRepository;
  }

  @InjectedFieldSignature("com.timeflow.app.receiver.DailyReviewAlarmReceiver.habitRepository")
  public static void injectHabitRepository(DailyReviewAlarmReceiver instance,
      HabitRepository habitRepository) {
    instance.habitRepository = habitRepository;
  }

  @InjectedFieldSignature("com.timeflow.app.receiver.DailyReviewAlarmReceiver.timeSessionRepository")
  public static void injectTimeSessionRepository(DailyReviewAlarmReceiver instance,
      TimeSessionRepository timeSessionRepository) {
    instance.timeSessionRepository = timeSessionRepository;
  }

  @InjectedFieldSignature("com.timeflow.app.receiver.DailyReviewAlarmReceiver.reflectionRepository")
  public static void injectReflectionRepository(DailyReviewAlarmReceiver instance,
      ReflectionRepository reflectionRepository) {
    instance.reflectionRepository = reflectionRepository;
  }

  @InjectedFieldSignature("com.timeflow.app.receiver.DailyReviewAlarmReceiver.goalRepository")
  public static void injectGoalRepository(DailyReviewAlarmReceiver instance,
      GoalRepository goalRepository) {
    instance.goalRepository = goalRepository;
  }
}
