package com.timeflow.app.data.model;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class WorkManager_Factory implements Factory<WorkManager> {
  @Override
  public WorkManager get() {
    return newInstance();
  }

  public static WorkManager_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static WorkManager newInstance() {
    return new WorkManager();
  }

  private static final class InstanceHolder {
    private static final WorkManager_Factory INSTANCE = new WorkManager_Factory();
  }
}
