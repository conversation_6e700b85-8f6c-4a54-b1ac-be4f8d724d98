package com.timeflow.app.service;

import com.timeflow.app.utils.TimeFlowNotificationManager;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class NotificationTestService_MembersInjector implements MembersInjector<NotificationTestService> {
  private final Provider<TimeFlowNotificationManager> notificationManagerProvider;

  public NotificationTestService_MembersInjector(
      Provider<TimeFlowNotificationManager> notificationManagerProvider) {
    this.notificationManagerProvider = notificationManagerProvider;
  }

  public static MembersInjector<NotificationTestService> create(
      Provider<TimeFlowNotificationManager> notificationManagerProvider) {
    return new NotificationTestService_MembersInjector(notificationManagerProvider);
  }

  @Override
  public void injectMembers(NotificationTestService instance) {
    injectNotificationManager(instance, notificationManagerProvider.get());
  }

  @InjectedFieldSignature("com.timeflow.app.service.NotificationTestService.notificationManager")
  public static void injectNotificationManager(NotificationTestService instance,
      TimeFlowNotificationManager notificationManager) {
    instance.notificationManager = notificationManager;
  }
}
