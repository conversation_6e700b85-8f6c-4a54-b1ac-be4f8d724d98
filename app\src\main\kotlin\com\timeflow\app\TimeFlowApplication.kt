package com.timeflow.app

import android.app.ActivityManager
import android.app.Application
import android.app.NotificationChannel
import android.app.NotificationManager
import android.content.Context
import android.content.res.Configuration as AndroidConfiguration
import android.os.Build
import android.os.StrictMode
import android.util.Log
import android.view.View
import androidx.appcompat.app.AppCompatDelegate
import androidx.work.Configuration as WorkConfiguration
import androidx.work.Configuration.Provider as WorkConfigProvider
import com.timeflow.app.di.AppInitializer
import com.timeflow.app.utils.AppExceptionHandler
import com.timeflow.app.utils.HwcLutsErrorFixer
import com.timeflow.app.utils.PerformanceMonitor
import com.timeflow.app.utils.PerformanceOptimizer
import dagger.hilt.android.HiltAndroidApp
import timber.log.Timber
import javax.inject.Inject
import coil.Coil
import coil.ImageLoader
import android.graphics.Bitmap
import coil.decode.BitmapFactoryDecoder
import coil.memory.MemoryCache
import androidx.room.RoomDatabase
import com.timeflow.app.utils.LogConfig
import com.timeflow.app.ui.screen.reflection.ReflectionRepository
import com.timeflow.app.ui.language.LanguageManager
import com.timeflow.app.service.AiSuggestionScheduler
import android.media.AudioAttributes
import android.media.RingtoneManager

/**
 * 时间流应用程序类
 * 负责初始化应用程序的各种组件
 */
@HiltAndroidApp
class TimeFlowApplication : Application(), WorkConfigProvider {

    @Inject
    lateinit var exceptionHandler: AppExceptionHandler
    
    @Inject
    lateinit var appInitializer: AppInitializer

    @Inject
    lateinit var appDatabase: RoomDatabase

    @Inject
    lateinit var reflectionRepository: ReflectionRepository

    @Inject
    lateinit var aiSuggestionScheduler: AiSuggestionScheduler

    @Inject
    lateinit var taskPersistentNotificationManager: com.timeflow.app.service.TaskPersistentNotificationManager

    private var isLowMemoryDevice = false
    private var supportsHardwareAcceleration = true
    private var hwcSafeEnabled = false

    companion object {
        private const val TAG = "TimeFlowApplication"
        
        // 通知渠道ID
        const val CHANNEL_TASK_REMINDER = "task_reminder"
        const val CHANNEL_DAILY_REVIEW = "daily_review"
        const val CHANNEL_SMART_SUGGESTIONS = "smart_suggestions"
        const val CHANNEL_AI_SUGGESTIONS = "ai_suggestions"
        const val CHANNEL_OPPO_SPECIAL = "oppo_special"
        const val CHANNEL_HABIT_REMINDER = "habit_reminder" // 新增习惯提醒渠道
        
        // 系统状态
        var isLowMemoryDevice = false
        var supportsHardwareAcceleration = true
        
        // 应用实例
        lateinit var instance: TimeFlowApplication
            private set
        
        // 检测是否为夜间模式
        fun isNightMode(context: Context): Boolean {
            val mode = context.resources?.configuration?.uiMode
                ?.and(AndroidConfiguration.UI_MODE_NIGHT_MASK)
            return mode == AndroidConfiguration.UI_MODE_NIGHT_YES
        }
    }

    override fun onCreate() {
        super.onCreate()
        
        // 使用LogConfig初始化日志
        LogConfig.initLogging()
        
        Timber.i("应用启动 - 版本: ${BuildConfig.VERSION_NAME}, 构建类型: ${BuildConfig.BUILD_TYPE}")
        
        // 检测设备能力
        detectDeviceCapabilities()
        
        // 如果是低内存设备，启用额外的内存优化
        val isLowMemory = isLowMemoryDevice
        val supportsHardware = supportsHardwareAcceleration
        
        // 应用渲染优化
        if (supportsHardware) {
            // 如果设备支持硬件加速，启用它
            hwcSafeEnabled = true
        } else {
            // 如果设备不支持硬件加速，禁用它
            hwcSafeEnabled = false
        }
        
        // 初始化HWC错误修复相关配置
        initializeHwcErrorHandling()
        
        // 初始化性能优化工具
        PerformanceOptimizer.initialize(this)
        
        // 初始化安全的图像加载器
        initializeSafeImageLoader()
        
        // 初始化应用程序
        appInitializer.initialize()
        
        // 预热数据库 - 使用自定义数据库预热优先级
        PerformanceOptimizer.preloadDatabase(this) {
            try {
                // 优先级预热 - 首先加载最常用的数据
                // 使用安全的调用方式
                val dao = appDatabase.javaClass.getDeclaredMethod("taskDao").invoke(appDatabase)
                
                // 首先预热未完成任务查询 - 这是主屏幕需要的
                dao?.javaClass?.getDeclaredMethod("getUncompletedTasks")
                    ?.invoke(dao)
                    
                // 然后预热其他查询
                dao?.javaClass?.getDeclaredMethod("getAllTasks")
                    ?.invoke(dao)
                    
                Timber.d("数据库预热成功")
            } catch (e: Exception) {
                Timber.e(e, "数据库预热失败")
            }
        }
        
        Log.d(TAG, "应用已初始化，设备能力: 低内存=$isLowMemory, 支持硬件加速=$supportsHardware")
        
        // 注册未捕获异常处理器
        Thread.setDefaultUncaughtExceptionHandler { thread, throwable ->
            android.util.Log.e("TimeFlow", "未捕获异常: ${throwable.message}", throwable)
            // 在生产环境中可以添加崩溃上报逻辑
        }
        
        // 应用初始化时不再需要ServiceLocator
        // 所有依赖都通过Hilt注入
        initializeServices()
    }
    
    /**
     * 判断是否应该对指定Activity应用HWC修复
     * 基于历史错误记录或设备特征进行判断
     */
    private fun shouldApplyHwcFix(activity: android.app.Activity): Boolean {
        // 1. 检查是否为任务相关页面，这些页面更容易出现问题
        val isTaskActivity = activity.javaClass.name.contains("task", ignoreCase = true) ||
                            activity.javaClass.name.contains("Task", ignoreCase = true)
        
        // 2. 检查设备制造商和型号
        val manufacturer = Build.MANUFACTURER.lowercase()
        val isProblematicDevice = manufacturer.contains("xiaomi") || 
                                 manufacturer.contains("oppo") ||
                                 manufacturer.contains("vivo") ||
                                 manufacturer.contains("samsung")
        
        // 如果是任务相关页面或者在问题设备上，应用修复
        return isTaskActivity || isProblematicDevice
    }
    
    /**
     * 检查是否为调试构建
     */
    private fun isDebugBuild(): Boolean {
        return applicationContext.applicationInfo.flags and android.content.pm.ApplicationInfo.FLAG_DEBUGGABLE != 0
    }
    
    /**
     * 发布版本专用日志树，仅输出警告和错误级别
     */
    private class ReleaseTree : Timber.Tree() {
        override fun log(priority: Int, tag: String?, message: String, t: Throwable?) {
            if (priority < Log.INFO) {
                return // 在发布版本中忽略VERBOSE和DEBUG日志
            }
            
            // 对于高优先级日志，使用Android的Log系统
            if (priority >= Log.WARN) {
                if (t != null) {
                    Log.println(priority, tag ?: "TimeFlow", "$message\n${Log.getStackTraceString(t)}")
                } else {
                    Log.println(priority, tag ?: "TimeFlow", message)
                }
            }
        }
    }
    
    /**
     * 创建应用程序使用的通知频道
     */
    private fun createNotificationChannels() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            
            // 创建任务提醒通知频道
            val taskChannel = NotificationChannel(
                CHANNEL_TASK_REMINDER,
                "任务提醒",
                NotificationManager.IMPORTANCE_HIGH
            ).apply {
                description = "用于提醒即将到期或重要的任务"
                enableVibration(true)
                vibrationPattern = longArrayOf(0, 500, 300, 500, 300, 500) // 🔧 强烈震动模式（重要任务）
                setShowBadge(true)
                // 🔧 修复：确保通知声音和震动不会被系统静音
                setBypassDnd(true) // 允许在免打扰模式下显示
                setSound(
                    RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION),
                    AudioAttributes.Builder()
                        .setUsage(AudioAttributes.USAGE_NOTIFICATION)
                        .setContentType(AudioAttributes.CONTENT_TYPE_SONIFICATION)
                        .build()
                )
            }
            
            // 创建每日回顾通知频道
            val dailyChannel = NotificationChannel(
                CHANNEL_DAILY_REVIEW,
                "每日回顾",
                NotificationManager.IMPORTANCE_DEFAULT
            ).apply {
                description = "每日任务完成情况和统计信息"
                enableVibration(true)
                vibrationPattern = longArrayOf(0, 300, 200, 300)
                setShowBadge(false)
            }
            
            // 创建智能建议通知频道
            val suggestChannel = NotificationChannel(
                CHANNEL_SMART_SUGGESTIONS,
                "智能建议",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "基于您的使用习惯提供的智能建议"
                setShowBadge(false)
            }
            
            // 创建AI建议通知频道
            val aiChannel = NotificationChannel(
                CHANNEL_AI_SUGGESTIONS,
                "AI建议",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "AI助手的智能建议和周报"
                setShowBadge(false)
            }
            
            // 创建习惯提醒通知频道
            val habitChannel = NotificationChannel(
                CHANNEL_HABIT_REMINDER,
                "习惯提醒",
                NotificationManager.IMPORTANCE_HIGH
            ).apply {
                description = "提醒您培养和保持好习惯"
                enableVibration(true)
                vibrationPattern = longArrayOf(0, 400, 200, 400)
                setShowBadge(true)
            }
            
            // 注册所有通知频道
            notificationManager.createNotificationChannels(
                listOf(taskChannel, dailyChannel, suggestChannel, aiChannel, habitChannel)
            )
            
            // 🔧 修复：强制更新现有通知渠道的震动设置
            updateNotificationChannelsForVibration()
        }
    }
    
    /**
     * 检查是否为OPPO设备
     */
    private fun isOppoDevice(): Boolean {
        return Build.BRAND.equals("oppo", ignoreCase = true) || 
               Build.MANUFACTURER.equals("oppo", ignoreCase = true)
    }
    
    /**
     * 🔧 修复震动功能：更新通知渠道的震动配置
     */
    private fun updateNotificationChannelsForVibration() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            try {
                val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
                
                // 检查现有渠道是否启用了震动
                val existingTaskChannel = notificationManager.getNotificationChannel(CHANNEL_TASK_REMINDER)
                val existingDailyChannel = notificationManager.getNotificationChannel(CHANNEL_DAILY_REVIEW)
                val existingHabitChannel = notificationManager.getNotificationChannel(CHANNEL_HABIT_REMINDER)
                
                // 任务提醒渠道
                if (existingTaskChannel?.shouldVibrate() != true) {
                    val taskChannel = NotificationChannel(
                        CHANNEL_TASK_REMINDER,
                        "任务提醒",
                        NotificationManager.IMPORTANCE_HIGH
                    ).apply {
                        description = "用于提醒即将到期或重要的任务"
                        enableVibration(true)
                        vibrationPattern = longArrayOf(0, 500, 300, 500, 300, 500)
                        setShowBadge(true)
                    }
                    notificationManager.createNotificationChannel(taskChannel)
                    Log.d(TAG, "✅ 任务提醒渠道震动已启用")
                }
                
                // 每日回顾渠道
                if (existingDailyChannel?.shouldVibrate() != true) {
                    val dailyChannel = NotificationChannel(
                        CHANNEL_DAILY_REVIEW,
                        "每日回顾",
                        NotificationManager.IMPORTANCE_DEFAULT
                    ).apply {
                        description = "每日任务完成情况和统计信息"
                        enableVibration(true)
                        vibrationPattern = longArrayOf(0, 300, 200, 300)
                        setShowBadge(false)
                    }
                    notificationManager.createNotificationChannel(dailyChannel)
                    Log.d(TAG, "✅ 每日回顾渠道震动已启用")
                }
                
                // 习惯提醒渠道
                if (existingHabitChannel?.shouldVibrate() != true) {
                    val habitChannel = NotificationChannel(
                        CHANNEL_HABIT_REMINDER,
                        "习惯提醒",
                        NotificationManager.IMPORTANCE_HIGH
                    ).apply {
                        description = "提醒您培养和保持好习惯"
                        enableVibration(true)
                        vibrationPattern = longArrayOf(0, 400, 200, 400)
                        setShowBadge(true)
                    }
                    notificationManager.createNotificationChannel(habitChannel)
                    Log.d(TAG, "✅ 习惯提醒渠道震动已启用")
                }
                
                Log.d(TAG, "🔧 通知渠道震动配置检查完成")
            } catch (e: Exception) {
                Log.e(TAG, "更新通知渠道震动配置失败", e)
            }
        }
    }
    
    /**
     * 为OPPO设备设置特殊配置
     */
    private fun setupOppoSpecialConfigurations() {
        // OPPO设备特殊处理：通知渠道、自启动权限检查等
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            
            // OPPO设备专用通知渠道，提高通知可靠性
            val oppoChannel = NotificationChannel(
                CHANNEL_OPPO_SPECIAL,
                "重要通知",
                NotificationManager.IMPORTANCE_HIGH
            ).apply {
                description = "重要提醒（OPPO优化）"
                enableVibration(true)
                vibrationPattern = longArrayOf(0, 600, 400, 600, 400, 600) // 🔧 OPPO设备强烈震动
                setShowBadge(true)
                lockscreenVisibility = NotificationManager.IMPORTANCE_HIGH
            }
            
            notificationManager.createNotificationChannel(oppoChannel)
        }
    }
    
    /**
     * 初始化HWC错误修复相关配置
     */
    private fun initializeHwcErrorHandling() {
        // 设置全局错误监控
        Thread {
            try {
                // 监控系统日志中的HWC错误
                val process = Runtime.getRuntime().exec("logcat -v threadtime HwcComposer:E HWComposer:E SurfaceFlinger:E *:S")
                val reader = process.inputStream.bufferedReader()
                
                var line: String?
                while (reader.readLine().also { line = it } != null) {
                    // 检测getLuts相关错误
                    val errorLine = line ?: continue
                    if (errorLine.contains("getLuts failed") || 
                        (errorLine.contains("HWComposer") && errorLine.contains("UNSUPPORTED"))) {
                        
                        // 更新设备能力标记，禁用硬件加速
                        if (supportsHardwareAcceleration) {
                            supportsHardwareAcceleration = false
                            Log.w(TAG, "检测到HWC错误，已更新设备硬件加速支持状态: $supportsHardwareAcceleration")
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "初始化HWC错误监控失败: ${e.message}")
            }
        }.apply {
            isDaemon = true
            name = "global-hwc-monitor"
            priority = Thread.MIN_PRIORITY
            start()
        }
    }
    
    /**
     * 检测设备能力
     */
    private fun detectDeviceCapabilities() {
        try {
            // 检测是否为低内存设备
            val activityManager = getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
            isLowMemoryDevice = activityManager.isLowRamDevice
            
            // 检测HDR能力作为硬件加速支持的指标
            supportsHardwareAcceleration = HwcLutsErrorFixer.checkHdrCapability(this) && !isLowMemoryDevice
            
            // 更新资源配置
            resources.getBoolean(R.bool.is_low_memory_device).also { resourceValue ->
                // 如果资源配置与检测到的设备能力不一致，以检测结果为准
                if (resourceValue != isLowMemoryDevice) {
                    isLowMemoryDevice = isLowMemoryDevice || resourceValue
                    Log.d(TAG, "资源配置与检测结果不一致，采用综合判断: 低内存=$isLowMemoryDevice")
                }
            }
            
            Log.d(TAG, "设备能力检测完成: 低内存=$isLowMemoryDevice, 支持硬件加速=$supportsHardwareAcceleration")
        } catch (e: Exception) {
            Log.e(TAG, "设备能力检测失败: ${e.message}")
            
            // 出错时保守处理，假设为低内存设备
            isLowMemoryDevice = true
            supportsHardwareAcceleration = false
        }
    }

    // 提供WorkManager配置
    override val workManagerConfiguration: WorkConfiguration
        get() = WorkConfiguration.Builder()
            .setMinimumLoggingLevel(if (BuildConfig.DEBUG) Log.INFO else Log.ERROR)
            .build()

    // This method is no longer needed as we now use the property above
    // However, if there's code that calls this method directly, we'll keep it for compatibility
    fun getWorkManagerConfigurationCompat(): WorkConfiguration {
        return workManagerConfiguration
    }

    /**
     * 初始化日志框架
     */
    private fun initLogger() {
        // 根据构建类型使用不同的日志树
        if (isDebugBuild()) {
            Timber.plant(Timber.DebugTree())
            Timber.d("已启用调试日志模式")
        } else {
            // 发布版本使用限制版日志树
            Timber.plant(ReleaseTree())
        }
    }
    
    /**
     * 设置严格模式
     * 仅在调试构建中启用
     */
    private fun setupStrictMode() {
        if (isDebugBuild()) {
            Timber.d("启用严格模式")
            StrictMode.setThreadPolicy(
                StrictMode.ThreadPolicy.Builder()
                    .detectAll()
                    .penaltyLog()
                    .build()
            )
            
            StrictMode.setVmPolicy(
                StrictMode.VmPolicy.Builder()
                    .detectLeakedSqlLiteObjects()
                    .detectLeakedClosableObjects()
                    .detectActivityLeaks()
                    .penaltyLog()
                    .build()
            )
        }
    }
    
    /**
     * 设置默认夜间模式
     */
    private fun setupDefaultNightMode() {
        // 读取用户偏好，默认跟随系统
        val nightMode = AppCompatDelegate.MODE_NIGHT_FOLLOW_SYSTEM
        AppCompatDelegate.setDefaultNightMode(nightMode)
        Timber.d("设置默认夜间模式: %s", when(nightMode) {
            AppCompatDelegate.MODE_NIGHT_NO -> "始终使用日间模式"
            AppCompatDelegate.MODE_NIGHT_YES -> "始终使用夜间模式"
            AppCompatDelegate.MODE_NIGHT_FOLLOW_SYSTEM -> "跟随系统"
            AppCompatDelegate.MODE_NIGHT_AUTO_BATTERY -> "省电模式时自动切换"
            else -> "未知模式"
        })
    }

    override fun onLowMemory() {
        super.onLowMemory()
        // 内存不足时释放缓存
        PerformanceOptimizer.releaseResources()
    }

    private fun initPerformanceFlags() {
        val activityManager = getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        // ... existing code ...
        
        isLowMemoryDevice = activityManager.isLowRamDevice
        supportsHardwareAcceleration = isLowMemoryDevice.not() && Build.VERSION.SDK_INT >= Build.VERSION_CODES.N
        
        // ... existing code ...
        
        hwcSafeEnabled = false
        if (hwcSafeEnabled) {
            // ... existing code ...
        }
    }

    /**
     * 初始化安全的图像加载器
     * 解决硬件位图与软件渲染冲突问题
     */
    private fun initializeSafeImageLoader() {
        try {
            Log.d(TAG, "正在初始化安全的图像加载器...")
            
            // 创建安全的ImageLoader配置
            val safeImageLoader = ImageLoader.Builder(this)
                .memoryCache {
                    MemoryCache.Builder(this)
                        .maxSizePercent(0.25) // 使用25%内存作为图像缓存
                        .build()
                }
                .components {
                    // 使用BitmapFactoryDecoder确保位图兼容性
                    add(BitmapFactoryDecoder.Factory())
                }
                // 关键设置：禁用硬件位图以避免软件渲染冲突
                .allowHardware(false)
                .allowRgb565(true) // 允许RGB565格式以节省内存
                .bitmapConfig(Bitmap.Config.RGB_565) // 默认使用RGB565
                .crossfade(300) // 300ms淡入淡出动画
                .respectCacheHeaders(false)
                .build()
            
            // 设置为全局默认ImageLoader
            Coil.setImageLoader(safeImageLoader)
            
            Log.d(TAG, "安全图像加载器初始化完成 - 已禁用硬件位图")
        } catch (e: Exception) {
            Log.e(TAG, "初始化安全图像加载器失败: ${e.message}")
            
            // 降级方案：创建最简单的配置
            try {
                val fallbackLoader = ImageLoader.Builder(this)
                    .allowHardware(false)
                    .bitmapConfig(Bitmap.Config.RGB_565)
                    .build()
                Coil.setImageLoader(fallbackLoader)
                Log.d(TAG, "使用降级图像加载器配置")
            } catch (fallbackException: Exception) {
                Log.e(TAG, "降级图像加载器配置也失败: ${fallbackException.message}")
            }
        }
    }

    /**
     * 初始化服务
     */
    private fun initializeServices() {
        try {
            // 确保Hilt容器已初始化
            android.util.Log.d("TimeFlow", "开始初始化服务...")
            
            // 创建通知渠道
            createNotificationChannels()
            
            // 🔧 修复震动功能：确保通知渠道启用震动
            updateNotificationChannelsForVibration()
            
            // 如果是OPPO设备，进行特殊配置
            if (isOppoDevice()) {
                setupOppoSpecialConfigurations()
            }
            
            // 🤖 启用周期性AI建议通知
            try {
                aiSuggestionScheduler.enablePeriodicAiSuggestions()
                android.util.Log.d("TimeFlow", "✅ AI建议通知调度已启用")
            } catch (e: Exception) {
                android.util.Log.e("TimeFlow", "❌ AI建议通知调度启用失败", e)
            }

            // 📋 根据用户设置启动任务常驻通知服务
            try {
                // 这里暂时默认启动，后续可以根据用户设置来决定
                // TODO: 从DataStore读取用户设置决定是否启动
                taskPersistentNotificationManager.startPersistentNotification()
                android.util.Log.d("TimeFlow", "✅ 任务常驻通知服务已启动")
            } catch (e: Exception) {
                android.util.Log.e("TimeFlow", "❌ 任务常驻通知服务启动失败", e)
            }

            android.util.Log.d("TimeFlow", "服务初始化完成")
        } catch (e: Exception) {
            android.util.Log.e("TimeFlow", "服务初始化失败", e)
        }
    }

    /**
     * 应用语言设置到Context
     */
    override fun attachBaseContext(base: Context?) {
        val context = base ?: return

        try {
            // 应用语言设置
            val localizedContext = LanguageManager.applyLanguageToContext(context)
            super.attachBaseContext(localizedContext)
        } catch (e: Exception) {
            Log.e(TAG, "应用语言设置失败: ${e.message}", e)
            super.attachBaseContext(context)
        }
    }
}

// 为Activity等提供方便的上下文扩展
fun Context.isNightMode(): Boolean = TimeFlowApplication.isNightMode(this) 