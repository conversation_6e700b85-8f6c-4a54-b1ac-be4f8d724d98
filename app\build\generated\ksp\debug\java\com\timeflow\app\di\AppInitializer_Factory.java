package com.timeflow.app.di;

import android.content.Context;
import com.timeflow.app.data.repository.DefaultTemplateInitializer;
import com.timeflow.app.utils.SampleDataGenerator;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AppInitializer_Factory implements Factory<AppInitializer> {
  private final Provider<Context> contextProvider;

  private final Provider<SampleDataGenerator> sampleDataGeneratorProvider;

  private final Provider<DefaultTemplateInitializer> templateInitializerProvider;

  public AppInitializer_Factory(Provider<Context> contextProvider,
      Provider<SampleDataGenerator> sampleDataGeneratorProvider,
      Provider<DefaultTemplateInitializer> templateInitializerProvider) {
    this.contextProvider = contextProvider;
    this.sampleDataGeneratorProvider = sampleDataGeneratorProvider;
    this.templateInitializerProvider = templateInitializerProvider;
  }

  @Override
  public AppInitializer get() {
    return newInstance(contextProvider.get(), sampleDataGeneratorProvider.get(), templateInitializerProvider.get());
  }

  public static AppInitializer_Factory create(Provider<Context> contextProvider,
      Provider<SampleDataGenerator> sampleDataGeneratorProvider,
      Provider<DefaultTemplateInitializer> templateInitializerProvider) {
    return new AppInitializer_Factory(contextProvider, sampleDataGeneratorProvider, templateInitializerProvider);
  }

  public static AppInitializer newInstance(Context context, SampleDataGenerator sampleDataGenerator,
      DefaultTemplateInitializer templateInitializer) {
    return new AppInitializer(context, sampleDataGenerator, templateInitializer);
  }
}
