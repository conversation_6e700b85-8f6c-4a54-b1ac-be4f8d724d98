package com.timeflow.app.ui.viewmodel;

import com.timeflow.app.data.repository.AiTaskRepository;
import com.timeflow.app.data.repository.TaskRepository;
import com.timeflow.app.data.repository.UserPreferenceRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AiAssistantViewModel_Factory implements Factory<AiAssistantViewModel> {
  private final Provider<AiTaskRepository> aiTaskRepositoryProvider;

  private final Provider<TaskRepository> taskRepositoryProvider;

  private final Provider<UserPreferenceRepository> userPreferenceRepositoryProvider;

  public AiAssistantViewModel_Factory(Provider<AiTaskRepository> aiTaskRepositoryProvider,
      Provider<TaskRepository> taskRepositoryProvider,
      Provider<UserPreferenceRepository> userPreferenceRepositoryProvider) {
    this.aiTaskRepositoryProvider = aiTaskRepositoryProvider;
    this.taskRepositoryProvider = taskRepositoryProvider;
    this.userPreferenceRepositoryProvider = userPreferenceRepositoryProvider;
  }

  @Override
  public AiAssistantViewModel get() {
    return newInstance(aiTaskRepositoryProvider.get(), taskRepositoryProvider.get(), userPreferenceRepositoryProvider.get());
  }

  public static AiAssistantViewModel_Factory create(
      Provider<AiTaskRepository> aiTaskRepositoryProvider,
      Provider<TaskRepository> taskRepositoryProvider,
      Provider<UserPreferenceRepository> userPreferenceRepositoryProvider) {
    return new AiAssistantViewModel_Factory(aiTaskRepositoryProvider, taskRepositoryProvider, userPreferenceRepositoryProvider);
  }

  public static AiAssistantViewModel newInstance(AiTaskRepository aiTaskRepository,
      TaskRepository taskRepository, UserPreferenceRepository userPreferenceRepository) {
    return new AiAssistantViewModel(aiTaskRepository, taskRepository, userPreferenceRepository);
  }
}
