package com.timeflow.app.di;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AnalyticsModule_ProvideCrashReporterFactory implements Factory<CrashReporter> {
  private final Provider<Context> contextProvider;

  public AnalyticsModule_ProvideCrashReporterFactory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public CrashReporter get() {
    return provideCrashReporter(contextProvider.get());
  }

  public static AnalyticsModule_ProvideCrashReporterFactory create(
      Provider<Context> contextProvider) {
    return new AnalyticsModule_ProvideCrashReporterFactory(contextProvider);
  }

  public static CrashReporter provideCrashReporter(Context context) {
    return Preconditions.checkNotNullFromProvides(AnalyticsModule.INSTANCE.provideCrashReporter(context));
  }
}
