package com.timeflow.app.utils;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class TemplateCache_Factory implements Factory<TemplateCache> {
  @Override
  public TemplateCache get() {
    return newInstance();
  }

  public static TemplateCache_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static TemplateCache newInstance() {
    return new TemplateCache();
  }

  private static final class InstanceHolder {
    private static final TemplateCache_Factory INSTANCE = new TemplateCache_Factory();
  }
}
