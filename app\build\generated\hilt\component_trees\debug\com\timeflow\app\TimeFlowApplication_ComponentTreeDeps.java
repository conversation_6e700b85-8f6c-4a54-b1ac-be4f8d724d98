package com.timeflow.app;

import dagger.hilt.internal.aggregatedroot.codegen._com_timeflow_app_TimeFlowApplication;
import dagger.hilt.internal.componenttreedeps.ComponentTreeDeps;
import dagger.hilt.processor.internal.definecomponent.codegen._dagger_hilt_android_components_ActivityComponent;
import dagger.hilt.processor.internal.definecomponent.codegen._dagger_hilt_android_components_ActivityRetainedComponent;
import dagger.hilt.processor.internal.definecomponent.codegen._dagger_hilt_android_components_FragmentComponent;
import dagger.hilt.processor.internal.definecomponent.codegen._dagger_hilt_android_components_ServiceComponent;
import dagger.hilt.processor.internal.definecomponent.codegen._dagger_hilt_android_components_ViewComponent;
import dagger.hilt.processor.internal.definecomponent.codegen._dagger_hilt_android_components_ViewModelComponent;
import dagger.hilt.processor.internal.definecomponent.codegen._dagger_hilt_android_components_ViewWithFragmentComponent;
import dagger.hilt.processor.internal.definecomponent.codegen._dagger_hilt_android_internal_builders_ActivityComponentBuilder;
import dagger.hilt.processor.internal.definecomponent.codegen._dagger_hilt_android_internal_builders_ActivityRetainedComponentBuilder;
import dagger.hilt.processor.internal.definecomponent.codegen._dagger_hilt_android_internal_builders_FragmentComponentBuilder;
import dagger.hilt.processor.internal.definecomponent.codegen._dagger_hilt_android_internal_builders_ServiceComponentBuilder;
import dagger.hilt.processor.internal.definecomponent.codegen._dagger_hilt_android_internal_builders_ViewComponentBuilder;
import dagger.hilt.processor.internal.definecomponent.codegen._dagger_hilt_android_internal_builders_ViewModelComponentBuilder;
import dagger.hilt.processor.internal.definecomponent.codegen._dagger_hilt_android_internal_builders_ViewWithFragmentComponentBuilder;
import dagger.hilt.processor.internal.definecomponent.codegen._dagger_hilt_components_SingletonComponent;
import hilt_aggregated_deps._androidx_hilt_work_HiltWrapper_WorkerFactoryModule;
import hilt_aggregated_deps._com_timeflow_app_MainActivity_GeneratedInjector;
import hilt_aggregated_deps._com_timeflow_app_TimeFlowApplication_GeneratedInjector;
import hilt_aggregated_deps._com_timeflow_app_di_AnalyticsModule;
import hilt_aggregated_deps._com_timeflow_app_di_AppModule;
import hilt_aggregated_deps._com_timeflow_app_di_DataStoreModule;
import hilt_aggregated_deps._com_timeflow_app_di_DatabaseModule;
import hilt_aggregated_deps._com_timeflow_app_di_ImageModule;
import hilt_aggregated_deps._com_timeflow_app_di_MedicationModule;
import hilt_aggregated_deps._com_timeflow_app_di_ReflectionModule;
import hilt_aggregated_deps._com_timeflow_app_di_RepositoryBindingsModule;
import hilt_aggregated_deps._com_timeflow_app_di_RepositoryModule;
import hilt_aggregated_deps._com_timeflow_app_di_SyncModule;
import hilt_aggregated_deps._com_timeflow_app_di_TaskTimeModule;
import hilt_aggregated_deps._com_timeflow_app_di_UtilsModule;
import hilt_aggregated_deps._com_timeflow_app_di_ViewModelModule;
import hilt_aggregated_deps._com_timeflow_app_di_WishModule;
import hilt_aggregated_deps._com_timeflow_app_di_WorkManagerInitializer_WorkManagerInitializerEntryPoint;
import hilt_aggregated_deps._com_timeflow_app_di_WorkManagerModule;
import hilt_aggregated_deps._com_timeflow_app_initializer_RecurringTaskInitializer_RecurringTaskInitializerEntryPoint;
import hilt_aggregated_deps._com_timeflow_app_receiver_BootCompletedReceiver_GeneratedInjector;
import hilt_aggregated_deps._com_timeflow_app_receiver_DailyReviewAlarmReceiver_GeneratedInjector;
import hilt_aggregated_deps._com_timeflow_app_receiver_FocusTimerActionReceiver_GeneratedInjector;
import hilt_aggregated_deps._com_timeflow_app_receiver_HabitAlarmReceiver_GeneratedInjector;
import hilt_aggregated_deps._com_timeflow_app_receiver_TaskAlarmReceiver_GeneratedInjector;
import hilt_aggregated_deps._com_timeflow_app_receiver_TaskPersistentNotificationActionReceiver_GeneratedInjector;
import hilt_aggregated_deps._com_timeflow_app_service_AutoBackupService_GeneratedInjector;
import hilt_aggregated_deps._com_timeflow_app_service_FocusTimerService_GeneratedInjector;
import hilt_aggregated_deps._com_timeflow_app_service_NotificationTestService_GeneratedInjector;
import hilt_aggregated_deps._com_timeflow_app_service_TaskPersistentNotificationService_GeneratedInjector;
import hilt_aggregated_deps._com_timeflow_app_service_TimeTrackingService_GeneratedInjector;
import hilt_aggregated_deps._com_timeflow_app_ui_MainActivity_GeneratedInjector;
import hilt_aggregated_deps._com_timeflow_app_ui_language_LanguageSettingsViewModel_HiltModules_BindsModule;
import hilt_aggregated_deps._com_timeflow_app_ui_language_LanguageSettingsViewModel_HiltModules_KeyModule;
import hilt_aggregated_deps._com_timeflow_app_ui_screen_ai_AIReviewViewModel_HiltModules_BindsModule;
import hilt_aggregated_deps._com_timeflow_app_ui_screen_ai_AIReviewViewModel_HiltModules_KeyModule;
import hilt_aggregated_deps._com_timeflow_app_ui_screen_analytics_AnalyticsViewModel_HiltModules_BindsModule;
import hilt_aggregated_deps._com_timeflow_app_ui_screen_analytics_AnalyticsViewModel_HiltModules_KeyModule;
import hilt_aggregated_deps._com_timeflow_app_ui_screen_calendar_CalendarViewModel_HiltModules_BindsModule;
import hilt_aggregated_deps._com_timeflow_app_ui_screen_calendar_CalendarViewModel_HiltModules_KeyModule;
import hilt_aggregated_deps._com_timeflow_app_ui_screen_discover_DiscoverViewModel_HiltModules_BindsModule;
import hilt_aggregated_deps._com_timeflow_app_ui_screen_discover_DiscoverViewModel_HiltModules_KeyModule;
import hilt_aggregated_deps._com_timeflow_app_ui_screen_goal_GoalTemplateViewModel_HiltModules_BindsModule;
import hilt_aggregated_deps._com_timeflow_app_ui_screen_goal_GoalTemplateViewModel_HiltModules_KeyModule;
import hilt_aggregated_deps._com_timeflow_app_ui_screen_goal_GoalViewModel_HiltModules_BindsModule;
import hilt_aggregated_deps._com_timeflow_app_ui_screen_goal_GoalViewModel_HiltModules_KeyModule;
import hilt_aggregated_deps._com_timeflow_app_ui_screen_health_AddHabitViewModel_HiltModules_BindsModule;
import hilt_aggregated_deps._com_timeflow_app_ui_screen_health_AddHabitViewModel_HiltModules_KeyModule;
import hilt_aggregated_deps._com_timeflow_app_ui_screen_health_ProfessionalMedicationViewModel_HiltModules_BindsModule;
import hilt_aggregated_deps._com_timeflow_app_ui_screen_health_ProfessionalMedicationViewModel_HiltModules_KeyModule;
import hilt_aggregated_deps._com_timeflow_app_ui_screen_health_UserPreferencesManagerEntryPoint;
import hilt_aggregated_deps._com_timeflow_app_ui_screen_milestone_MilestoneViewModel_HiltModules_BindsModule;
import hilt_aggregated_deps._com_timeflow_app_ui_screen_milestone_MilestoneViewModel_HiltModules_KeyModule;
import hilt_aggregated_deps._com_timeflow_app_ui_screen_profile_EmotionStatisticsViewModel_HiltModules_BindsModule;
import hilt_aggregated_deps._com_timeflow_app_ui_screen_profile_EmotionStatisticsViewModel_HiltModules_KeyModule;
import hilt_aggregated_deps._com_timeflow_app_ui_screen_profile_ProfileViewModel_HiltModules_BindsModule;
import hilt_aggregated_deps._com_timeflow_app_ui_screen_profile_ProfileViewModel_HiltModules_KeyModule;
import hilt_aggregated_deps._com_timeflow_app_ui_screen_reflection_ReflectionDetailViewModel_HiltModules_BindsModule;
import hilt_aggregated_deps._com_timeflow_app_ui_screen_reflection_ReflectionDetailViewModel_HiltModules_KeyModule;
import hilt_aggregated_deps._com_timeflow_app_ui_screen_reflection_ReflectionViewModel_HiltModules_BindsModule;
import hilt_aggregated_deps._com_timeflow_app_ui_screen_reflection_ReflectionViewModel_HiltModules_KeyModule;
import hilt_aggregated_deps._com_timeflow_app_ui_screen_settings_NotificationSettingsViewModel_HiltModules_BindsModule;
import hilt_aggregated_deps._com_timeflow_app_ui_screen_settings_NotificationSettingsViewModel_HiltModules_KeyModule;
import hilt_aggregated_deps._com_timeflow_app_ui_screen_settings_SettingsViewModel_HiltModules_BindsModule;
import hilt_aggregated_deps._com_timeflow_app_ui_screen_settings_SettingsViewModel_HiltModules_KeyModule;
import hilt_aggregated_deps._com_timeflow_app_ui_screen_settings_SyncSettingsViewModel_HiltModules_BindsModule;
import hilt_aggregated_deps._com_timeflow_app_ui_screen_settings_SyncSettingsViewModel_HiltModules_KeyModule;
import hilt_aggregated_deps._com_timeflow_app_ui_screen_task_TaskDetailViewModel_HiltModules_BindsModule;
import hilt_aggregated_deps._com_timeflow_app_ui_screen_task_TaskDetailViewModel_HiltModules_KeyModule;
import hilt_aggregated_deps._com_timeflow_app_ui_screen_task_TaskViewModel_HiltModules_BindsModule;
import hilt_aggregated_deps._com_timeflow_app_ui_screen_task_TaskViewModel_HiltModules_KeyModule;
import hilt_aggregated_deps._com_timeflow_app_ui_settings_PresetThemeViewModel_HiltModules_BindsModule;
import hilt_aggregated_deps._com_timeflow_app_ui_settings_PresetThemeViewModel_HiltModules_KeyModule;
import hilt_aggregated_deps._com_timeflow_app_ui_settings_ThemeSettingsViewModel_HiltModules_BindsModule;
import hilt_aggregated_deps._com_timeflow_app_ui_settings_ThemeSettingsViewModel_HiltModules_KeyModule;
import hilt_aggregated_deps._com_timeflow_app_ui_statistics_TimeStatisticsViewModel_HiltModules_BindsModule;
import hilt_aggregated_deps._com_timeflow_app_ui_statistics_TimeStatisticsViewModel_HiltModules_KeyModule;
import hilt_aggregated_deps._com_timeflow_app_ui_task_KanbanViewModel_HiltModules_BindsModule;
import hilt_aggregated_deps._com_timeflow_app_ui_task_KanbanViewModel_HiltModules_KeyModule;
import hilt_aggregated_deps._com_timeflow_app_ui_task_TaskListViewModel_HiltModules_BindsModule;
import hilt_aggregated_deps._com_timeflow_app_ui_task_TaskListViewModel_HiltModules_KeyModule;
import hilt_aggregated_deps._com_timeflow_app_ui_timetracking_TaskTimeStatisticsViewModel_HiltModules_BindsModule;
import hilt_aggregated_deps._com_timeflow_app_ui_timetracking_TaskTimeStatisticsViewModel_HiltModules_KeyModule;
import hilt_aggregated_deps._com_timeflow_app_ui_timetracking_TimeTrackingViewModel_HiltModules_BindsModule;
import hilt_aggregated_deps._com_timeflow_app_ui_timetracking_TimeTrackingViewModel_HiltModules_KeyModule;
import hilt_aggregated_deps._com_timeflow_app_ui_viewmodel_AiAssistantViewModel_HiltModules_BindsModule;
import hilt_aggregated_deps._com_timeflow_app_ui_viewmodel_AiAssistantViewModel_HiltModules_KeyModule;
import hilt_aggregated_deps._com_timeflow_app_ui_viewmodel_AiConfigViewModel_HiltModules_BindsModule;
import hilt_aggregated_deps._com_timeflow_app_ui_viewmodel_AiConfigViewModel_HiltModules_KeyModule;
import hilt_aggregated_deps._com_timeflow_app_ui_viewmodel_AiSettingsViewModel_HiltModules_BindsModule;
import hilt_aggregated_deps._com_timeflow_app_ui_viewmodel_AiSettingsViewModel_HiltModules_KeyModule;
import hilt_aggregated_deps._com_timeflow_app_ui_viewmodel_GlobalTimerViewModel_HiltModules_BindsModule;
import hilt_aggregated_deps._com_timeflow_app_ui_viewmodel_GlobalTimerViewModel_HiltModules_KeyModule;
import hilt_aggregated_deps._com_timeflow_app_ui_viewmodel_GoalCreationViewModel_HiltModules_BindsModule;
import hilt_aggregated_deps._com_timeflow_app_ui_viewmodel_GoalCreationViewModel_HiltModules_KeyModule;
import hilt_aggregated_deps._com_timeflow_app_ui_viewmodel_GoalViewModel_HiltModules_BindsModule;
import hilt_aggregated_deps._com_timeflow_app_ui_viewmodel_GoalViewModel_HiltModules_KeyModule;
import hilt_aggregated_deps._com_timeflow_app_ui_viewmodel_HabitViewModel_HiltModules_BindsModule;
import hilt_aggregated_deps._com_timeflow_app_ui_viewmodel_HabitViewModel_HiltModules_KeyModule;
import hilt_aggregated_deps._com_timeflow_app_ui_viewmodel_MenstrualCycleViewModel_HiltModules_BindsModule;
import hilt_aggregated_deps._com_timeflow_app_ui_viewmodel_MenstrualCycleViewModel_HiltModules_KeyModule;
import hilt_aggregated_deps._com_timeflow_app_ui_viewmodel_TaskTimeViewModel_HiltModules_BindsModule;
import hilt_aggregated_deps._com_timeflow_app_ui_viewmodel_TaskTimeViewModel_HiltModules_KeyModule;
import hilt_aggregated_deps._com_timeflow_app_ui_viewmodel_TimeFlowViewModel_HiltModules_BindsModule;
import hilt_aggregated_deps._com_timeflow_app_ui_viewmodel_TimeFlowViewModel_HiltModules_KeyModule;
import hilt_aggregated_deps._com_timeflow_app_ui_viewmodel_WishListViewModel_HiltModules_BindsModule;
import hilt_aggregated_deps._com_timeflow_app_ui_viewmodel_WishListViewModel_HiltModules_KeyModule;
import hilt_aggregated_deps._com_timeflow_app_ui_viewmodel_WishStatisticsViewModel_HiltModules_BindsModule;
import hilt_aggregated_deps._com_timeflow_app_ui_viewmodel_WishStatisticsViewModel_HiltModules_KeyModule;
import hilt_aggregated_deps._com_timeflow_app_viewmodel_BackupRestoreViewModel_HiltModules_BindsModule;
import hilt_aggregated_deps._com_timeflow_app_viewmodel_BackupRestoreViewModel_HiltModules_KeyModule;
import hilt_aggregated_deps._com_timeflow_app_viewmodel_BackupSettingsViewModel_HiltModules_BindsModule;
import hilt_aggregated_deps._com_timeflow_app_viewmodel_BackupSettingsViewModel_HiltModules_KeyModule;
import hilt_aggregated_deps._com_timeflow_app_viewmodel_DataManagementViewModel_HiltModules_BindsModule;
import hilt_aggregated_deps._com_timeflow_app_viewmodel_DataManagementViewModel_HiltModules_KeyModule;
import hilt_aggregated_deps._com_timeflow_app_worker_AutoBackupWorker_HiltModule;
import hilt_aggregated_deps._com_timeflow_app_worker_OverdueTaskCheckWorkerEntryPoint;
import hilt_aggregated_deps._com_timeflow_app_worker_RecurringTaskWorker_HiltModule;
import hilt_aggregated_deps._dagger_hilt_android_flags_FragmentGetContextFix_FragmentGetContextFixEntryPoint;
import hilt_aggregated_deps._dagger_hilt_android_flags_HiltWrapper_FragmentGetContextFix_FragmentGetContextFixModule;
import hilt_aggregated_deps._dagger_hilt_android_internal_lifecycle_DefaultViewModelFactories_ActivityEntryPoint;
import hilt_aggregated_deps._dagger_hilt_android_internal_lifecycle_DefaultViewModelFactories_FragmentEntryPoint;
import hilt_aggregated_deps._dagger_hilt_android_internal_lifecycle_HiltViewModelFactory_ViewModelFactoriesEntryPoint;
import hilt_aggregated_deps._dagger_hilt_android_internal_lifecycle_HiltWrapper_DefaultViewModelFactories_ActivityModule;
import hilt_aggregated_deps._dagger_hilt_android_internal_lifecycle_HiltWrapper_HiltViewModelFactory_ActivityCreatorEntryPoint;
import hilt_aggregated_deps._dagger_hilt_android_internal_lifecycle_HiltWrapper_HiltViewModelFactory_ViewModelModule;
import hilt_aggregated_deps._dagger_hilt_android_internal_managers_ActivityComponentManager_ActivityComponentBuilderEntryPoint;
import hilt_aggregated_deps._dagger_hilt_android_internal_managers_FragmentComponentManager_FragmentComponentBuilderEntryPoint;
import hilt_aggregated_deps._dagger_hilt_android_internal_managers_HiltWrapper_ActivityRetainedComponentManager_ActivityRetainedComponentBuilderEntryPoint;
import hilt_aggregated_deps._dagger_hilt_android_internal_managers_HiltWrapper_ActivityRetainedComponentManager_ActivityRetainedLifecycleEntryPoint;
import hilt_aggregated_deps._dagger_hilt_android_internal_managers_HiltWrapper_ActivityRetainedComponentManager_LifecycleModule;
import hilt_aggregated_deps._dagger_hilt_android_internal_managers_HiltWrapper_SavedStateHandleModule;
import hilt_aggregated_deps._dagger_hilt_android_internal_managers_ServiceComponentManager_ServiceComponentBuilderEntryPoint;
import hilt_aggregated_deps._dagger_hilt_android_internal_managers_ViewComponentManager_ViewComponentBuilderEntryPoint;
import hilt_aggregated_deps._dagger_hilt_android_internal_managers_ViewComponentManager_ViewWithFragmentComponentBuilderEntryPoint;
import hilt_aggregated_deps._dagger_hilt_android_internal_modules_ApplicationContextModule;
import hilt_aggregated_deps._dagger_hilt_android_internal_modules_HiltWrapper_ActivityModule;

@ComponentTreeDeps(
    rootDeps = _com_timeflow_app_TimeFlowApplication.class,
    defineComponentDeps = {
        _dagger_hilt_android_components_ActivityComponent.class,
        _dagger_hilt_android_components_ActivityRetainedComponent.class,
        _dagger_hilt_android_components_FragmentComponent.class,
        _dagger_hilt_android_components_ServiceComponent.class,
        _dagger_hilt_android_components_ViewComponent.class,
        _dagger_hilt_android_components_ViewModelComponent.class,
        _dagger_hilt_android_components_ViewWithFragmentComponent.class,
        _dagger_hilt_android_internal_builders_ActivityComponentBuilder.class,
        _dagger_hilt_android_internal_builders_ActivityRetainedComponentBuilder.class,
        _dagger_hilt_android_internal_builders_FragmentComponentBuilder.class,
        _dagger_hilt_android_internal_builders_ServiceComponentBuilder.class,
        _dagger_hilt_android_internal_builders_ViewComponentBuilder.class,
        _dagger_hilt_android_internal_builders_ViewModelComponentBuilder.class,
        _dagger_hilt_android_internal_builders_ViewWithFragmentComponentBuilder.class,
        _dagger_hilt_components_SingletonComponent.class
    },
    aggregatedDeps = {
        _androidx_hilt_work_HiltWrapper_WorkerFactoryModule.class,
        _com_timeflow_app_MainActivity_GeneratedInjector.class,
        _com_timeflow_app_TimeFlowApplication_GeneratedInjector.class,
        _com_timeflow_app_di_AnalyticsModule.class,
        _com_timeflow_app_di_AppModule.class,
        _com_timeflow_app_di_DataStoreModule.class,
        _com_timeflow_app_di_DatabaseModule.class,
        _com_timeflow_app_di_ImageModule.class,
        _com_timeflow_app_di_MedicationModule.class,
        _com_timeflow_app_di_ReflectionModule.class,
        _com_timeflow_app_di_RepositoryBindingsModule.class,
        _com_timeflow_app_di_RepositoryModule.class,
        _com_timeflow_app_di_SyncModule.class,
        _com_timeflow_app_di_TaskTimeModule.class,
        _com_timeflow_app_di_UtilsModule.class,
        _com_timeflow_app_di_ViewModelModule.class,
        _com_timeflow_app_di_WishModule.class,
        _com_timeflow_app_di_WorkManagerInitializer_WorkManagerInitializerEntryPoint.class,
        _com_timeflow_app_di_WorkManagerModule.class,
        _com_timeflow_app_initializer_RecurringTaskInitializer_RecurringTaskInitializerEntryPoint.class,
        _com_timeflow_app_receiver_BootCompletedReceiver_GeneratedInjector.class,
        _com_timeflow_app_receiver_DailyReviewAlarmReceiver_GeneratedInjector.class,
        _com_timeflow_app_receiver_FocusTimerActionReceiver_GeneratedInjector.class,
        _com_timeflow_app_receiver_HabitAlarmReceiver_GeneratedInjector.class,
        _com_timeflow_app_receiver_TaskAlarmReceiver_GeneratedInjector.class,
        _com_timeflow_app_receiver_TaskPersistentNotificationActionReceiver_GeneratedInjector.class,
        _com_timeflow_app_service_AutoBackupService_GeneratedInjector.class,
        _com_timeflow_app_service_FocusTimerService_GeneratedInjector.class,
        _com_timeflow_app_service_NotificationTestService_GeneratedInjector.class,
        _com_timeflow_app_service_TaskPersistentNotificationService_GeneratedInjector.class,
        _com_timeflow_app_service_TimeTrackingService_GeneratedInjector.class,
        _com_timeflow_app_ui_MainActivity_GeneratedInjector.class,
        _com_timeflow_app_ui_language_LanguageSettingsViewModel_HiltModules_BindsModule.class,
        _com_timeflow_app_ui_language_LanguageSettingsViewModel_HiltModules_KeyModule.class,
        _com_timeflow_app_ui_screen_ai_AIReviewViewModel_HiltModules_BindsModule.class,
        _com_timeflow_app_ui_screen_ai_AIReviewViewModel_HiltModules_KeyModule.class,
        _com_timeflow_app_ui_screen_analytics_AnalyticsViewModel_HiltModules_BindsModule.class,
        _com_timeflow_app_ui_screen_analytics_AnalyticsViewModel_HiltModules_KeyModule.class,
        _com_timeflow_app_ui_screen_calendar_CalendarViewModel_HiltModules_BindsModule.class,
        _com_timeflow_app_ui_screen_calendar_CalendarViewModel_HiltModules_KeyModule.class,
        _com_timeflow_app_ui_screen_discover_DiscoverViewModel_HiltModules_BindsModule.class,
        _com_timeflow_app_ui_screen_discover_DiscoverViewModel_HiltModules_KeyModule.class,
        _com_timeflow_app_ui_screen_goal_GoalTemplateViewModel_HiltModules_BindsModule.class,
        _com_timeflow_app_ui_screen_goal_GoalTemplateViewModel_HiltModules_KeyModule.class,
        _com_timeflow_app_ui_screen_goal_GoalViewModel_HiltModules_BindsModule.class,
        _com_timeflow_app_ui_screen_goal_GoalViewModel_HiltModules_KeyModule.class,
        _com_timeflow_app_ui_screen_health_AddHabitViewModel_HiltModules_BindsModule.class,
        _com_timeflow_app_ui_screen_health_AddHabitViewModel_HiltModules_KeyModule.class,
        _com_timeflow_app_ui_screen_health_ProfessionalMedicationViewModel_HiltModules_BindsModule.class,
        _com_timeflow_app_ui_screen_health_ProfessionalMedicationViewModel_HiltModules_KeyModule.class,
        _com_timeflow_app_ui_screen_health_UserPreferencesManagerEntryPoint.class,
        _com_timeflow_app_ui_screen_milestone_MilestoneViewModel_HiltModules_BindsModule.class,
        _com_timeflow_app_ui_screen_milestone_MilestoneViewModel_HiltModules_KeyModule.class,
        _com_timeflow_app_ui_screen_profile_EmotionStatisticsViewModel_HiltModules_BindsModule.class,
        _com_timeflow_app_ui_screen_profile_EmotionStatisticsViewModel_HiltModules_KeyModule.class,
        _com_timeflow_app_ui_screen_profile_ProfileViewModel_HiltModules_BindsModule.class,
        _com_timeflow_app_ui_screen_profile_ProfileViewModel_HiltModules_KeyModule.class,
        _com_timeflow_app_ui_screen_reflection_ReflectionDetailViewModel_HiltModules_BindsModule.class,
        _com_timeflow_app_ui_screen_reflection_ReflectionDetailViewModel_HiltModules_KeyModule.class,
        _com_timeflow_app_ui_screen_reflection_ReflectionViewModel_HiltModules_BindsModule.class,
        _com_timeflow_app_ui_screen_reflection_ReflectionViewModel_HiltModules_KeyModule.class,
        _com_timeflow_app_ui_screen_settings_NotificationSettingsViewModel_HiltModules_BindsModule.class,
        _com_timeflow_app_ui_screen_settings_NotificationSettingsViewModel_HiltModules_KeyModule.class,
        _com_timeflow_app_ui_screen_settings_SettingsViewModel_HiltModules_BindsModule.class,
        _com_timeflow_app_ui_screen_settings_SettingsViewModel_HiltModules_KeyModule.class,
        _com_timeflow_app_ui_screen_settings_SyncSettingsViewModel_HiltModules_BindsModule.class,
        _com_timeflow_app_ui_screen_settings_SyncSettingsViewModel_HiltModules_KeyModule.class,
        _com_timeflow_app_ui_screen_task_TaskDetailViewModel_HiltModules_BindsModule.class,
        _com_timeflow_app_ui_screen_task_TaskDetailViewModel_HiltModules_KeyModule.class,
        _com_timeflow_app_ui_screen_task_TaskViewModel_HiltModules_BindsModule.class,
        _com_timeflow_app_ui_screen_task_TaskViewModel_HiltModules_KeyModule.class,
        _com_timeflow_app_ui_settings_PresetThemeViewModel_HiltModules_BindsModule.class,
        _com_timeflow_app_ui_settings_PresetThemeViewModel_HiltModules_KeyModule.class,
        _com_timeflow_app_ui_settings_ThemeSettingsViewModel_HiltModules_BindsModule.class,
        _com_timeflow_app_ui_settings_ThemeSettingsViewModel_HiltModules_KeyModule.class,
        _com_timeflow_app_ui_statistics_TimeStatisticsViewModel_HiltModules_BindsModule.class,
        _com_timeflow_app_ui_statistics_TimeStatisticsViewModel_HiltModules_KeyModule.class,
        _com_timeflow_app_ui_task_KanbanViewModel_HiltModules_BindsModule.class,
        _com_timeflow_app_ui_task_KanbanViewModel_HiltModules_KeyModule.class,
        _com_timeflow_app_ui_task_TaskListViewModel_HiltModules_BindsModule.class,
        _com_timeflow_app_ui_task_TaskListViewModel_HiltModules_KeyModule.class,
        _com_timeflow_app_ui_timetracking_TaskTimeStatisticsViewModel_HiltModules_BindsModule.class,
        _com_timeflow_app_ui_timetracking_TaskTimeStatisticsViewModel_HiltModules_KeyModule.class,
        _com_timeflow_app_ui_timetracking_TimeTrackingViewModel_HiltModules_BindsModule.class,
        _com_timeflow_app_ui_timetracking_TimeTrackingViewModel_HiltModules_KeyModule.class,
        _com_timeflow_app_ui_viewmodel_AiAssistantViewModel_HiltModules_BindsModule.class,
        _com_timeflow_app_ui_viewmodel_AiAssistantViewModel_HiltModules_KeyModule.class,
        _com_timeflow_app_ui_viewmodel_AiConfigViewModel_HiltModules_BindsModule.class,
        _com_timeflow_app_ui_viewmodel_AiConfigViewModel_HiltModules_KeyModule.class,
        _com_timeflow_app_ui_viewmodel_AiSettingsViewModel_HiltModules_BindsModule.class,
        _com_timeflow_app_ui_viewmodel_AiSettingsViewModel_HiltModules_KeyModule.class,
        _com_timeflow_app_ui_viewmodel_GlobalTimerViewModel_HiltModules_BindsModule.class,
        _com_timeflow_app_ui_viewmodel_GlobalTimerViewModel_HiltModules_KeyModule.class,
        _com_timeflow_app_ui_viewmodel_GoalCreationViewModel_HiltModules_BindsModule.class,
        _com_timeflow_app_ui_viewmodel_GoalCreationViewModel_HiltModules_KeyModule.class,
        _com_timeflow_app_ui_viewmodel_GoalViewModel_HiltModules_BindsModule.class,
        _com_timeflow_app_ui_viewmodel_GoalViewModel_HiltModules_KeyModule.class,
        _com_timeflow_app_ui_viewmodel_HabitViewModel_HiltModules_BindsModule.class,
        _com_timeflow_app_ui_viewmodel_HabitViewModel_HiltModules_KeyModule.class,
        _com_timeflow_app_ui_viewmodel_MenstrualCycleViewModel_HiltModules_BindsModule.class,
        _com_timeflow_app_ui_viewmodel_MenstrualCycleViewModel_HiltModules_KeyModule.class,
        _com_timeflow_app_ui_viewmodel_TaskTimeViewModel_HiltModules_BindsModule.class,
        _com_timeflow_app_ui_viewmodel_TaskTimeViewModel_HiltModules_KeyModule.class,
        _com_timeflow_app_ui_viewmodel_TimeFlowViewModel_HiltModules_BindsModule.class,
        _com_timeflow_app_ui_viewmodel_TimeFlowViewModel_HiltModules_KeyModule.class,
        _com_timeflow_app_ui_viewmodel_WishListViewModel_HiltModules_BindsModule.class,
        _com_timeflow_app_ui_viewmodel_WishListViewModel_HiltModules_KeyModule.class,
        _com_timeflow_app_ui_viewmodel_WishStatisticsViewModel_HiltModules_BindsModule.class,
        _com_timeflow_app_ui_viewmodel_WishStatisticsViewModel_HiltModules_KeyModule.class,
        _com_timeflow_app_viewmodel_BackupRestoreViewModel_HiltModules_BindsModule.class,
        _com_timeflow_app_viewmodel_BackupRestoreViewModel_HiltModules_KeyModule.class,
        _com_timeflow_app_viewmodel_BackupSettingsViewModel_HiltModules_BindsModule.class,
        _com_timeflow_app_viewmodel_BackupSettingsViewModel_HiltModules_KeyModule.class,
        _com_timeflow_app_viewmodel_DataManagementViewModel_HiltModules_BindsModule.class,
        _com_timeflow_app_viewmodel_DataManagementViewModel_HiltModules_KeyModule.class,
        _com_timeflow_app_worker_AutoBackupWorker_HiltModule.class,
        _com_timeflow_app_worker_OverdueTaskCheckWorkerEntryPoint.class,
        _com_timeflow_app_worker_RecurringTaskWorker_HiltModule.class,
        _dagger_hilt_android_flags_FragmentGetContextFix_FragmentGetContextFixEntryPoint.class,
        _dagger_hilt_android_flags_HiltWrapper_FragmentGetContextFix_FragmentGetContextFixModule.class,
        _dagger_hilt_android_internal_lifecycle_DefaultViewModelFactories_ActivityEntryPoint.class,
        _dagger_hilt_android_internal_lifecycle_DefaultViewModelFactories_FragmentEntryPoint.class,
        _dagger_hilt_android_internal_lifecycle_HiltViewModelFactory_ViewModelFactoriesEntryPoint.class,
        _dagger_hilt_android_internal_lifecycle_HiltWrapper_DefaultViewModelFactories_ActivityModule.class,
        _dagger_hilt_android_internal_lifecycle_HiltWrapper_HiltViewModelFactory_ActivityCreatorEntryPoint.class,
        _dagger_hilt_android_internal_lifecycle_HiltWrapper_HiltViewModelFactory_ViewModelModule.class,
        _dagger_hilt_android_internal_managers_ActivityComponentManager_ActivityComponentBuilderEntryPoint.class,
        _dagger_hilt_android_internal_managers_FragmentComponentManager_FragmentComponentBuilderEntryPoint.class,
        _dagger_hilt_android_internal_managers_HiltWrapper_ActivityRetainedComponentManager_ActivityRetainedComponentBuilderEntryPoint.class,
        _dagger_hilt_android_internal_managers_HiltWrapper_ActivityRetainedComponentManager_ActivityRetainedLifecycleEntryPoint.class,
        _dagger_hilt_android_internal_managers_HiltWrapper_ActivityRetainedComponentManager_LifecycleModule.class,
        _dagger_hilt_android_internal_managers_HiltWrapper_SavedStateHandleModule.class,
        _dagger_hilt_android_internal_managers_ServiceComponentManager_ServiceComponentBuilderEntryPoint.class,
        _dagger_hilt_android_internal_managers_ViewComponentManager_ViewComponentBuilderEntryPoint.class,
        _dagger_hilt_android_internal_managers_ViewComponentManager_ViewWithFragmentComponentBuilderEntryPoint.class,
        _dagger_hilt_android_internal_modules_ApplicationContextModule.class,
        _dagger_hilt_android_internal_modules_HiltWrapper_ActivityModule.class
    }
)
public final class TimeFlowApplication_ComponentTreeDeps {
}
