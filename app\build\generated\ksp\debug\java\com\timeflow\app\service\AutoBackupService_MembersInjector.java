package com.timeflow.app.service;

import com.timeflow.app.utils.DatabaseBackupManager;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AutoBackupService_MembersInjector implements MembersInjector<AutoBackupService> {
  private final Provider<DatabaseBackupManager> backupManagerProvider;

  public AutoBackupService_MembersInjector(Provider<DatabaseBackupManager> backupManagerProvider) {
    this.backupManagerProvider = backupManagerProvider;
  }

  public static MembersInjector<AutoBackupService> create(
      Provider<DatabaseBackupManager> backupManagerProvider) {
    return new AutoBackupService_MembersInjector(backupManagerProvider);
  }

  @Override
  public void injectMembers(AutoBackupService instance) {
    injectBackupManager(instance, backupManagerProvider.get());
  }

  @InjectedFieldSignature("com.timeflow.app.service.AutoBackupService.backupManager")
  public static void injectBackupManager(AutoBackupService instance,
      DatabaseBackupManager backupManager) {
    instance.backupManager = backupManager;
  }
}
