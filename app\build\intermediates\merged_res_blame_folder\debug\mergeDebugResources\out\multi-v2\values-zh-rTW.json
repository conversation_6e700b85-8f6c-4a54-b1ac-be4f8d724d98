{"logs": [{"outputFile": "com.timeflow.app-mergeDebugResources-85:/values-zh-rTW/values-zh-rTW.xml", "map": [{"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\443a6cfc0de72abe8b7a8ef4c7b7a3e3\\transformed\\core-1.12.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "54,55,56,57,58,59,60,253", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3929,4021,4120,4214,4308,4401,4494,18391", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "4016,4115,4209,4303,4396,4489,4585,18487"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\6e1bec7b61f7c5e14c57eb6146ea2199\\transformed\\ui-release\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "67,68,77,78,82,95,96,219,222,225,227,239,240,249,256,257,260", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4976,5053,5527,5614,5819,6513,6587,16505,16653,16853,16952,17646,17719,18183,18699,18773,18920", "endColumns": "76,75,86,90,77,73,76,77,74,64,64,72,74,67,73,67,115", "endOffsets": "5048,5124,5609,5700,5892,6582,6659,16578,16723,16913,17012,17714,17789,18246,18768,18836,19031"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\5619827a7b7a666ff443af1afbf85287\\transformed\\preference-1.2.1\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "72,87,223,232,255,270,271", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5270,6076,16728,17238,18531,19559,19638", "endColumns": "65,80,69,119,167,78,75", "endOffsets": "5331,6152,16793,17353,18694,19633,19709"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\8502c7d6e294f2825540546f57fbfcc0\\transformed\\material-1.11.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,47,48,49,50,51,62,63,65,86,88,94,98,159,160,161,162,163,164,165,166,167,168,169,170,171,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,228", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3502,3566,3628,3695,3765,4628,4722,4866,6014,6157,6453,6700,12408,12486,12547,12605,12661,12721,12779,12833,12918,12974,13032,13086,13151,13521,13595,13672,13792,13855,13918,14017,14094,14168,14218,14269,14335,14398,14466,14544,14615,14676,14747,14814,14876,14963,15042,15107,15190,15275,15349,15413,15489,15537,15610,15674,15750,15828,15890,15954,16017,16083,16163,16241,16317,16396,16450,17017", "endLines": "5,47,48,49,50,51,62,63,65,86,88,94,98,159,160,161,162,163,164,165,166,167,168,169,170,171,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,228", "endColumns": "12,63,61,66,69,76,93,106,72,61,77,59,59,77,60,57,55,59,57,53,84,55,57,53,64,91,73,76,119,62,62,98,76,73,49,50,65,62,67,77,70,60,70,66,61,86,78,64,82,84,73,63,75,47,72,63,75,77,61,63,62,65,79,77,75,78,53,54,68", "endOffsets": "292,3561,3623,3690,3760,3837,4717,4824,4934,6071,6230,6508,6755,12481,12542,12600,12656,12716,12774,12828,12913,12969,13027,13081,13146,13238,13590,13667,13787,13850,13913,14012,14089,14163,14213,14264,14330,14393,14461,14539,14610,14671,14742,14809,14871,14958,15037,15102,15185,15270,15344,15408,15484,15532,15605,15669,15745,15823,15885,15949,16012,16078,16158,16236,16312,16391,16445,16500,17081"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\71256635af31e1aec955d3a558d276eb\\transformed\\foundation-release\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "265,266", "startColumns": "4,4", "startOffsets": "19225,19308", "endColumns": "82,78", "endOffsets": "19303,19382"}}, {"source": "D:\\development\\Codes\\MyApplication\\app\\src\\main\\res\\values-zh-rTW\\strings.xml", "from": {"startLines": "70,81,14,15,7,53,54,2,67,85,87,45,46,47,10,26,73,83,86,36,37,74,38,28,25,27,80,8,78,79,58,92,21,90,55,66,3,95,60,59,52,91,33,35,34,18,19,88,77,68,75,22,24,20,89,39,40,69,9,29,82,84,62,61,72,41,42,65,23,56,93,4,5,43,44,32,6,57,48,49,71,94,11,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3030,3430,474,528,247,2258,2304,55,2872,3577,3651,1904,1953,2012,367,984,3142,3503,3614,1432,1478,3180,1534,1074,940,1031,3392,288,3320,3356,2511,3846,743,3765,2359,2822,96,3961,2606,2555,2207,3803,1251,1365,1305,596,645,3687,3287,2925,3219,790,889,693,3724,1590,1636,2980,328,1122,3465,3540,2714,2669,3106,1692,1741,2782,841,2406,3883,132,169,1799,1847,1203,211,2458,2065,2117,3067,3922,407,3253", "endColumns": "36,34,53,42,40,45,54,40,52,36,35,48,58,52,39,46,37,36,36,45,55,38,55,47,43,42,37,39,35,35,43,36,46,37,46,49,35,35,62,50,50,42,53,66,59,48,47,36,32,54,33,50,50,49,40,45,55,49,38,55,37,36,46,44,35,48,57,39,47,51,38,36,41,47,56,47,35,52,51,66,38,38,41,33", "endOffsets": "3062,3460,523,566,283,2299,2354,91,2920,3609,3682,1948,2007,2060,402,1026,3175,3535,3646,1473,1529,3214,1585,1117,979,1069,3425,323,3351,3387,2550,3878,785,3798,2401,2867,127,3992,2664,2601,2253,3841,1300,1427,1360,640,688,3719,3315,2975,3248,836,935,738,3760,1631,1687,3025,362,1173,3498,3572,2756,2709,3137,1736,1794,2817,884,2453,3917,164,206,1842,1899,1246,242,2506,2112,2179,3101,3956,444,3282"}, "to": {"startLines": "33,34,35,36,37,38,39,40,41,42,43,44,45,46,52,53,61,64,66,69,70,71,73,74,75,76,79,80,81,83,84,85,89,90,91,92,93,97,99,100,101,102,172,173,174,175,176,220,221,224,226,229,230,231,233,234,235,236,237,238,241,242,243,244,245,247,248,250,251,252,254,258,259,261,262,263,264,267,268,269,272,273,274,275", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2863,2900,2935,2989,3032,3073,3119,3174,3215,3268,3305,3341,3390,3449,3842,3882,4590,4829,4939,5129,5175,5231,5336,5392,5440,5484,5705,5743,5783,5897,5933,5977,6235,6282,6320,6367,6417,6664,6760,6823,6874,6925,13243,13297,13364,13424,13473,16583,16620,16798,16918,17086,17137,17188,17358,17399,17445,17501,17551,17590,17794,17832,17869,17916,17961,18076,18125,18251,18291,18339,18492,18841,18878,19036,19084,19141,19189,19387,19440,19492,19714,19753,19792,19834", "endColumns": "36,34,53,42,40,45,54,40,52,36,35,48,58,52,39,46,37,36,36,45,55,38,55,47,43,42,37,39,35,35,43,36,46,37,46,49,35,35,62,50,50,42,53,66,59,48,47,36,32,54,33,50,50,49,40,45,55,49,38,55,37,36,46,44,35,48,57,39,47,51,38,36,41,47,56,47,35,52,51,66,38,38,41,33", "endOffsets": "2895,2930,2984,3027,3068,3114,3169,3210,3263,3300,3336,3385,3444,3497,3877,3924,4623,4861,4971,5170,5226,5265,5387,5435,5479,5522,5738,5778,5814,5928,5972,6009,6277,6315,6362,6412,6448,6695,6818,6869,6920,6963,13292,13359,13419,13468,13516,16615,16648,16848,16947,17132,17183,17233,17394,17440,17496,17546,17585,17641,17827,17864,17911,17956,17992,18120,18178,18286,18334,18386,18526,18873,18915,19079,19136,19184,19220,19435,19487,19554,19748,19787,19829,19863"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\e8414bc356727da52467a60536d06b50\\transformed\\material3-release\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6968,7072,7175,7279,7381,7473,7561,7665,7770,7875,7991,8073,8169,8253,8341,8446,8559,8660,8769,8876,8984,9101,9206,9307,9411,9516,9601,9696,9801,9910,10000,10100,10198,10309,10425,10525,10616,10690,10780,10869,10952,11034,11123,11203,11285,11382,11476,11569,11662,11746,11843,11939,12034,12142,12222,12316", "endColumns": "103,102,103,101,91,87,103,104,104,115,81,95,83,87,104,112,100,108,106,107,116,104,100,103,104,84,94,104,108,89,99,97,110,115,99,90,73,89,88,82,81,88,79,81,96,93,92,92,83,96,95,94,107,79,93,91", "endOffsets": "7067,7170,7274,7376,7468,7556,7660,7765,7870,7986,8068,8164,8248,8336,8441,8554,8655,8764,8871,8979,9096,9201,9302,9406,9511,9596,9691,9796,9905,9995,10095,10193,10304,10420,10520,10611,10685,10775,10864,10947,11029,11118,11198,11280,11377,11471,11564,11657,11741,11838,11934,12029,12137,12217,12311,12403"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\2e72a71af7cea86f1e691bad46c56138\\transformed\\appcompat-1.6.1\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,246", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "297,392,485,585,667,764,872,949,1024,1116,1210,1307,1403,1498,1592,1688,1780,1872,1964,2042,2138,2233,2328,2425,2521,2619,2769,17997", "endColumns": "94,92,99,81,96,107,76,74,91,93,96,95,94,93,95,91,91,91,77,95,94,94,96,95,97,149,93,78", "endOffsets": "387,480,580,662,759,867,944,1019,1111,1205,1302,1398,1493,1587,1683,1775,1867,1959,2037,2133,2228,2323,2420,2516,2614,2764,2858,18071"}}]}, {"outputFile": "D:\\development\\Android\\gradle\\daemon\\8.11.1\\com.timeflow.app-mergeDebugResources-85:\\values-zh-rTW\\values-zh-rTW.xml", "map": [{"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\443a6cfc0de72abe8b7a8ef4c7b7a3e3\\transformed\\core-1.12.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,246,340,434,527,620,716", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "142,241,335,429,522,615,711,812"}, "to": {"startLines": "38,39,40,41,42,43,44,183", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3203,3295,3394,3488,3582,3675,3768,15201", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "3290,3389,3483,3577,3670,3763,3859,15297"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\6e1bec7b61f7c5e14c57eb6146ea2199\\transformed\\ui-release\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,182,258,345,436,514,588,665,743,818,883,948,1021,1096,1164,1238,1306", "endColumns": "76,75,86,90,77,73,76,77,74,64,64,72,74,67,73,67,115", "endOffsets": "177,253,340,431,509,583,660,738,813,878,943,1016,1091,1159,1233,1301,1417"}, "to": {"startLines": "48,49,51,52,53,58,59,172,173,175,176,179,180,182,185,186,187", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4138,4215,4357,4444,4535,4894,4968,14364,14442,14587,14652,14906,14979,15133,15470,15544,15612", "endColumns": "76,75,86,90,77,73,76,77,74,64,64,72,74,67,73,67,115", "endOffsets": "4210,4286,4439,4530,4608,4963,5040,14437,14512,14647,14712,14974,15049,15196,15539,15607,15723"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\5619827a7b7a666ff443af1afbf85287\\transformed\\preference-1.2.1\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,171,252,322,442,610,689", "endColumns": "65,80,69,119,167,78,75", "endOffsets": "166,247,317,437,605,684,760"}, "to": {"startLines": "50,55,174,178,184,190,191", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4291,4675,14517,14786,15302,15890,15969", "endColumns": "65,80,69,119,167,78,75", "endOffsets": "4352,4751,14582,14901,15465,15964,16040"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\8502c7d6e294f2825540546f57fbfcc0\\transformed\\material-1.11.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,247,311,373,440,510,587,681,788,861,923,1001,1061,1121,1199,1260,1318,1374,1434,1492,1546,1631,1687,1745,1799,1864,1956,2030,2107,2227,2290,2353,2452,2529,2603,2653,2704,2770,2833,2901,2979,3050,3111,3182,3249,3311,3398,3477,3542,3625,3710,3784,3848,3924,3972,4045,4109,4185,4263,4325,4389,4452,4518,4598,4676,4752,4831,4885,4940", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,63,61,66,69,76,93,106,72,61,77,59,59,77,60,57,55,59,57,53,84,55,57,53,64,91,73,76,119,62,62,98,76,73,49,50,65,62,67,77,70,60,70,66,61,86,78,64,82,84,73,63,75,47,72,63,75,77,61,63,62,65,79,77,75,78,53,54,68", "endOffsets": "242,306,368,435,505,582,676,783,856,918,996,1056,1116,1194,1255,1313,1369,1429,1487,1541,1626,1682,1740,1794,1859,1951,2025,2102,2222,2285,2348,2447,2524,2598,2648,2699,2765,2828,2896,2974,3045,3106,3177,3244,3306,3393,3472,3537,3620,3705,3779,3843,3919,3967,4040,4104,4180,4258,4320,4384,4447,4513,4593,4671,4747,4826,4880,4935,5004"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,54,56,57,60,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,177", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2863,2927,2989,3056,3126,3864,3958,4065,4613,4756,4834,5045,10545,10623,10684,10742,10798,10858,10916,10970,11055,11111,11169,11223,11288,11380,11454,11531,11651,11714,11777,11876,11953,12027,12077,12128,12194,12257,12325,12403,12474,12535,12606,12673,12735,12822,12901,12966,13049,13134,13208,13272,13348,13396,13469,13533,13609,13687,13749,13813,13876,13942,14022,14100,14176,14255,14309,14717", "endLines": "5,33,34,35,36,37,45,46,47,54,56,57,60,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,177", "endColumns": "12,63,61,66,69,76,93,106,72,61,77,59,59,77,60,57,55,59,57,53,84,55,57,53,64,91,73,76,119,62,62,98,76,73,49,50,65,62,67,77,70,60,70,66,61,86,78,64,82,84,73,63,75,47,72,63,75,77,61,63,62,65,79,77,75,78,53,54,68", "endOffsets": "292,2922,2984,3051,3121,3198,3953,4060,4133,4670,4829,4889,5100,10618,10679,10737,10793,10853,10911,10965,11050,11106,11164,11218,11283,11375,11449,11526,11646,11709,11772,11871,11948,12022,12072,12123,12189,12252,12320,12398,12469,12530,12601,12668,12730,12817,12896,12961,13044,13129,13203,13267,13343,13391,13464,13528,13604,13682,13744,13808,13871,13937,14017,14095,14171,14250,14304,14359,14781"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\71256635af31e1aec955d3a558d276eb\\transformed\\foundation-release\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,138", "endColumns": "82,78", "endOffsets": "133,212"}, "to": {"startLines": "188,189", "startColumns": "4,4", "startOffsets": "15728,15811", "endColumns": "82,78", "endOffsets": "15806,15885"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\e8414bc356727da52467a60536d06b50\\transformed\\material3-release\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,159,262,366,468,560,648,752,857,962,1078,1160,1256,1340,1428,1533,1646,1747,1856,1963,2071,2188,2293,2394,2498,2603,2688,2783,2888,2997,3087,3187,3285,3396,3512,3612,3703,3777,3867,3956,4039,4121,4210,4290,4372,4469,4563,4656,4749,4833,4930,5026,5121,5229,5309,5403", "endColumns": "103,102,103,101,91,87,103,104,104,115,81,95,83,87,104,112,100,108,106,107,116,104,100,103,104,84,94,104,108,89,99,97,110,115,99,90,73,89,88,82,81,88,79,81,96,93,92,92,83,96,95,94,107,79,93,91", "endOffsets": "154,257,361,463,555,643,747,852,957,1073,1155,1251,1335,1423,1528,1641,1742,1851,1958,2066,2183,2288,2389,2493,2598,2683,2778,2883,2992,3082,3182,3280,3391,3507,3607,3698,3772,3862,3951,4034,4116,4205,4285,4367,4464,4558,4651,4744,4828,4925,5021,5116,5224,5304,5398,5490"}, "to": {"startLines": "61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5105,5209,5312,5416,5518,5610,5698,5802,5907,6012,6128,6210,6306,6390,6478,6583,6696,6797,6906,7013,7121,7238,7343,7444,7548,7653,7738,7833,7938,8047,8137,8237,8335,8446,8562,8662,8753,8827,8917,9006,9089,9171,9260,9340,9422,9519,9613,9706,9799,9883,9980,10076,10171,10279,10359,10453", "endColumns": "103,102,103,101,91,87,103,104,104,115,81,95,83,87,104,112,100,108,106,107,116,104,100,103,104,84,94,104,108,89,99,97,110,115,99,90,73,89,88,82,81,88,79,81,96,93,92,92,83,96,95,94,107,79,93,91", "endOffsets": "5204,5307,5411,5513,5605,5693,5797,5902,6007,6123,6205,6301,6385,6473,6578,6691,6792,6901,7008,7116,7233,7338,7439,7543,7648,7733,7828,7933,8042,8132,8232,8330,8441,8557,8657,8748,8822,8912,9001,9084,9166,9255,9335,9417,9514,9608,9701,9794,9878,9975,10071,10166,10274,10354,10448,10540"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\2e72a71af7cea86f1e691bad46c56138\\transformed\\appcompat-1.6.1\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,757,832,924,1018,1115,1211,1306,1400,1496,1588,1680,1772,1850,1946,2041,2136,2233,2329,2427,2577,2671", "endColumns": "94,92,99,81,96,107,76,74,91,93,96,95,94,93,95,91,91,91,77,95,94,94,96,95,97,149,93,78", "endOffsets": "195,288,388,470,567,675,752,827,919,1013,1110,1206,1301,1395,1491,1583,1675,1767,1845,1941,2036,2131,2228,2324,2422,2572,2666,2745"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,181", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "297,392,485,585,667,764,872,949,1024,1116,1210,1307,1403,1498,1592,1688,1780,1872,1964,2042,2138,2233,2328,2425,2521,2619,2769,15054", "endColumns": "94,92,99,81,96,107,76,74,91,93,96,95,94,93,95,91,91,91,77,95,94,94,96,95,97,149,93,78", "endOffsets": "387,480,580,662,759,867,944,1019,1111,1205,1302,1398,1493,1587,1683,1775,1867,1959,2037,2133,2228,2323,2420,2516,2614,2764,2858,15128"}}]}]}