{"logs": [{"outputFile": "com.timeflow.app-mergeDebugResources-85:/values-zh-rTW/values-zh-rTW.xml", "map": [{"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\443a6cfc0de72abe8b7a8ef4c7b7a3e3\\transformed\\core-1.12.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "66,67,68,69,70,71,72,361", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "5014,5106,5205,5299,5393,5486,5579,25346", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "5101,5200,5294,5388,5481,5574,5670,25442"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\6e1bec7b61f7c5e14c57eb6146ea2199\\transformed\\ui-release\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "79,80,91,92,100,138,139,264,267,278,280,292,293,356,365,366,370", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6061,6138,6720,6807,7265,9510,9584,19596,19744,20544,20643,21337,21410,25079,25692,25766,25962", "endColumns": "76,75,86,90,77,73,76,77,74,64,64,72,74,67,73,67,115", "endOffsets": "6133,6209,6802,6893,7338,9579,9656,19669,19814,20604,20703,21405,21480,25142,25761,25829,26073"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\5619827a7b7a666ff443af1afbf85287\\transformed\\preference-1.2.1\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "86,112,268,285,363,387,388", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6463,8096,19819,20929,25486,26950,27029", "endColumns": "65,80,69,119,167,78,75", "endOffsets": "6524,8172,19884,21044,25649,27024,27100"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\8502c7d6e294f2825540546f57fbfcc0\\transformed\\material-1.11.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,59,60,61,62,63,74,75,77,111,113,137,141,202,203,204,205,206,207,208,209,210,211,212,213,214,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,281", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,4587,4651,4713,4780,4850,5713,5807,5951,8034,8177,9450,9697,15405,15483,15544,15602,15658,15718,15776,15830,15915,15971,16029,16083,16148,16612,16686,16763,16883,16946,17009,17108,17185,17259,17309,17360,17426,17489,17557,17635,17706,17767,17838,17905,17967,18054,18133,18198,18281,18366,18440,18504,18580,18628,18701,18765,18841,18919,18981,19045,19108,19174,19254,19332,19408,19487,19541,20708", "endLines": "5,59,60,61,62,63,74,75,77,111,113,137,141,202,203,204,205,206,207,208,209,210,211,212,213,214,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,281", "endColumns": "12,63,61,66,69,76,93,106,72,61,77,59,59,77,60,57,55,59,57,53,84,55,57,53,64,91,73,76,119,62,62,98,76,73,49,50,65,62,67,77,70,60,70,66,61,86,78,64,82,84,73,63,75,47,72,63,75,77,61,63,62,65,79,77,75,78,53,54,68", "endOffsets": "292,4646,4708,4775,4845,4922,5802,5909,6019,8091,8250,9505,9752,15478,15539,15597,15653,15713,15771,15825,15910,15966,16024,16078,16143,16235,16681,16758,16878,16941,17004,17103,17180,17254,17304,17355,17421,17484,17552,17630,17701,17762,17833,17900,17962,18049,18128,18193,18276,18361,18435,18499,18575,18623,18696,18760,18836,18914,18976,19040,19103,19169,19249,19327,19403,19482,19536,19591,20772"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\71256635af31e1aec955d3a558d276eb\\transformed\\foundation-release\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "381,382", "startColumns": "4,4", "startOffsets": "26577,26660", "endColumns": "82,78", "endOffsets": "26655,26734"}}, {"source": "D:\\development\\Codes\\MyApplication\\app\\src\\main\\res\\values-zh-rTW\\strings.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,247,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,248", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,11729,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,11781", "endColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,51,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,58", "endOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,11776,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,11835"}, "to": {"startLines": "33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,64,65,73,76,78,81,82,83,84,85,87,88,89,90,93,94,95,96,97,98,99,101,102,103,104,105,106,107,108,109,110,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,140,142,143,144,145,215,216,217,218,219,220,221,265,266,269,270,271,272,273,274,275,276,277,279,282,283,284,286,287,288,289,290,291,294,295,296,297,298,299,300,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,357,358,359,360,362,364,367,368,369,371,372,373,374,375,376,377,378,379,380,383,384,385,386,389,390,391,392,393,394,395", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2863,2900,2935,2989,3042,3103,3146,3221,3303,3379,3449,3635,3700,3798,3866,4053,4117,4158,4204,4259,4300,4353,4390,4426,4475,4534,4927,4967,5675,5914,6024,6214,6260,6316,6355,6410,6529,6585,6633,6677,6898,6936,6992,7058,7124,7189,7229,7343,7379,7462,7548,7635,7721,7805,7892,7936,7997,8255,8311,8364,8418,8488,8538,8593,8644,8694,8746,8806,8853,8891,8943,8994,9041,9103,9162,9218,9268,9326,9364,9414,9661,9757,9820,9871,9922,16240,16294,16361,16421,16470,16518,16556,19674,19711,19889,19960,20043,20115,20195,20266,20346,20412,20489,20609,20777,20828,20879,21049,21090,21136,21192,21242,21281,21485,21527,21565,21602,21649,21694,21734,21849,21909,21968,22025,22081,22141,22195,22253,22320,22385,22456,22513,22568,22625,22688,22744,22804,22873,22946,23015,23072,23133,23188,23243,23303,23361,23418,23478,23535,23600,23664,23732,23787,23843,23900,23957,24016,24071,24135,24198,24260,24326,24384,24438,24492,24552,24606,24671,24736,24803,24866,24920,24972,25021,25147,25206,25246,25294,25447,25654,25834,25883,25920,26078,26126,26183,26231,26271,26307,26361,26417,26462,26522,26739,26792,26831,26883,27105,27144,27183,27224,27286,27328,27362", "endColumns": "36,34,53,52,60,42,74,81,75,69,185,64,97,67,186,63,40,45,54,40,52,36,35,48,58,52,39,46,37,36,36,45,55,38,54,52,55,47,43,42,37,55,65,65,64,39,35,35,82,85,86,85,83,86,43,60,36,55,52,53,69,49,54,50,49,51,59,46,37,51,50,46,61,58,55,49,57,37,49,35,35,62,50,50,42,53,66,59,48,47,37,55,36,32,70,82,71,79,70,79,65,76,54,33,50,50,49,40,45,55,49,38,55,41,37,36,46,44,39,35,59,58,56,55,59,53,57,66,64,70,56,54,56,62,55,59,68,72,68,56,60,54,54,59,57,56,59,56,64,63,67,54,55,56,56,58,54,63,62,61,65,57,53,53,59,53,64,64,66,62,53,51,48,57,58,39,47,51,38,37,48,36,41,47,56,47,39,35,53,55,44,59,54,52,38,51,66,38,38,40,61,41,33,58", "endOffsets": "2895,2930,2984,3037,3098,3141,3216,3298,3374,3444,3630,3695,3793,3861,4048,4112,4153,4199,4254,4295,4348,4385,4421,4470,4529,4582,4962,5009,5708,5946,6056,6255,6311,6350,6405,6458,6580,6628,6672,6715,6931,6987,7053,7119,7184,7224,7260,7374,7457,7543,7630,7716,7800,7887,7931,7992,8029,8306,8359,8413,8483,8533,8588,8639,8689,8741,8801,8848,8886,8938,8989,9036,9098,9157,9213,9263,9321,9359,9409,9445,9692,9815,9866,9917,9960,16289,16356,16416,16465,16513,16551,16607,19706,19739,19955,20038,20110,20190,20261,20341,20407,20484,20539,20638,20823,20874,20924,21085,21131,21187,21237,21276,21332,21522,21560,21597,21644,21689,21729,21765,21904,21963,22020,22076,22136,22190,22248,22315,22380,22451,22508,22563,22620,22683,22739,22799,22868,22941,23010,23067,23128,23183,23238,23298,23356,23413,23473,23530,23595,23659,23727,23782,23838,23895,23952,24011,24066,24130,24193,24255,24321,24379,24433,24487,24547,24601,24666,24731,24798,24861,24915,24967,25016,25074,25201,25241,25289,25341,25481,25687,25878,25915,25957,26121,26178,26226,26266,26302,26356,26412,26457,26517,26572,26787,26826,26878,26945,27139,27178,27219,27281,27323,27357,27416"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\e8414bc356727da52467a60536d06b50\\transformed\\material3-release\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "9965,10069,10172,10276,10378,10470,10558,10662,10767,10872,10988,11070,11166,11250,11338,11443,11556,11657,11766,11873,11981,12098,12203,12304,12408,12513,12598,12693,12798,12907,12997,13097,13195,13306,13422,13522,13613,13687,13777,13866,13949,14031,14120,14200,14282,14379,14473,14566,14659,14743,14840,14936,15031,15139,15219,15313", "endColumns": "103,102,103,101,91,87,103,104,104,115,81,95,83,87,104,112,100,108,106,107,116,104,100,103,104,84,94,104,108,89,99,97,110,115,99,90,73,89,88,82,81,88,79,81,96,93,92,92,83,96,95,94,107,79,93,91", "endOffsets": "10064,10167,10271,10373,10465,10553,10657,10762,10867,10983,11065,11161,11245,11333,11438,11551,11652,11761,11868,11976,12093,12198,12299,12403,12508,12593,12688,12793,12902,12992,13092,13190,13301,13417,13517,13608,13682,13772,13861,13944,14026,14115,14195,14277,14374,14468,14561,14654,14738,14835,14931,15026,15134,15214,15308,15400"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\2e72a71af7cea86f1e691bad46c56138\\transformed\\appcompat-1.6.1\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,301", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "297,392,485,585,667,764,872,949,1024,1116,1210,1307,1403,1498,1592,1688,1780,1872,1964,2042,2138,2233,2328,2425,2521,2619,2769,21770", "endColumns": "94,92,99,81,96,107,76,74,91,93,96,95,94,93,95,91,91,91,77,95,94,94,96,95,97,149,93,78", "endOffsets": "387,480,580,662,759,867,944,1019,1111,1205,1302,1398,1493,1587,1683,1775,1867,1959,2037,2133,2228,2323,2420,2516,2614,2764,2858,21844"}}]}, {"outputFile": "D:\\development\\Android\\gradle\\daemon\\8.11.1\\com.timeflow.app-mergeDebugResources-85:\\values-zh-rTW\\values-zh-rTW.xml", "map": [{"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\443a6cfc0de72abe8b7a8ef4c7b7a3e3\\transformed\\core-1.12.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,246,340,434,527,620,716", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "142,241,335,429,522,615,711,812"}, "to": {"startLines": "56,57,58,59,60,61,62,297", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4043,4135,4234,4328,4422,4515,4608,21149", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "4130,4229,4323,4417,4510,4603,4699,21245"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\6e1bec7b61f7c5e14c57eb6146ea2199\\transformed\\ui-release\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,182,258,345,436,514,588,665,743,818,883,948,1021,1096,1164,1238,1306", "endColumns": "76,75,86,90,77,73,76,77,74,64,64,72,74,67,73,67,115", "endOffsets": "177,253,340,431,509,583,660,738,813,878,943,1016,1091,1159,1233,1301,1417"}, "to": {"startLines": "69,70,81,82,90,126,127,252,255,266,268,280,281,292,301,302,305", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5090,5167,5749,5836,6294,8436,8510,18522,18670,19470,19569,20263,20336,20882,21495,21569,21716", "endColumns": "76,75,86,90,77,73,76,77,74,64,64,72,74,67,73,67,115", "endOffsets": "5162,5238,5831,5922,6367,8505,8582,18595,18740,19530,19629,20331,20406,20945,21564,21632,21827"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\5619827a7b7a666ff443af1afbf85287\\transformed\\preference-1.2.1\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,171,252,322,442,610,689", "endColumns": "65,80,69,119,167,78,75", "endOffsets": "166,247,317,437,605,684,760"}, "to": {"startLines": "76,102,256,273,299,321,322", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5492,7125,18745,19855,21289,22659,22738", "endColumns": "65,80,69,119,167,78,75", "endOffsets": "5553,7201,18810,19970,21452,22733,22809"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\8502c7d6e294f2825540546f57fbfcc0\\transformed\\material-1.11.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,247,311,373,440,510,587,681,788,861,923,1001,1061,1121,1199,1260,1318,1374,1434,1492,1546,1631,1687,1745,1799,1864,1956,2030,2107,2227,2290,2353,2452,2529,2603,2653,2704,2770,2833,2901,2979,3050,3111,3182,3249,3311,3398,3477,3542,3625,3710,3784,3848,3924,3972,4045,4109,4185,4263,4325,4389,4452,4518,4598,4676,4752,4831,4885,4940", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,63,61,66,69,76,93,106,72,61,77,59,59,77,60,57,55,59,57,53,84,55,57,53,64,91,73,76,119,62,62,98,76,73,49,50,65,62,67,77,70,60,70,66,61,86,78,64,82,84,73,63,75,47,72,63,75,77,61,63,62,65,79,77,75,78,53,54,68", "endOffsets": "242,306,368,435,505,582,676,783,856,918,996,1056,1116,1194,1255,1313,1369,1429,1487,1541,1626,1682,1740,1794,1859,1951,2025,2102,2222,2285,2348,2447,2524,2598,2648,2699,2765,2828,2896,2974,3045,3106,3177,3244,3306,3393,3472,3537,3620,3705,3779,3843,3919,3967,4040,4104,4180,4258,4320,4384,4447,4513,4593,4671,4747,4826,4880,4935,5004"}, "to": {"startLines": "2,49,50,51,52,53,64,65,67,101,103,125,129,190,191,192,193,194,195,196,197,198,199,200,201,202,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,269", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3616,3680,3742,3809,3879,4742,4836,4980,7063,7206,8376,8623,14331,14409,14470,14528,14584,14644,14702,14756,14841,14897,14955,15009,15074,15538,15612,15689,15809,15872,15935,16034,16111,16185,16235,16286,16352,16415,16483,16561,16632,16693,16764,16831,16893,16980,17059,17124,17207,17292,17366,17430,17506,17554,17627,17691,17767,17845,17907,17971,18034,18100,18180,18258,18334,18413,18467,19634", "endLines": "5,49,50,51,52,53,64,65,67,101,103,125,129,190,191,192,193,194,195,196,197,198,199,200,201,202,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,269", "endColumns": "12,63,61,66,69,76,93,106,72,61,77,59,59,77,60,57,55,59,57,53,84,55,57,53,64,91,73,76,119,62,62,98,76,73,49,50,65,62,67,77,70,60,70,66,61,86,78,64,82,84,73,63,75,47,72,63,75,77,61,63,62,65,79,77,75,78,53,54,68", "endOffsets": "292,3675,3737,3804,3874,3951,4831,4938,5048,7120,7279,8431,8678,14404,14465,14523,14579,14639,14697,14751,14836,14892,14950,15004,15069,15161,15607,15684,15804,15867,15930,16029,16106,16180,16230,16281,16347,16410,16478,16556,16627,16688,16759,16826,16888,16975,17054,17119,17202,17287,17361,17425,17501,17549,17622,17686,17762,17840,17902,17966,18029,18095,18175,18253,18329,18408,18462,18517,19698"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\71256635af31e1aec955d3a558d276eb\\transformed\\foundation-release\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,138", "endColumns": "82,78", "endOffsets": "133,212"}, "to": {"startLines": "315,316", "startColumns": "4,4", "startOffsets": "22286,22369", "endColumns": "82,78", "endOffsets": "22364,22443"}}, {"source": "D:\\development\\Codes\\MyApplication\\app\\src\\main\\res\\values-zh-rTW\\strings.xml", "from": {"startLines": "70,81,14,127,128,15,7,53,54,2,67,85,87,45,46,47,10,26,73,83,86,36,37,74,150,151,38,28,25,27,80,116,115,117,118,8,78,79,155,157,156,154,158,159,58,129,92,100,98,99,132,101,103,105,107,104,106,21,90,55,110,112,109,111,113,139,66,3,95,60,59,52,91,33,35,34,18,19,135,143,88,77,162,163,166,167,168,169,164,165,68,75,22,24,20,89,39,40,69,9,29,147,82,84,62,61,140,72,41,42,126,65,23,56,93,141,4,5,43,44,32,138,6,120,121,123,122,57,136,48,49,71,94,137,144,11,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3030,3430,474,5346,5399,528,247,2258,2304,55,2872,3577,3651,1904,1953,2012,367,984,3142,3503,3614,1432,1478,3180,6106,6161,1534,1074,940,1031,3392,4853,4787,4909,4975,288,3320,3356,6322,6492,6405,6236,6578,6662,2511,5460,3846,4125,4018,4071,5542,4181,4232,4339,4450,4287,4390,743,3765,2359,4557,4669,4501,4619,4728,5791,2822,96,3961,2606,2555,2207,3803,1251,1365,1305,596,645,5633,5908,3687,3287,6768,6839,7065,7137,7217,7288,6922,6988,2925,3219,790,889,693,3724,1590,1636,2980,328,1122,6045,3465,3540,2714,2669,5829,3106,1692,1741,5287,2782,841,2406,3883,5869,132,169,1799,1847,1203,5751,211,5041,5095,5206,5151,2458,5671,2065,2117,3067,3922,5710,5964,407,3253", "endColumns": "36,34,53,52,60,42,40,45,54,40,52,36,35,48,58,52,39,46,37,36,36,45,55,38,54,52,55,47,43,42,37,55,65,65,64,39,35,35,82,85,86,85,83,86,43,60,36,55,52,53,69,49,54,50,49,51,59,46,37,46,61,58,55,49,57,37,49,35,35,62,50,50,42,53,66,59,48,47,37,55,36,32,70,82,71,79,70,79,65,76,54,33,50,50,49,40,45,55,49,38,55,41,37,36,46,44,39,35,48,57,58,39,47,51,38,37,36,41,47,56,47,39,35,53,55,59,54,52,38,51,66,38,38,40,61,41,33", "endOffsets": "3062,3460,523,5394,5455,566,283,2299,2354,91,2920,3609,3682,1948,2007,2060,402,1026,3175,3535,3646,1473,1529,3214,6156,6209,1585,1117,979,1069,3425,4904,4848,4970,5035,323,3351,3387,6400,6573,6487,6317,6657,6744,2550,5516,3878,4176,4066,4120,5607,4226,4282,4385,4495,4334,4445,785,3798,2401,4614,4723,4552,4664,4781,5824,2867,127,3992,2664,2601,2253,3841,1300,1427,1360,640,688,5666,5959,3719,3315,6834,6917,7132,7212,7283,7363,6983,7060,2975,3248,836,935,738,3760,1631,1687,3025,362,1173,6082,3498,3572,2756,2709,5864,3137,1736,1794,5341,2817,884,2453,3917,5902,164,206,1842,1899,1246,5786,242,5090,5146,5261,5201,2506,5705,2112,2179,3101,3956,5746,6021,444,3282"}, "to": {"startLines": "33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,54,55,63,66,68,71,72,73,74,75,77,78,79,80,83,84,85,86,87,88,89,91,92,93,94,95,96,97,98,99,100,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,128,130,131,132,133,203,204,205,206,207,208,209,253,254,257,258,259,260,261,262,263,264,265,267,270,271,272,274,275,276,277,278,279,282,283,284,285,286,287,288,290,291,293,294,295,296,298,300,303,304,306,307,308,309,310,311,312,313,314,317,318,319,320,323,324,325,326,327,328", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2863,2900,2935,2989,3042,3103,3146,3187,3233,3288,3329,3382,3419,3455,3504,3563,3956,3996,4704,4943,5053,5243,5289,5345,5384,5439,5558,5614,5662,5706,5927,5965,6021,6087,6153,6218,6258,6372,6408,6491,6577,6664,6750,6834,6921,6965,7026,7284,7340,7393,7447,7517,7567,7622,7673,7723,7775,7835,7882,7920,7967,8029,8088,8144,8194,8252,8290,8340,8587,8683,8746,8797,8848,15166,15220,15287,15347,15396,15444,15482,18600,18637,18815,18886,18969,19041,19121,19192,19272,19338,19415,19535,19703,19754,19805,19975,20016,20062,20118,20168,20207,20411,20453,20491,20528,20575,20620,20660,20775,20824,20950,21009,21049,21097,21250,21457,21637,21674,21832,21880,21937,21985,22025,22061,22115,22171,22231,22448,22501,22540,22592,22814,22853,22892,22933,22995,23037", "endColumns": "36,34,53,52,60,42,40,45,54,40,52,36,35,48,58,52,39,46,37,36,36,45,55,38,54,52,55,47,43,42,37,55,65,65,64,39,35,35,82,85,86,85,83,86,43,60,36,55,52,53,69,49,54,50,49,51,59,46,37,46,61,58,55,49,57,37,49,35,35,62,50,50,42,53,66,59,48,47,37,55,36,32,70,82,71,79,70,79,65,76,54,33,50,50,49,40,45,55,49,38,55,41,37,36,46,44,39,35,48,57,58,39,47,51,38,37,36,41,47,56,47,39,35,53,55,59,54,52,38,51,66,38,38,40,61,41,33", "endOffsets": "2895,2930,2984,3037,3098,3141,3182,3228,3283,3324,3377,3414,3450,3499,3558,3611,3991,4038,4737,4975,5085,5284,5340,5379,5434,5487,5609,5657,5701,5744,5960,6016,6082,6148,6213,6253,6289,6403,6486,6572,6659,6745,6829,6916,6960,7021,7058,7335,7388,7442,7512,7562,7617,7668,7718,7770,7830,7877,7915,7962,8024,8083,8139,8189,8247,8285,8335,8371,8618,8741,8792,8843,8886,15215,15282,15342,15391,15439,15477,15533,18632,18665,18881,18964,19036,19116,19187,19267,19333,19410,19465,19564,19749,19800,19850,20011,20057,20113,20163,20202,20258,20448,20486,20523,20570,20615,20655,20691,20819,20877,21004,21044,21092,21144,21284,21490,21669,21711,21875,21932,21980,22020,22056,22110,22166,22226,22281,22496,22535,22587,22654,22848,22887,22928,22990,23032,23066"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\e8414bc356727da52467a60536d06b50\\transformed\\material3-release\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,159,262,366,468,560,648,752,857,962,1078,1160,1256,1340,1428,1533,1646,1747,1856,1963,2071,2188,2293,2394,2498,2603,2688,2783,2888,2997,3087,3187,3285,3396,3512,3612,3703,3777,3867,3956,4039,4121,4210,4290,4372,4469,4563,4656,4749,4833,4930,5026,5121,5229,5309,5403", "endColumns": "103,102,103,101,91,87,103,104,104,115,81,95,83,87,104,112,100,108,106,107,116,104,100,103,104,84,94,104,108,89,99,97,110,115,99,90,73,89,88,82,81,88,79,81,96,93,92,92,83,96,95,94,107,79,93,91", "endOffsets": "154,257,361,463,555,643,747,852,957,1073,1155,1251,1335,1423,1528,1641,1742,1851,1958,2066,2183,2288,2389,2493,2598,2683,2778,2883,2992,3082,3182,3280,3391,3507,3607,3698,3772,3862,3951,4034,4116,4205,4285,4367,4464,4558,4651,4744,4828,4925,5021,5116,5224,5304,5398,5490"}, "to": {"startLines": "134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8891,8995,9098,9202,9304,9396,9484,9588,9693,9798,9914,9996,10092,10176,10264,10369,10482,10583,10692,10799,10907,11024,11129,11230,11334,11439,11524,11619,11724,11833,11923,12023,12121,12232,12348,12448,12539,12613,12703,12792,12875,12957,13046,13126,13208,13305,13399,13492,13585,13669,13766,13862,13957,14065,14145,14239", "endColumns": "103,102,103,101,91,87,103,104,104,115,81,95,83,87,104,112,100,108,106,107,116,104,100,103,104,84,94,104,108,89,99,97,110,115,99,90,73,89,88,82,81,88,79,81,96,93,92,92,83,96,95,94,107,79,93,91", "endOffsets": "8990,9093,9197,9299,9391,9479,9583,9688,9793,9909,9991,10087,10171,10259,10364,10477,10578,10687,10794,10902,11019,11124,11225,11329,11434,11519,11614,11719,11828,11918,12018,12116,12227,12343,12443,12534,12608,12698,12787,12870,12952,13041,13121,13203,13300,13394,13487,13580,13664,13761,13857,13952,14060,14140,14234,14326"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\2e72a71af7cea86f1e691bad46c56138\\transformed\\appcompat-1.6.1\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,757,832,924,1018,1115,1211,1306,1400,1496,1588,1680,1772,1850,1946,2041,2136,2233,2329,2427,2577,2671", "endColumns": "94,92,99,81,96,107,76,74,91,93,96,95,94,93,95,91,91,91,77,95,94,94,96,95,97,149,93,78", "endOffsets": "195,288,388,470,567,675,752,827,919,1013,1110,1206,1301,1395,1491,1583,1675,1767,1845,1941,2036,2131,2228,2324,2422,2572,2666,2745"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,289", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "297,392,485,585,667,764,872,949,1024,1116,1210,1307,1403,1498,1592,1688,1780,1872,1964,2042,2138,2233,2328,2425,2521,2619,2769,20696", "endColumns": "94,92,99,81,96,107,76,74,91,93,96,95,94,93,95,91,91,91,77,95,94,94,96,95,97,149,93,78", "endOffsets": "387,480,580,662,759,867,944,1019,1111,1205,1302,1398,1493,1587,1683,1775,1867,1959,2037,2133,2228,2323,2420,2516,2614,2764,2858,20770"}}]}]}