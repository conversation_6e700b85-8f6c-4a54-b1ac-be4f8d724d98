{"logs": [{"outputFile": "com.timeflow.app-mergeDebugResources-85:/values-zh-rTW/values-zh-rTW.xml", "map": [{"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\443a6cfc0de72abe8b7a8ef4c7b7a3e3\\transformed\\core-1.12.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "91,92,93,94,95,96,97,489", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "6261,6353,6452,6546,6640,6733,6826,32356", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "6348,6447,6541,6635,6728,6821,6917,32452"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\6e1bec7b61f7c5e14c57eb6146ea2199\\transformed\\ui-release\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "107,108,132,133,149,224,226,364,374,386,388,405,406,481,493,497,503", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7468,7545,8836,8923,9835,14312,14438,25147,25665,26500,26599,27536,27609,31918,32702,32923,33266", "endColumns": "76,75,86,90,77,73,76,77,74,64,64,72,74,67,73,67,115", "endOffsets": "7540,7616,8918,9009,9908,14381,14510,25220,25735,26560,26659,27604,27679,31981,32771,32986,33377"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\5619827a7b7a666ff443af1afbf85287\\transformed\\preference-1.2.1\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "118,166,395,491,524,525", "startColumns": "4,4,4,4,4,4", "startOffsets": "8056,10913,26988,32496,34442,34521", "endColumns": "65,80,119,167,78,75", "endOffsets": "8117,10989,27103,32659,34516,34592"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\8502c7d6e294f2825540546f57fbfcc0\\transformed\\material-1.11.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,82,83,84,85,86,101,102,104,165,167,222,229,297,298,299,300,301,302,303,304,305,306,307,308,309,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,390", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,5753,5817,5879,5946,6016,7060,7154,7298,10851,10994,14205,14630,20744,20822,20883,20941,20997,21057,21115,21169,21254,21310,21368,21422,21487,22074,22148,22225,22345,22408,22471,22570,22647,22721,22771,22822,22888,22951,23019,23097,23168,23229,23300,23367,23429,23516,23595,23660,23743,23828,23902,23966,24042,24090,24163,24227,24303,24381,24443,24507,24570,24636,24716,24794,24870,24949,25003,26715", "endLines": "5,82,83,84,85,86,101,102,104,165,167,222,229,297,298,299,300,301,302,303,304,305,306,307,308,309,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,390", "endColumns": "12,63,61,66,69,76,93,106,72,61,77,59,59,77,60,57,55,59,57,53,84,55,57,53,64,91,73,76,119,62,62,98,76,73,49,50,65,62,67,77,70,60,70,66,61,86,78,64,82,84,73,63,75,47,72,63,75,77,61,63,62,65,79,77,75,78,53,54,68", "endOffsets": "292,5812,5874,5941,6011,6088,7149,7256,7366,10908,11067,14260,14685,20817,20878,20936,20992,21052,21110,21164,21249,21305,21363,21417,21482,21574,22143,22220,22340,22403,22466,22565,22642,22716,22766,22817,22883,22946,23014,23092,23163,23224,23295,23362,23424,23511,23590,23655,23738,23823,23897,23961,24037,24085,24158,24222,24298,24376,24438,24502,24565,24631,24711,24789,24865,24944,24998,25053,26779"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\71256635af31e1aec955d3a558d276eb\\transformed\\foundation-release\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "516,517", "startColumns": "4,4", "startOffsets": "33988,34071", "endColumns": "82,78", "endOffsets": "34066,34145"}}, {"source": "D:\\development\\Codes\\MyApplication\\app\\src\\main\\res\\values-zh-rTW\\strings.xml", "from": {"startLines": "-1,-1,426,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,432,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,436,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,427,-1,-1,-1,-1,-1,-1,-1,-1,-1,425,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,429,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,424,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,433,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,428,-1,-1,-1,-1", "startColumns": "-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1", "startOffsets": "-1,-1,19662,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,19910,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,20025,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,19719,-1,-1,-1,-1,-1,-1,-1,-1,-1,19598,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,19831,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,19551,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,19954,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,19784,-1,-1,-1,-1", "endColumns": "-1,-1,56,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,43,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,64,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,64,-1,-1,-1,-1,-1,-1,-1,-1,-1,63,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,59,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,46,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,51,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,46,-1,-1,-1,-1", "endOffsets": "-1,-1,19714,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,19949,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,20085,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,19779,-1,-1,-1,-1,-1,-1,-1,-1,-1,19657,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,19886,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,19593,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,20001,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,19826,-1,-1,-1,-1"}, "to": {"startLines": "33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,87,88,89,90,98,99,100,103,105,106,109,110,111,112,113,114,115,116,117,119,120,121,122,123,124,125,126,127,128,129,130,131,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,223,225,227,228,230,231,232,233,234,235,236,237,238,239,296,310,311,312,313,314,315,316,317,318,319,362,363,365,366,367,368,369,370,371,372,373,375,376,377,378,379,380,381,382,383,384,385,387,389,391,392,393,394,396,397,398,399,400,401,402,403,404,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,482,483,484,485,486,487,488,490,492,494,495,496,498,499,500,501,502,504,505,506,507,508,509,510,511,512,513,514,515,518,519,520,521,522,523,526,527,528,529,530,531,532,533,534,535,536,537,538", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2863,2900,2947,3004,3057,3115,3171,3222,3280,3315,3358,3415,3457,3511,3564,3625,3701,3744,3789,3845,3920,4002,4078,4148,4334,4399,4497,4565,4752,4816,4857,4897,4938,4980,5026,5081,5122,5175,5212,5250,5308,5344,5393,5452,5505,5543,5589,5639,5690,6093,6137,6174,6214,6922,6960,7009,7261,7371,7431,7621,7667,7723,7773,7821,7860,7902,7948,8003,8122,8169,8224,8289,8345,8393,8437,8474,8532,8636,8685,8750,8793,9014,9071,9109,9156,9228,9293,9349,9403,9469,9535,9600,9645,9685,9735,9771,9913,9949,9999,10051,10100,10148,10196,10279,10365,10452,10538,10622,10709,10753,10814,11072,11124,11193,11245,11313,11368,11441,11498,11570,11626,11698,11753,11825,11884,11965,12026,12103,12160,12233,12289,12342,12396,12466,12516,12571,12622,12672,12724,12784,12831,12869,12921,12972,13021,13082,13138,13207,13254,13316,13375,13431,13481,13539,13577,13627,13711,13774,13836,13885,13946,14002,14069,14117,14169,14265,14386,14515,14551,14690,14753,14804,14855,14915,14974,15023,15066,15136,15183,20684,21579,21633,21700,21760,21809,21857,21895,21931,21987,22026,25058,25100,25225,25262,25295,25351,25405,25455,25498,25557,25603,25740,25780,25851,25934,26006,26086,26157,26237,26303,26380,26435,26565,26664,26784,26835,26886,26938,27108,27149,27195,27251,27301,27361,27400,27456,27495,27684,27746,27788,27834,27881,27940,27978,28029,28066,28113,28158,28197,28261,28306,28346,28461,28521,28580,28637,28693,28753,28807,28865,28932,28997,29068,29125,29180,29237,29300,29356,29416,29485,29558,29627,29684,29745,29800,29855,29915,29973,30030,30090,30147,30212,30276,30344,30399,30455,30512,30569,30628,30683,30747,30810,30872,30938,30996,31050,31104,31164,31218,31283,31348,31415,31478,31532,31584,31633,31691,31755,31815,31860,31986,32045,32104,32159,32216,32256,32304,32457,32664,32776,32818,32865,32991,33040,33104,33187,33224,33382,33430,33487,33535,33594,33634,33670,33724,33780,33828,33873,33933,34150,34203,34242,34278,34330,34397,34597,34636,34685,34724,34765,34827,34865,34909,34951,34998,35033,35071,35105", "endColumns": "36,46,56,52,57,55,50,57,34,42,56,41,53,52,60,75,42,44,55,74,81,75,69,185,64,97,67,186,63,40,39,40,41,45,54,40,52,36,37,57,35,48,58,52,37,45,49,50,62,43,36,39,46,37,48,50,36,59,36,45,55,49,47,38,41,45,54,52,46,54,64,55,47,43,36,57,103,48,64,42,42,56,37,46,71,64,55,53,65,65,64,44,39,49,35,63,35,49,51,48,47,47,82,85,86,85,83,86,43,60,36,51,68,51,67,54,72,56,71,55,71,54,71,58,80,60,76,56,72,55,52,53,69,49,54,50,49,51,59,46,37,51,50,48,60,55,68,46,61,58,55,49,57,37,49,83,62,61,48,60,55,66,47,51,35,46,51,35,78,62,50,50,59,58,48,42,69,46,60,59,53,66,59,48,47,37,35,55,38,47,41,46,36,32,55,53,49,42,58,45,61,39,70,82,71,79,70,79,65,76,54,64,33,50,50,50,51,49,40,45,55,49,59,38,55,38,40,61,41,45,46,58,37,50,36,46,44,38,63,44,39,35,59,58,56,55,59,53,57,66,64,70,56,54,56,62,55,59,68,72,68,56,60,54,54,59,57,56,59,56,64,63,67,54,55,56,56,58,54,63,62,61,65,57,53,53,59,53,64,64,66,62,53,51,48,57,63,59,44,57,58,58,54,56,39,47,51,38,37,41,46,57,48,63,82,36,41,47,56,47,58,39,35,53,55,47,44,59,54,52,38,35,51,66,44,38,48,38,40,61,37,43,41,46,34,37,33,58", "endOffsets": "2895,2942,2999,3052,3110,3166,3217,3275,3310,3353,3410,3452,3506,3559,3620,3696,3739,3784,3840,3915,3997,4073,4143,4329,4394,4492,4560,4747,4811,4852,4892,4933,4975,5021,5076,5117,5170,5207,5245,5303,5339,5388,5447,5500,5538,5584,5634,5685,5748,6132,6169,6209,6256,6955,7004,7055,7293,7426,7463,7662,7718,7768,7816,7855,7897,7943,7998,8051,8164,8219,8284,8340,8388,8432,8469,8527,8631,8680,8745,8788,8831,9066,9104,9151,9223,9288,9344,9398,9464,9530,9595,9640,9680,9730,9766,9830,9944,9994,10046,10095,10143,10191,10274,10360,10447,10533,10617,10704,10748,10809,10846,11119,11188,11240,11308,11363,11436,11493,11565,11621,11693,11748,11820,11879,11960,12021,12098,12155,12228,12284,12337,12391,12461,12511,12566,12617,12667,12719,12779,12826,12864,12916,12967,13016,13077,13133,13202,13249,13311,13370,13426,13476,13534,13572,13622,13706,13769,13831,13880,13941,13997,14064,14112,14164,14200,14307,14433,14546,14625,14748,14799,14850,14910,14969,15018,15061,15131,15178,15239,20739,21628,21695,21755,21804,21852,21890,21926,21982,22021,22069,25095,25142,25257,25290,25346,25400,25450,25493,25552,25598,25660,25775,25846,25929,26001,26081,26152,26232,26298,26375,26430,26495,26594,26710,26830,26881,26933,26983,27144,27190,27246,27296,27356,27395,27451,27490,27531,27741,27783,27829,27876,27935,27973,28024,28061,28108,28153,28192,28256,28301,28341,28377,28516,28575,28632,28688,28748,28802,28860,28927,28992,29063,29120,29175,29232,29295,29351,29411,29480,29553,29622,29679,29740,29795,29850,29910,29968,30025,30085,30142,30207,30271,30339,30394,30450,30507,30564,30623,30678,30742,30805,30867,30933,30991,31045,31099,31159,31213,31278,31343,31410,31473,31527,31579,31628,31686,31750,31810,31855,31913,32040,32099,32154,32211,32251,32299,32351,32491,32697,32813,32860,32918,33035,33099,33182,33219,33261,33425,33482,33530,33589,33629,33665,33719,33775,33823,33868,33928,33983,34198,34237,34273,34325,34392,34437,34631,34680,34719,34760,34822,34860,34904,34946,34993,35028,35066,35100,35159"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\e8414bc356727da52467a60536d06b50\\transformed\\material3-release\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15244,15348,15451,15555,15657,15749,15837,15941,16046,16151,16267,16349,16445,16529,16617,16722,16835,16936,17045,17152,17260,17377,17482,17583,17687,17792,17877,17972,18077,18186,18276,18376,18474,18585,18701,18801,18892,18966,19056,19145,19228,19310,19399,19479,19561,19658,19752,19845,19938,20022,20119,20215,20310,20418,20498,20592", "endColumns": "103,102,103,101,91,87,103,104,104,115,81,95,83,87,104,112,100,108,106,107,116,104,100,103,104,84,94,104,108,89,99,97,110,115,99,90,73,89,88,82,81,88,79,81,96,93,92,92,83,96,95,94,107,79,93,91", "endOffsets": "15343,15446,15550,15652,15744,15832,15936,16041,16146,16262,16344,16440,16524,16612,16717,16830,16931,17040,17147,17255,17372,17477,17578,17682,17787,17872,17967,18072,18181,18271,18371,18469,18580,18696,18796,18887,18961,19051,19140,19223,19305,19394,19474,19556,19653,19747,19840,19933,20017,20114,20210,20305,20413,20493,20587,20679"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\2e72a71af7cea86f1e691bad46c56138\\transformed\\appcompat-1.6.1\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,422", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "297,392,485,585,667,764,872,949,1024,1116,1210,1307,1403,1498,1592,1688,1780,1872,1964,2042,2138,2233,2328,2425,2521,2619,2769,28382", "endColumns": "94,92,99,81,96,107,76,74,91,93,96,95,94,93,95,91,91,91,77,95,94,94,96,95,97,149,93,78", "endOffsets": "387,480,580,662,759,867,944,1019,1111,1205,1302,1398,1493,1587,1683,1775,1867,1959,2037,2133,2228,2323,2420,2516,2614,2764,2858,28456"}}]}, {"outputFile": "D:\\development\\Android\\gradle\\daemon\\8.11.1\\com.timeflow.app-mergeDebugResources-85:\\values-zh-rTW\\values-zh-rTW.xml", "map": [{"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\443a6cfc0de72abe8b7a8ef4c7b7a3e3\\transformed\\core-1.12.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,246,340,434,527,620,716", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "142,241,335,429,522,615,711,812"}, "to": {"startLines": "87,88,89,90,91,92,93,474", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "6060,6152,6251,6345,6439,6532,6625,31527", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "6147,6246,6340,6434,6527,6620,6716,31623"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\6e1bec7b61f7c5e14c57eb6146ea2199\\transformed\\ui-release\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,182,258,345,436,514,588,665,743,818,883,948,1021,1096,1164,1238,1306", "endColumns": "76,75,86,90,77,73,76,77,74,64,64,72,74,67,73,67,115", "endOffsets": "177,253,340,431,509,583,660,738,813,878,943,1016,1091,1159,1233,1301,1417"}, "to": {"startLines": "103,104,127,128,142,214,216,352,360,372,374,390,391,466,478,482,488", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7267,7344,8570,8657,9440,13744,13870,24472,24888,25723,25822,26707,26780,31089,31873,32094,32437", "endColumns": "76,75,86,90,77,73,76,77,74,64,64,72,74,67,73,67,115", "endOffsets": "7339,7415,8652,8743,9513,13813,13942,24545,24958,25783,25882,26775,26850,31152,31942,32157,32548"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\5619827a7b7a666ff443af1afbf85287\\transformed\\preference-1.2.1\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,5,6,7,8", "startColumns": "4,4,4,4,4,4", "startOffsets": "105,171,322,442,610,689", "endColumns": "65,80,119,167,78,75", "endOffsets": "166,247,437,605,684,760"}, "to": {"startLines": "114,159,380,476,508,509", "startColumns": "4,4,4,4,4,4", "startOffsets": "7855,10518,26159,31667,33577,33656", "endColumns": "65,80,119,167,78,75", "endOffsets": "7916,10594,26274,31830,33651,33727"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\8502c7d6e294f2825540546f57fbfcc0\\transformed\\material-1.11.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,247,311,373,440,510,587,681,788,861,923,1001,1061,1121,1199,1260,1318,1374,1434,1492,1546,1631,1687,1745,1799,1864,1956,2030,2107,2227,2290,2353,2452,2529,2603,2653,2704,2770,2833,2901,2979,3050,3111,3182,3249,3311,3398,3477,3542,3625,3710,3784,3848,3924,3972,4045,4109,4185,4263,4325,4389,4452,4518,4598,4676,4752,4831,4885,4940", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,63,61,66,69,76,93,106,72,61,77,59,59,77,60,57,55,59,57,53,84,55,57,53,64,91,73,76,119,62,62,98,76,73,49,50,65,62,67,77,70,60,70,66,61,86,78,64,82,84,73,63,75,47,72,63,75,77,61,63,62,65,79,77,75,78,53,54,68", "endOffsets": "242,306,368,435,505,582,676,783,856,918,996,1056,1116,1194,1255,1313,1369,1429,1487,1541,1626,1682,1740,1794,1859,1951,2025,2102,2222,2285,2348,2447,2524,2598,2648,2699,2765,2828,2896,2974,3045,3106,3177,3244,3306,3393,3472,3537,3620,3705,3779,3843,3919,3967,4040,4104,4180,4258,4320,4384,4447,4513,4593,4671,4747,4826,4880,4935,5004"}, "to": {"startLines": "2,79,80,81,82,83,97,98,100,158,160,212,219,286,287,288,289,290,291,292,293,294,295,296,297,298,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,376", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,5596,5660,5722,5789,5859,6859,6953,7097,10456,10599,13637,14062,20116,20194,20255,20313,20369,20429,20487,20541,20626,20682,20740,20794,20859,21446,21520,21597,21717,21780,21843,21942,22019,22093,22143,22194,22260,22323,22391,22469,22540,22601,22672,22739,22801,22888,22967,23032,23115,23200,23274,23338,23414,23462,23535,23599,23675,23753,23815,23879,23942,24008,24088,24166,24242,24321,24375,25938", "endLines": "5,79,80,81,82,83,97,98,100,158,160,212,219,286,287,288,289,290,291,292,293,294,295,296,297,298,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,376", "endColumns": "12,63,61,66,69,76,93,106,72,61,77,59,59,77,60,57,55,59,57,53,84,55,57,53,64,91,73,76,119,62,62,98,76,73,49,50,65,62,67,77,70,60,70,66,61,86,78,64,82,84,73,63,75,47,72,63,75,77,61,63,62,65,79,77,75,78,53,54,68", "endOffsets": "292,5655,5717,5784,5854,5931,6948,7055,7165,10513,10672,13692,14117,20189,20250,20308,20364,20424,20482,20536,20621,20677,20735,20789,20854,20946,21515,21592,21712,21775,21838,21937,22014,22088,22138,22189,22255,22318,22386,22464,22535,22596,22667,22734,22796,22883,22962,23027,23110,23195,23269,23333,23409,23457,23530,23594,23670,23748,23810,23874,23937,24003,24083,24161,24237,24316,24370,24425,26002"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\71256635af31e1aec955d3a558d276eb\\transformed\\foundation-release\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,138", "endColumns": "82,78", "endOffsets": "133,212"}, "to": {"startLines": "501,502", "startColumns": "4,4", "startOffsets": "33159,33242", "endColumns": "82,78", "endOffsets": "33237,33316"}}, {"source": "D:\\development\\Codes\\MyApplication\\app\\src\\main\\res\\values-zh-rTW\\strings.xml", "from": {"startLines": "70,285,393,391,390,392,389,81,302,14,127,128,330,15,283,284,242,243,241,244,236,235,238,237,234,233,334,402,7,366,53,54,2,67,85,261,382,87,45,46,47,288,367,369,387,388,368,10,26,73,259,257,83,263,86,36,37,380,333,74,357,358,150,151,305,376,38,28,25,370,409,377,281,282,356,27,295,80,364,365,116,396,115,117,118,359,8,395,78,79,275,276,273,272,274,155,157,156,154,158,159,58,129,92,383,384,306,307,314,315,312,313,316,317,318,319,320,321,310,311,308,309,100,98,99,132,101,103,105,107,104,106,21,90,174,175,346,347,348,349,55,110,112,109,111,113,139,66,406,342,343,344,345,324,3,290,381,95,325,60,59,52,252,360,266,91,262,385,386,33,35,34,18,19,135,399,143,372,267,271,88,77,374,405,404,335,301,296,162,163,166,167,168,169,164,165,68,253,75,373,22,24,20,89,39,40,69,254,9,29,398,401,336,147,268,293,294,82,258,84,62,61,289,375,339,140,72,205,226,230,214,212,216,178,179,181,180,203,227,222,229,219,196,198,197,199,208,202,215,220,225,213,209,224,192,193,195,194,228,217,206,223,221,187,188,189,191,190,204,210,211,218,182,183,184,185,186,207,247,41,42,328,329,353,403,126,350,352,351,65,23,56,93,141,361,279,280,172,340,341,4,5,43,44,32,251,138,6,120,121,394,173,123,122,57,136,48,49,260,71,300,94,137,144,371,299,11,397,400,76,248", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3030,13306,18329,18220,18164,18278,18106,3430,13899,474,5346,5399,15344,528,13205,13250,11483,11558,11407,11640,11034,10969,11288,11220,10782,10718,15487,18723,247,16965,2258,2304,55,2872,3577,12325,17705,3651,1904,1953,2012,13375,17007,17090,17992,18043,17053,367,984,3142,12231,12129,3503,12433,3614,1432,1478,17603,15439,3180,16593,16635,6106,6161,13962,17425,1534,1074,940,17140,19028,17480,13091,13140,16550,1031,13626,3392,16846,16893,4853,18480,4787,4909,4975,16681,288,18430,3320,3356,12863,12913,12766,12718,12815,6322,6492,6405,6236,6578,6662,2511,5460,3846,17763,17815,14009,14061,14526,14581,14397,14454,14654,14710,14782,14837,14909,14968,14259,14320,14129,14186,4125,4018,4071,5542,4181,4232,4339,4450,4287,4390,743,3765,7481,7533,16080,16129,16190,16246,2359,4557,4669,4501,4619,4728,5791,2822,18925,15847,15896,15957,16013,15070,96,13452,17653,3961,15122,2606,2555,2207,11923,16726,12512,3803,12363,17884,17931,1251,1365,1305,596,645,5633,18608,5908,17215,12561,12676,3687,3287,17305,18871,18821,15528,13837,13683,6768,6839,7065,7137,7217,7288,6922,6988,2925,11983,3219,17254,790,889,693,3724,1590,1636,2980,12048,328,1122,18569,18682,15574,6045,12609,13520,13567,3465,12180,3540,2714,2669,13413,17361,15655,5829,3106,9212,10408,10640,9723,9605,9834,7605,7663,7801,7730,9097,10467,10174,10577,10004,8744,8877,8804,8946,9383,9036,9779,10060,10348,9665,9440,10288,8490,8547,8680,8612,10522,9888,9272,10231,10115,8180,8235,8299,8428,8362,9154,9497,9551,9944,7866,7920,7985,8050,8117,9329,11729,1692,1741,15220,15284,16486,18763,5287,16315,16431,16374,2782,841,2406,3883,5869,16785,12986,13033,7387,15700,15764,132,169,1799,1847,1203,11864,5751,211,5041,5095,18382,7436,5206,5151,2458,5671,2065,2117,12280,3067,13788,3922,5710,5964,17177,13744,407,18534,18644,3253,11781", "endColumns": "36,46,52,57,55,50,57,34,41,53,52,60,75,42,44,55,74,81,75,69,185,64,97,67,186,63,40,39,40,41,45,54,40,52,36,37,57,35,48,58,52,37,45,49,50,62,36,39,46,37,48,50,36,59,36,45,55,49,47,38,41,45,54,52,46,54,55,47,43,36,57,103,48,64,42,42,56,37,46,71,55,53,65,65,64,44,39,49,35,35,49,51,48,47,47,82,85,86,85,83,86,43,60,36,51,68,51,67,54,72,56,71,55,71,54,71,58,80,60,76,56,72,55,52,53,69,49,54,50,49,51,59,46,37,51,50,48,60,55,68,46,61,58,55,49,57,37,49,83,48,60,55,66,51,35,46,51,35,78,62,50,50,59,58,48,42,69,46,60,53,66,59,48,47,37,35,55,38,47,41,36,32,55,53,49,45,61,39,70,82,71,79,70,79,65,76,54,64,33,50,50,50,49,40,45,55,49,59,38,55,38,40,61,41,45,46,58,37,50,36,46,44,38,63,44,39,35,59,58,56,55,59,53,57,66,64,70,56,54,56,62,55,59,68,72,68,56,60,54,54,59,57,56,59,56,64,63,67,54,55,56,56,58,54,63,62,61,65,57,53,53,59,53,64,64,66,62,53,51,48,57,63,59,44,57,58,58,54,56,39,47,51,38,37,41,46,57,48,63,82,36,41,47,56,47,58,39,35,53,55,47,44,59,54,52,38,51,66,44,38,48,38,40,61,37,43,41,34,37,33,58", "endOffsets": "3062,13348,18377,18273,18215,18324,18159,3460,13936,523,5394,5455,15415,566,13245,13301,11553,11635,11478,11705,11215,11029,11381,11283,10964,10777,15523,18758,283,17002,2299,2354,91,2920,3609,12358,17758,3682,1948,2007,2060,13408,17048,17135,18038,18101,17085,402,1026,3175,12275,12175,3535,12488,3646,1473,1529,17648,15482,3214,16630,16676,6156,6209,14004,17475,1585,1117,979,17172,19081,17579,13135,13200,16588,1069,13678,3425,16888,16960,4904,18529,4848,4970,5035,16721,323,18475,3351,3387,12908,12960,12810,12761,12858,6400,6573,6487,6317,6657,6744,2550,5516,3878,17810,17879,14056,14124,14576,14649,14449,14521,14705,14777,14832,14904,14963,15044,14315,14392,14181,14254,4176,4066,4120,5607,4226,4282,4385,4495,4334,4445,785,3798,7528,7579,16124,16185,16241,16310,2401,4614,4723,4552,4664,4781,5824,2867,19004,15891,15952,16008,16075,15117,127,13494,17700,3992,15196,2664,2601,2253,11978,16780,12556,3841,12428,17926,17987,1300,1427,1360,640,688,5666,18639,5959,17249,12604,12713,3719,3315,17356,18920,18866,15569,13894,13718,6834,6917,7132,7212,7283,7363,6983,7060,2975,12043,3248,17300,836,935,738,3760,1631,1687,3025,12103,362,1173,18603,18718,15631,6082,12650,13562,13621,3498,12226,3572,2756,2709,13447,17420,15695,5864,3137,9267,10462,10692,9774,9660,9883,7658,7725,7861,7796,9149,10517,10226,10635,10055,8799,8941,8872,9010,9435,9092,9829,10110,10403,9718,9492,10343,8542,8607,8739,8675,10572,9939,9324,10283,10169,8230,8294,8357,8485,8423,9207,9546,9600,9999,7915,7980,8045,8112,8175,9378,11776,1736,1794,15279,15339,16526,18816,5341,16369,16481,16426,2817,884,2453,3917,5902,16822,13028,13086,7431,15759,15842,164,206,1842,1899,1246,11918,5786,242,5090,5146,18425,7476,5261,5201,2506,5705,2112,2179,12320,3101,13832,3956,5746,6021,17210,13783,444,18564,18677,3282,11835"}, "to": {"startLines": "33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,84,85,86,94,95,96,99,101,102,105,106,107,108,109,110,111,112,113,115,116,117,118,119,120,121,122,123,124,125,126,129,130,131,132,133,134,135,136,137,138,139,140,141,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,213,215,217,218,220,221,222,223,224,225,226,227,228,229,299,300,301,302,303,304,305,306,307,308,351,353,354,355,356,357,358,359,361,362,363,364,365,366,367,368,369,370,371,373,375,377,378,379,381,382,383,384,385,386,387,388,389,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,467,468,469,470,471,472,473,475,477,479,480,481,483,484,485,486,487,489,490,491,492,493,494,495,496,497,498,499,500,503,504,505,506,507,510,511,512,513,514,515,516,517,518,519,520,521", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2863,2900,2947,3000,3058,3114,3165,3223,3258,3300,3354,3407,3468,3544,3587,3632,3688,3763,3845,3921,3991,4177,4242,4340,4408,4595,4659,4700,4740,4781,4823,4869,4924,4965,5018,5055,5093,5151,5187,5236,5295,5348,5386,5432,5482,5533,5936,5973,6013,6721,6759,6808,7060,7170,7230,7420,7466,7522,7572,7620,7659,7701,7747,7802,7921,7968,8023,8079,8127,8171,8208,8266,8370,8419,8484,8527,8748,8805,8843,8890,8962,9018,9072,9138,9204,9269,9314,9354,9404,9518,9554,9604,9656,9705,9753,9801,9884,9970,10057,10143,10227,10314,10358,10419,10677,10729,10798,10850,10918,10973,11046,11103,11175,11231,11303,11358,11430,11489,11570,11631,11708,11765,11838,11894,11947,12001,12071,12121,12176,12227,12277,12329,12389,12436,12474,12526,12577,12626,12687,12743,12812,12859,12921,12980,13036,13086,13144,13182,13232,13316,13365,13426,13482,13549,13601,13697,13818,13947,13983,14122,14185,14236,14287,14347,14406,14455,14498,14568,14615,20951,21005,21072,21132,21181,21229,21267,21303,21359,21398,24430,24550,24587,24620,24676,24730,24780,24826,24963,25003,25074,25157,25229,25309,25380,25460,25526,25603,25658,25788,25887,26007,26058,26109,26279,26320,26366,26422,26472,26532,26571,26627,26666,26855,26917,26959,27005,27052,27111,27149,27200,27237,27284,27329,27368,27432,27477,27517,27632,27692,27751,27808,27864,27924,27978,28036,28103,28168,28239,28296,28351,28408,28471,28527,28587,28656,28729,28798,28855,28916,28971,29026,29086,29144,29201,29261,29318,29383,29447,29515,29570,29626,29683,29740,29799,29854,29918,29981,30043,30109,30167,30221,30275,30335,30389,30454,30519,30586,30649,30703,30755,30804,30862,30926,30986,31031,31157,31216,31275,31330,31387,31427,31475,31628,31835,31947,31989,32036,32162,32211,32275,32358,32395,32553,32601,32658,32706,32765,32805,32841,32895,32951,32999,33044,33104,33321,33374,33413,33465,33532,33732,33771,33820,33859,33900,33962,34000,34044,34086,34121,34159,34193", "endColumns": "36,46,52,57,55,50,57,34,41,53,52,60,75,42,44,55,74,81,75,69,185,64,97,67,186,63,40,39,40,41,45,54,40,52,36,37,57,35,48,58,52,37,45,49,50,62,36,39,46,37,48,50,36,59,36,45,55,49,47,38,41,45,54,52,46,54,55,47,43,36,57,103,48,64,42,42,56,37,46,71,55,53,65,65,64,44,39,49,35,35,49,51,48,47,47,82,85,86,85,83,86,43,60,36,51,68,51,67,54,72,56,71,55,71,54,71,58,80,60,76,56,72,55,52,53,69,49,54,50,49,51,59,46,37,51,50,48,60,55,68,46,61,58,55,49,57,37,49,83,48,60,55,66,51,35,46,51,35,78,62,50,50,59,58,48,42,69,46,60,53,66,59,48,47,37,35,55,38,47,41,36,32,55,53,49,45,61,39,70,82,71,79,70,79,65,76,54,64,33,50,50,50,49,40,45,55,49,59,38,55,38,40,61,41,45,46,58,37,50,36,46,44,38,63,44,39,35,59,58,56,55,59,53,57,66,64,70,56,54,56,62,55,59,68,72,68,56,60,54,54,59,57,56,59,56,64,63,67,54,55,56,56,58,54,63,62,61,65,57,53,53,59,53,64,64,66,62,53,51,48,57,63,59,44,57,58,58,54,56,39,47,51,38,37,41,46,57,48,63,82,36,41,47,56,47,58,39,35,53,55,47,44,59,54,52,38,51,66,44,38,48,38,40,61,37,43,41,34,37,33,58", "endOffsets": "2895,2942,2995,3053,3109,3160,3218,3253,3295,3349,3402,3463,3539,3582,3627,3683,3758,3840,3916,3986,4172,4237,4335,4403,4590,4654,4695,4735,4776,4818,4864,4919,4960,5013,5050,5088,5146,5182,5231,5290,5343,5381,5427,5477,5528,5591,5968,6008,6055,6754,6803,6854,7092,7225,7262,7461,7517,7567,7615,7654,7696,7742,7797,7850,7963,8018,8074,8122,8166,8203,8261,8365,8414,8479,8522,8565,8800,8838,8885,8957,9013,9067,9133,9199,9264,9309,9349,9399,9435,9549,9599,9651,9700,9748,9796,9879,9965,10052,10138,10222,10309,10353,10414,10451,10724,10793,10845,10913,10968,11041,11098,11170,11226,11298,11353,11425,11484,11565,11626,11703,11760,11833,11889,11942,11996,12066,12116,12171,12222,12272,12324,12384,12431,12469,12521,12572,12621,12682,12738,12807,12854,12916,12975,13031,13081,13139,13177,13227,13311,13360,13421,13477,13544,13596,13632,13739,13865,13978,14057,14180,14231,14282,14342,14401,14450,14493,14563,14610,14671,21000,21067,21127,21176,21224,21262,21298,21354,21393,21441,24467,24582,24615,24671,24725,24775,24821,24883,24998,25069,25152,25224,25304,25375,25455,25521,25598,25653,25718,25817,25933,26053,26104,26154,26315,26361,26417,26467,26527,26566,26622,26661,26702,26912,26954,27000,27047,27106,27144,27195,27232,27279,27324,27363,27427,27472,27512,27548,27687,27746,27803,27859,27919,27973,28031,28098,28163,28234,28291,28346,28403,28466,28522,28582,28651,28724,28793,28850,28911,28966,29021,29081,29139,29196,29256,29313,29378,29442,29510,29565,29621,29678,29735,29794,29849,29913,29976,30038,30104,30162,30216,30270,30330,30384,30449,30514,30581,30644,30698,30750,30799,30857,30921,30981,31026,31084,31211,31270,31325,31382,31422,31470,31522,31662,31868,31984,32031,32089,32206,32270,32353,32390,32432,32596,32653,32701,32760,32800,32836,32890,32946,32994,33039,33099,33154,33369,33408,33460,33527,33572,33766,33815,33854,33895,33957,33995,34039,34081,34116,34154,34188,34247"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\e8414bc356727da52467a60536d06b50\\transformed\\material3-release\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,159,262,366,468,560,648,752,857,962,1078,1160,1256,1340,1428,1533,1646,1747,1856,1963,2071,2188,2293,2394,2498,2603,2688,2783,2888,2997,3087,3187,3285,3396,3512,3612,3703,3777,3867,3956,4039,4121,4210,4290,4372,4469,4563,4656,4749,4833,4930,5026,5121,5229,5309,5403", "endColumns": "103,102,103,101,91,87,103,104,104,115,81,95,83,87,104,112,100,108,106,107,116,104,100,103,104,84,94,104,108,89,99,97,110,115,99,90,73,89,88,82,81,88,79,81,96,93,92,92,83,96,95,94,107,79,93,91", "endOffsets": "154,257,361,463,555,643,747,852,957,1073,1155,1251,1335,1423,1528,1641,1742,1851,1958,2066,2183,2288,2389,2493,2598,2683,2778,2883,2992,3082,3182,3280,3391,3507,3607,3698,3772,3862,3951,4034,4116,4205,4285,4367,4464,4558,4651,4744,4828,4925,5021,5116,5224,5304,5398,5490"}, "to": {"startLines": "230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14676,14780,14883,14987,15089,15181,15269,15373,15478,15583,15699,15781,15877,15961,16049,16154,16267,16368,16477,16584,16692,16809,16914,17015,17119,17224,17309,17404,17509,17618,17708,17808,17906,18017,18133,18233,18324,18398,18488,18577,18660,18742,18831,18911,18993,19090,19184,19277,19370,19454,19551,19647,19742,19850,19930,20024", "endColumns": "103,102,103,101,91,87,103,104,104,115,81,95,83,87,104,112,100,108,106,107,116,104,100,103,104,84,94,104,108,89,99,97,110,115,99,90,73,89,88,82,81,88,79,81,96,93,92,92,83,96,95,94,107,79,93,91", "endOffsets": "14775,14878,14982,15084,15176,15264,15368,15473,15578,15694,15776,15872,15956,16044,16149,16262,16363,16472,16579,16687,16804,16909,17010,17114,17219,17304,17399,17504,17613,17703,17803,17901,18012,18128,18228,18319,18393,18483,18572,18655,18737,18826,18906,18988,19085,19179,19272,19365,19449,19546,19642,19737,19845,19925,20019,20111"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\2e72a71af7cea86f1e691bad46c56138\\transformed\\appcompat-1.6.1\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,757,832,924,1018,1115,1211,1306,1400,1496,1588,1680,1772,1850,1946,2041,2136,2233,2329,2427,2577,2671", "endColumns": "94,92,99,81,96,107,76,74,91,93,96,95,94,93,95,91,91,91,77,95,94,94,96,95,97,149,93,78", "endOffsets": "195,288,388,470,567,675,752,827,919,1013,1110,1206,1301,1395,1491,1583,1675,1767,1845,1941,2036,2131,2228,2324,2422,2572,2666,2745"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,407", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "297,392,485,585,667,764,872,949,1024,1116,1210,1307,1403,1498,1592,1688,1780,1872,1964,2042,2138,2233,2328,2425,2521,2619,2769,27553", "endColumns": "94,92,99,81,96,107,76,74,91,93,96,95,94,93,95,91,91,91,77,95,94,94,96,95,97,149,93,78", "endOffsets": "387,480,580,662,759,867,944,1019,1111,1205,1302,1398,1493,1587,1683,1775,1867,1959,2037,2133,2228,2323,2420,2516,2614,2764,2858,27627"}}]}]}