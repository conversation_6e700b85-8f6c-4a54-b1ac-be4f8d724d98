{"logs": [{"outputFile": "com.timeflow.app-mergeDebugResources-85:/values-zh-rTW/values-zh-rTW.xml", "map": [{"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\443a6cfc0de72abe8b7a8ef4c7b7a3e3\\transformed\\core-1.12.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,246,340,434,527,620,716", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "142,241,335,429,522,615,711,812"}, "to": {"startLines": "78,79,80,81,82,83,84,449", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "5572,5664,5763,5857,5951,6044,6137,30184", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "5659,5758,5852,5946,6039,6132,6228,30280"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\6e1bec7b61f7c5e14c57eb6146ea2199\\transformed\\ui-release\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,182,258,345,436,514,588,665,743,818,883,948,1021,1096,1164,1238,1306", "endColumns": "76,75,86,90,77,73,76,77,74,64,64,72,74,67,73,67,115", "endOffsets": "177,253,340,431,509,583,660,738,813,878,943,1016,1091,1159,1233,1301,1417"}, "to": {"startLines": "94,95,116,117,129,198,199,332,338,350,352,366,367,441,453,457,463", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6779,6856,7974,8061,8740,12839,12913,23371,23683,24518,24617,25422,25495,29746,30530,30751,31094", "endColumns": "76,75,86,90,77,73,76,77,74,64,64,72,74,67,73,67,115", "endOffsets": "6851,6927,8056,8147,8813,12908,12985,23444,23753,24578,24677,25490,25565,29809,30599,30814,31205"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\5619827a7b7a666ff443af1afbf85287\\transformed\\preference-1.2.1\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,5,6,7,8", "startColumns": "4,4,4,4,4,4", "startOffsets": "105,171,322,442,610,689", "endColumns": "65,80,119,167,78,75", "endOffsets": "166,247,437,605,684,760"}, "to": {"startLines": "104,146,358,451,482,483", "startColumns": "4,4,4,4,4,4", "startOffsets": "7317,9818,24954,30324,32186,32265", "endColumns": "65,80,119,167,78,75", "endOffsets": "7378,9894,25069,30487,32260,32336"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\8502c7d6e294f2825540546f57fbfcc0\\transformed\\material-1.11.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,247,311,373,440,510,587,681,788,861,923,1001,1061,1121,1199,1260,1318,1374,1434,1492,1546,1631,1687,1745,1799,1864,1956,2030,2107,2227,2290,2353,2452,2529,2603,2653,2704,2770,2833,2901,2979,3050,3111,3182,3249,3311,3398,3477,3542,3625,3710,3784,3848,3924,3972,4045,4109,4185,4263,4325,4389,4452,4518,4598,4676,4752,4831,4885,4940", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,63,61,66,69,76,93,106,72,61,77,59,59,77,60,57,55,59,57,53,84,55,57,53,64,91,73,76,119,62,62,98,76,73,49,50,65,62,67,77,70,60,70,66,61,86,78,64,82,84,73,63,75,47,72,63,75,77,61,63,62,65,79,77,75,78,53,54,68", "endOffsets": "242,306,368,435,505,582,676,783,856,918,996,1056,1116,1194,1255,1313,1369,1429,1487,1541,1626,1682,1740,1794,1859,1951,2025,2102,2222,2285,2348,2447,2524,2598,2648,2699,2765,2828,2896,2974,3045,3106,3177,3244,3306,3393,3472,3537,3620,3705,3779,3843,3919,3967,4040,4104,4180,4258,4320,4384,4447,4513,4593,4671,4747,4826,4880,4935,5004"}, "to": {"startLines": "2,70,71,72,73,74,88,89,91,145,147,196,202,267,268,269,270,271,272,273,274,275,276,277,278,279,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,354", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,5108,5172,5234,5301,5371,6371,6465,6609,9756,9899,12732,13105,19051,19129,19190,19248,19304,19364,19422,19476,19561,19617,19675,19729,19794,20345,20419,20496,20616,20679,20742,20841,20918,20992,21042,21093,21159,21222,21290,21368,21439,21500,21571,21638,21700,21787,21866,21931,22014,22099,22173,22237,22313,22361,22434,22498,22574,22652,22714,22778,22841,22907,22987,23065,23141,23220,23274,24733", "endLines": "5,70,71,72,73,74,88,89,91,145,147,196,202,267,268,269,270,271,272,273,274,275,276,277,278,279,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,354", "endColumns": "12,63,61,66,69,76,93,106,72,61,77,59,59,77,60,57,55,59,57,53,84,55,57,53,64,91,73,76,119,62,62,98,76,73,49,50,65,62,67,77,70,60,70,66,61,86,78,64,82,84,73,63,75,47,72,63,75,77,61,63,62,65,79,77,75,78,53,54,68", "endOffsets": "292,5167,5229,5296,5366,5443,6460,6567,6677,9813,9972,12787,13160,19124,19185,19243,19299,19359,19417,19471,19556,19612,19670,19724,19789,19881,20414,20491,20611,20674,20737,20836,20913,20987,21037,21088,21154,21217,21285,21363,21434,21495,21566,21633,21695,21782,21861,21926,22009,22094,22168,22232,22308,22356,22429,22493,22569,22647,22709,22773,22836,22902,22982,23060,23136,23215,23269,23324,24797"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\71256635af31e1aec955d3a558d276eb\\transformed\\foundation-release\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,138", "endColumns": "82,78", "endOffsets": "133,212"}, "to": {"startLines": "475,476", "startColumns": "4,4", "startOffsets": "31768,31851", "endColumns": "82,78", "endOffsets": "31846,31925"}}, {"source": "D:\\development\\Codes\\MyApplication\\app\\src\\main\\res\\values-zh-rTW\\strings.xml", "from": {"startLines": "70,285,81,302,14,127,128,330,15,283,284,242,243,241,244,236,235,238,237,234,233,334,7,366,53,54,2,67,85,261,87,45,46,47,288,367,369,368,10,26,73,259,257,83,263,86,36,37,333,74,357,358,150,151,305,376,38,28,25,370,377,281,282,356,27,295,80,364,365,116,115,117,118,359,8,78,79,275,276,273,272,274,155,157,156,154,158,159,58,129,92,306,307,314,315,312,313,316,317,318,319,320,321,310,311,308,309,100,98,99,132,101,103,105,107,104,106,21,90,174,175,346,347,348,349,55,110,112,109,111,113,139,66,342,343,344,345,324,3,290,95,325,60,59,52,252,360,266,91,262,33,35,34,18,19,135,143,372,267,271,88,77,374,335,301,296,162,163,166,167,168,169,164,165,68,253,75,373,22,24,20,89,39,40,69,254,9,29,336,147,268,293,294,82,258,84,62,61,289,375,339,140,72,205,226,230,214,212,216,178,179,181,180,203,227,222,229,219,196,198,197,199,208,202,215,220,225,213,209,224,192,193,195,194,228,217,206,223,221,187,188,189,191,190,204,210,211,218,182,183,184,185,186,207,247,41,42,328,329,353,126,350,352,351,65,23,56,93,141,361,279,280,172,340,341,4,5,43,44,32,251,138,6,120,121,173,123,122,57,136,48,49,260,71,300,94,137,144,371,299,11,76,248", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3030,13306,3430,13899,474,5346,5399,15344,528,13205,13250,11483,11558,11407,11640,11034,10969,11288,11220,10782,10718,15487,247,16965,2258,2304,55,2872,3577,12325,3651,1904,1953,2012,13375,17007,17090,17053,367,984,3142,12231,12129,3503,12433,3614,1432,1478,15439,3180,16593,16635,6106,6161,13962,17425,1534,1074,940,17140,17480,13091,13140,16550,1031,13626,3392,16846,16893,4853,4787,4909,4975,16681,288,3320,3356,12863,12913,12766,12718,12815,6322,6492,6405,6236,6578,6662,2511,5460,3846,14009,14061,14526,14581,14397,14454,14654,14710,14782,14837,14909,14968,14259,14320,14129,14186,4125,4018,4071,5542,4181,4232,4339,4450,4287,4390,743,3765,7481,7533,16080,16129,16190,16246,2359,4557,4669,4501,4619,4728,5791,2822,15847,15896,15957,16013,15070,96,13452,3961,15122,2606,2555,2207,11923,16726,12512,3803,12363,1251,1365,1305,596,645,5633,5908,17215,12561,12676,3687,3287,17305,15528,13837,13683,6768,6839,7065,7137,7217,7288,6922,6988,2925,11983,3219,17254,790,889,693,3724,1590,1636,2980,12048,328,1122,15574,6045,12609,13520,13567,3465,12180,3540,2714,2669,13413,17361,15655,5829,3106,9212,10408,10640,9723,9605,9834,7605,7663,7801,7730,9097,10467,10174,10577,10004,8744,8877,8804,8946,9383,9036,9779,10060,10348,9665,9440,10288,8490,8547,8680,8612,10522,9888,9272,10231,10115,8180,8235,8299,8428,8362,9154,9497,9551,9944,7866,7920,7985,8050,8117,9329,11729,1692,1741,15220,15284,16486,5287,16315,16431,16374,2782,841,2406,3883,5869,16785,12986,13033,7387,15700,15764,132,169,1799,1847,1203,11864,5751,211,5041,5095,7436,5206,5151,2458,5671,2065,2117,12280,3067,13788,3922,5710,5964,17177,13744,407,3253,11781", "endColumns": "36,46,34,41,53,52,60,75,42,44,55,74,81,75,69,185,64,97,67,186,63,40,40,41,45,54,40,52,36,37,35,48,58,52,37,45,49,36,39,46,37,48,50,36,59,36,45,55,47,38,41,45,54,52,46,54,55,47,43,36,103,48,64,42,42,56,37,46,71,55,65,65,64,44,39,35,35,49,51,48,47,47,82,85,86,85,83,86,43,60,36,51,67,54,72,56,71,55,71,54,71,58,80,60,76,56,72,55,52,53,69,49,54,50,49,51,59,46,37,51,50,48,60,55,68,46,61,58,55,49,57,37,49,48,60,55,66,51,35,46,35,78,62,50,50,59,58,48,42,69,53,66,59,48,47,37,55,38,47,41,36,32,55,45,61,39,70,82,71,79,70,79,65,76,54,64,33,50,50,50,49,40,45,55,49,59,38,55,61,41,45,46,58,37,50,36,46,44,38,63,44,39,35,59,58,56,55,59,53,57,66,64,70,56,54,56,62,55,59,68,72,68,56,60,54,54,59,57,56,59,56,64,63,67,54,55,56,56,58,54,63,62,61,65,57,53,53,59,53,64,64,66,62,53,51,48,57,63,59,44,58,58,54,56,39,47,51,38,37,41,46,57,48,63,82,36,41,47,56,47,58,39,35,53,55,44,59,54,52,38,51,66,44,38,48,38,40,61,37,43,41,33,58", "endOffsets": "3062,13348,3460,13936,523,5394,5455,15415,566,13245,13301,11553,11635,11478,11705,11215,11029,11381,11283,10964,10777,15523,283,17002,2299,2354,91,2920,3609,12358,3682,1948,2007,2060,13408,17048,17135,17085,402,1026,3175,12275,12175,3535,12488,3646,1473,1529,15482,3214,16630,16676,6156,6209,14004,17475,1585,1117,979,17172,17579,13135,13200,16588,1069,13678,3425,16888,16960,4904,4848,4970,5035,16721,323,3351,3387,12908,12960,12810,12761,12858,6400,6573,6487,6317,6657,6744,2550,5516,3878,14056,14124,14576,14649,14449,14521,14705,14777,14832,14904,14963,15044,14315,14392,14181,14254,4176,4066,4120,5607,4226,4282,4385,4495,4334,4445,785,3798,7528,7579,16124,16185,16241,16310,2401,4614,4723,4552,4664,4781,5824,2867,15891,15952,16008,16075,15117,127,13494,3992,15196,2664,2601,2253,11978,16780,12556,3841,12428,1300,1427,1360,640,688,5666,5959,17249,12604,12713,3719,3315,17356,15569,13894,13718,6834,6917,7132,7212,7283,7363,6983,7060,2975,12043,3248,17300,836,935,738,3760,1631,1687,3025,12103,362,1173,15631,6082,12650,13562,13621,3498,12226,3572,2756,2709,13447,17420,15695,5864,3137,9267,10462,10692,9774,9660,9883,7658,7725,7861,7796,9149,10517,10226,10635,10055,8799,8941,8872,9010,9435,9092,9829,10110,10403,9718,9492,10343,8542,8607,8739,8675,10572,9939,9324,10283,10169,8230,8294,8357,8485,8423,9207,9546,9600,9999,7915,7980,8045,8112,8175,9378,11776,1736,1794,15279,15339,16526,5341,16369,16481,16426,2817,884,2453,3917,5902,16822,13028,13086,7431,15759,15842,164,206,1842,1899,1246,11918,5786,242,5090,5146,7476,5261,5201,2506,5705,2112,2179,12320,3101,13832,3956,5746,6021,17210,13783,444,3282,11835"}, "to": {"startLines": "33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,75,76,77,85,86,87,90,92,93,96,97,98,99,100,101,102,103,105,106,107,108,109,110,111,112,113,114,115,118,119,120,121,122,123,124,125,126,127,128,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,197,200,201,203,204,205,206,207,208,209,210,280,281,282,283,284,285,286,287,288,331,333,334,335,336,337,339,340,341,342,343,344,345,346,347,348,349,351,353,355,356,357,359,360,361,362,363,364,365,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,442,443,444,445,446,447,448,450,452,454,455,456,458,459,460,461,462,464,465,466,467,468,469,470,471,472,473,474,477,478,479,480,481,484,485,486,487,488,489,490,491,492,493", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2863,2900,2947,2982,3024,3078,3131,3192,3268,3311,3356,3412,3487,3569,3645,3715,3901,3966,4064,4132,4319,4383,4424,4465,4507,4553,4608,4649,4702,4739,4777,4813,4862,4921,4974,5012,5058,5448,5485,5525,6233,6271,6320,6572,6682,6742,6932,6978,7034,7082,7121,7163,7209,7264,7383,7430,7485,7541,7589,7633,7670,7774,7823,7888,7931,8152,8209,8247,8294,8366,8422,8488,8554,8619,8664,8704,8818,8854,8904,8956,9005,9053,9101,9184,9270,9357,9443,9527,9614,9658,9719,9977,10029,10097,10152,10225,10282,10354,10410,10482,10537,10609,10668,10749,10810,10887,10944,11017,11073,11126,11180,11250,11300,11355,11406,11456,11508,11568,11615,11653,11705,11756,11805,11866,11922,11991,12038,12100,12159,12215,12265,12323,12361,12411,12460,12521,12577,12644,12696,12792,12990,13026,13165,13228,13279,13330,13390,13449,13498,13541,19886,19940,20007,20067,20116,20164,20202,20258,20297,23329,23449,23486,23519,23575,23621,23758,23798,23869,23952,24024,24104,24175,24255,24321,24398,24453,24583,24682,24802,24853,24904,25074,25115,25161,25217,25267,25327,25366,25570,25632,25674,25720,25767,25826,25864,25915,25952,25999,26044,26083,26147,26192,26232,26347,26407,26466,26523,26579,26639,26693,26751,26818,26883,26954,27011,27066,27123,27186,27242,27302,27371,27444,27513,27570,27631,27686,27741,27801,27859,27916,27976,28033,28098,28162,28230,28285,28341,28398,28455,28514,28569,28633,28696,28758,28824,28882,28936,28990,29050,29104,29169,29234,29301,29364,29418,29470,29519,29577,29641,29701,29814,29873,29932,29987,30044,30084,30132,30285,30492,30604,30646,30693,30819,30868,30932,31015,31052,31210,31258,31315,31363,31422,31462,31498,31552,31608,31653,31713,31930,31983,32022,32074,32141,32341,32380,32429,32468,32509,32571,32609,32653,32695,32729", "endColumns": "36,46,34,41,53,52,60,75,42,44,55,74,81,75,69,185,64,97,67,186,63,40,40,41,45,54,40,52,36,37,35,48,58,52,37,45,49,36,39,46,37,48,50,36,59,36,45,55,47,38,41,45,54,52,46,54,55,47,43,36,103,48,64,42,42,56,37,46,71,55,65,65,64,44,39,35,35,49,51,48,47,47,82,85,86,85,83,86,43,60,36,51,67,54,72,56,71,55,71,54,71,58,80,60,76,56,72,55,52,53,69,49,54,50,49,51,59,46,37,51,50,48,60,55,68,46,61,58,55,49,57,37,49,48,60,55,66,51,35,46,35,78,62,50,50,59,58,48,42,69,53,66,59,48,47,37,55,38,47,41,36,32,55,45,61,39,70,82,71,79,70,79,65,76,54,64,33,50,50,50,49,40,45,55,49,59,38,55,61,41,45,46,58,37,50,36,46,44,38,63,44,39,35,59,58,56,55,59,53,57,66,64,70,56,54,56,62,55,59,68,72,68,56,60,54,54,59,57,56,59,56,64,63,67,54,55,56,56,58,54,63,62,61,65,57,53,53,59,53,64,64,66,62,53,51,48,57,63,59,44,58,58,54,56,39,47,51,38,37,41,46,57,48,63,82,36,41,47,56,47,58,39,35,53,55,44,59,54,52,38,51,66,44,38,48,38,40,61,37,43,41,33,58", "endOffsets": "2895,2942,2977,3019,3073,3126,3187,3263,3306,3351,3407,3482,3564,3640,3710,3896,3961,4059,4127,4314,4378,4419,4460,4502,4548,4603,4644,4697,4734,4772,4808,4857,4916,4969,5007,5053,5103,5480,5520,5567,6266,6315,6366,6604,6737,6774,6973,7029,7077,7116,7158,7204,7259,7312,7425,7480,7536,7584,7628,7665,7769,7818,7883,7926,7969,8204,8242,8289,8361,8417,8483,8549,8614,8659,8699,8735,8849,8899,8951,9000,9048,9096,9179,9265,9352,9438,9522,9609,9653,9714,9751,10024,10092,10147,10220,10277,10349,10405,10477,10532,10604,10663,10744,10805,10882,10939,11012,11068,11121,11175,11245,11295,11350,11401,11451,11503,11563,11610,11648,11700,11751,11800,11861,11917,11986,12033,12095,12154,12210,12260,12318,12356,12406,12455,12516,12572,12639,12691,12727,12834,13021,13100,13223,13274,13325,13385,13444,13493,13536,13606,19935,20002,20062,20111,20159,20197,20253,20292,20340,23366,23481,23514,23570,23616,23678,23793,23864,23947,24019,24099,24170,24250,24316,24393,24448,24513,24612,24728,24848,24899,24949,25110,25156,25212,25262,25322,25361,25417,25627,25669,25715,25762,25821,25859,25910,25947,25994,26039,26078,26142,26187,26227,26263,26402,26461,26518,26574,26634,26688,26746,26813,26878,26949,27006,27061,27118,27181,27237,27297,27366,27439,27508,27565,27626,27681,27736,27796,27854,27911,27971,28028,28093,28157,28225,28280,28336,28393,28450,28509,28564,28628,28691,28753,28819,28877,28931,28985,29045,29099,29164,29229,29296,29359,29413,29465,29514,29572,29636,29696,29741,29868,29927,29982,30039,30079,30127,30179,30319,30525,30641,30688,30746,30863,30927,31010,31047,31089,31253,31310,31358,31417,31457,31493,31547,31603,31648,31708,31763,31978,32017,32069,32136,32181,32375,32424,32463,32504,32566,32604,32648,32690,32724,32783"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\e8414bc356727da52467a60536d06b50\\transformed\\material3-release\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,159,262,366,468,560,648,752,857,962,1078,1160,1256,1340,1428,1533,1646,1747,1856,1963,2071,2188,2293,2394,2498,2603,2688,2783,2888,2997,3087,3187,3285,3396,3512,3612,3703,3777,3867,3956,4039,4121,4210,4290,4372,4469,4563,4656,4749,4833,4930,5026,5121,5229,5309,5403", "endColumns": "103,102,103,101,91,87,103,104,104,115,81,95,83,87,104,112,100,108,106,107,116,104,100,103,104,84,94,104,108,89,99,97,110,115,99,90,73,89,88,82,81,88,79,81,96,93,92,92,83,96,95,94,107,79,93,91", "endOffsets": "154,257,361,463,555,643,747,852,957,1073,1155,1251,1335,1423,1528,1641,1742,1851,1958,2066,2183,2288,2389,2493,2598,2683,2778,2883,2992,3082,3182,3280,3391,3507,3607,3698,3772,3862,3951,4034,4116,4205,4285,4367,4464,4558,4651,4744,4828,4925,5021,5116,5224,5304,5398,5490"}, "to": {"startLines": "211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "13611,13715,13818,13922,14024,14116,14204,14308,14413,14518,14634,14716,14812,14896,14984,15089,15202,15303,15412,15519,15627,15744,15849,15950,16054,16159,16244,16339,16444,16553,16643,16743,16841,16952,17068,17168,17259,17333,17423,17512,17595,17677,17766,17846,17928,18025,18119,18212,18305,18389,18486,18582,18677,18785,18865,18959", "endColumns": "103,102,103,101,91,87,103,104,104,115,81,95,83,87,104,112,100,108,106,107,116,104,100,103,104,84,94,104,108,89,99,97,110,115,99,90,73,89,88,82,81,88,79,81,96,93,92,92,83,96,95,94,107,79,93,91", "endOffsets": "13710,13813,13917,14019,14111,14199,14303,14408,14513,14629,14711,14807,14891,14979,15084,15197,15298,15407,15514,15622,15739,15844,15945,16049,16154,16239,16334,16439,16548,16638,16738,16836,16947,17063,17163,17254,17328,17418,17507,17590,17672,17761,17841,17923,18020,18114,18207,18300,18384,18481,18577,18672,18780,18860,18954,19046"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\2e72a71af7cea86f1e691bad46c56138\\transformed\\appcompat-1.6.1\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,757,832,924,1018,1115,1211,1306,1400,1496,1588,1680,1772,1850,1946,2041,2136,2233,2329,2427,2577,2671", "endColumns": "94,92,99,81,96,107,76,74,91,93,96,95,94,93,95,91,91,91,77,95,94,94,96,95,97,149,93,78", "endOffsets": "195,288,388,470,567,675,752,827,919,1013,1110,1206,1301,1395,1491,1583,1675,1767,1845,1941,2036,2131,2228,2324,2422,2572,2666,2745"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,383", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "297,392,485,585,667,764,872,949,1024,1116,1210,1307,1403,1498,1592,1688,1780,1872,1964,2042,2138,2233,2328,2425,2521,2619,2769,26268", "endColumns": "94,92,99,81,96,107,76,74,91,93,96,95,94,93,95,91,91,91,77,95,94,94,96,95,97,149,93,78", "endOffsets": "387,480,580,662,759,867,944,1019,1111,1205,1302,1398,1493,1587,1683,1775,1867,1959,2037,2133,2228,2323,2420,2516,2614,2764,2858,26342"}}]}]}