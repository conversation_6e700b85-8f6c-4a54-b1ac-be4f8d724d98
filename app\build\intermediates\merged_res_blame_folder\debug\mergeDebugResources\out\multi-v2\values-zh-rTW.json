{"logs": [{"outputFile": "com.timeflow.app-mergeDebugResources-85:/values-zh-rTW/values-zh-rTW.xml", "map": [{"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\443a6cfc0de72abe8b7a8ef4c7b7a3e3\\transformed\\core-1.12.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,246,340,434,527,620,716", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "142,241,335,429,522,615,711,812"}, "to": {"startLines": "72,73,74,75,76,77,78,409", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "5280,5372,5471,5565,5659,5752,5845,27992", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "5367,5466,5560,5654,5747,5840,5936,28088"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\6e1bec7b61f7c5e14c57eb6146ea2199\\transformed\\ui-release\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,182,258,345,436,514,588,665,743,818,883,948,1021,1096,1164,1238,1306", "endColumns": "76,75,86,90,77,73,76,77,74,64,64,72,74,67,73,67,115", "endOffsets": "177,253,340,431,509,583,660,738,813,878,943,1016,1091,1159,1233,1301,1417"}, "to": {"startLines": "88,89,103,104,113,173,174,304,308,320,322,335,336,404,413,416,420", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6487,6564,7307,7394,7909,11488,11562,21843,22053,22888,22987,23741,23814,27725,28338,28517,28713", "endColumns": "76,75,86,90,77,73,76,77,74,64,64,72,74,67,73,67,115", "endOffsets": "6559,6635,7389,7480,7982,11557,11634,21916,22123,22948,23047,23809,23884,27788,28407,28580,28824"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\5619827a7b7a666ff443af1afbf85287\\transformed\\preference-1.2.1\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,5,6,7,8", "startColumns": "4,4,4,4,4,4", "startOffsets": "105,171,322,442,610,689", "endColumns": "65,80,119,167,78,75", "endOffsets": "166,247,437,605,684,760"}, "to": {"startLines": "95,130,327,411,439,440", "startColumns": "4,4,4,4,4,4", "startOffsets": "6889,8987,23273,28132,29805,29884", "endColumns": "65,80,119,167,78,75", "endOffsets": "6950,9063,23388,28295,29879,29955"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\8502c7d6e294f2825540546f57fbfcc0\\transformed\\material-1.11.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,247,311,373,440,510,587,681,788,861,923,1001,1061,1121,1199,1260,1318,1374,1434,1492,1546,1631,1687,1745,1799,1864,1956,2030,2107,2227,2290,2353,2452,2529,2603,2653,2704,2770,2833,2901,2979,3050,3111,3182,3249,3311,3398,3477,3542,3625,3710,3784,3848,3924,3972,4045,4109,4185,4263,4325,4389,4452,4518,4598,4676,4752,4831,4885,4940", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,63,61,66,69,76,93,106,72,61,77,59,59,77,60,57,55,59,57,53,84,55,57,53,64,91,73,76,119,62,62,98,76,73,49,50,65,62,67,77,70,60,70,66,61,86,78,64,82,84,73,63,75,47,72,63,75,77,61,63,62,65,79,77,75,78,53,54,68", "endOffsets": "242,306,368,435,505,582,676,783,856,918,996,1056,1116,1194,1255,1313,1369,1429,1487,1541,1626,1682,1740,1794,1859,1951,2025,2102,2222,2285,2348,2447,2524,2598,2648,2699,2765,2828,2896,2974,3045,3106,3177,3244,3306,3393,3472,3537,3620,3705,3779,3843,3919,3967,4040,4104,4180,4258,4320,4384,4447,4513,4593,4671,4747,4826,4880,4935,5004"}, "to": {"startLines": "2,65,66,67,68,69,82,83,85,129,131,171,176,240,241,242,243,244,245,246,247,248,249,250,251,252,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,323", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,4853,4917,4979,5046,5116,6079,6173,6317,8925,9068,11381,11675,17562,17640,17701,17759,17815,17875,17933,17987,18072,18128,18186,18240,18305,18817,18891,18968,19088,19151,19214,19313,19390,19464,19514,19565,19631,19694,19762,19840,19911,19972,20043,20110,20172,20259,20338,20403,20486,20571,20645,20709,20785,20833,20906,20970,21046,21124,21186,21250,21313,21379,21459,21537,21613,21692,21746,23052", "endLines": "5,65,66,67,68,69,82,83,85,129,131,171,176,240,241,242,243,244,245,246,247,248,249,250,251,252,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,323", "endColumns": "12,63,61,66,69,76,93,106,72,61,77,59,59,77,60,57,55,59,57,53,84,55,57,53,64,91,73,76,119,62,62,98,76,73,49,50,65,62,67,77,70,60,70,66,61,86,78,64,82,84,73,63,75,47,72,63,75,77,61,63,62,65,79,77,75,78,53,54,68", "endOffsets": "292,4912,4974,5041,5111,5188,6168,6275,6385,8982,9141,11436,11730,17635,17696,17754,17810,17870,17928,17982,18067,18123,18181,18235,18300,18392,18886,18963,19083,19146,19209,19308,19385,19459,19509,19560,19626,19689,19757,19835,19906,19967,20038,20105,20167,20254,20333,20398,20481,20566,20640,20704,20780,20828,20901,20965,21041,21119,21181,21245,21308,21374,21454,21532,21608,21687,21741,21796,23116"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\71256635af31e1aec955d3a558d276eb\\transformed\\foundation-release\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,138", "endColumns": "82,78", "endOffsets": "133,212"}, "to": {"startLines": "432,433", "startColumns": "4,4", "startOffsets": "29387,29470", "endColumns": "82,78", "endOffsets": "29465,29544"}}, {"source": "D:\\development\\Codes\\MyApplication\\app\\src\\main\\res\\values-zh-rTW\\strings.xml", "from": {"startLines": "70,285,81,302,14,127,128,15,283,284,242,243,241,244,236,235,238,237,234,233,7,53,54,2,67,85,261,87,45,46,47,288,10,26,73,259,257,83,263,86,36,37,74,150,151,305,38,28,25,281,282,27,295,80,116,115,117,118,8,78,79,275,276,273,272,274,155,157,156,154,158,159,58,129,92,306,307,314,315,312,313,316,317,318,319,320,321,310,311,308,309,100,98,99,132,101,103,105,107,104,106,21,90,174,175,55,110,112,109,111,113,139,66,3,290,95,60,59,52,252,266,91,262,33,35,34,18,19,135,143,267,271,88,77,301,296,162,163,166,167,168,169,164,165,68,253,75,22,24,20,89,39,40,69,254,9,29,147,268,293,294,82,258,84,62,61,289,140,72,205,226,230,214,212,216,178,179,181,180,203,227,222,229,219,196,198,197,199,208,202,215,220,225,213,209,224,192,193,195,194,228,217,206,223,221,187,188,189,191,190,204,210,211,218,182,183,184,185,186,207,247,41,42,126,65,23,56,93,141,279,280,172,4,5,43,44,32,251,138,6,120,121,173,123,122,57,136,48,49,260,71,300,94,137,144,299,11,76,248", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3030,13306,3430,13899,474,5346,5399,528,13205,13250,11483,11558,11407,11640,11034,10969,11288,11220,10782,10718,247,2258,2304,55,2872,3577,12325,3651,1904,1953,2012,13375,367,984,3142,12231,12129,3503,12433,3614,1432,1478,3180,6106,6161,13962,1534,1074,940,13091,13140,1031,13626,3392,4853,4787,4909,4975,288,3320,3356,12863,12913,12766,12718,12815,6322,6492,6405,6236,6578,6662,2511,5460,3846,14009,14061,14526,14581,14397,14454,14654,14710,14782,14837,14909,14968,14259,14320,14129,14186,4125,4018,4071,5542,4181,4232,4339,4450,4287,4390,743,3765,7481,7533,2359,4557,4669,4501,4619,4728,5791,2822,96,13452,3961,2606,2555,2207,11923,12512,3803,12363,1251,1365,1305,596,645,5633,5908,12561,12676,3687,3287,13837,13683,6768,6839,7065,7137,7217,7288,6922,6988,2925,11983,3219,790,889,693,3724,1590,1636,2980,12048,328,1122,6045,12609,13520,13567,3465,12180,3540,2714,2669,13413,5829,3106,9212,10408,10640,9723,9605,9834,7605,7663,7801,7730,9097,10467,10174,10577,10004,8744,8877,8804,8946,9383,9036,9779,10060,10348,9665,9440,10288,8490,8547,8680,8612,10522,9888,9272,10231,10115,8180,8235,8299,8428,8362,9154,9497,9551,9944,7866,7920,7985,8050,8117,9329,11729,1692,1741,5287,2782,841,2406,3883,5869,12986,13033,7387,132,169,1799,1847,1203,11864,5751,211,5041,5095,7436,5206,5151,2458,5671,2065,2117,12280,3067,13788,3922,5710,5964,13744,407,3253,11781", "endColumns": "36,46,34,41,53,52,60,42,44,55,74,81,75,69,185,64,97,67,186,63,40,45,54,40,52,36,37,35,48,58,52,37,39,46,37,48,50,36,59,36,45,55,38,54,52,46,55,47,43,48,64,42,56,37,55,65,65,64,39,35,35,49,51,48,47,47,82,85,86,85,83,86,43,60,36,51,67,54,72,56,71,55,71,54,71,58,80,60,76,56,72,55,52,53,69,49,54,50,49,51,59,46,37,51,50,46,61,58,55,49,57,37,49,35,46,35,62,50,50,59,48,42,69,53,66,59,48,47,37,55,47,41,36,32,61,39,70,82,71,79,70,79,65,76,54,64,33,50,50,49,40,45,55,49,59,38,55,41,45,46,58,37,50,36,46,44,38,39,35,59,58,56,55,59,53,57,66,64,70,56,54,56,62,55,59,68,72,68,56,60,54,54,59,57,56,59,56,64,63,67,54,55,56,56,58,54,63,62,61,65,57,53,53,59,53,64,64,66,62,53,51,48,57,58,39,47,51,38,37,46,57,48,36,41,47,56,47,58,39,35,53,55,44,59,54,52,38,51,66,44,38,48,38,40,61,43,41,33,58", "endOffsets": "3062,13348,3460,13936,523,5394,5455,566,13245,13301,11553,11635,11478,11705,11215,11029,11381,11283,10964,10777,283,2299,2354,91,2920,3609,12358,3682,1948,2007,2060,13408,402,1026,3175,12275,12175,3535,12488,3646,1473,1529,3214,6156,6209,14004,1585,1117,979,13135,13200,1069,13678,3425,4904,4848,4970,5035,323,3351,3387,12908,12960,12810,12761,12858,6400,6573,6487,6317,6657,6744,2550,5516,3878,14056,14124,14576,14649,14449,14521,14705,14777,14832,14904,14963,15044,14315,14392,14181,14254,4176,4066,4120,5607,4226,4282,4385,4495,4334,4445,785,3798,7528,7579,2401,4614,4723,4552,4664,4781,5824,2867,127,13494,3992,2664,2601,2253,11978,12556,3841,12428,1300,1427,1360,640,688,5666,5959,12604,12713,3719,3315,13894,13718,6834,6917,7132,7212,7283,7363,6983,7060,2975,12043,3248,836,935,738,3760,1631,1687,3025,12103,362,1173,6082,12650,13562,13621,3498,12226,3572,2756,2709,13447,5864,3137,9267,10462,10692,9774,9660,9883,7658,7725,7861,7796,9149,10517,10226,10635,10055,8799,8941,8872,9010,9435,9092,9829,10110,10403,9718,9492,10343,8542,8607,8739,8675,10572,9939,9324,10283,10169,8230,8294,8357,8485,8423,9207,9546,9600,9999,7915,7980,8045,8112,8175,9378,11776,1736,1794,5341,2817,884,2453,3917,5902,13028,13086,7431,164,206,1842,1899,1246,11918,5786,242,5090,5146,7476,5261,5201,2506,5705,2112,2179,12320,3101,13832,3956,5746,6021,13783,444,3282,11835"}, "to": {"startLines": "33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,70,71,79,80,81,84,86,87,90,91,92,93,94,96,97,98,99,100,101,102,105,106,107,108,109,110,111,112,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,172,175,177,178,179,180,181,182,183,253,254,255,256,257,258,259,260,303,305,306,307,309,310,311,312,313,314,315,316,317,318,319,321,324,325,326,328,329,330,331,332,333,334,337,338,339,340,341,342,343,344,345,346,347,348,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,405,406,407,408,410,412,414,415,417,418,419,421,422,423,424,425,426,427,428,429,430,431,434,435,436,437,438,441,442,443,444,445,446,447,448,449", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2863,2900,2947,2982,3024,3078,3131,3192,3235,3280,3336,3411,3493,3569,3639,3825,3890,3988,4056,4243,4307,4348,4394,4449,4490,4543,4580,4618,4654,4703,4762,4815,5193,5233,5941,5979,6028,6280,6390,6450,6640,6686,6742,6781,6836,6955,7002,7058,7106,7150,7199,7264,7485,7542,7580,7636,7702,7768,7833,7873,7987,8023,8073,8125,8174,8222,8270,8353,8439,8526,8612,8696,8783,8827,8888,9146,9198,9266,9321,9394,9451,9523,9579,9651,9706,9778,9837,9918,9979,10056,10113,10186,10242,10295,10349,10419,10469,10524,10575,10625,10677,10737,10784,10822,10874,10925,10972,11034,11093,11149,11199,11257,11295,11345,11441,11639,11735,11798,11849,11900,11960,12009,12052,18397,18451,18518,18578,18627,18675,18713,18769,21801,21921,21958,21991,22128,22168,22239,22322,22394,22474,22545,22625,22691,22768,22823,22953,23121,23172,23223,23393,23434,23480,23536,23586,23646,23685,23889,23931,23977,24024,24083,24121,24172,24209,24256,24301,24340,24380,24495,24555,24614,24671,24727,24787,24841,24899,24966,25031,25102,25159,25214,25271,25334,25390,25450,25519,25592,25661,25718,25779,25834,25889,25949,26007,26064,26124,26181,26246,26310,26378,26433,26489,26546,26603,26662,26717,26781,26844,26906,26972,27030,27084,27138,27198,27252,27317,27382,27449,27512,27566,27618,27667,27793,27852,27892,27940,28093,28300,28412,28459,28585,28634,28671,28829,28877,28934,28982,29041,29081,29117,29171,29227,29272,29332,29549,29602,29641,29693,29760,29960,29999,30048,30087,30128,30190,30234,30276,30310", "endColumns": "36,46,34,41,53,52,60,42,44,55,74,81,75,69,185,64,97,67,186,63,40,45,54,40,52,36,37,35,48,58,52,37,39,46,37,48,50,36,59,36,45,55,38,54,52,46,55,47,43,48,64,42,56,37,55,65,65,64,39,35,35,49,51,48,47,47,82,85,86,85,83,86,43,60,36,51,67,54,72,56,71,55,71,54,71,58,80,60,76,56,72,55,52,53,69,49,54,50,49,51,59,46,37,51,50,46,61,58,55,49,57,37,49,35,46,35,62,50,50,59,48,42,69,53,66,59,48,47,37,55,47,41,36,32,61,39,70,82,71,79,70,79,65,76,54,64,33,50,50,49,40,45,55,49,59,38,55,41,45,46,58,37,50,36,46,44,38,39,35,59,58,56,55,59,53,57,66,64,70,56,54,56,62,55,59,68,72,68,56,60,54,54,59,57,56,59,56,64,63,67,54,55,56,56,58,54,63,62,61,65,57,53,53,59,53,64,64,66,62,53,51,48,57,58,39,47,51,38,37,46,57,48,36,41,47,56,47,58,39,35,53,55,44,59,54,52,38,51,66,44,38,48,38,40,61,43,41,33,58", "endOffsets": "2895,2942,2977,3019,3073,3126,3187,3230,3275,3331,3406,3488,3564,3634,3820,3885,3983,4051,4238,4302,4343,4389,4444,4485,4538,4575,4613,4649,4698,4757,4810,4848,5228,5275,5974,6023,6074,6312,6445,6482,6681,6737,6776,6831,6884,6997,7053,7101,7145,7194,7259,7302,7537,7575,7631,7697,7763,7828,7868,7904,8018,8068,8120,8169,8217,8265,8348,8434,8521,8607,8691,8778,8822,8883,8920,9193,9261,9316,9389,9446,9518,9574,9646,9701,9773,9832,9913,9974,10051,10108,10181,10237,10290,10344,10414,10464,10519,10570,10620,10672,10732,10779,10817,10869,10920,10967,11029,11088,11144,11194,11252,11290,11340,11376,11483,11670,11793,11844,11895,11955,12004,12047,12117,18446,18513,18573,18622,18670,18708,18764,18812,21838,21953,21986,22048,22163,22234,22317,22389,22469,22540,22620,22686,22763,22818,22883,22982,23167,23218,23268,23429,23475,23531,23581,23641,23680,23736,23926,23972,24019,24078,24116,24167,24204,24251,24296,24335,24375,24411,24550,24609,24666,24722,24782,24836,24894,24961,25026,25097,25154,25209,25266,25329,25385,25445,25514,25587,25656,25713,25774,25829,25884,25944,26002,26059,26119,26176,26241,26305,26373,26428,26484,26541,26598,26657,26712,26776,26839,26901,26967,27025,27079,27133,27193,27247,27312,27377,27444,27507,27561,27613,27662,27720,27847,27887,27935,27987,28127,28333,28454,28512,28629,28666,28708,28872,28929,28977,29036,29076,29112,29166,29222,29267,29327,29382,29597,29636,29688,29755,29800,29994,30043,30082,30123,30185,30229,30271,30305,30364"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\e8414bc356727da52467a60536d06b50\\transformed\\material3-release\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,159,262,366,468,560,648,752,857,962,1078,1160,1256,1340,1428,1533,1646,1747,1856,1963,2071,2188,2293,2394,2498,2603,2688,2783,2888,2997,3087,3187,3285,3396,3512,3612,3703,3777,3867,3956,4039,4121,4210,4290,4372,4469,4563,4656,4749,4833,4930,5026,5121,5229,5309,5403", "endColumns": "103,102,103,101,91,87,103,104,104,115,81,95,83,87,104,112,100,108,106,107,116,104,100,103,104,84,94,104,108,89,99,97,110,115,99,90,73,89,88,82,81,88,79,81,96,93,92,92,83,96,95,94,107,79,93,91", "endOffsets": "154,257,361,463,555,643,747,852,957,1073,1155,1251,1335,1423,1528,1641,1742,1851,1958,2066,2183,2288,2389,2493,2598,2683,2778,2883,2992,3082,3182,3280,3391,3507,3607,3698,3772,3862,3951,4034,4116,4205,4285,4367,4464,4558,4651,4744,4828,4925,5021,5116,5224,5304,5398,5490"}, "to": {"startLines": "184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12122,12226,12329,12433,12535,12627,12715,12819,12924,13029,13145,13227,13323,13407,13495,13600,13713,13814,13923,14030,14138,14255,14360,14461,14565,14670,14755,14850,14955,15064,15154,15254,15352,15463,15579,15679,15770,15844,15934,16023,16106,16188,16277,16357,16439,16536,16630,16723,16816,16900,16997,17093,17188,17296,17376,17470", "endColumns": "103,102,103,101,91,87,103,104,104,115,81,95,83,87,104,112,100,108,106,107,116,104,100,103,104,84,94,104,108,89,99,97,110,115,99,90,73,89,88,82,81,88,79,81,96,93,92,92,83,96,95,94,107,79,93,91", "endOffsets": "12221,12324,12428,12530,12622,12710,12814,12919,13024,13140,13222,13318,13402,13490,13595,13708,13809,13918,14025,14133,14250,14355,14456,14560,14665,14750,14845,14950,15059,15149,15249,15347,15458,15574,15674,15765,15839,15929,16018,16101,16183,16272,16352,16434,16531,16625,16718,16811,16895,16992,17088,17183,17291,17371,17465,17557"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\2e72a71af7cea86f1e691bad46c56138\\transformed\\appcompat-1.6.1\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,757,832,924,1018,1115,1211,1306,1400,1496,1588,1680,1772,1850,1946,2041,2136,2233,2329,2427,2577,2671", "endColumns": "94,92,99,81,96,107,76,74,91,93,96,95,94,93,95,91,91,91,77,95,94,94,96,95,97,149,93,78", "endOffsets": "195,288,388,470,567,675,752,827,919,1013,1110,1206,1301,1395,1491,1583,1675,1767,1845,1941,2036,2131,2228,2324,2422,2572,2666,2745"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,349", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "297,392,485,585,667,764,872,949,1024,1116,1210,1307,1403,1498,1592,1688,1780,1872,1964,2042,2138,2233,2328,2425,2521,2619,2769,24416", "endColumns": "94,92,99,81,96,107,76,74,91,93,96,95,94,93,95,91,91,91,77,95,94,94,96,95,97,149,93,78", "endOffsets": "387,480,580,662,759,867,944,1019,1111,1205,1302,1398,1493,1587,1683,1775,1867,1959,2037,2133,2228,2323,2420,2516,2614,2764,2858,24490"}}]}]}