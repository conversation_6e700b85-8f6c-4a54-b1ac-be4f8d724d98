{"logs": [{"outputFile": "com.timeflow.app-mergeDebugResources-85:/values-zh-rTW/values-zh-rTW.xml", "map": [{"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\443a6cfc0de72abe8b7a8ef4c7b7a3e3\\transformed\\core-1.12.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,246,340,434,527,620,716", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "142,241,335,429,522,615,711,812"}, "to": {"startLines": "74,75,76,77,78,79,80,436", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "5397,5489,5588,5682,5776,5869,5962,29484", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "5484,5583,5677,5771,5864,5957,6053,29580"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\6e1bec7b61f7c5e14c57eb6146ea2199\\transformed\\ui-release\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,182,258,345,436,514,588,665,743,818,883,948,1021,1096,1164,1238,1306", "endColumns": "76,75,86,90,77,73,76,77,74,64,64,72,74,67,73,67,115", "endOffsets": "177,253,340,431,509,583,660,738,813,878,943,1016,1091,1159,1233,1301,1417"}, "to": {"startLines": "90,91,109,110,120,189,190,322,327,339,341,354,355,428,440,444,450", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6604,6681,7603,7690,8250,12349,12423,22842,23098,23933,24032,24786,24859,29046,29830,30051,30394", "endColumns": "76,75,86,90,77,73,76,77,74,64,64,72,74,67,73,67,115", "endOffsets": "6676,6752,7685,7776,8323,12418,12495,22915,23168,23993,24092,24854,24929,29109,29899,30114,30505"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\5619827a7b7a666ff443af1afbf85287\\transformed\\preference-1.2.1\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,5,6,7,8", "startColumns": "4,4,4,4,4,4", "startOffsets": "105,171,322,442,610,689", "endColumns": "65,80,119,167,78,75", "endOffsets": "166,247,437,605,684,760"}, "to": {"startLines": "100,137,346,438,469,470", "startColumns": "4,4,4,4,4,4", "startOffsets": "7142,9328,24318,29624,31486,31565", "endColumns": "65,80,119,167,78,75", "endOffsets": "7203,9404,24433,29787,31560,31636"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\8502c7d6e294f2825540546f57fbfcc0\\transformed\\material-1.11.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,247,311,373,440,510,587,681,788,861,923,1001,1061,1121,1199,1260,1318,1374,1434,1492,1546,1631,1687,1745,1799,1864,1956,2030,2107,2227,2290,2353,2452,2529,2603,2653,2704,2770,2833,2901,2979,3050,3111,3182,3249,3311,3398,3477,3542,3625,3710,3784,3848,3924,3972,4045,4109,4185,4263,4325,4389,4452,4518,4598,4676,4752,4831,4885,4940", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,63,61,66,69,76,93,106,72,61,77,59,59,77,60,57,55,59,57,53,84,55,57,53,64,91,73,76,119,62,62,98,76,73,49,50,65,62,67,77,70,60,70,66,61,86,78,64,82,84,73,63,75,47,72,63,75,77,61,63,62,65,79,77,75,78,53,54,68", "endOffsets": "242,306,368,435,505,582,676,783,856,918,996,1056,1116,1194,1255,1313,1369,1429,1487,1541,1626,1682,1740,1794,1859,1951,2025,2102,2222,2285,2348,2447,2524,2598,2648,2699,2765,2828,2896,2974,3045,3106,3177,3244,3306,3393,3472,3537,3620,3705,3779,3843,3919,3967,4040,4104,4180,4258,4320,4384,4447,4513,4593,4671,4747,4826,4880,4935,5004"}, "to": {"startLines": "2,67,68,69,70,71,84,85,87,136,138,187,193,258,259,260,261,262,263,264,265,266,267,268,269,270,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,342", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,4970,5034,5096,5163,5233,6196,6290,6434,9266,9409,12242,12615,18561,18639,18700,18758,18814,18874,18932,18986,19071,19127,19185,19239,19304,19816,19890,19967,20087,20150,20213,20312,20389,20463,20513,20564,20630,20693,20761,20839,20910,20971,21042,21109,21171,21258,21337,21402,21485,21570,21644,21708,21784,21832,21905,21969,22045,22123,22185,22249,22312,22378,22458,22536,22612,22691,22745,24097", "endLines": "5,67,68,69,70,71,84,85,87,136,138,187,193,258,259,260,261,262,263,264,265,266,267,268,269,270,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,342", "endColumns": "12,63,61,66,69,76,93,106,72,61,77,59,59,77,60,57,55,59,57,53,84,55,57,53,64,91,73,76,119,62,62,98,76,73,49,50,65,62,67,77,70,60,70,66,61,86,78,64,82,84,73,63,75,47,72,63,75,77,61,63,62,65,79,77,75,78,53,54,68", "endOffsets": "292,5029,5091,5158,5228,5305,6285,6392,6502,9323,9482,12297,12670,18634,18695,18753,18809,18869,18927,18981,19066,19122,19180,19234,19299,19391,19885,19962,20082,20145,20208,20307,20384,20458,20508,20559,20625,20688,20756,20834,20905,20966,21037,21104,21166,21253,21332,21397,21480,21565,21639,21703,21779,21827,21900,21964,22040,22118,22180,22244,22307,22373,22453,22531,22607,22686,22740,22795,24161"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\71256635af31e1aec955d3a558d276eb\\transformed\\foundation-release\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,138", "endColumns": "82,78", "endOffsets": "133,212"}, "to": {"startLines": "462,463", "startColumns": "4,4", "startOffsets": "31068,31151", "endColumns": "82,78", "endOffsets": "31146,31225"}}, {"source": "D:\\development\\Codes\\MyApplication\\app\\src\\main\\res\\values-zh-rTW\\strings.xml", "from": {"startLines": "70,285,81,302,14,127,128,330,15,283,284,242,243,241,244,236,235,238,237,234,233,334,7,53,54,2,67,85,261,87,45,46,47,288,10,26,73,259,257,83,263,86,36,37,333,74,357,358,150,151,305,38,28,25,281,282,356,27,295,80,116,115,117,118,359,8,78,79,275,276,273,272,274,155,157,156,154,158,159,58,129,92,306,307,314,315,312,313,316,317,318,319,320,321,310,311,308,309,100,98,99,132,101,103,105,107,104,106,21,90,174,175,346,347,348,349,55,110,112,109,111,113,139,66,342,343,344,345,324,3,290,95,325,60,59,52,252,360,266,91,262,33,35,34,18,19,135,143,267,271,88,77,335,301,296,162,163,166,167,168,169,164,165,68,253,75,22,24,20,89,39,40,69,254,9,29,336,147,268,293,294,82,258,84,62,61,289,339,140,72,205,226,230,214,212,216,178,179,181,180,203,227,222,229,219,196,198,197,199,208,202,215,220,225,213,209,224,192,193,195,194,228,217,206,223,221,187,188,189,191,190,204,210,211,218,182,183,184,185,186,207,247,41,42,328,329,353,126,350,352,351,65,23,56,93,141,361,279,280,172,340,341,4,5,43,44,32,251,138,6,120,121,173,123,122,57,136,48,49,260,71,300,94,137,144,299,11,76,248", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3030,13306,3430,13899,474,5346,5399,15344,528,13205,13250,11483,11558,11407,11640,11034,10969,11288,11220,10782,10718,15487,247,2258,2304,55,2872,3577,12325,3651,1904,1953,2012,13375,367,984,3142,12231,12129,3503,12433,3614,1432,1478,15439,3180,16593,16635,6106,6161,13962,1534,1074,940,13091,13140,16550,1031,13626,3392,4853,4787,4909,4975,16681,288,3320,3356,12863,12913,12766,12718,12815,6322,6492,6405,6236,6578,6662,2511,5460,3846,14009,14061,14526,14581,14397,14454,14654,14710,14782,14837,14909,14968,14259,14320,14129,14186,4125,4018,4071,5542,4181,4232,4339,4450,4287,4390,743,3765,7481,7533,16080,16129,16190,16246,2359,4557,4669,4501,4619,4728,5791,2822,15847,15896,15957,16013,15070,96,13452,3961,15122,2606,2555,2207,11923,16726,12512,3803,12363,1251,1365,1305,596,645,5633,5908,12561,12676,3687,3287,15528,13837,13683,6768,6839,7065,7137,7217,7288,6922,6988,2925,11983,3219,790,889,693,3724,1590,1636,2980,12048,328,1122,15574,6045,12609,13520,13567,3465,12180,3540,2714,2669,13413,15655,5829,3106,9212,10408,10640,9723,9605,9834,7605,7663,7801,7730,9097,10467,10174,10577,10004,8744,8877,8804,8946,9383,9036,9779,10060,10348,9665,9440,10288,8490,8547,8680,8612,10522,9888,9272,10231,10115,8180,8235,8299,8428,8362,9154,9497,9551,9944,7866,7920,7985,8050,8117,9329,11729,1692,1741,15220,15284,16486,5287,16315,16431,16374,2782,841,2406,3883,5869,16785,12986,13033,7387,15700,15764,132,169,1799,1847,1203,11864,5751,211,5041,5095,7436,5206,5151,2458,5671,2065,2117,12280,3067,13788,3922,5710,5964,13744,407,3253,11781", "endColumns": "36,46,34,41,53,52,60,75,42,44,55,74,81,75,69,185,64,97,67,186,63,40,40,45,54,40,52,36,37,35,48,58,52,37,39,46,37,48,50,36,59,36,45,55,47,38,41,45,54,52,46,55,47,43,48,64,42,42,56,37,55,65,65,64,44,39,35,35,49,51,48,47,47,82,85,86,85,83,86,43,60,36,51,67,54,72,56,71,55,71,54,71,58,80,60,76,56,72,55,52,53,69,49,54,50,49,51,59,46,37,51,50,48,60,55,68,46,61,58,55,49,57,37,49,48,60,55,66,51,35,46,35,78,62,50,50,59,58,48,42,69,53,66,59,48,47,37,55,47,41,36,32,45,61,39,70,82,71,79,70,79,65,76,54,64,33,50,50,49,40,45,55,49,59,38,55,61,41,45,46,58,37,50,36,46,44,38,44,39,35,59,58,56,55,59,53,57,66,64,70,56,54,56,62,55,59,68,72,68,56,60,54,54,59,57,56,59,56,64,63,67,54,55,56,56,58,54,63,62,61,65,57,53,53,59,53,64,64,66,62,53,51,48,57,63,59,44,58,58,54,56,39,47,51,38,37,41,46,57,48,63,82,36,41,47,56,47,58,39,35,53,55,44,59,54,52,38,51,66,44,38,48,38,40,61,43,41,33,58", "endOffsets": "3062,13348,3460,13936,523,5394,5455,15415,566,13245,13301,11553,11635,11478,11705,11215,11029,11381,11283,10964,10777,15523,283,2299,2354,91,2920,3609,12358,3682,1948,2007,2060,13408,402,1026,3175,12275,12175,3535,12488,3646,1473,1529,15482,3214,16630,16676,6156,6209,14004,1585,1117,979,13135,13200,16588,1069,13678,3425,4904,4848,4970,5035,16721,323,3351,3387,12908,12960,12810,12761,12858,6400,6573,6487,6317,6657,6744,2550,5516,3878,14056,14124,14576,14649,14449,14521,14705,14777,14832,14904,14963,15044,14315,14392,14181,14254,4176,4066,4120,5607,4226,4282,4385,4495,4334,4445,785,3798,7528,7579,16124,16185,16241,16310,2401,4614,4723,4552,4664,4781,5824,2867,15891,15952,16008,16075,15117,127,13494,3992,15196,2664,2601,2253,11978,16780,12556,3841,12428,1300,1427,1360,640,688,5666,5959,12604,12713,3719,3315,15569,13894,13718,6834,6917,7132,7212,7283,7363,6983,7060,2975,12043,3248,836,935,738,3760,1631,1687,3025,12103,362,1173,15631,6082,12650,13562,13621,3498,12226,3572,2756,2709,13447,15695,5864,3137,9267,10462,10692,9774,9660,9883,7658,7725,7861,7796,9149,10517,10226,10635,10055,8799,8941,8872,9010,9435,9092,9829,10110,10403,9718,9492,10343,8542,8607,8739,8675,10572,9939,9324,10283,10169,8230,8294,8357,8485,8423,9207,9546,9600,9999,7915,7980,8045,8112,8175,9378,11776,1736,1794,15279,15339,16526,5341,16369,16481,16426,2817,884,2453,3917,5902,16822,13028,13086,7431,15759,15842,164,206,1842,1899,1246,11918,5786,242,5090,5146,7476,5261,5201,2506,5705,2112,2179,12320,3101,13832,3956,5746,6021,13783,444,3282,11835"}, "to": {"startLines": "33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,72,73,81,82,83,86,88,89,92,93,94,95,96,97,98,99,101,102,103,104,105,106,107,108,111,112,113,114,115,116,117,118,119,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,188,191,192,194,195,196,197,198,199,200,201,271,272,273,274,275,276,277,278,321,323,324,325,326,328,329,330,331,332,333,334,335,336,337,338,340,343,344,345,347,348,349,350,351,352,353,356,357,358,359,360,361,362,363,364,365,366,367,368,369,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,429,430,431,432,433,434,435,437,439,441,442,443,445,446,447,448,449,451,452,453,454,455,456,457,458,459,460,461,464,465,466,467,468,471,472,473,474,475,476,477,478,479", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2863,2900,2947,2982,3024,3078,3131,3192,3268,3311,3356,3412,3487,3569,3645,3715,3901,3966,4064,4132,4319,4383,4424,4465,4511,4566,4607,4660,4697,4735,4771,4820,4879,4932,5310,5350,6058,6096,6145,6397,6507,6567,6757,6803,6859,6907,6946,6988,7034,7089,7208,7255,7311,7359,7403,7452,7517,7560,7781,7838,7876,7932,7998,8064,8129,8174,8214,8328,8364,8414,8466,8515,8563,8611,8694,8780,8867,8953,9037,9124,9168,9229,9487,9539,9607,9662,9735,9792,9864,9920,9992,10047,10119,10178,10259,10320,10397,10454,10527,10583,10636,10690,10760,10810,10865,10916,10966,11018,11078,11125,11163,11215,11266,11315,11376,11432,11501,11548,11610,11669,11725,11775,11833,11871,11921,11970,12031,12087,12154,12206,12302,12500,12536,12675,12738,12789,12840,12900,12959,13008,13051,19396,19450,19517,19577,19626,19674,19712,19768,22800,22920,22957,22990,23036,23173,23213,23284,23367,23439,23519,23590,23670,23736,23813,23868,23998,24166,24217,24268,24438,24479,24525,24581,24631,24691,24730,24934,24996,25038,25084,25131,25190,25228,25279,25316,25363,25408,25447,25492,25532,25647,25707,25766,25823,25879,25939,25993,26051,26118,26183,26254,26311,26366,26423,26486,26542,26602,26671,26744,26813,26870,26931,26986,27041,27101,27159,27216,27276,27333,27398,27462,27530,27585,27641,27698,27755,27814,27869,27933,27996,28058,28124,28182,28236,28290,28350,28404,28469,28534,28601,28664,28718,28770,28819,28877,28941,29001,29114,29173,29232,29287,29344,29384,29432,29585,29792,29904,29946,29993,30119,30168,30232,30315,30352,30510,30558,30615,30663,30722,30762,30798,30852,30908,30953,31013,31230,31283,31322,31374,31441,31641,31680,31729,31768,31809,31871,31915,31957,31991", "endColumns": "36,46,34,41,53,52,60,75,42,44,55,74,81,75,69,185,64,97,67,186,63,40,40,45,54,40,52,36,37,35,48,58,52,37,39,46,37,48,50,36,59,36,45,55,47,38,41,45,54,52,46,55,47,43,48,64,42,42,56,37,55,65,65,64,44,39,35,35,49,51,48,47,47,82,85,86,85,83,86,43,60,36,51,67,54,72,56,71,55,71,54,71,58,80,60,76,56,72,55,52,53,69,49,54,50,49,51,59,46,37,51,50,48,60,55,68,46,61,58,55,49,57,37,49,48,60,55,66,51,35,46,35,78,62,50,50,59,58,48,42,69,53,66,59,48,47,37,55,47,41,36,32,45,61,39,70,82,71,79,70,79,65,76,54,64,33,50,50,49,40,45,55,49,59,38,55,61,41,45,46,58,37,50,36,46,44,38,44,39,35,59,58,56,55,59,53,57,66,64,70,56,54,56,62,55,59,68,72,68,56,60,54,54,59,57,56,59,56,64,63,67,54,55,56,56,58,54,63,62,61,65,57,53,53,59,53,64,64,66,62,53,51,48,57,63,59,44,58,58,54,56,39,47,51,38,37,41,46,57,48,63,82,36,41,47,56,47,58,39,35,53,55,44,59,54,52,38,51,66,44,38,48,38,40,61,43,41,33,58", "endOffsets": "2895,2942,2977,3019,3073,3126,3187,3263,3306,3351,3407,3482,3564,3640,3710,3896,3961,4059,4127,4314,4378,4419,4460,4506,4561,4602,4655,4692,4730,4766,4815,4874,4927,4965,5345,5392,6091,6140,6191,6429,6562,6599,6798,6854,6902,6941,6983,7029,7084,7137,7250,7306,7354,7398,7447,7512,7555,7598,7833,7871,7927,7993,8059,8124,8169,8209,8245,8359,8409,8461,8510,8558,8606,8689,8775,8862,8948,9032,9119,9163,9224,9261,9534,9602,9657,9730,9787,9859,9915,9987,10042,10114,10173,10254,10315,10392,10449,10522,10578,10631,10685,10755,10805,10860,10911,10961,11013,11073,11120,11158,11210,11261,11310,11371,11427,11496,11543,11605,11664,11720,11770,11828,11866,11916,11965,12026,12082,12149,12201,12237,12344,12531,12610,12733,12784,12835,12895,12954,13003,13046,13116,19445,19512,19572,19621,19669,19707,19763,19811,22837,22952,22985,23031,23093,23208,23279,23362,23434,23514,23585,23665,23731,23808,23863,23928,24027,24212,24263,24313,24474,24520,24576,24626,24686,24725,24781,24991,25033,25079,25126,25185,25223,25274,25311,25358,25403,25442,25487,25527,25563,25702,25761,25818,25874,25934,25988,26046,26113,26178,26249,26306,26361,26418,26481,26537,26597,26666,26739,26808,26865,26926,26981,27036,27096,27154,27211,27271,27328,27393,27457,27525,27580,27636,27693,27750,27809,27864,27928,27991,28053,28119,28177,28231,28285,28345,28399,28464,28529,28596,28659,28713,28765,28814,28872,28936,28996,29041,29168,29227,29282,29339,29379,29427,29479,29619,29825,29941,29988,30046,30163,30227,30310,30347,30389,30553,30610,30658,30717,30757,30793,30847,30903,30948,31008,31063,31278,31317,31369,31436,31481,31675,31724,31763,31804,31866,31910,31952,31986,32045"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\e8414bc356727da52467a60536d06b50\\transformed\\material3-release\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,159,262,366,468,560,648,752,857,962,1078,1160,1256,1340,1428,1533,1646,1747,1856,1963,2071,2188,2293,2394,2498,2603,2688,2783,2888,2997,3087,3187,3285,3396,3512,3612,3703,3777,3867,3956,4039,4121,4210,4290,4372,4469,4563,4656,4749,4833,4930,5026,5121,5229,5309,5403", "endColumns": "103,102,103,101,91,87,103,104,104,115,81,95,83,87,104,112,100,108,106,107,116,104,100,103,104,84,94,104,108,89,99,97,110,115,99,90,73,89,88,82,81,88,79,81,96,93,92,92,83,96,95,94,107,79,93,91", "endOffsets": "154,257,361,463,555,643,747,852,957,1073,1155,1251,1335,1423,1528,1641,1742,1851,1958,2066,2183,2288,2389,2493,2598,2683,2778,2883,2992,3082,3182,3280,3391,3507,3607,3698,3772,3862,3951,4034,4116,4205,4285,4367,4464,4558,4651,4744,4828,4925,5021,5116,5224,5304,5398,5490"}, "to": {"startLines": "202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "13121,13225,13328,13432,13534,13626,13714,13818,13923,14028,14144,14226,14322,14406,14494,14599,14712,14813,14922,15029,15137,15254,15359,15460,15564,15669,15754,15849,15954,16063,16153,16253,16351,16462,16578,16678,16769,16843,16933,17022,17105,17187,17276,17356,17438,17535,17629,17722,17815,17899,17996,18092,18187,18295,18375,18469", "endColumns": "103,102,103,101,91,87,103,104,104,115,81,95,83,87,104,112,100,108,106,107,116,104,100,103,104,84,94,104,108,89,99,97,110,115,99,90,73,89,88,82,81,88,79,81,96,93,92,92,83,96,95,94,107,79,93,91", "endOffsets": "13220,13323,13427,13529,13621,13709,13813,13918,14023,14139,14221,14317,14401,14489,14594,14707,14808,14917,15024,15132,15249,15354,15455,15559,15664,15749,15844,15949,16058,16148,16248,16346,16457,16573,16673,16764,16838,16928,17017,17100,17182,17271,17351,17433,17530,17624,17717,17810,17894,17991,18087,18182,18290,18370,18464,18556"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\2e72a71af7cea86f1e691bad46c56138\\transformed\\appcompat-1.6.1\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,757,832,924,1018,1115,1211,1306,1400,1496,1588,1680,1772,1850,1946,2041,2136,2233,2329,2427,2577,2671", "endColumns": "94,92,99,81,96,107,76,74,91,93,96,95,94,93,95,91,91,91,77,95,94,94,96,95,97,149,93,78", "endOffsets": "195,288,388,470,567,675,752,827,919,1013,1110,1206,1301,1395,1491,1583,1675,1767,1845,1941,2036,2131,2228,2324,2422,2572,2666,2745"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,370", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "297,392,485,585,667,764,872,949,1024,1116,1210,1307,1403,1498,1592,1688,1780,1872,1964,2042,2138,2233,2328,2425,2521,2619,2769,25568", "endColumns": "94,92,99,81,96,107,76,74,91,93,96,95,94,93,95,91,91,91,77,95,94,94,96,95,97,149,93,78", "endOffsets": "387,480,580,662,759,867,944,1019,1111,1205,1302,1398,1493,1587,1683,1775,1867,1959,2037,2133,2228,2323,2420,2516,2614,2764,2858,25642"}}]}]}