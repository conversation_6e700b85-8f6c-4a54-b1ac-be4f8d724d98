{"logs": [{"outputFile": "com.timeflow.app-mergeDebugResources-85:/values-zh-rTW/values-zh-rTW.xml", "map": [{"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\443a6cfc0de72abe8b7a8ef4c7b7a3e3\\transformed\\core-1.12.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,246,340,434,527,620,716", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "142,241,335,429,522,615,711,812"}, "to": {"startLines": "71,72,73,74,75,76,77,397", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "5242,5334,5433,5527,5621,5714,5807,27345", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "5329,5428,5522,5616,5709,5802,5898,27441"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\6e1bec7b61f7c5e14c57eb6146ea2199\\transformed\\ui-release\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,182,258,345,436,514,588,665,743,818,883,948,1021,1096,1164,1238,1306", "endColumns": "76,75,86,90,77,73,76,77,74,64,64,72,74,67,73,67,115", "endOffsets": "177,253,340,431,509,583,660,738,813,878,943,1016,1091,1159,1233,1301,1417"}, "to": {"startLines": "84,85,99,100,109,169,170,296,300,311,313,325,326,392,401,404,408", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6289,6366,7109,7196,7711,11290,11364,21418,21628,22398,22497,23191,23264,27078,27691,27870,28066", "endColumns": "76,75,86,90,77,73,76,77,74,64,64,72,74,67,73,67,115", "endOffsets": "6361,6437,7191,7282,7784,11359,11436,21491,21698,22458,22557,23259,23334,27141,27760,27933,28177"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\5619827a7b7a666ff443af1afbf85287\\transformed\\preference-1.2.1\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,5,6,7,8", "startColumns": "4,4,4,4,4,4", "startOffsets": "105,171,322,442,610,689", "endColumns": "65,80,119,167,78,75", "endOffsets": "166,247,437,605,684,760"}, "to": {"startLines": "91,126,318,399,425,426", "startColumns": "4,4,4,4,4,4", "startOffsets": "6691,8789,22783,27485,29054,29133", "endColumns": "65,80,119,167,78,75", "endOffsets": "6752,8865,22898,27648,29128,29204"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\8502c7d6e294f2825540546f57fbfcc0\\transformed\\material-1.11.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,247,311,373,440,510,587,681,788,861,923,1001,1061,1121,1199,1260,1318,1374,1434,1492,1546,1631,1687,1745,1799,1864,1956,2030,2107,2227,2290,2353,2452,2529,2603,2653,2704,2770,2833,2901,2979,3050,3111,3182,3249,3311,3398,3477,3542,3625,3710,3784,3848,3924,3972,4045,4109,4185,4263,4325,4389,4452,4518,4598,4676,4752,4831,4885,4940", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,63,61,66,69,76,93,106,72,61,77,59,59,77,60,57,55,59,57,53,84,55,57,53,64,91,73,76,119,62,62,98,76,73,49,50,65,62,67,77,70,60,70,66,61,86,78,64,82,84,73,63,75,47,72,63,75,77,61,63,62,65,79,77,75,78,53,54,68", "endOffsets": "242,306,368,435,505,582,676,783,856,918,996,1056,1116,1194,1255,1313,1369,1429,1487,1541,1626,1682,1740,1794,1859,1951,2025,2102,2222,2285,2348,2447,2524,2598,2648,2699,2765,2828,2896,2974,3045,3106,3177,3244,3306,3393,3472,3537,3620,3705,3779,3843,3919,3967,4040,4104,4180,4258,4320,4384,4447,4513,4593,4671,4747,4826,4880,4935,5004"}, "to": {"startLines": "2,64,65,66,67,68,79,80,82,125,127,167,172,233,234,235,236,237,238,239,240,241,242,243,244,245,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,314", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,4815,4879,4941,5008,5078,5941,6035,6179,8727,8870,11183,11477,17185,17263,17324,17382,17438,17498,17556,17610,17695,17751,17809,17863,17928,18392,18466,18543,18663,18726,18789,18888,18965,19039,19089,19140,19206,19269,19337,19415,19486,19547,19618,19685,19747,19834,19913,19978,20061,20146,20220,20284,20360,20408,20481,20545,20621,20699,20761,20825,20888,20954,21034,21112,21188,21267,21321,22562", "endLines": "5,64,65,66,67,68,79,80,82,125,127,167,172,233,234,235,236,237,238,239,240,241,242,243,244,245,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,314", "endColumns": "12,63,61,66,69,76,93,106,72,61,77,59,59,77,60,57,55,59,57,53,84,55,57,53,64,91,73,76,119,62,62,98,76,73,49,50,65,62,67,77,70,60,70,66,61,86,78,64,82,84,73,63,75,47,72,63,75,77,61,63,62,65,79,77,75,78,53,54,68", "endOffsets": "292,4874,4936,5003,5073,5150,6030,6137,6247,8784,8943,11238,11532,17258,17319,17377,17433,17493,17551,17605,17690,17746,17804,17858,17923,18015,18461,18538,18658,18721,18784,18883,18960,19034,19084,19135,19201,19264,19332,19410,19481,19542,19613,19680,19742,19829,19908,19973,20056,20141,20215,20279,20355,20403,20476,20540,20616,20694,20756,20820,20883,20949,21029,21107,21183,21262,21316,21371,22626"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\71256635af31e1aec955d3a558d276eb\\transformed\\foundation-release\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,138", "endColumns": "82,78", "endOffsets": "133,212"}, "to": {"startLines": "419,420", "startColumns": "4,4", "startOffsets": "28681,28764", "endColumns": "82,78", "endOffsets": "28759,28838"}}, {"source": "D:\\development\\Codes\\MyApplication\\app\\src\\main\\res\\values-zh-rTW\\strings.xml", "from": {"startLines": "70,265,81,282,14,127,128,15,263,264,242,243,241,244,236,235,238,237,234,233,7,53,54,2,67,85,87,45,46,47,268,10,26,73,83,86,36,37,74,150,151,285,38,28,25,261,262,27,275,80,116,115,117,118,8,78,79,255,256,253,252,254,155,157,156,154,158,159,58,129,92,286,287,294,295,292,293,296,297,298,299,300,301,290,291,288,289,100,98,99,132,101,103,105,107,104,106,21,90,174,175,55,110,112,109,111,113,139,66,3,270,95,60,59,52,91,33,35,34,18,19,135,143,251,88,77,281,276,162,163,166,167,168,169,164,165,68,75,22,24,20,89,39,40,69,9,29,147,273,274,82,84,62,61,269,140,72,205,226,230,214,212,216,178,179,181,180,203,227,222,229,219,196,198,197,199,208,202,215,220,225,213,209,224,192,193,195,194,228,217,206,223,221,187,188,189,191,190,204,210,211,218,182,183,184,185,186,207,247,41,42,126,65,23,56,93,141,259,260,172,4,5,43,44,32,138,6,120,121,173,123,122,57,136,48,49,71,280,94,137,144,279,11,76,248", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3030,12491,3430,13084,474,5346,5399,528,12390,12435,11483,11558,11407,11640,11034,10969,11288,11220,10782,10718,247,2258,2304,55,2872,3577,3651,1904,1953,2012,12560,367,984,3142,3503,3614,1432,1478,3180,6106,6161,13147,1534,1074,940,12276,12325,1031,12811,3392,4853,4787,4909,4975,288,3320,3356,12048,12098,11951,11903,12000,6322,6492,6405,6236,6578,6662,2511,5460,3846,13194,13246,13711,13766,13582,13639,13839,13895,13967,14022,14094,14153,13444,13505,13314,13371,4125,4018,4071,5542,4181,4232,4339,4450,4287,4390,743,3765,7481,7533,2359,4557,4669,4501,4619,4728,5791,2822,96,12637,3961,2606,2555,2207,3803,1251,1365,1305,596,645,5633,5908,11861,3687,3287,13022,12868,6768,6839,7065,7137,7217,7288,6922,6988,2925,3219,790,889,693,3724,1590,1636,2980,328,1122,6045,12705,12752,3465,3540,2714,2669,12598,5829,3106,9212,10408,10640,9723,9605,9834,7605,7663,7801,7730,9097,10467,10174,10577,10004,8744,8877,8804,8946,9383,9036,9779,10060,10348,9665,9440,10288,8490,8547,8680,8612,10522,9888,9272,10231,10115,8180,8235,8299,8428,8362,9154,9497,9551,9944,7866,7920,7985,8050,8117,9329,11729,1692,1741,5287,2782,841,2406,3883,5869,12171,12218,7387,132,169,1799,1847,1203,5751,211,5041,5095,7436,5206,5151,2458,5671,2065,2117,3067,12973,3922,5710,5964,12929,407,3253,11781", "endColumns": "36,46,34,41,53,52,60,42,44,55,74,81,75,69,185,64,97,67,186,63,40,45,54,40,52,36,35,48,58,52,37,39,46,37,36,36,45,55,38,54,52,46,55,47,43,48,64,42,56,37,55,65,65,64,39,35,35,49,51,48,47,47,82,85,86,85,83,86,43,60,36,51,67,54,72,56,71,55,71,54,71,58,80,60,76,56,72,55,52,53,69,49,54,50,49,51,59,46,37,51,50,46,61,58,55,49,57,37,49,35,46,35,62,50,50,42,53,66,59,48,47,37,55,41,36,32,61,39,70,82,71,79,70,79,65,76,54,33,50,50,49,40,45,55,49,38,55,41,46,58,37,36,46,44,38,39,35,59,58,56,55,59,53,57,66,64,70,56,54,56,62,55,59,68,72,68,56,60,54,54,59,57,56,59,56,64,63,67,54,55,56,56,58,54,63,62,61,65,57,53,53,59,53,64,64,66,62,53,51,48,57,58,39,47,51,38,37,46,57,48,36,41,47,56,47,39,35,53,55,44,59,54,52,38,51,66,38,48,38,40,61,43,41,33,58", "endOffsets": "3062,12533,3460,13121,523,5394,5455,566,12430,12486,11553,11635,11478,11705,11215,11029,11381,11283,10964,10777,283,2299,2354,91,2920,3609,3682,1948,2007,2060,12593,402,1026,3175,3535,3646,1473,1529,3214,6156,6209,13189,1585,1117,979,12320,12385,1069,12863,3425,4904,4848,4970,5035,323,3351,3387,12093,12145,11995,11946,12043,6400,6573,6487,6317,6657,6744,2550,5516,3878,13241,13309,13761,13834,13634,13706,13890,13962,14017,14089,14148,14229,13500,13577,13366,13439,4176,4066,4120,5607,4226,4282,4385,4495,4334,4445,785,3798,7528,7579,2401,4614,4723,4552,4664,4781,5824,2867,127,12679,3992,2664,2601,2253,3841,1300,1427,1360,640,688,5666,5959,11898,3719,3315,13079,12903,6834,6917,7132,7212,7283,7363,6983,7060,2975,3248,836,935,738,3760,1631,1687,3025,362,1173,6082,12747,12806,3498,3572,2756,2709,12632,5864,3137,9267,10462,10692,9774,9660,9883,7658,7725,7861,7796,9149,10517,10226,10635,10055,8799,8941,8872,9010,9435,9092,9829,10110,10403,9718,9492,10343,8542,8607,8739,8675,10572,9939,9324,10283,10169,8230,8294,8357,8485,8423,9207,9546,9600,9999,7915,7980,8045,8112,8175,9378,11776,1736,1794,5341,2817,884,2453,3917,5902,12213,12271,7431,164,206,1842,1899,1246,5786,242,5090,5146,7476,5261,5201,2506,5705,2112,2179,3101,13017,3956,5746,6021,12968,444,3282,11835"}, "to": {"startLines": "33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,69,70,78,81,83,86,87,88,89,90,92,93,94,95,96,97,98,101,102,103,104,105,106,107,108,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,168,171,173,174,175,176,246,247,248,249,250,251,252,295,297,298,299,301,302,303,304,305,306,307,308,309,310,312,315,316,317,319,320,321,322,323,324,327,328,329,330,331,332,333,334,335,336,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,393,394,395,396,398,400,402,403,405,406,407,409,410,411,412,413,414,415,416,417,418,421,422,423,424,427,428,429,430,431,432,433,434,435", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2863,2900,2947,2982,3024,3078,3131,3192,3235,3280,3336,3411,3493,3569,3639,3825,3890,3988,4056,4243,4307,4348,4394,4449,4490,4543,4580,4616,4665,4724,4777,5155,5195,5903,6142,6252,6442,6488,6544,6583,6638,6757,6804,6860,6908,6952,7001,7066,7287,7344,7382,7438,7504,7570,7635,7675,7789,7825,7875,7927,7976,8024,8072,8155,8241,8328,8414,8498,8585,8629,8690,8948,9000,9068,9123,9196,9253,9325,9381,9453,9508,9580,9639,9720,9781,9858,9915,9988,10044,10097,10151,10221,10271,10326,10377,10427,10479,10539,10586,10624,10676,10727,10774,10836,10895,10951,11001,11059,11097,11147,11243,11441,11537,11600,11651,11702,18020,18074,18141,18201,18250,18298,18336,21376,21496,21533,21566,21703,21743,21814,21897,21969,22049,22120,22200,22266,22343,22463,22631,22682,22733,22903,22944,22990,23046,23096,23135,23339,23381,23428,23487,23525,23562,23609,23654,23693,23733,23848,23908,23967,24024,24080,24140,24194,24252,24319,24384,24455,24512,24567,24624,24687,24743,24803,24872,24945,25014,25071,25132,25187,25242,25302,25360,25417,25477,25534,25599,25663,25731,25786,25842,25899,25956,26015,26070,26134,26197,26259,26325,26383,26437,26491,26551,26605,26670,26735,26802,26865,26919,26971,27020,27146,27205,27245,27293,27446,27653,27765,27812,27938,27987,28024,28182,28230,28287,28335,28375,28411,28465,28521,28566,28626,28843,28896,28935,28987,29209,29248,29297,29336,29377,29439,29483,29525,29559", "endColumns": "36,46,34,41,53,52,60,42,44,55,74,81,75,69,185,64,97,67,186,63,40,45,54,40,52,36,35,48,58,52,37,39,46,37,36,36,45,55,38,54,52,46,55,47,43,48,64,42,56,37,55,65,65,64,39,35,35,49,51,48,47,47,82,85,86,85,83,86,43,60,36,51,67,54,72,56,71,55,71,54,71,58,80,60,76,56,72,55,52,53,69,49,54,50,49,51,59,46,37,51,50,46,61,58,55,49,57,37,49,35,46,35,62,50,50,42,53,66,59,48,47,37,55,41,36,32,61,39,70,82,71,79,70,79,65,76,54,33,50,50,49,40,45,55,49,38,55,41,46,58,37,36,46,44,38,39,35,59,58,56,55,59,53,57,66,64,70,56,54,56,62,55,59,68,72,68,56,60,54,54,59,57,56,59,56,64,63,67,54,55,56,56,58,54,63,62,61,65,57,53,53,59,53,64,64,66,62,53,51,48,57,58,39,47,51,38,37,46,57,48,36,41,47,56,47,39,35,53,55,44,59,54,52,38,51,66,38,48,38,40,61,43,41,33,58", "endOffsets": "2895,2942,2977,3019,3073,3126,3187,3230,3275,3331,3406,3488,3564,3634,3820,3885,3983,4051,4238,4302,4343,4389,4444,4485,4538,4575,4611,4660,4719,4772,4810,5190,5237,5936,6174,6284,6483,6539,6578,6633,6686,6799,6855,6903,6947,6996,7061,7104,7339,7377,7433,7499,7565,7630,7670,7706,7820,7870,7922,7971,8019,8067,8150,8236,8323,8409,8493,8580,8624,8685,8722,8995,9063,9118,9191,9248,9320,9376,9448,9503,9575,9634,9715,9776,9853,9910,9983,10039,10092,10146,10216,10266,10321,10372,10422,10474,10534,10581,10619,10671,10722,10769,10831,10890,10946,10996,11054,11092,11142,11178,11285,11472,11595,11646,11697,11740,18069,18136,18196,18245,18293,18331,18387,21413,21528,21561,21623,21738,21809,21892,21964,22044,22115,22195,22261,22338,22393,22492,22677,22728,22778,22939,22985,23041,23091,23130,23186,23376,23423,23482,23520,23557,23604,23649,23688,23728,23764,23903,23962,24019,24075,24135,24189,24247,24314,24379,24450,24507,24562,24619,24682,24738,24798,24867,24940,25009,25066,25127,25182,25237,25297,25355,25412,25472,25529,25594,25658,25726,25781,25837,25894,25951,26010,26065,26129,26192,26254,26320,26378,26432,26486,26546,26600,26665,26730,26797,26860,26914,26966,27015,27073,27200,27240,27288,27340,27480,27686,27807,27865,27982,28019,28061,28225,28282,28330,28370,28406,28460,28516,28561,28621,28676,28891,28930,28982,29049,29243,29292,29331,29372,29434,29478,29520,29554,29613"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\e8414bc356727da52467a60536d06b50\\transformed\\material3-release\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,159,262,366,468,560,648,752,857,962,1078,1160,1256,1340,1428,1533,1646,1747,1856,1963,2071,2188,2293,2394,2498,2603,2688,2783,2888,2997,3087,3187,3285,3396,3512,3612,3703,3777,3867,3956,4039,4121,4210,4290,4372,4469,4563,4656,4749,4833,4930,5026,5121,5229,5309,5403", "endColumns": "103,102,103,101,91,87,103,104,104,115,81,95,83,87,104,112,100,108,106,107,116,104,100,103,104,84,94,104,108,89,99,97,110,115,99,90,73,89,88,82,81,88,79,81,96,93,92,92,83,96,95,94,107,79,93,91", "endOffsets": "154,257,361,463,555,643,747,852,957,1073,1155,1251,1335,1423,1528,1641,1742,1851,1958,2066,2183,2288,2389,2493,2598,2683,2778,2883,2992,3082,3182,3280,3391,3507,3607,3698,3772,3862,3951,4034,4116,4205,4285,4367,4464,4558,4651,4744,4828,4925,5021,5116,5224,5304,5398,5490"}, "to": {"startLines": "177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "11745,11849,11952,12056,12158,12250,12338,12442,12547,12652,12768,12850,12946,13030,13118,13223,13336,13437,13546,13653,13761,13878,13983,14084,14188,14293,14378,14473,14578,14687,14777,14877,14975,15086,15202,15302,15393,15467,15557,15646,15729,15811,15900,15980,16062,16159,16253,16346,16439,16523,16620,16716,16811,16919,16999,17093", "endColumns": "103,102,103,101,91,87,103,104,104,115,81,95,83,87,104,112,100,108,106,107,116,104,100,103,104,84,94,104,108,89,99,97,110,115,99,90,73,89,88,82,81,88,79,81,96,93,92,92,83,96,95,94,107,79,93,91", "endOffsets": "11844,11947,12051,12153,12245,12333,12437,12542,12647,12763,12845,12941,13025,13113,13218,13331,13432,13541,13648,13756,13873,13978,14079,14183,14288,14373,14468,14573,14682,14772,14872,14970,15081,15197,15297,15388,15462,15552,15641,15724,15806,15895,15975,16057,16154,16248,16341,16434,16518,16615,16711,16806,16914,16994,17088,17180"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\2e72a71af7cea86f1e691bad46c56138\\transformed\\appcompat-1.6.1\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,757,832,924,1018,1115,1211,1306,1400,1496,1588,1680,1772,1850,1946,2041,2136,2233,2329,2427,2577,2671", "endColumns": "94,92,99,81,96,107,76,74,91,93,96,95,94,93,95,91,91,91,77,95,94,94,96,95,97,149,93,78", "endOffsets": "195,288,388,470,567,675,752,827,919,1013,1110,1206,1301,1395,1491,1583,1675,1767,1845,1941,2036,2131,2228,2324,2422,2572,2666,2745"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,337", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "297,392,485,585,667,764,872,949,1024,1116,1210,1307,1403,1498,1592,1688,1780,1872,1964,2042,2138,2233,2328,2425,2521,2619,2769,23769", "endColumns": "94,92,99,81,96,107,76,74,91,93,96,95,94,93,95,91,91,91,77,95,94,94,96,95,97,149,93,78", "endOffsets": "387,480,580,662,759,867,944,1019,1111,1205,1302,1398,1493,1587,1683,1775,1867,1959,2037,2133,2228,2323,2420,2516,2614,2764,2858,23843"}}]}]}