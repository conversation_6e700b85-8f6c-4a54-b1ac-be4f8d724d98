package com.timeflow.app.service;

import android.content.Context;
import com.timeflow.app.data.repository.TaskRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class RecurringTaskManager_Factory implements Factory<RecurringTaskManager> {
  private final Provider<Context> contextProvider;

  private final Provider<TaskRepository> taskRepositoryProvider;

  private final Provider<RecurrenceCalculator> recurrenceCalculatorProvider;

  public RecurringTaskManager_Factory(Provider<Context> contextProvider,
      Provider<TaskRepository> taskRepositoryProvider,
      Provider<RecurrenceCalculator> recurrenceCalculatorProvider) {
    this.contextProvider = contextProvider;
    this.taskRepositoryProvider = taskRepositoryProvider;
    this.recurrenceCalculatorProvider = recurrenceCalculatorProvider;
  }

  @Override
  public RecurringTaskManager get() {
    return newInstance(contextProvider.get(), taskRepositoryProvider.get(), recurrenceCalculatorProvider.get());
  }

  public static RecurringTaskManager_Factory create(Provider<Context> contextProvider,
      Provider<TaskRepository> taskRepositoryProvider,
      Provider<RecurrenceCalculator> recurrenceCalculatorProvider) {
    return new RecurringTaskManager_Factory(contextProvider, taskRepositoryProvider, recurrenceCalculatorProvider);
  }

  public static RecurringTaskManager newInstance(Context context, TaskRepository taskRepository,
      RecurrenceCalculator recurrenceCalculator) {
    return new RecurringTaskManager(context, taskRepository, recurrenceCalculator);
  }
}
