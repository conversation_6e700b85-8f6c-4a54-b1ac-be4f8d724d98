package com.timeflow.app.service;

import android.content.SharedPreferences;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class FocusTimerService_MembersInjector implements MembersInjector<FocusTimerService> {
  private final Provider<SharedPreferences> sharedPreferencesProvider;

  public FocusTimerService_MembersInjector(Provider<SharedPreferences> sharedPreferencesProvider) {
    this.sharedPreferencesProvider = sharedPreferencesProvider;
  }

  public static MembersInjector<FocusTimerService> create(
      Provider<SharedPreferences> sharedPreferencesProvider) {
    return new FocusTimerService_MembersInjector(sharedPreferencesProvider);
  }

  @Override
  public void injectMembers(FocusTimerService instance) {
    injectSharedPreferences(instance, sharedPreferencesProvider.get());
  }

  @InjectedFieldSignature("com.timeflow.app.service.FocusTimerService.sharedPreferences")
  public static void injectSharedPreferences(FocusTimerService instance,
      SharedPreferences sharedPreferences) {
    instance.sharedPreferences = sharedPreferences;
  }
}
