package com.timeflow.app.data.repository;

import com.timeflow.app.data.dao.MedicationRecordDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class MedicationRepositoryImpl_Factory implements Factory<MedicationRepositoryImpl> {
  private final Provider<MedicationRecordDao> medicationRecordDaoProvider;

  public MedicationRepositoryImpl_Factory(
      Provider<MedicationRecordDao> medicationRecordDaoProvider) {
    this.medicationRecordDaoProvider = medicationRecordDaoProvider;
  }

  @Override
  public MedicationRepositoryImpl get() {
    return newInstance(medicationRecordDaoProvider.get());
  }

  public static MedicationRepositoryImpl_Factory create(
      Provider<MedicationRecordDao> medicationRecordDaoProvider) {
    return new MedicationRepositoryImpl_Factory(medicationRecordDaoProvider);
  }

  public static MedicationRepositoryImpl newInstance(MedicationRecordDao medicationRecordDao) {
    return new MedicationRepositoryImpl(medicationRecordDao);
  }
}
