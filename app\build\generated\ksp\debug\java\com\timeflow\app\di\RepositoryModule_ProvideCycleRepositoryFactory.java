package com.timeflow.app.di;

import com.timeflow.app.data.dao.CycleDao;
import com.timeflow.app.data.repository.CycleRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class RepositoryModule_ProvideCycleRepositoryFactory implements Factory<CycleRepository> {
  private final Provider<CycleDao> cycleDaoProvider;

  public RepositoryModule_ProvideCycleRepositoryFactory(Provider<CycleDao> cycleDaoProvider) {
    this.cycleDaoProvider = cycleDaoProvider;
  }

  @Override
  public CycleRepository get() {
    return provideCycleRepository(cycleDaoProvider.get());
  }

  public static RepositoryModule_ProvideCycleRepositoryFactory create(
      Provider<CycleDao> cycleDaoProvider) {
    return new RepositoryModule_ProvideCycleRepositoryFactory(cycleDaoProvider);
  }

  public static CycleRepository provideCycleRepository(CycleDao cycleDao) {
    return Preconditions.checkNotNullFromProvides(RepositoryModule.INSTANCE.provideCycleRepository(cycleDao));
  }
}
