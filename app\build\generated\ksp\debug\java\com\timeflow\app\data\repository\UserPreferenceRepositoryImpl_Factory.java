package com.timeflow.app.data.repository;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class UserPreferenceRepositoryImpl_Factory implements Factory<UserPreferenceRepositoryImpl> {
  @Override
  public UserPreferenceRepositoryImpl get() {
    return newInstance();
  }

  public static UserPreferenceRepositoryImpl_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static UserPreferenceRepositoryImpl newInstance() {
    return new UserPreferenceRepositoryImpl();
  }

  private static final class InstanceHolder {
    private static final UserPreferenceRepositoryImpl_Factory INSTANCE = new UserPreferenceRepositoryImpl_Factory();
  }
}
