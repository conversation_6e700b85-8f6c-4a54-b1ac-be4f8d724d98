{"logs": [{"outputFile": "D:\\development\\Android\\gradle\\daemon\\8.11.1\\com.timeflow.app-mergeDebugResources-85:\\values\\values.xml", "map": [{"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\269f815ecf39126ae6ebdbf9d5fd71ed\\transformed\\lifecycle-runtime-2.7.0\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "1924", "startColumns": "4", "startOffsets": "127472", "endColumns": "42", "endOffsets": "127510"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\443a6cfc0de72abe8b7a8ef4c7b7a3e3\\transformed\\core-1.12.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "419,569,570,585,586,875,876,1085,1086,1087,1088,1089,1090,1091,1772,1773,1774,1775,1776,1777,1778,1779,1780,1781,1782,1783,1784,1785,1786,1804,1805,1806,1856,1857,1894,1895,1896,1897,1898,1899,1900,1901,1902,1903,1904,1905,1906,1907,1908,1916,1999,2071,2072,2073,2074,2075,2076,2077,2459,6600,6601,6605,6606,6610,7808,7809", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "21704,29445,29517,30652,30717,52188,52257,65141,65211,65279,65351,65421,65482,65556,118779,118840,118901,118963,119027,119089,119150,119218,119318,119378,119444,119517,119586,119643,119695,120920,120992,121068,123949,123984,125920,125975,126038,126093,126151,126209,126270,126333,126390,126441,126491,126552,126609,126675,126709,127091,132368,137969,138036,138108,138177,138246,138320,138392,171903,458292,458409,458610,458720,458921,543800,543872", "endLines": "419,569,570,585,586,875,876,1085,1086,1087,1088,1089,1090,1091,1772,1773,1774,1775,1776,1777,1778,1779,1780,1781,1782,1783,1784,1785,1786,1804,1805,1806,1856,1857,1894,1895,1896,1897,1898,1899,1900,1901,1902,1903,1904,1905,1906,1907,1908,1916,1999,2071,2072,2073,2074,2075,2076,2077,2459,6600,6604,6605,6609,6610,7808,7809", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66", "endOffsets": "21759,29512,29600,30712,30778,52252,52315,65206,65274,65346,65416,65477,65551,65624,118835,118896,118958,119022,119084,119145,119213,119313,119373,119439,119512,119581,119638,119690,119752,120987,121063,121128,123979,124014,125970,126033,126088,126146,126204,126265,126328,126385,126436,126486,126547,126604,126670,126704,126739,127121,132433,138031,138103,138172,138241,138315,138387,138475,171969,458404,458605,458715,458916,459045,543867,543934"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\2e72a71af7cea86f1e691bad46c56138\\transformed\\appcompat-1.6.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "120,208,211,509,552,553,562,563,564,565,566,567,568,572,573,574,575,577,578,579,580,581,582,583,584,626,627,628,629,631,632,633,634,635,636,809,810,811,812,813,814,815,816,817,818,819,820,885,886,888,889,890,891,892,893,898,899,902,903,904,905,915,916,917,918,922,923,999,1000,1001,1002,1003,1004,1005,1006,1007,1008,1009,1010,1011,1012,1013,1014,1015,1016,1017,1018,1019,1020,1021,1022,1023,1024,1025,1026,1027,1028,1029,1030,1031,1032,1033,1034,1035,1036,1037,1038,1039,1040,1041,1042,1043,1044,1045,1046,1047,1048,1049,1050,1051,1052,1053,1054,1055,1056,1057,1058,1059,1060,1061,1062,1063,1064,1065,1066,1067,1068,1069,1070,1071,1072,1073,1074,1075,1076,1077,1078,1079,1141,1142,1146,1147,1148,1149,1150,1151,1152,1794,1795,1796,1797,1798,1799,1800,1801,1839,1840,1841,1842,1852,1882,1883,1893,1922,1930,1931,1934,1935,2004,2005,2006,2007,2008,2009,2010,2011,2012,2013,2014,2015,2016,2017,2018,2019,2020,2021,2022,2023,2024,2025,2026,2027,2028,2029,2030,2387,2498,2499,2500,2501,2502,2524,2532,2533,2537,2541,2552,2557,2586,2593,2597,2601,2606,2610,2614,2618,2622,2626,2630,2636,2640,2646,2650,2656,2660,2665,2669,2672,2676,2682,2686,2692,2696,2702,2705,2709,2713,2717,2721,2725,2726,2727,2728,2731,2734,2737,2740,2744,2745,2746,2747,2788,2791,2793,2795,2797,2802,2803,2807,2813,2817,2818,2820,2832,2833,2837,2843,2847,2924,2925,2929,2956,2960,2961,2965,4762,4934,4960,5131,5157,5188,5196,5202,5218,5240,5245,5250,5260,5269,5278,5282,5289,5308,5315,5316,5325,5328,5331,5335,5339,5343,5346,5347,5352,5357,5367,5372,5379,5385,5386,5389,5393,5398,5400,5402,5405,5408,5410,5414,5417,5424,5427,5430,5434,5436,5440,5442,5444,5446,5450,5458,5466,5478,5484,5493,5496,5507,5510,5511,5516,5517,6030,6099,6173,6174,6184,6193,6345,6347,6351,6354,6357,6360,6363,6366,6369,6372,6376,6379,6382,6385,6389,6392,6396,6544,6545,6546,6547,6548,6549,6550,6551,6552,6553,6554,6555,6556,6557,6558,6559,6560,6561,6562,6563,6564,6566,6568,6569,6570,6571,6572,6573,6574,6575,6577,6578,6580,6581,6583,6585,6586,6588,6589,6590,6591,6592,6593,6595,6596,6597,6598,6599,6884,6886,6888,6890,6891,6892,6893,6894,6895,6896,6897,6898,6899,6900,6901,6902,6904,6905,6906,6907,6908,6909,6910,6912,6916,7161,7162,7163,7164,7165,7166,7170,7171,7172,7713,7715,7717,7719,7721,7723,7724,7725,7726,7728,7730,7732,7733,7734,7735,7736,7737,7738,7739,7740,7741,7742,7743,7746,7747,7748,7749,7751,7753,7754,7756,7757,7759,7761,7763,7764,7765,7766,7767,7768,7769,7770,7771,7772,7773,7774,7776,7777,7778,7779,7781,7782,7783,7784,7785,7787,7789,7791,7793,7794,7795,7796,7797,7798,7799,7800,7801,7802,7803,7804,7805,7806,7807", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6094,10548,10701,26567,28323,28378,28944,29008,29078,29139,29214,29290,29367,29650,29735,29817,29893,30011,30088,30166,30272,30378,30457,30537,30594,33455,33529,33604,33669,33775,33835,33896,33968,34041,34108,47310,47369,47428,47487,47546,47605,47659,47713,47766,47820,47874,47928,52707,52781,52908,52981,53055,53126,53198,53270,53534,53591,53743,53816,53890,53964,54469,54541,54614,54684,54902,54962,59402,59471,59540,59610,59684,59760,59824,59901,59977,60054,60119,60188,60265,60340,60409,60477,60554,60620,60681,60778,60843,60912,61011,61082,61141,61199,61256,61315,61379,61450,61522,61594,61666,61738,61805,61873,61941,62000,62063,62127,62217,62308,62368,62434,62501,62567,62637,62701,62754,62821,62882,62949,63062,63120,63183,63248,63313,63388,63461,63533,63577,63624,63670,63719,63780,63841,63902,63964,64028,64092,64156,64221,64284,64344,64405,64471,64530,64590,64652,64723,64783,68781,68867,69117,69207,69294,69382,69464,69547,69637,120190,120242,120300,120345,120411,120475,120532,120589,123043,123100,123148,123197,123740,125348,125395,125874,127392,127796,127860,128050,128110,132758,132832,132902,132980,133034,133104,133189,133237,133283,133344,133407,133473,133537,133608,133671,133736,133800,133861,133922,133974,134047,134121,134190,134265,134339,134413,134554,167191,174013,174091,174181,174269,174365,175874,176456,176545,176792,177073,177739,178024,179833,180310,180532,180754,181030,181257,181487,181717,181947,182177,182404,182823,183049,183474,183704,184132,184351,184634,184842,184973,185200,185626,185851,186278,186499,186924,187044,187320,187621,187945,188236,188550,188687,188818,188923,189165,189332,189536,189744,190015,190127,190239,190344,192437,192651,192797,192937,193023,193371,193459,193705,194123,194372,194454,194552,195209,195309,195561,195985,196240,202130,202219,202456,204480,204722,204824,205077,340157,350838,352354,363049,364577,366334,366960,367380,368641,369906,370162,370398,370945,371439,372044,372242,372822,374190,374565,374683,375221,375378,375574,375847,376103,376273,376414,376478,376843,377210,377886,378150,378488,378841,378935,379121,379427,379689,379814,379941,380180,380391,380510,380703,380880,381335,381516,381638,381897,382010,382197,382299,382406,382535,382810,383318,383814,384691,384985,385555,385704,386436,386608,386692,387028,387120,420170,425401,431116,431178,431756,432340,440287,440400,440629,440789,440941,441112,441278,441447,441614,441777,442020,442190,442363,442534,442808,443007,443212,452578,452662,452758,452854,452952,453052,453154,453256,453358,453460,453562,453662,453758,453870,453999,454122,454253,454384,454482,454596,454690,454830,454964,455060,455172,455272,455388,455484,455596,455696,455836,455972,456136,456266,456424,456574,456715,456859,456994,457106,457256,457384,457512,457648,457780,457910,458040,458152,475810,475956,476100,476238,476304,476394,476470,476574,476664,476766,476874,476982,477082,477162,477254,477352,477462,477514,477592,477698,477790,477894,478004,478126,478289,495354,495434,495534,495624,495734,495824,496065,496159,496265,536144,536244,536356,536470,536586,536702,536796,536910,537022,537124,537244,537366,537448,537552,537672,537798,537896,537990,538078,538190,538306,538428,538540,538715,538831,538917,539009,539121,539245,539312,539438,539506,539634,539778,539906,539975,540070,540185,540298,540397,540506,540617,540728,540829,540934,541034,541164,541255,541378,541472,541584,541670,541774,541870,541958,542076,542180,542284,542410,542498,542606,542706,542796,542906,542990,543092,543176,543230,543294,543400,543486,543596,543680", "endLines": "120,208,211,509,552,553,562,563,564,565,566,567,568,572,573,574,575,577,578,579,580,581,582,583,584,626,627,628,629,631,632,633,634,635,636,809,810,811,812,813,814,815,816,817,818,819,820,885,886,888,889,890,891,892,893,898,899,902,903,904,905,915,916,917,918,922,923,999,1000,1001,1002,1003,1004,1005,1006,1007,1008,1009,1010,1011,1012,1013,1014,1015,1016,1017,1018,1019,1020,1021,1022,1023,1024,1025,1026,1027,1028,1029,1030,1031,1032,1033,1034,1035,1036,1037,1038,1039,1040,1041,1042,1043,1044,1045,1046,1047,1048,1049,1050,1051,1052,1053,1054,1055,1056,1057,1058,1059,1060,1061,1062,1063,1064,1065,1066,1067,1068,1069,1070,1071,1072,1073,1074,1075,1076,1077,1078,1079,1141,1142,1146,1147,1148,1149,1150,1151,1152,1794,1795,1796,1797,1798,1799,1800,1801,1839,1840,1841,1842,1852,1882,1883,1893,1922,1930,1931,1934,1935,2004,2005,2006,2007,2008,2009,2010,2011,2012,2013,2014,2015,2016,2017,2018,2019,2020,2021,2022,2023,2024,2025,2026,2027,2028,2029,2030,2387,2498,2499,2500,2501,2502,2531,2532,2536,2540,2544,2556,2562,2592,2596,2600,2605,2609,2613,2617,2621,2625,2629,2635,2639,2645,2649,2655,2659,2664,2668,2671,2675,2681,2685,2691,2695,2701,2704,2708,2712,2716,2720,2724,2725,2726,2727,2730,2733,2736,2739,2743,2744,2745,2746,2747,2790,2792,2794,2796,2801,2802,2806,2812,2816,2817,2819,2831,2832,2836,2842,2846,2847,2924,2928,2955,2959,2960,2964,2992,4933,4959,5130,5156,5187,5195,5201,5217,5239,5244,5249,5259,5268,5277,5281,5288,5307,5314,5315,5324,5327,5330,5334,5338,5342,5345,5346,5351,5356,5366,5371,5378,5384,5385,5388,5392,5397,5399,5401,5404,5407,5409,5413,5416,5423,5426,5429,5433,5435,5439,5441,5443,5445,5449,5457,5465,5477,5483,5492,5495,5506,5509,5510,5515,5516,5521,6098,6168,6173,6183,6192,6193,6346,6350,6353,6356,6359,6362,6365,6368,6371,6375,6378,6381,6384,6388,6391,6395,6399,6544,6545,6546,6547,6548,6549,6550,6551,6552,6553,6554,6555,6556,6557,6558,6559,6560,6561,6562,6563,6565,6567,6568,6569,6570,6571,6572,6573,6574,6576,6577,6579,6580,6582,6584,6585,6587,6588,6589,6590,6591,6592,6594,6595,6596,6597,6598,6599,6885,6887,6889,6890,6891,6892,6893,6894,6895,6896,6897,6898,6899,6900,6901,6903,6904,6905,6906,6907,6908,6909,6911,6915,6919,7161,7162,7163,7164,7165,7169,7170,7171,7172,7714,7716,7718,7720,7722,7723,7724,7725,7727,7729,7731,7732,7733,7734,7735,7736,7737,7738,7739,7740,7741,7742,7745,7746,7747,7748,7750,7752,7753,7755,7756,7758,7760,7762,7763,7764,7765,7766,7767,7768,7769,7770,7771,7772,7773,7775,7776,7777,7778,7780,7781,7782,7783,7784,7786,7788,7790,7792,7793,7794,7795,7796,7797,7798,7799,7800,7801,7802,7803,7804,7805,7806,7807", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119", "endOffsets": "6144,10588,10745,26603,28373,28435,29003,29073,29134,29209,29285,29362,29440,29730,29812,29888,29964,30083,30161,30267,30373,30452,30532,30589,30647,33524,33599,33664,33730,33830,33891,33963,34036,34103,34171,47364,47423,47482,47541,47600,47654,47708,47761,47815,47869,47923,47977,52776,52855,52976,53050,53121,53193,53265,53338,53586,53644,53811,53885,53959,54034,54536,54609,54679,54750,54957,55018,59466,59535,59605,59679,59755,59819,59896,59972,60049,60114,60183,60260,60335,60404,60472,60549,60615,60676,60773,60838,60907,61006,61077,61136,61194,61251,61310,61374,61445,61517,61589,61661,61733,61800,61868,61936,61995,62058,62122,62212,62303,62363,62429,62496,62562,62632,62696,62749,62816,62877,62944,63057,63115,63178,63243,63308,63383,63456,63528,63572,63619,63665,63714,63775,63836,63897,63959,64023,64087,64151,64216,64279,64339,64400,64466,64525,64585,64647,64718,64778,64846,68862,68949,69202,69289,69377,69459,69542,69632,69723,120237,120295,120340,120406,120470,120527,120584,120638,123095,123143,123192,123243,123769,125390,125439,125915,127419,127855,127917,128105,128162,132827,132897,132975,133029,133099,133184,133232,133278,133339,133402,133468,133532,133603,133666,133731,133795,133856,133917,133969,134042,134116,134185,134260,134334,134408,134549,134619,167239,174086,174176,174264,174360,174450,176451,176540,176787,177068,177320,178019,178412,180305,180527,180749,181025,181252,181482,181712,181942,182172,182399,182818,183044,183469,183699,184127,184346,184629,184837,184968,185195,185621,185846,186273,186494,186919,187039,187315,187616,187940,188231,188545,188682,188813,188918,189160,189327,189531,189739,190010,190122,190234,190339,190456,192646,192792,192932,193018,193366,193454,193700,194118,194367,194449,194547,195204,195304,195556,195980,196235,196329,202214,202451,204475,204717,204819,205072,207228,350833,352349,363044,364572,366329,366955,367375,368636,369901,370157,370393,370940,371434,372039,372237,372817,374185,374560,374678,375216,375373,375569,375842,376098,376268,376409,376473,376838,377205,377881,378145,378483,378836,378930,379116,379422,379684,379809,379936,380175,380386,380505,380698,380875,381330,381511,381633,381892,382005,382192,382294,382401,382530,382805,383313,383809,384686,384980,385550,385699,386431,386603,386687,387023,387115,387393,425396,430767,431173,431751,432335,432426,440395,440624,440784,440936,441107,441273,441442,441609,441772,442015,442185,442358,442529,442803,443002,443207,443537,452657,452753,452849,452947,453047,453149,453251,453353,453455,453557,453657,453753,453865,453994,454117,454248,454379,454477,454591,454685,454825,454959,455055,455167,455267,455383,455479,455591,455691,455831,455967,456131,456261,456419,456569,456710,456854,456989,457101,457251,457379,457507,457643,457775,457905,458035,458147,458287,475951,476095,476233,476299,476389,476465,476569,476659,476761,476869,476977,477077,477157,477249,477347,477457,477509,477587,477693,477785,477889,477999,478121,478284,478441,495429,495529,495619,495729,495819,496060,496154,496260,496352,536239,536351,536465,536581,536697,536791,536905,537017,537119,537239,537361,537443,537547,537667,537793,537891,537985,538073,538185,538301,538423,538535,538710,538826,538912,539004,539116,539240,539307,539433,539501,539629,539773,539901,539970,540065,540180,540293,540392,540501,540612,540723,540824,540929,541029,541159,541250,541373,541467,541579,541665,541769,541865,541953,542071,542175,542279,542405,542493,542601,542701,542791,542901,542985,543087,543171,543225,543289,543395,543481,543591,543675,543795"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\d17c3cc083b583ce71e54f6d227cea13\\transformed\\transition-1.4.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "1849,1850,1877,1887,1888,1917,1918,1919,1920,1921", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "123602,123642,125081,125575,125630,127126,127180,127232,127281,127342", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "123637,123684,125119,125625,125672,127175,127227,127276,127337,127387"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\5fb9f1b19747d5d55118ab1a0045d5ba\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "1854,1878", "startColumns": "4,4", "startOffsets": "123829,125124", "endColumns": "53,66", "endOffsets": "123878,125186"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\e8a1fad92796dfcc9d449130c7157984\\transformed\\lifecycle-viewmodel-2.7.0\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "1927", "startColumns": "4", "startOffsets": "127629", "endColumns": "49", "endOffsets": "127674"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\c844dc5032d7376f9b48ed94bcd5a918\\transformed\\recyclerview-1.1.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "437,1143,1144,1145,1153,1154,1155,1855", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "22445,68954,69013,69061,69728,69803,69879,123883", "endColumns": "55,58,47,55,74,75,71,65", "endOffsets": "22496,69008,69056,69112,69798,69874,69946,123944"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\4d4153ca3cac4b70eeadb3640318cf03\\transformed\\metrics-performance-1.0.0-beta01\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "1861,1862", "startColumns": "4,4", "startOffsets": "124168,124214", "endColumns": "45,47", "endOffsets": "124209,124257"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\9016448c9bf9cd5590d66215cffcbf2c\\transformed\\drawerlayout-1.1.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1", "startColumns": "-1,-1,-1", "startOffsets": "-1,-1,-1"}, "to": {"startLines": "121,124,1092", "startColumns": "4,4,4", "startOffsets": "6149,6313,65629", "endColumns": "55,47,51", "endOffsets": "6200,6356,65676"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\e8414bc356727da52467a60536d06b50\\transformed\\material3-release\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2182,2183,2184,2185,2186,2187,2188,2189,2190,2191,2194,2195,2196,2197,2198,2199,2200,2201,2202,2203,2204,2207,2208,2209,2210,2211,2212,2213,2214,2215,2216,2217,2218,2219,2220,2221,2222,2223,2224,2225,2226,2227,2228,2229,2230,2231,2232,2233,2234,2235,2236,2237,2238,2239,2240,2241", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "145817,145905,145991,146072,146156,146225,146290,146373,146479,146565,146685,146739,146808,146869,146938,147027,147122,147196,147293,147386,147484,147633,147724,147812,147908,148006,148070,148138,148225,148319,148386,148458,148530,148631,148740,148816,148885,148933,148999,149063,149120,149177,149249,149299,149353,149424,149495,149565,149634,149692,149768,149839,149913,149999,150049,150119", "endLines": "2182,2183,2184,2185,2186,2187,2188,2189,2190,2193,2194,2195,2196,2197,2198,2199,2200,2201,2202,2203,2206,2207,2208,2209,2210,2211,2212,2213,2214,2215,2216,2217,2218,2219,2220,2221,2222,2223,2224,2225,2226,2227,2228,2229,2230,2231,2232,2233,2234,2235,2236,2237,2238,2239,2240,2241", "endColumns": "87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64", "endOffsets": "145900,145986,146067,146151,146220,146285,146368,146474,146560,146680,146734,146803,146864,146933,147022,147117,147191,147288,147381,147479,147628,147719,147807,147903,148001,148065,148133,148220,148314,148381,148453,148525,148626,148735,148811,148880,148928,148994,149058,149115,149172,149244,149294,149348,149419,149490,149560,149629,149687,149763,149834,149908,149994,150044,150114,150179"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\ac812fd31ead2ff3fba2b67a79fdb627\\transformed\\window-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1"}, "to": {"startLines": "459,464,465,466,1844", "startColumns": "4,4,4,4,4", "startOffsets": "23650,23829,23889,23941,123322", "endLines": "463,464,465,466,1844", "endColumns": "11,59,51,44,59", "endOffsets": "23824,23884,23936,23981,123377"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\71256635af31e1aec955d3a558d276eb\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "2481,2482", "startColumns": "4,4", "startOffsets": "173134,173190", "endColumns": "55,54", "endOffsets": "173185,173240"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\be96a3cc4d9fb8c1f62d02a0f08f5225\\transformed\\coordinatorlayout-1.1.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "94,9795", "startColumns": "4,4", "startOffsets": "5016,673483", "endLines": "94,9797", "endColumns": "60,12", "endOffsets": "5072,673623"}}, {"source": "D:\\development\\Codes\\MyApplication\\app\\src\\main\\res\\values\\bools.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "555,559", "startColumns": "4,4", "startOffsets": "28513,28770", "endColumns": "57,50", "endOffsets": "28566,28816"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\bb69df46b629c4933421c332e21438f5\\transformed\\coil-base-2.4.0\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "1845", "startColumns": "4", "startOffsets": "123382", "endColumns": "49", "endOffsets": "123427"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\9f98cf38d23a9440ede43a1dc0784488\\transformed\\activity-1.8.2\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "1884,1925", "startColumns": "4,4", "startOffsets": "125444,127515", "endColumns": "41,59", "endOffsets": "125481,127570"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\c8644e9637794500baaef0ec63c3c74d\\transformed\\aws-android-sdk-auth-core-2.76.0\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2454", "startColumns": "4", "startOffsets": "171588", "endColumns": "90", "endOffsets": "171674"}}, {"source": "D:\\development\\Codes\\MyApplication\\app\\src\\main\\res\\values\\widget_colors.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "926,927,928,929,930,931,932,933,934,935,936,937,938,939,940,941,942,944,945,946,947,948,949,950,951,952,953,954,955,956,957,958,959,960,961,962,963,964,965,966,967,968,969,970,971,972,973,974,975,976,977,978,979,980,981,982,983,984,985,986,987,988,989,990,991,992,993,994,995,996,997", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55109,55162,55216,55271,55324,55379,55431,55484,55544,55602,55660,55714,55775,55828,55882,55937,55990,56097,56162,56228,56295,56363,56422,56483,56537,56597,56659,56720,56783,56844,56907,56963,57019,57076,57131,57185,57242,57292,57349,57402,57458,57512,57566,57618,57672,57728,57783,57841,57906,57965,58019,58071,58133,58194,58263,58334,58396,58460,58519,58579,58642,58706,58765,58825,58886,58948,59008,59069,59134,59200,59267", "endColumns": "52,53,54,52,54,51,52,59,57,57,53,60,52,53,54,52,51,64,65,66,67,58,60,53,59,61,60,62,60,62,55,55,56,54,53,56,49,56,52,55,53,53,51,53,55,54,57,64,58,53,51,61,60,68,70,61,63,58,59,62,63,58,59,60,61,59,60,64,65,66,67", "endOffsets": "55157,55211,55266,55319,55374,55426,55479,55539,55597,55655,55709,55770,55823,55877,55932,55985,56037,56157,56223,56290,56358,56417,56478,56532,56592,56654,56715,56778,56839,56902,56958,57014,57071,57126,57180,57237,57287,57344,57397,57453,57507,57561,57613,57667,57723,57778,57836,57901,57960,58014,58066,58128,58189,58258,58329,58391,58455,58514,58574,58637,58701,58760,58820,58881,58943,59003,59064,59129,59195,59262,59330"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\55566c834c8001acc9b240421f8a423e\\transformed\\navigation-runtime-2.7.7\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "1870", "startColumns": "4", "startOffsets": "124640", "endColumns": "52", "endOffsets": "124688"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\6245402d8a114e85318ed4c000100fb2\\transformed\\cardview-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "26,587,588,589,590,1081,1082,1083,2545,5912,5914,5917", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1254,30783,30844,30906,30968,64916,64975,65032,177325,412554,412618,412744", "endLines": "26,587,588,589,590,1081,1082,1083,2551,5913,5916,5919", "endColumns": "51,60,61,61,63,58,56,53,12,12,12,12", "endOffsets": "1301,30839,30901,30963,31027,64970,65027,65081,177734,412613,412739,412867"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\c1c22d75c8cbd1da7dd9b0cc24fa738b\\transformed\\fragment-1.5.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1", "startColumns": "-1,-1,-1", "startOffsets": "-1,-1,-1"}, "to": {"startLines": "1848,1892,1928", "startColumns": "4,4,4", "startOffsets": "123545,125809,127679", "endColumns": "56,64,63", "endOffsets": "123597,125869,127738"}}, {"source": "D:\\development\\Codes\\MyApplication\\app\\src\\main\\res\\values\\ids.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "1885", "startColumns": "4", "startOffsets": "125486", "endColumns": "45", "endOffsets": "125527"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\38fdf0035e0d96201bc1ef115272625c\\transformed\\constraintlayout-2.0.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,4,10,11,19,27,33,91,92,93,95,99,100,101,104,112,122,150,151,156,157,162,167,168,169,174,175,180,181,186,187,188,194,195,196,201,206,220,221,225,226,227,228,231,232,235,238,239,240,241,242,245,248,249,250,251,256,259,262,263,264,269,270,271,274,277,278,281,284,287,290,291,292,295,298,299,304,305,310,313,316,317,318,319,320,321,322,323,324,325,412,413,414,415,420,426,427,428,431,457,472,512,513,523,529,532,536,537,538,539,548,1863", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,263,498,559,850,1306,1613,4857,4909,4970,5077,5210,5262,5312,5473,5782,6205,8147,8206,8403,8460,8655,8837,8891,8948,9140,9198,9394,9450,9644,9701,9752,9974,10026,10081,10271,10447,11194,11250,11410,11471,11531,11601,11734,11802,11931,12057,12119,12184,12252,12319,12442,12567,12634,12699,12764,12945,13066,13187,13253,13320,13530,13599,13665,13790,13916,13983,14109,14236,14361,14488,14544,14609,14735,14858,14923,15131,15198,15378,15498,15618,15683,15745,15807,15869,15928,15988,16049,16110,16169,21329,21380,21429,21477,21764,21994,22041,22101,22207,23554,24237,26717,26769,27144,27382,27560,27699,27745,27800,27845,28186,124262", "endLines": "2,8,10,18,19,27,33,91,92,93,98,99,100,101,111,119,122,150,155,156,161,166,167,168,173,174,179,180,185,186,187,193,194,195,200,205,206,220,224,225,226,227,230,231,234,237,238,239,240,241,244,247,248,249,250,255,258,261,262,263,268,269,270,273,276,277,280,283,286,289,290,291,294,297,298,303,304,309,312,315,316,317,318,319,320,321,322,323,324,336,412,413,414,415,425,426,427,430,435,457,472,512,521,528,529,535,536,537,538,547,551,1863", "endColumns": "56,11,60,11,51,47,50,51,60,45,11,51,49,50,11,11,44,58,11,56,11,11,53,56,11,57,11,55,11,56,50,11,51,54,11,11,49,55,11,60,59,69,11,67,11,11,61,64,67,66,11,11,66,64,64,11,11,11,65,66,11,68,65,11,11,66,11,11,11,11,55,64,11,11,64,11,66,11,11,11,64,61,61,61,58,59,60,60,58,11,50,48,47,57,11,46,59,11,11,45,46,51,11,11,54,11,45,54,44,11,11,40", "endOffsets": "202,444,554,845,897,1349,1659,4904,4965,5011,5205,5257,5307,5358,5777,6089,6245,8201,8398,8455,8650,8832,8886,8943,9135,9193,9389,9445,9639,9696,9747,9969,10021,10076,10266,10442,10492,11245,11405,11466,11526,11596,11729,11797,11926,12052,12114,12179,12247,12314,12437,12562,12629,12694,12759,12940,13061,13182,13248,13315,13525,13594,13660,13785,13911,13978,14104,14231,14356,14483,14539,14604,14730,14853,14918,15126,15193,15373,15493,15613,15678,15740,15802,15864,15923,15983,16044,16105,16164,16632,21375,21424,21472,21530,21989,22036,22096,22202,22382,23595,24279,26764,27094,27377,27432,27694,27740,27795,27840,28181,28318,124298"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\86a8ee86ddb77f4af22b4f128e92bf76\\transformed\\savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "1926", "startColumns": "4", "startOffsets": "127575", "endColumns": "53", "endOffsets": "127624"}}, {"source": "D:\\development\\Codes\\MyApplication\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,271,272,270,273,265,264,267,266,263,262,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,203,204,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,234,255,259,243,241,245,207,208,210,209,232,256,251,258,248,225,227,226,228,237,231,244,249,254,242,238,253,221,222,224,223,257,246,235,252,250,216,217,218,220,219,233,239,240,247,211,212,213,214,215,236,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,201,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,202,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,4,4,4,4,4,4,4,4,4,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,13072,13147,12996,13229,12623,12558,12877,12809,12371,12307,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,9070,9122,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,10801,11997,12229,11312,11194,11423,9194,9252,9390,9319,10686,12056,11763,12166,11593,10333,10466,10393,10535,10972,10625,11368,11649,11937,11254,11029,11877,10079,10136,10269,10201,12111,11477,10861,11820,11704,9769,9824,9888,10017,9951,10743,11086,11140,11533,9455,9509,9574,9639,9706,10918,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,8976,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,9025,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "endColumns": "-1,-1,-1,-1,-1,-1,74,81,75,69,185,64,97,67,186,63,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,51,50,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,59,58,56,55,59,53,57,66,64,70,56,54,56,62,55,59,68,72,68,56,60,54,54,59,57,56,59,56,64,63,67,54,55,56,56,58,54,63,62,61,65,57,53,53,59,53,64,64,66,62,53,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,48,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,44,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "endOffsets": "-1,-1,-1,-1,-1,-1,13142,13224,13067,13294,12804,12618,12970,12872,12553,12366,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,9117,9168,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,10856,12051,12281,11363,11249,11472,9247,9314,9450,9385,10738,12106,11815,12224,11644,10388,10530,10461,10599,11024,10681,11418,11699,11992,11307,11081,11932,10131,10196,10328,10264,12161,11528,10913,11872,11758,9819,9883,9946,10074,10012,10796,11135,11189,11588,9504,9569,9634,9701,9764,10967,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,9020,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,9065,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2031,2032,2033,2034,2035,2036,2037,2038,2039,2040,2041,2042,2043,2044,2045,2046,2047,2049,2050,2051,2053,2054,2055,2056,2057,2058,2059,2066,2067,2068,2069,2070,2078,2082,2084,2087,2088,2089,2090,2091,2093,2094,2095,2096,2097,2098,2099,2102,2103,2104,2105,2106,2107,2108,2110,2111,2112,2113,2114,2115,2116,2117,2118,2119,2128,2129,2130,2131,2132,2133,2134,2135,2136,2137,2138,2139,2140,2141,2142,2143,2144,2145,2146,2147,2148,2149,2150,2151,2153,2154,2155,2156,2160,2162,2163,2164,2165,2264,2265,2266,2267,2268,2269,2270,2340,2341,2344,2345,2346,2347,2348,2349,2350,2351,2352,2354,2361,2362,2363,2367,2368,2369,2370,2371,2372,2373,2374,2375,2376,2379,2380,2381,2382,2383,2384,2385,2386,2388,2389,2390,2391,2392,2393,2394,2395,2396,2397,2398,2399,2400,2401,2402,2403,2404,2405,2406,2407,2408,2409,2410,2411,2412,2413,2414,2415,2416,2417,2418,2419,2420,2421,2422,2423,2424,2425,2426,2427,2428,2429,2430,2431,2432,2433,2434,2435,2436,2437,2438,2444,2445,2447,2448,2449,2450,2451,2455,2456,2457,2458,2460,2462,2465,2466,2467,2469,2470,2471,2472,2473,2474,2475,2476,2477,2478,2479,2480,2483,2484,2485,2486,2487,2488,2491,2492,2493,2494,2495,2496,2497", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "134624,134661,134696,134750,134803,134864,134907,134982,135064,135140,135210,135396,135461,135559,135627,135814,135878,136002,136048,136103,136294,136347,136384,136430,136466,136515,136574,137716,137756,137805,137859,137922,138480,138816,138932,139088,139134,139190,139229,139284,139375,139431,139479,139523,139566,139619,139673,139883,139921,139977,140043,140109,140174,140214,140308,140344,140427,140513,140600,140686,140770,140857,140901,140962,141684,141740,141793,141847,141917,141967,142022,142073,142123,142175,142235,142282,142320,142372,142423,142497,142544,142606,142665,142721,142771,142829,142867,142917,143143,143179,143224,143274,143597,143692,143755,143806,143857,153316,153370,153437,153497,153546,153594,153632,164214,164251,164382,164453,164536,164608,164688,164759,164839,164905,164982,165073,165943,165994,166045,166235,166276,166322,166378,166428,166467,166515,166568,166632,166688,166860,166902,166940,166977,167023,167070,167115,167155,167244,167304,167363,167420,167476,167536,167590,167648,167715,167780,167851,167908,167963,168020,168083,168139,168199,168268,168341,168410,168467,168528,168583,168638,168698,168756,168813,168873,168930,168995,169059,169127,169182,169238,169295,169352,169411,169466,169530,169593,169655,169721,169779,169833,169887,169947,170001,170066,170131,170198,170261,170823,170872,170976,171035,171075,171129,171192,171679,171731,171782,171838,171974,172152,172273,172322,172359,172491,172539,172596,172644,172684,172720,172774,172830,172904,172949,173009,173064,173245,173298,173337,173389,173448,173518,173683,173722,173761,173802,173864,173937,173979", "endColumns": "36,34,53,52,60,42,74,81,75,69,185,64,97,67,186,63,40,45,54,40,52,36,45,35,48,58,52,39,48,53,62,46,37,36,36,45,55,38,54,52,55,47,43,42,52,53,42,37,55,65,65,64,39,35,35,82,85,86,85,83,86,43,60,36,55,52,53,69,49,54,50,49,51,59,46,37,51,50,73,46,61,58,55,49,57,37,49,72,35,44,49,56,35,62,50,50,42,53,66,59,48,47,37,55,36,32,70,82,71,79,70,79,65,76,54,33,50,50,49,40,45,55,49,38,47,52,63,55,67,41,37,36,45,46,44,39,35,59,58,56,55,59,53,57,66,64,70,56,54,56,62,55,59,68,72,68,56,60,54,54,59,57,56,59,56,64,63,67,54,55,56,56,58,54,63,62,61,65,57,53,53,59,53,64,64,66,62,53,48,57,58,39,53,62,47,51,50,55,64,38,37,48,36,41,47,56,47,39,35,53,55,73,44,59,54,69,52,38,51,58,69,66,38,38,40,61,72,41,33", "endOffsets": "134656,134691,134745,134798,134859,134902,134977,135059,135135,135205,135391,135456,135554,135622,135809,135873,135914,136043,136098,136139,136342,136379,136425,136461,136510,136569,136622,137751,137800,137854,137917,137964,138513,138848,138964,139129,139185,139224,139279,139332,139426,139474,139518,139561,139614,139668,139711,139916,139972,140038,140104,140169,140209,140245,140339,140422,140508,140595,140681,140765,140852,140896,140957,140994,141735,141788,141842,141912,141962,142017,142068,142118,142170,142230,142277,142315,142367,142418,142492,142539,142601,142660,142716,142766,142824,142862,142912,142985,143174,143219,143269,143326,143628,143750,143801,143852,143895,153365,153432,153492,153541,153589,153627,153683,164246,164279,164448,164531,164603,164683,164754,164834,164900,164977,165032,165102,165989,166040,166090,166271,166317,166373,166423,166462,166510,166563,166627,166683,166751,166897,166935,166972,167018,167065,167110,167150,167186,167299,167358,167415,167471,167531,167585,167643,167710,167775,167846,167903,167958,168015,168078,168134,168194,168263,168336,168405,168462,168523,168578,168633,168693,168751,168808,168868,168925,168990,169054,169122,169177,169233,169290,169347,169406,169461,169525,169588,169650,169716,169774,169828,169882,169942,169996,170061,170126,170193,170256,170310,170867,170925,171030,171070,171124,171187,171235,171726,171777,171833,171898,172008,172185,172317,172354,172396,172534,172591,172639,172679,172715,172769,172825,172899,172944,173004,173059,173129,173293,173332,173384,173443,173513,173580,173717,173756,173797,173859,173932,173974,174008"}}, {"source": "D:\\development\\Codes\\MyApplication\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "6024,7078,7079,7093,7119,7135,7160", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "419714,490903,490967,491811,493278,493944,495292", "endLines": "6029,7078,7092,7118,7134,7159,7160", "endColumns": "12,63,12,12,12,12,61", "endOffsets": "420165,490962,491806,493273,493939,495287,495349"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\6e1bec7b61f7c5e14c57eb6146ea2199\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "1807,1808,1809,1810,1811,1812,1813,1814,1815,1816,1817,1818,1819,1820,1821,1822,1823,1824,1825,1826,1827,1828,1829,1830,1831,1832,1833,1834,1835,1836,1837,1838,1843,1846,1847,1851,1853,1929,2085,2086,2100,2101,2109,2158,2159,2339,2342,2353,2355,2377,2378,2446,2463,2464,2468,5920,5923,5926", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "121133,121192,121251,121311,121371,121431,121491,121551,121611,121671,121731,121791,121851,121910,121970,122030,122090,122150,122210,122270,122330,122390,122450,122510,122569,122629,122689,122748,122807,122866,122925,122984,123248,123432,123490,123689,123774,127743,138969,139034,139716,139782,140250,143485,143537,164152,164284,165037,165107,166756,166806,170930,172190,172237,172401,412872,412984,413095", "endLines": "1807,1808,1809,1810,1811,1812,1813,1814,1815,1816,1817,1818,1819,1820,1821,1822,1823,1824,1825,1826,1827,1828,1829,1830,1831,1832,1833,1834,1835,1836,1837,1838,1843,1846,1847,1851,1853,1929,2085,2086,2100,2101,2109,2158,2159,2339,2342,2353,2355,2377,2378,2446,2463,2464,2468,5922,5925,5929", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,35,33,49,53,45,46,35,89,12,12,12", "endOffsets": "121187,121246,121306,121366,121426,121486,121546,121606,121666,121726,121786,121846,121905,121965,122025,122085,122145,122205,122265,122325,122385,122445,122505,122564,122624,122684,122743,122802,122861,122920,122979,123038,123317,123485,123540,123735,123824,127791,139029,139083,139777,139878,140303,143532,143592,164209,164333,165068,165136,166801,166855,170971,172232,172268,172486,412979,413090,413285"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\8502c7d6e294f2825540546f57fbfcc0\\transformed\\material-1.11.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "3,9,20,21,22,23,24,25,28,29,30,31,32,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,102,103,123,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,207,209,210,212,213,214,215,216,217,218,219,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,416,417,418,436,438,439,440,441,442,443,444,445,446,447,448,449,453,454,455,456,458,467,468,469,470,471,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,510,511,522,530,531,560,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,638,639,640,641,642,643,644,645,646,647,648,649,650,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,700,701,702,703,704,705,706,707,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,777,778,779,780,781,782,783,784,785,786,787,788,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,804,805,806,807,808,821,822,823,824,825,826,827,828,829,830,831,832,833,834,835,836,837,838,839,840,841,842,843,844,845,846,847,848,849,850,851,852,853,854,855,856,857,858,859,860,861,862,863,864,865,866,867,868,869,870,871,872,873,874,1080,1084,1093,1094,1095,1096,1097,1098,1099,1100,1101,1102,1103,1104,1105,1106,1107,1108,1109,1110,1111,1112,1113,1114,1115,1116,1117,1118,1119,1120,1121,1122,1123,1124,1125,1126,1127,1128,1129,1130,1131,1132,1133,1134,1135,1136,1137,1138,1139,1140,1156,1157,1158,1159,1160,1161,1162,1163,1164,1165,1166,1167,1168,1169,1170,1171,1172,1173,1174,1175,1176,1177,1178,1179,1180,1181,1182,1183,1184,1185,1186,1187,1188,1189,1190,1191,1192,1193,1194,1195,1196,1197,1198,1199,1200,1201,1202,1203,1204,1205,1206,1207,1208,1209,1210,1211,1212,1213,1214,1215,1216,1217,1218,1219,1220,1221,1222,1223,1224,1225,1226,1227,1228,1229,1230,1231,1232,1233,1234,1235,1236,1237,1238,1239,1240,1241,1242,1243,1244,1245,1246,1247,1248,1249,1250,1251,1252,1253,1254,1255,1256,1257,1258,1259,1260,1261,1262,1263,1264,1265,1266,1267,1268,1269,1270,1271,1272,1273,1274,1275,1276,1277,1278,1279,1280,1281,1282,1283,1284,1285,1286,1287,1288,1289,1290,1291,1292,1293,1294,1295,1296,1297,1298,1299,1300,1301,1302,1303,1304,1305,1306,1307,1308,1309,1310,1311,1312,1313,1314,1315,1316,1317,1318,1319,1320,1321,1322,1323,1324,1325,1326,1327,1328,1329,1330,1331,1332,1333,1334,1335,1336,1337,1338,1339,1340,1341,1342,1343,1344,1345,1346,1347,1348,1349,1350,1351,1352,1353,1354,1355,1356,1357,1358,1359,1360,1361,1362,1363,1364,1365,1366,1367,1368,1369,1370,1371,1372,1373,1374,1375,1376,1377,1378,1379,1380,1381,1382,1383,1384,1385,1386,1387,1388,1389,1390,1391,1392,1393,1394,1395,1396,1397,1398,1399,1400,1401,1402,1403,1404,1405,1406,1407,1408,1409,1410,1411,1412,1413,1414,1415,1416,1417,1418,1419,1420,1421,1422,1423,1424,1425,1426,1427,1428,1429,1430,1431,1432,1433,1434,1435,1436,1437,1438,1439,1440,1441,1442,1443,1444,1445,1446,1447,1448,1449,1450,1451,1452,1453,1454,1455,1456,1457,1458,1459,1460,1461,1462,1463,1464,1465,1466,1467,1468,1469,1470,1471,1472,1473,1474,1475,1476,1477,1478,1479,1480,1481,1482,1483,1484,1485,1486,1487,1488,1489,1490,1491,1492,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1532,1533,1534,1535,1536,1537,1538,1539,1540,1541,1542,1543,1544,1545,1546,1547,1548,1549,1550,1551,1552,1553,1554,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1568,1569,1570,1571,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1586,1587,1588,1589,1590,1591,1592,1593,1594,1595,1596,1597,1598,1599,1600,1601,1602,1603,1604,1605,1606,1607,1608,1609,1610,1611,1612,1613,1614,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1627,1628,1629,1630,1631,1632,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1646,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1658,1659,1660,1661,1662,1663,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1692,1693,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1711,1712,1713,1714,1715,1716,1717,1718,1719,1720,1721,1722,1723,1724,1725,1726,1727,1728,1729,1730,1731,1732,1733,1734,1735,1736,1737,1738,1739,1740,1741,1742,1743,1744,1745,1746,1747,1748,1749,1750,1751,1752,1753,1754,1755,1756,1757,1758,1759,1760,1761,1762,1763,1764,1765,1766,1767,1768,1769,1770,1771,1802,1803,1858,1859,1860,1864,1865,1866,1867,1868,1869,1871,1872,1873,1874,1875,1876,1886,1889,1890,1891,1909,1910,1911,1912,1913,1914,1915,1923,1932,1933,1936,1937,1938,1939,1940,1941,1942,1943,1944,1945,1946,1947,1948,1949,1950,1951,1952,1953,1954,1955,1956,1957,1958,1959,1960,1961,1962,1963,1964,1965,1966,1967,1968,1969,1970,1971,1972,1973,1974,1975,1976,1977,1978,1979,1980,1981,1982,1983,1984,1985,1986,1987,1988,1989,1990,1991,1992,1993,1994,1995,1998,2000,2052,2060,2061,2062,2063,2064,2065,2079,2080,2081,2083,2120,2123,2125,2126,2127,2152,2157,2161,2166,2167,2168,2169,2170,2171,2172,2173,2174,2175,2176,2177,2178,2179,2180,2181,2242,2243,2244,2245,2246,2247,2248,2249,2250,2251,2252,2253,2254,2255,2256,2257,2258,2259,2260,2261,2262,2263,2271,2272,2273,2274,2275,2276,2277,2278,2279,2280,2283,2286,2289,2290,2291,2292,2293,2294,2295,2296,2297,2298,2299,2300,2301,2302,2303,2304,2305,2306,2307,2308,2309,2310,2311,2312,2313,2314,2315,2316,2317,2318,2319,2320,2321,2322,2323,2324,2325,2326,2327,2328,2329,2330,2331,2332,2333,2334,2335,2336,2337,2338,2356,2357,2358,2359,2360,2439,2442,2443,2452,2453,2503,2507,2511,2515,2519,2520,2563,2571,2578,2748,2751,2761,2770,2779,2848,2849,2850,2851,2857,2858,2859,2860,2861,2862,2868,2869,2870,2871,2872,2877,2878,2882,2883,2889,2893,2894,2895,2896,2906,2907,2908,2912,2913,2919,2923,2993,2996,2997,3001,3002,3005,3006,3007,3008,3271,3278,3538,3544,3807,3814,4074,4080,4143,4225,4277,4359,4421,4503,4567,4619,4701,4709,4715,4726,4730,4734,4747,5522,5538,5545,5551,5568,5581,5601,5618,5627,5632,5639,5659,5672,5689,5695,5701,5708,5712,5718,5732,5735,5745,5746,5747,5795,5799,5803,5807,5808,5809,5812,5828,5835,5849,5894,5930,5936,5940,5944,5949,5956,5962,5963,5966,5970,5975,5988,5992,5997,6002,6007,6010,6013,6016,6020,6169,6170,6171,6172,6400,6401,6402,6403,6404,6405,6406,6407,6408,6409,6410,6411,6412,6413,6414,6415,6416,6417,6418,6422,6426,6430,6434,6438,6442,6446,6447,6448,6449,6450,6451,6452,6453,6457,6461,6462,6466,6467,6470,6474,6477,6480,6483,6487,6490,6493,6497,6501,6505,6509,6512,6513,6514,6515,6518,6522,6525,6528,6531,6534,6537,6540,6611,6614,6615,6618,6621,6622,6625,6626,6627,6631,6632,6637,6644,6651,6658,6665,6672,6679,6686,6693,6700,6709,6718,6727,6734,6743,6752,6755,6758,6759,6760,6761,6762,6763,6764,6765,6766,6767,6768,6769,6770,6774,6779,6784,6787,6788,6789,6790,6791,6799,6807,6808,6816,6820,6828,6836,6844,6852,6860,6861,6869,6877,6878,6881,6920,6922,6927,6929,6934,6938,6942,6943,6944,6945,6949,6953,6954,6958,6959,6960,6961,6962,6963,6964,6965,6966,6967,6968,6969,6970,6971,6972,6973,6977,6981,6982,6986,6987,6988,6993,6994,6995,6996,6997,6998,6999,7000,7001,7002,7003,7004,7005,7006,7007,7008,7009,7010,7011,7012,7013,7017,7018,7019,7025,7026,7030,7032,7033,7038,7039,7040,7041,7042,7043,7047,7048,7049,7055,7056,7060,7062,7066,7070,7074,7173,7174,7175,7176,7179,7182,7185,7188,7191,7196,7200,7203,7204,7209,7213,7218,7224,7230,7235,7239,7244,7248,7252,7293,7294,7295,7296,7297,7301,7302,7303,7304,7308,7312,7316,7320,7324,7328,7332,7336,7342,7343,7384,7398,7403,7429,7436,7439,7450,7455,7458,7461,7516,7522,7523,7526,7529,7532,7535,7538,7541,7544,7548,7551,7552,7553,7561,7569,7572,7577,7582,7587,7592,7596,7600,7601,7609,7610,7611,7612,7613,7621,7626,7631,7632,7633,7634,7659,7665,7670,7673,7677,7680,7684,7694,7697,7702,7705,7709,7810,7818,7832,7845,7849,7864,7875,7878,7889,7894,7898,7933,7934,7935,7947,7955,7963,7971,7979,7999,8002,8029,8034,8054,8057,8060,8067,8080,8089,8092,8112,8122,8126,8130,8143,8147,8151,8155,8161,8165,8182,8190,8194,8198,8202,8205,8209,8213,8217,8227,8234,8241,8245,8271,8281,8306,8315,8335,8345,8349,8359,8384,8394,8397,8401,8402,8403,8404,8408,8414,8420,8421,8434,8435,8436,8439,8442,8445,8448,8451,8454,8457,8460,8463,8466,8469,8472,8475,8478,8481,8484,8487,8490,8493,8496,8499,8500,8505,8506,8519,8529,8533,8538,8543,8547,8550,8554,8558,8561,8565,8568,8572,8577,8582,8585,8592,8596,8600,8609,8614,8619,8620,8624,8627,8631,8644,8649,8657,8661,8665,8682,8686,8691,8709,8716,8720,8750,8753,8756,8759,8762,8765,8768,8787,8793,8801,8808,8820,8828,8833,8838,8842,8853,8857,8865,8868,8873,8874,8875,8876,8880,8884,8888,8892,8927,8930,8934,8938,8972,8975,8979,8983,8992,8998,9001,9011,9015,9016,9023,9027,9034,9035,9036,9039,9044,9049,9050,9054,9069,9088,9092,9093,9105,9115,9116,9128,9133,9157,9160,9166,9169,9178,9186,9190,9193,9196,9199,9203,9206,9223,9227,9230,9245,9248,9256,9261,9268,9273,9274,9279,9280,9286,9292,9298,9330,9341,9358,9365,9369,9372,9385,9394,9398,9403,9407,9411,9415,9419,9423,9427,9431,9436,9439,9451,9456,9465,9468,9475,9476,9480,9489,9495,9499,9500,9504,9525,9531,9535,9539,9540,9558,9559,9560,9561,9562,9567,9570,9571,9577,9578,9590,9602,9609,9610,9615,9620,9621,9625,9639,9644,9650,9656,9662,9667,9673,9679,9680,9686,9701,9706,9715,9724,9727,9741,9746,9757,9761,9770,9779,9780,9787,13296,13297,13298,13299,13300,13301,13302,13303,13304,13305,13306,13307,13308,13309,13310,13311,13312,13313,13314,13315,13316,13317,13318,13319,13320,13321,13322,13323,13324,13325,13326,13327,13328,13329,13330,13331,13332,13333,13334,13335,13336,13337,13338,13339,13340,13341,13342,13343,13344,13345,13346,13347,13348,13349,13350,13351,13352,13353,13354,13355,13356,13357,13358,13359,13360,13361,13362,13363,13364,13365,13366,13367,13368,13369,13370,13371,13372,13373,13374,13375,13376,13377,13378,13379,13380,13381,13382,13383,13384,13385,13386,13387,13388,13389,13390,13391,13392,13393,13394,13395,13396,13397,13398,13399,13400,13401,13402,13403,13404,13405,13406,13407,13408,13409,13410,13411,13412,13413,13414,13415,13416,13417,13418,13419,13420,13421,13422,13423,13424,13425,13426,13427,13428,13429,13430,13431,13432,13433,13434,13435,13436,13437,13438,13439,13440,13441,13442,13443,13444,13445,13446,13447,13448,13449,13450,13451,13452,13453,13454,13455,13456,13457,13458,13459,13460,13461,13462,13463,13464,13465,13466,13467,13468,13469,13470,13471,13472,13473,13474,13475,13476,13477,13478,13479,13480,13481,13482,13483,13484,13485,13486,13487,13488,13489,13490,13491,13492,13493,13494,13495,13496,13497,13498,13499,13500,13501,13502,13503,13504,13505,13506,13507,13508,13509,13510,13511,13512,13513,13514,13515,13516,13517,13518,13519,13520,13521,13522,13523,13524,13525,13526,13527,13528,13529,13530,13531,13532,13533,13534,13535,13536,13537,13538,13539,13540,13541,13542,13543,13544,13545,13546,13547,13548,13549,13550,13551,13552,13553,13554,13555,13556,13557,13558,13559,13560,13561,13562,13563,13564,13565,13566,13567,13568,13569,13570,13571,13572,13573,13574,13575,13576,13577,13578,13579,13580,13581,13582,13583,13584,13585,13586,13587,13588,13589,13590,13591,13592,13593,13594,13595,13596,13597,13598,13599,13600,13601,13602,13603,13604,13605,13606,13607,13608,13609,13610,13611,13612,13613,13614,13615,13616,13617,13618,13619,13620,13621,13622,13623,13624,13625,13626,13627,13628,13629,13630,13631,13632,13633,13634,13635,13636,13637,13638,13639,13640,13641,13642,13643,13644,13645,13646,13647,13648,13649,13650,13651,13652,13653,13654,13655,13656,13657,13658,13659,13660,13661,13662,13663,13664,13665,13666,13667,13668,13669,13670,13671", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "207,449,902,957,1013,1073,1134,1199,1354,1404,1454,1507,1565,1664,1733,1781,1852,1924,1996,2069,2136,2185,2239,2276,2327,2387,2434,2490,2539,2597,2651,2712,2768,2819,2879,2935,2998,3047,3103,3159,3209,3268,3323,3385,3432,3486,3542,3594,3649,3703,3757,3811,3860,3918,3972,4029,4085,4132,4185,4241,4301,4364,4423,4485,4535,4589,4643,4691,4748,4801,5363,5417,6250,6361,6423,6479,6539,6592,6653,6732,6813,6885,6964,7044,7120,7198,7267,7343,7420,7491,7564,7640,7718,7787,7863,7940,8004,8075,10497,10593,10646,10750,10817,10870,10922,10972,11030,11095,11143,16637,16704,16770,16828,16897,16955,17024,17094,17167,17241,17309,17376,17446,17512,17585,17645,17721,17781,17841,17916,17984,18050,18118,18178,18237,18294,18360,18422,18479,18547,18620,18690,18752,18813,18881,18943,19013,19082,19138,19197,19259,19321,19388,19445,19506,19567,19628,19689,19745,19801,19857,19913,19971,20029,20087,20145,20202,20259,20316,20373,20432,20491,20549,20632,20715,20788,20842,20911,20967,21048,21129,21200,21535,21588,21646,22387,22501,22547,22607,22661,22731,22801,22866,22932,22997,23065,23134,23202,23332,23385,23444,23502,23600,23986,24038,24084,24134,24190,24284,24342,24400,24462,24525,24587,24646,24706,24771,24837,24902,24964,25026,25088,25150,25212,25274,25340,25407,25473,25536,25600,25663,25731,25792,25854,25916,25979,26043,26106,26170,26248,26307,26373,26453,26514,26608,26666,27099,27437,27501,28821,31032,31106,31177,31243,31317,31386,31457,31530,31601,31669,31742,31818,31888,31966,32034,32100,32161,32230,32294,32360,32428,32494,32557,32625,32696,32761,32834,32897,32978,33042,33108,33178,33248,33318,33388,34217,34274,34332,34391,34451,34510,34569,34628,34687,34746,34805,34864,34923,34982,35041,35101,35162,35224,35285,35346,35407,35468,35529,35590,35650,35711,35772,35832,35893,35954,36015,36076,36137,36198,36259,36320,36381,36442,36503,36571,36640,36710,36779,36848,36917,36986,37055,37124,37193,37262,37331,37400,37460,37521,37583,37644,37705,37766,37827,37888,37949,38010,38071,38132,38193,38255,38318,38382,38445,38508,38571,38634,38697,38760,38823,38886,38949,39012,39073,39135,39198,39260,39322,39384,39446,39508,39570,39632,39694,39756,39818,39875,39961,40041,40131,40226,40318,40410,40500,40583,40676,40763,40860,40951,41052,41139,41242,41331,41430,41522,41622,41706,41800,41888,41986,42069,42160,42254,42353,42455,42553,42653,42740,42840,42926,43022,43110,43191,43282,43378,43471,43564,43655,43740,43834,43923,44021,44114,44216,44304,44408,44499,44599,44692,44793,44878,44973,45062,45161,45246,45338,45433,45533,45636,45735,45838,45927,46028,46115,46212,46300,46396,46488,46588,46678,46776,46861,46950,47039,47132,47219,47982,48048,48124,48193,48272,48345,48425,48505,48582,48650,48728,48804,48875,48956,49029,49112,49187,49272,49345,49426,49507,49581,49665,49735,49813,49883,49963,50041,50113,50195,50265,50342,50422,50507,50595,50679,50766,50840,50918,50996,51067,51148,51239,51322,51418,51516,51623,51688,51754,51807,51883,51949,52036,52112,64851,65086,65681,65735,65814,65892,65965,66030,66093,66159,66230,66301,66371,66433,66502,66568,66628,66695,66762,66818,66869,66922,66974,67028,67099,67162,67221,67283,67342,67415,67482,67552,67612,67675,67750,67822,67918,67989,68045,68116,68173,68230,68296,68360,68431,68488,68541,68604,68656,68714,69951,70020,70086,70145,70228,70287,70344,70411,70481,70555,70617,70686,70756,70855,70952,71051,71137,71223,71304,71379,71468,71559,71643,71702,71748,71814,71871,71938,71995,72077,72142,72208,72331,72415,72536,72601,72663,72761,72835,72918,73007,73071,73150,73224,73286,73382,73447,73506,73562,73618,73678,73785,73832,73892,73953,74017,74078,74138,74196,74239,74288,74340,74391,74443,74492,74541,74606,74672,74732,74793,74849,74908,74957,75005,75063,75120,75222,75279,75354,75402,75453,75515,75580,75632,75706,75769,75832,75900,75950,76012,76072,76129,76189,76238,76306,76412,76514,76583,76654,76710,76759,76859,76930,77040,77129,77220,77302,77400,77456,77557,77667,77766,77829,77935,78012,78124,78251,78363,78490,78560,78674,78805,78902,78970,79088,79191,79309,79370,79444,79511,79616,79738,79812,79879,79989,80088,80161,80258,80380,80498,80616,80677,80799,80916,80984,81090,81192,81272,81343,81439,81506,81580,81654,81740,81828,81918,81996,82073,82173,82244,82365,82486,82550,82675,82749,82873,82997,83064,83173,83301,83413,83492,83570,83671,83742,83864,83986,84051,84177,84289,84395,84463,84562,84666,84729,84795,84879,84992,85105,85223,85301,85373,85509,85645,85730,85870,86008,86146,86288,86370,86479,86590,86718,86846,86978,87108,87238,87372,87434,87530,87597,87714,87835,87932,88014,88101,88188,88319,88450,88585,88662,88739,88850,88964,89038,89147,89259,89361,89457,89561,89628,89722,89794,89904,90010,90083,90174,90276,90379,90474,90581,90686,90808,90930,91056,91115,91173,91297,91421,91549,91667,91785,91907,91993,92090,92224,92358,92438,92576,92708,92840,92976,93051,93127,93230,93304,93417,93498,93555,93616,93675,93735,93793,93854,93912,93962,94011,94078,94137,94196,94245,94316,94400,94470,94541,94621,94690,94753,94821,94887,94955,95020,95086,95163,95241,95347,95453,95549,95678,95767,95894,95960,96029,96115,96181,96264,96362,96458,96554,96652,96761,96856,96945,97007,97067,97132,97189,97270,97324,97381,97478,97588,97649,97764,97885,97980,98072,98165,98267,98323,98382,98431,98523,98572,98626,98680,98734,98788,98842,98897,99007,99117,99225,99335,99445,99555,99665,99773,99879,99983,100087,100191,100286,100381,100474,100567,100671,100777,100881,100985,101078,101171,101264,101357,101465,101571,101677,101783,101880,101975,102070,102165,102271,102377,102483,102589,102687,102783,102879,102977,103042,103146,103204,103268,103329,103391,103451,103516,103578,103646,103704,103767,103830,103897,103972,104045,104111,104163,104216,104268,104325,104409,104504,104589,104670,104750,104827,104906,104983,105057,105131,105202,105282,105354,105429,105494,105555,105615,105690,105764,105837,105907,105979,106049,106122,106186,106256,106302,106371,106423,106508,106591,106648,106714,106781,106847,106928,107003,107059,107112,107173,107231,107281,107330,107379,107428,107490,107542,107587,107668,107719,107773,107826,107880,107931,107980,108046,108097,108158,108219,108281,108331,108372,108449,108508,108567,108626,108687,108743,108799,108866,108927,108992,109047,109112,109181,109249,109327,109396,109456,109527,109601,109666,109738,109808,109875,109959,110028,110095,110165,110228,110295,110363,110446,110525,110615,110692,110760,110827,110905,110962,111019,111087,111153,111209,111269,111328,111382,111432,111482,111530,111592,111643,111716,111796,111876,111940,112007,112078,112136,112197,112263,112322,112389,112449,112509,112572,112640,112701,112768,112846,112916,112965,113022,113091,113152,113240,113328,113416,113504,113591,113678,113765,113852,113910,113984,114054,114110,114181,114246,114308,114383,114456,114546,114612,114678,114739,114803,114865,114923,114994,115077,115136,115207,115273,115338,115399,115458,115529,115595,115660,115743,115819,115894,115975,116035,116104,116174,116243,116298,116354,116410,116471,116529,116585,116639,116694,116756,116813,116907,116976,117077,117128,117198,117261,117317,117375,117434,117488,117574,117658,117728,117797,117867,117982,118103,118170,118237,118312,118379,118438,118492,118546,118600,118653,118705,120643,120780,124019,124068,124118,124303,124351,124407,124465,124527,124582,124693,124764,124828,124887,124949,125015,125532,125677,125721,125766,126744,126795,126842,126887,126938,126989,127040,127424,127922,127988,128167,128230,128302,128359,128413,128468,128526,128581,128640,128696,128765,128834,128903,128973,129036,129099,129162,129225,129290,129355,129420,129485,129548,129612,129676,129740,129791,129869,129947,130018,130090,130163,130235,130301,130367,130435,130503,130569,130636,130710,130773,130830,130890,130955,131022,131087,131144,131205,131263,131367,131477,131586,131690,131768,131833,131900,131966,132036,132083,132135,132311,132438,136144,136627,136758,136942,137120,137358,137547,138518,138616,138731,138853,140999,141159,141281,141370,141527,142990,143331,143633,143900,144087,144183,144273,144369,144459,144625,144748,144871,145041,145147,145262,145377,145479,145585,145702,150184,150266,150439,150607,150755,150914,151069,151242,151359,151476,151644,151756,151870,152042,152218,152376,152509,152621,152767,152919,153051,153194,153688,153866,154002,154098,154234,154329,154496,154589,154681,154868,155024,155202,155366,155548,155865,156047,156229,156419,156651,156841,157018,157180,157337,157447,157630,157767,157971,158155,158339,158499,158657,158841,159068,159271,159442,159662,159884,160039,160239,160423,160526,160716,160857,161022,161193,161393,161597,161799,161964,162169,162368,162567,162764,162855,163004,163154,163238,163387,163532,163684,163825,163991,165141,165219,165520,165686,165841,170315,170473,170637,171240,171463,174455,174732,175004,175282,175527,175589,178417,178868,179324,190461,190609,191123,191560,191994,196334,196419,196540,196639,197044,197141,197258,197345,197468,197569,197975,198074,198193,198286,198393,198736,198843,199088,199209,199618,199866,199966,200071,200190,200699,200846,200965,201216,201349,201764,202018,207233,207480,207605,207922,208043,208271,208392,208525,208672,229303,229795,250175,250599,271275,271769,292194,292620,297461,302878,306969,312400,317142,322519,326503,330495,335886,336433,336866,337622,337852,338095,339228,387398,388302,388886,389359,390789,391533,392726,393780,394258,394551,394934,396449,397214,398357,398798,399239,399835,400109,400520,401536,401714,402467,402604,402695,404889,405155,405477,405687,405796,405915,406099,407217,407687,408438,411021,413290,413666,413894,414150,414409,414985,415339,415461,415600,415892,416152,417080,417366,417769,418171,418514,418726,418927,419140,419429,430772,430845,430932,431017,443542,443654,443760,443883,444015,444138,444268,444392,444525,444656,444781,444898,445018,445150,445278,445392,445510,445623,445744,445932,446119,446300,446483,446667,446832,447014,447134,447254,447362,447472,447584,447692,447802,447967,448133,448285,448450,448551,448671,448842,449003,449166,449327,449494,449613,449730,449910,450092,450273,450456,450611,450756,450878,451013,451176,451369,451495,451647,451789,451959,452115,452287,459050,459245,459337,459510,459672,459767,459936,460030,460119,460362,460451,460744,461160,461580,462001,462427,462844,463260,463677,464095,464509,464979,465452,465924,466335,466806,467278,467468,467674,467780,467888,467994,468106,468220,468332,468446,468562,468676,468784,468894,469002,469264,469643,470047,470194,470302,470412,470520,470634,471043,471457,471573,471991,472232,472662,473097,473507,473929,474339,474461,474870,475286,475408,475626,478446,478514,478858,478938,479294,479444,479588,479664,479776,479866,480128,480393,480501,480653,480761,480837,480949,481039,481141,481249,481357,481457,481565,481650,481754,481841,481919,482033,482125,482389,482656,482766,482919,483029,483113,483502,483600,483708,483802,483932,484040,484162,484298,484406,484526,484660,484782,484910,485052,485178,485318,485444,485562,485694,485792,485902,486202,486314,486432,486896,487012,487315,487441,487537,487938,488048,488172,488310,488420,488542,488854,488978,489108,489584,489712,490027,490165,490327,490543,490699,496357,496425,496509,496613,496816,497005,497206,497399,497604,497917,498129,498295,498411,498657,498873,499186,499612,500074,500311,500463,500723,500867,501009,504241,504355,504475,504591,504685,505006,505105,505223,505324,505603,505888,506167,506449,506702,506961,507214,507470,507894,507970,511220,512575,513019,514873,515448,515656,516666,517046,517212,517353,522373,522799,522911,523046,523199,523396,523567,523750,523925,524112,524384,524542,524626,524730,525217,525773,525931,526150,526381,526604,526839,527061,527327,527465,528064,528178,528316,528428,528552,529123,529618,530164,530309,530402,530494,532421,532991,533289,533478,533684,533877,534087,534971,535116,535508,535666,535883,543939,544371,545246,545866,546063,547011,547776,547899,548672,548893,549093,551070,551170,551260,551946,552699,553464,554227,555002,556215,556380,557993,558314,559377,559587,559757,560327,561222,561855,562021,563507,564123,564359,564580,565538,565803,566068,566315,566729,566965,568250,568699,568886,569135,569377,569553,569794,570027,570252,570847,571322,571846,572107,573458,573933,575159,575629,576677,577129,577373,577830,579075,579558,579708,580052,580198,580336,580472,580760,581264,581773,581889,582791,582913,583025,583202,583468,583738,584004,584272,584528,584788,585044,585302,585554,585810,586062,586316,586548,586784,587036,587292,587544,587798,588030,588264,588376,588801,588925,590017,590832,591028,591352,591741,592093,592334,592548,592847,593039,593354,593561,593907,594207,594608,594827,595240,595477,595847,596571,596926,597195,597335,597589,597733,598010,599002,599411,600043,600389,600757,601831,602194,602594,604102,604687,605005,607540,607734,607952,608178,608390,608589,608796,610000,610295,610852,611242,611874,612351,612596,612947,613193,613953,614217,614640,614831,615210,615298,615406,615514,615827,616152,616471,616802,619505,619693,619954,620203,622787,622979,623244,623497,624029,624437,624636,625220,625455,625579,625991,626205,626607,626710,626840,627015,627267,627463,627603,627797,628808,629877,630165,630295,631072,631729,631875,632581,632819,634359,634509,634926,635091,635777,636247,636443,636534,636618,636762,636996,637163,638091,638377,638537,639152,639311,639639,639866,640378,640740,640819,641158,641263,641628,641999,642360,644234,644863,645939,646363,646616,646768,647816,648553,648756,649002,649249,649467,649709,650030,650294,650599,650822,651133,651322,652037,652306,652800,653026,653466,653625,653909,654654,655019,655324,655482,655720,657039,657437,657665,657885,658027,659317,659423,659553,659691,659815,660103,660272,660372,660657,660771,661654,662409,662848,662972,663218,663411,663545,663736,664515,664733,665024,665303,665620,665842,666137,666420,666524,666865,667681,667997,668558,669064,669269,670055,670460,671121,671310,671861,672427,672547,672949,797758,797853,797946,798009,798091,798184,798277,798364,798462,798553,798644,798732,798816,798912,799016,799116,799222,799325,799426,799530,799636,799735,799841,799943,800050,800159,800270,800401,800521,800637,800755,800854,800961,801077,801196,801324,801413,801508,801585,801674,801765,801858,801932,802029,802124,802222,802321,802425,802521,802623,802726,802826,802929,803014,803115,803213,803303,803398,803485,803591,803693,803787,803878,803972,804048,804140,804229,804332,804443,804526,804612,804707,804804,804900,804988,805089,805190,805293,805399,805497,805594,805689,805787,805890,805990,806093,806198,806316,806432,806527,806620,806705,806801,806895,806987,807089,807196,807279,807383,807488,807588,807689,807794,807894,807995,808094,808196,808290,808397,808499,808602,808695,808791,808893,808996,809092,809194,809297,809394,809497,809595,809699,809804,809901,810009,810123,810238,810346,810460,810575,810677,810782,810890,811000,811116,811233,811328,811425,811524,811629,811735,811834,811939,812045,812145,812251,812352,812459,812578,812677,812782,812884,812986,813086,813189,813284,813388,813473,813577,813681,813779,813883,813989,814087,814192,814290,814403,814497,814586,814675,814758,814849,814932,815030,815120,815216,815305,815399,815487,815583,815668,815776,815877,815978,816076,816182,816273,816372,816469,816567,816663,816756,816866,816964,817059,817169,817261,817361,817460,817547,817651,817756,817855,817962,818069,818168,818277,818369,818480,818591,818702,818806,818921,819037,819164,819284,819381,819480,819572,819671,819763,819862,819948,820042,820145,820241,820344,820440,820543,820640,820738,820841,820934,821024,821125,821208,821299,821384,821476,821579,821674,821770,821863,821957,822036,822143,822234,822333,822426,822529,822633,822734,822835,822939,823033,823137,823241,823354,823460,823566,823674,823791,823892,824000,824100,824203,824308,824415,824511,824590,824680,824764,824856,824929,825026,825108,825193,825278,825375,825468,825563,825662,825759,825850,825941,826033,826128,826235,826343,826445,826542,826639,826732,826819,826903,827000,827097,827190,827277,827368,827467,827566,827661,827750,827831,827930,828034,828131,828236,828333,828417,828516,828620,828717,828822,828919,829017,829118,829224,829323,829430,829529,829628,829719,829808,829897,829979,830072,830163,830274,830375,830475,830587,830700,830798,830906,831000,831100,831189,831281,831392,831502,831597,831713,831839,831965,832084,832212,832337,832462,832580,832707,832816,832925,833038,833161,833284,833400,833525,833622,833730,833852,833968,834084,834193,834281,834382,834471,834572,834659,834747,834844,834936,835042,835142,835218", "endLines": "3,9,20,21,22,23,24,25,28,29,30,31,32,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,102,103,123,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,207,209,210,212,213,214,215,216,217,218,219,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,411,416,417,418,436,438,439,440,441,442,443,444,445,446,447,448,452,453,454,455,456,458,467,468,469,470,471,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,510,511,522,530,531,560,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,638,639,640,641,642,643,644,645,646,647,648,649,650,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,700,701,702,703,704,705,706,707,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,777,778,779,780,781,782,783,784,785,786,787,788,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,804,805,806,807,808,821,822,823,824,825,826,827,828,829,830,831,832,833,834,835,836,837,838,839,840,841,842,843,844,845,846,847,848,849,850,851,852,853,854,855,856,857,858,859,860,861,862,863,864,865,866,867,868,869,870,871,872,873,874,1080,1084,1093,1094,1095,1096,1097,1098,1099,1100,1101,1102,1103,1104,1105,1106,1107,1108,1109,1110,1111,1112,1113,1114,1115,1116,1117,1118,1119,1120,1121,1122,1123,1124,1125,1126,1127,1128,1129,1130,1131,1132,1133,1134,1135,1136,1137,1138,1139,1140,1156,1157,1158,1159,1160,1161,1162,1163,1164,1165,1166,1167,1168,1169,1170,1171,1172,1173,1174,1175,1176,1177,1178,1179,1180,1181,1182,1183,1184,1185,1186,1187,1188,1189,1190,1191,1192,1193,1194,1195,1196,1197,1198,1199,1200,1201,1202,1203,1204,1205,1206,1207,1208,1209,1210,1211,1212,1213,1214,1215,1216,1217,1218,1219,1220,1221,1222,1223,1224,1225,1226,1227,1228,1229,1230,1231,1232,1233,1234,1235,1236,1237,1238,1239,1240,1241,1242,1243,1244,1245,1246,1247,1248,1249,1250,1251,1252,1253,1254,1255,1256,1257,1258,1259,1260,1261,1262,1263,1264,1265,1266,1267,1268,1269,1270,1271,1272,1273,1274,1275,1276,1277,1278,1279,1280,1281,1282,1283,1284,1285,1286,1287,1288,1289,1290,1291,1292,1293,1294,1295,1296,1297,1298,1299,1300,1301,1302,1303,1304,1305,1306,1307,1308,1309,1310,1311,1312,1313,1314,1315,1316,1317,1318,1319,1320,1321,1322,1323,1324,1325,1326,1327,1328,1329,1330,1331,1332,1333,1334,1335,1336,1337,1338,1339,1340,1341,1342,1343,1344,1345,1346,1347,1348,1349,1350,1351,1352,1353,1354,1355,1356,1357,1358,1359,1360,1361,1362,1363,1364,1365,1366,1367,1368,1369,1370,1371,1372,1373,1374,1375,1376,1377,1378,1379,1380,1381,1382,1383,1384,1385,1386,1387,1388,1389,1390,1391,1392,1393,1394,1395,1396,1397,1398,1399,1400,1401,1402,1403,1404,1405,1406,1407,1408,1409,1410,1411,1412,1413,1414,1415,1416,1417,1418,1419,1420,1421,1422,1423,1424,1425,1426,1427,1428,1429,1430,1431,1432,1433,1434,1435,1436,1437,1438,1439,1440,1441,1442,1443,1444,1445,1446,1447,1448,1449,1450,1451,1452,1453,1454,1455,1456,1457,1458,1459,1460,1461,1462,1463,1464,1465,1466,1467,1468,1469,1470,1471,1472,1473,1474,1475,1476,1477,1478,1479,1480,1481,1482,1483,1484,1485,1486,1487,1488,1489,1490,1491,1492,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1532,1533,1534,1535,1536,1537,1538,1539,1540,1541,1542,1543,1544,1545,1546,1547,1548,1549,1550,1551,1552,1553,1554,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1568,1569,1570,1571,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1586,1587,1588,1589,1590,1591,1592,1593,1594,1595,1596,1597,1598,1599,1600,1601,1602,1603,1604,1605,1606,1607,1608,1609,1610,1611,1612,1613,1614,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1627,1628,1629,1630,1631,1632,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1646,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1658,1659,1660,1661,1662,1663,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1692,1693,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1711,1712,1713,1714,1715,1716,1717,1718,1719,1720,1721,1722,1723,1724,1725,1726,1727,1728,1729,1730,1731,1732,1733,1734,1735,1736,1737,1738,1739,1740,1741,1742,1743,1744,1745,1746,1747,1748,1749,1750,1751,1752,1753,1754,1755,1756,1757,1758,1759,1760,1761,1762,1763,1764,1765,1766,1767,1768,1769,1770,1771,1802,1803,1858,1859,1860,1864,1865,1866,1867,1868,1869,1871,1872,1873,1874,1875,1876,1886,1889,1890,1891,1909,1910,1911,1912,1913,1914,1915,1923,1932,1933,1936,1937,1938,1939,1940,1941,1942,1943,1944,1945,1946,1947,1948,1949,1950,1951,1952,1953,1954,1955,1956,1957,1958,1959,1960,1961,1962,1963,1964,1965,1966,1967,1968,1969,1970,1971,1972,1973,1974,1975,1976,1977,1978,1979,1980,1981,1982,1983,1984,1985,1986,1987,1988,1989,1990,1991,1992,1993,1994,1995,1998,2003,2052,2060,2061,2062,2063,2064,2065,2079,2080,2081,2083,2122,2123,2125,2126,2127,2152,2157,2161,2166,2167,2168,2169,2170,2171,2172,2173,2174,2175,2176,2177,2178,2179,2180,2181,2242,2243,2244,2245,2246,2247,2248,2249,2250,2251,2252,2253,2254,2255,2256,2257,2258,2259,2260,2261,2262,2263,2271,2272,2273,2274,2275,2276,2277,2278,2279,2282,2285,2288,2289,2290,2291,2292,2293,2294,2295,2296,2297,2298,2299,2300,2301,2302,2303,2304,2305,2306,2307,2308,2309,2310,2311,2312,2313,2314,2315,2316,2317,2318,2319,2320,2321,2322,2323,2324,2325,2326,2327,2328,2329,2330,2331,2332,2333,2334,2335,2336,2337,2338,2356,2357,2358,2359,2360,2441,2442,2443,2452,2453,2506,2510,2514,2518,2519,2523,2570,2577,2585,2750,2760,2769,2778,2787,2848,2849,2850,2856,2857,2858,2859,2860,2861,2867,2868,2869,2870,2871,2876,2877,2881,2882,2888,2892,2893,2894,2895,2905,2906,2907,2911,2912,2918,2922,2923,2995,2996,3000,3001,3004,3005,3006,3007,3270,3277,3537,3543,3806,3813,4073,4079,4142,4224,4276,4358,4420,4502,4566,4618,4700,4708,4714,4725,4729,4733,4746,4761,5537,5544,5550,5567,5580,5600,5617,5626,5631,5638,5658,5671,5688,5694,5700,5707,5711,5717,5731,5734,5744,5745,5746,5794,5798,5802,5806,5807,5808,5811,5827,5834,5848,5893,5894,5935,5939,5943,5948,5955,5961,5962,5965,5969,5974,5987,5991,5996,6001,6006,6009,6012,6015,6019,6023,6169,6170,6171,6172,6400,6401,6402,6403,6404,6405,6406,6407,6408,6409,6410,6411,6412,6413,6414,6415,6416,6417,6421,6425,6429,6433,6437,6441,6445,6446,6447,6448,6449,6450,6451,6452,6456,6460,6461,6465,6466,6469,6473,6476,6479,6482,6486,6489,6492,6496,6500,6504,6508,6511,6512,6513,6514,6517,6521,6524,6527,6530,6533,6536,6539,6543,6613,6614,6617,6620,6621,6624,6625,6626,6630,6631,6636,6643,6650,6657,6664,6671,6678,6685,6692,6699,6708,6717,6726,6733,6742,6751,6754,6757,6758,6759,6760,6761,6762,6763,6764,6765,6766,6767,6768,6769,6773,6778,6783,6786,6787,6788,6789,6790,6798,6806,6807,6815,6819,6827,6835,6843,6851,6859,6860,6868,6876,6877,6880,6883,6921,6926,6928,6933,6937,6941,6942,6943,6944,6948,6952,6953,6957,6958,6959,6960,6961,6962,6963,6964,6965,6966,6967,6968,6969,6970,6971,6972,6976,6980,6981,6985,6986,6987,6992,6993,6994,6995,6996,6997,6998,6999,7000,7001,7002,7003,7004,7005,7006,7007,7008,7009,7010,7011,7012,7016,7017,7018,7024,7025,7029,7031,7032,7037,7038,7039,7040,7041,7042,7046,7047,7048,7054,7055,7059,7061,7065,7069,7073,7077,7173,7174,7175,7178,7181,7184,7187,7190,7195,7199,7202,7203,7208,7212,7217,7223,7229,7234,7238,7243,7247,7251,7292,7293,7294,7295,7296,7300,7301,7302,7303,7307,7311,7315,7319,7323,7327,7331,7335,7341,7342,7383,7397,7402,7428,7435,7438,7449,7454,7457,7460,7515,7521,7522,7525,7528,7531,7534,7537,7540,7543,7547,7550,7551,7552,7560,7568,7571,7576,7581,7586,7591,7595,7599,7600,7608,7609,7610,7611,7612,7620,7625,7630,7631,7632,7633,7658,7664,7669,7672,7676,7679,7683,7693,7696,7701,7704,7708,7712,7817,7831,7844,7848,7863,7874,7877,7888,7893,7897,7932,7933,7934,7946,7954,7962,7970,7978,7998,8001,8028,8033,8053,8056,8059,8066,8079,8088,8091,8111,8121,8125,8129,8142,8146,8150,8154,8160,8164,8181,8189,8193,8197,8201,8204,8208,8212,8216,8226,8233,8240,8244,8270,8280,8305,8314,8334,8344,8348,8358,8383,8393,8396,8400,8401,8402,8403,8407,8413,8419,8420,8433,8434,8435,8438,8441,8444,8447,8450,8453,8456,8459,8462,8465,8468,8471,8474,8477,8480,8483,8486,8489,8492,8495,8498,8499,8504,8505,8518,8528,8532,8537,8542,8546,8549,8553,8557,8560,8564,8567,8571,8576,8581,8584,8591,8595,8599,8608,8613,8618,8619,8623,8626,8630,8643,8648,8656,8660,8664,8681,8685,8690,8708,8715,8719,8749,8752,8755,8758,8761,8764,8767,8786,8792,8800,8807,8819,8827,8832,8837,8841,8852,8856,8864,8867,8872,8873,8874,8875,8879,8883,8887,8891,8926,8929,8933,8937,8971,8974,8978,8982,8991,8997,9000,9010,9014,9015,9022,9026,9033,9034,9035,9038,9043,9048,9049,9053,9068,9087,9091,9092,9104,9114,9115,9127,9132,9156,9159,9165,9168,9177,9185,9189,9192,9195,9198,9202,9205,9222,9226,9229,9244,9247,9255,9260,9267,9272,9273,9278,9279,9285,9291,9297,9329,9340,9357,9364,9368,9371,9384,9393,9397,9402,9406,9410,9414,9418,9422,9426,9430,9435,9438,9450,9455,9464,9467,9474,9475,9479,9488,9494,9498,9499,9503,9524,9530,9534,9538,9539,9557,9558,9559,9560,9561,9566,9569,9570,9576,9577,9589,9601,9608,9609,9614,9619,9620,9624,9638,9643,9649,9655,9661,9666,9672,9678,9679,9685,9700,9705,9714,9723,9726,9740,9745,9756,9760,9769,9778,9779,9786,9794,13296,13297,13298,13299,13300,13301,13302,13303,13304,13305,13306,13307,13308,13309,13310,13311,13312,13313,13314,13315,13316,13317,13318,13319,13320,13321,13322,13323,13324,13325,13326,13327,13328,13329,13330,13331,13332,13333,13334,13335,13336,13337,13338,13339,13340,13341,13342,13343,13344,13345,13346,13347,13348,13349,13350,13351,13352,13353,13354,13355,13356,13357,13358,13359,13360,13361,13362,13363,13364,13365,13366,13367,13368,13369,13370,13371,13372,13373,13374,13375,13376,13377,13378,13379,13380,13381,13382,13383,13384,13385,13386,13387,13388,13389,13390,13391,13392,13393,13394,13395,13396,13397,13398,13399,13400,13401,13402,13403,13404,13405,13406,13407,13408,13409,13410,13411,13412,13413,13414,13415,13416,13417,13418,13419,13420,13421,13422,13423,13424,13425,13426,13427,13428,13429,13430,13431,13432,13433,13434,13435,13436,13437,13438,13439,13440,13441,13442,13443,13444,13445,13446,13447,13448,13449,13450,13451,13452,13453,13454,13455,13456,13457,13458,13459,13460,13461,13462,13463,13464,13465,13466,13467,13468,13469,13470,13471,13472,13473,13474,13475,13476,13477,13478,13479,13480,13481,13482,13483,13484,13485,13486,13487,13488,13489,13490,13491,13492,13493,13494,13495,13496,13497,13498,13499,13500,13501,13502,13503,13504,13505,13506,13507,13508,13509,13510,13511,13512,13513,13514,13515,13516,13517,13518,13519,13520,13521,13522,13523,13524,13525,13526,13527,13528,13529,13530,13531,13532,13533,13534,13535,13536,13537,13538,13539,13540,13541,13542,13543,13544,13545,13546,13547,13548,13549,13550,13551,13552,13553,13554,13555,13556,13557,13558,13559,13560,13561,13562,13563,13564,13565,13566,13567,13568,13569,13570,13571,13572,13573,13574,13575,13576,13577,13578,13579,13580,13581,13582,13583,13584,13585,13586,13587,13588,13589,13590,13591,13592,13593,13594,13595,13596,13597,13598,13599,13600,13601,13602,13603,13604,13605,13606,13607,13608,13609,13610,13611,13612,13613,13614,13615,13616,13617,13618,13619,13620,13621,13622,13623,13624,13625,13626,13627,13628,13629,13630,13631,13632,13633,13634,13635,13636,13637,13638,13639,13640,13641,13642,13643,13644,13645,13646,13647,13648,13649,13650,13651,13652,13653,13654,13655,13656,13657,13658,13659,13660,13661,13662,13663,13664,13665,13666,13667,13668,13669,13670,13671", "endColumns": "55,48,54,55,59,60,64,54,49,49,52,57,47,68,47,70,71,71,72,66,48,53,36,50,59,46,55,48,57,53,60,55,50,59,55,62,48,55,55,49,58,54,61,46,53,55,51,54,53,53,53,48,57,53,56,55,46,52,55,59,62,58,61,49,53,53,47,56,52,55,53,55,62,61,55,59,52,60,78,80,71,78,79,75,77,68,75,76,70,72,75,77,68,75,76,63,70,71,50,52,54,66,52,51,49,57,64,47,50,66,65,57,68,57,68,69,72,73,67,66,69,65,72,59,75,59,59,74,67,65,67,59,58,56,65,61,56,67,72,69,61,60,67,61,69,68,55,58,61,61,66,56,60,60,60,60,55,55,55,55,57,57,57,57,56,56,56,56,58,58,57,82,82,72,53,68,55,80,80,70,9,52,57,57,57,45,59,53,69,69,64,65,64,67,68,67,9,52,58,57,51,49,51,45,49,55,46,57,57,61,62,61,58,59,64,65,64,61,61,61,61,61,61,65,66,65,62,63,62,67,60,61,61,62,63,62,63,77,58,65,79,60,52,57,50,44,63,58,61,73,70,65,73,68,70,72,70,67,72,75,69,77,67,65,60,68,63,65,67,65,62,67,70,64,72,62,80,63,65,69,69,69,69,66,56,57,58,59,58,58,58,58,58,58,58,58,58,58,59,60,61,60,60,60,60,60,60,59,60,60,59,60,60,60,60,60,60,60,60,60,60,60,67,68,69,68,68,68,68,68,68,68,68,68,68,59,60,61,60,60,60,60,60,60,60,60,60,60,61,62,63,62,62,62,62,62,62,62,62,62,62,60,61,62,61,61,61,61,61,61,61,61,61,61,56,85,79,89,94,91,91,89,82,92,86,96,90,100,86,102,88,98,91,99,83,93,87,97,82,90,93,98,101,97,99,86,99,85,95,87,80,90,95,92,92,90,84,93,88,97,92,101,87,103,90,99,92,100,84,94,88,98,84,91,94,99,102,98,102,88,100,86,96,87,95,91,99,89,97,84,88,88,92,86,90,65,75,68,78,72,79,79,76,67,77,75,70,80,72,82,74,84,72,80,80,73,83,69,77,69,79,77,71,81,69,76,79,84,87,83,86,73,77,77,70,80,90,82,95,97,106,64,65,52,75,65,86,75,75,64,54,53,78,77,72,64,62,65,70,70,69,61,68,65,59,66,66,55,50,52,51,53,70,62,58,61,58,72,66,69,59,62,74,71,95,70,55,70,56,56,65,63,70,56,52,62,51,57,66,68,65,58,82,58,56,66,69,73,61,68,69,98,96,98,85,85,80,74,88,90,83,58,45,65,56,66,56,81,64,65,122,83,120,64,61,97,73,82,88,63,78,73,61,95,64,58,55,55,59,106,46,59,60,63,60,59,57,42,48,51,50,51,48,48,64,65,59,60,55,58,48,47,57,56,101,56,74,47,50,61,64,51,73,62,62,67,49,61,59,56,59,48,67,105,101,68,70,55,48,99,70,109,88,90,81,97,55,100,109,98,62,105,76,111,126,111,126,69,113,130,96,67,117,102,117,60,73,66,104,121,73,66,109,98,72,96,121,117,117,60,121,116,67,105,101,79,70,95,66,73,73,85,87,89,77,76,99,70,120,120,63,124,73,123,123,66,108,127,111,78,77,100,70,121,121,64,125,111,105,67,98,103,62,65,83,112,112,117,77,71,135,135,84,139,137,137,141,81,108,110,127,127,131,129,129,133,61,95,66,116,120,96,81,86,86,130,130,134,76,76,110,113,73,108,111,101,95,103,66,93,71,109,105,72,90,101,102,94,106,104,121,121,125,58,57,123,123,127,117,117,121,85,96,133,133,79,137,131,131,135,74,75,102,73,112,80,56,60,58,59,57,60,57,49,48,66,58,58,48,70,83,69,70,79,68,62,67,65,67,64,65,76,77,105,105,95,128,88,126,65,68,85,65,82,97,95,95,97,108,94,88,61,59,64,56,80,53,56,96,109,60,114,120,94,91,92,101,55,58,48,91,48,53,53,53,53,53,54,109,109,107,109,109,109,109,107,105,103,103,103,94,94,92,92,103,105,103,103,92,92,92,92,107,105,105,105,96,94,94,94,105,105,105,105,97,95,95,97,64,103,57,63,60,61,59,64,61,67,57,62,62,66,74,72,65,51,52,51,56,83,94,84,80,79,76,78,76,73,73,70,79,71,74,64,60,59,74,73,72,69,71,69,72,63,69,45,68,51,84,82,56,65,66,65,80,74,55,52,60,57,49,48,48,48,61,51,44,80,50,53,52,53,50,48,65,50,60,60,61,49,40,76,58,58,58,60,55,55,66,60,64,54,64,68,67,77,68,59,70,73,64,71,69,66,83,68,66,69,62,66,67,82,78,89,76,67,66,77,56,56,67,65,55,59,58,53,49,49,47,61,50,72,79,79,63,66,70,57,60,65,58,66,59,59,62,67,60,66,77,69,48,56,68,60,87,87,87,87,86,86,86,86,57,73,69,55,70,64,61,74,72,89,65,65,60,63,61,57,70,82,58,70,65,64,60,58,70,65,64,82,75,74,80,59,68,69,68,54,55,55,60,57,55,53,54,61,56,93,68,100,50,69,62,55,57,58,53,85,83,69,68,69,114,120,66,66,74,66,58,53,53,53,52,51,73,136,139,48,49,49,47,55,57,61,54,57,70,63,58,61,65,65,42,43,44,42,50,46,44,50,50,50,50,47,65,61,62,71,56,53,54,57,54,58,55,68,68,68,69,62,62,62,62,64,64,64,64,62,63,63,63,50,77,77,70,71,72,71,65,65,67,67,65,66,73,62,56,59,64,66,64,56,60,57,103,109,108,103,77,64,66,65,69,46,51,49,56,12,149,130,183,177,237,188,168,97,114,84,78,11,64,88,156,156,152,153,58,186,95,89,95,89,165,122,122,169,105,114,114,101,105,116,114,81,172,167,147,158,154,172,116,116,167,111,113,171,175,157,132,111,145,151,131,142,121,177,135,95,135,94,166,92,91,186,11,11,11,181,316,181,181,189,231,189,176,161,156,109,182,136,203,183,183,159,157,183,226,202,170,219,221,154,199,183,102,189,140,164,170,199,203,201,164,204,198,198,196,90,148,149,83,148,144,151,140,165,160,77,300,165,154,101,11,163,185,222,124,10,10,10,10,61,10,10,10,10,10,10,10,10,10,84,120,98,10,96,116,86,122,100,10,98,118,92,106,10,106,10,120,10,10,99,104,118,10,146,118,10,132,10,10,111,10,124,10,120,10,120,132,146,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,136,90,10,10,10,10,108,118,10,10,10,10,10,94,10,10,10,10,10,10,121,10,10,10,10,10,10,10,10,10,10,10,10,10,72,86,84,98,111,105,122,131,122,129,123,132,130,124,116,119,131,127,113,117,112,120,10,10,10,10,10,10,10,119,119,107,109,111,107,109,10,10,151,10,100,10,10,10,10,10,10,10,10,10,10,10,10,10,144,121,134,10,10,10,10,10,10,10,10,10,10,91,10,10,94,10,93,88,10,88,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,105,107,105,111,113,111,113,115,113,107,109,107,10,10,10,10,107,109,107,113,10,10,115,10,10,10,10,10,10,10,121,10,10,121,10,10,10,10,10,10,10,10,75,111,89,10,10,107,10,107,75,111,89,101,107,107,99,107,84,103,86,77,113,91,10,10,109,10,109,83,10,97,107,93,129,107,121,135,107,119,133,121,127,141,125,139,125,117,131,97,109,10,111,117,10,115,10,10,95,10,109,123,137,109,121,10,123,129,10,127,10,10,10,10,10,10,67,83,103,10,10,10,10,10,10,10,10,115,10,10,10,10,10,10,10,10,10,10,10,113,119,115,93,10,98,117,100,10,10,10,10,10,10,10,10,10,75,10,10,10,10,10,10,10,10,10,10,10,10,111,10,10,10,10,10,10,10,10,10,83,103,10,10,10,10,10,10,10,10,10,137,10,113,137,111,123,10,10,10,144,92,91,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,99,89,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,145,137,135,10,10,10,115,10,121,111,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,111,10,123,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,139,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,87,107,107,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,123,10,10,10,102,129,10,10,10,139,10,10,10,10,129,10,10,145,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,78,10,104,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,158,10,10,10,10,157,10,10,10,10,10,141,10,105,129,137,123,10,10,99,10,113,10,10,10,123,10,10,133,10,10,10,10,10,10,10,10,10,103,10,10,10,10,10,10,10,10,10,10,10,10,119,10,10,94,92,62,81,92,92,86,97,90,90,87,83,95,103,99,105,102,100,103,105,98,105,101,106,108,110,130,119,115,117,98,106,115,118,127,88,94,76,88,90,92,73,96,94,97,98,103,95,101,102,99,102,84,100,97,89,94,86,105,101,93,90,93,75,91,88,102,110,82,85,94,96,95,87,100,100,102,105,97,96,94,97,102,99,102,104,117,115,94,92,84,95,93,91,101,106,82,103,104,99,100,104,99,100,98,101,93,106,101,102,92,95,101,102,95,101,102,96,102,97,103,104,96,107,113,114,107,113,114,101,104,107,109,115,116,94,96,98,104,105,98,104,105,99,105,100,106,118,98,104,101,101,99,102,94,103,84,103,103,97,103,105,97,104,97,112,93,88,88,82,90,82,97,89,95,88,93,87,95,84,107,100,100,97,105,90,98,96,97,95,92,109,97,94,109,91,99,98,86,103,104,98,106,106,98,108,91,110,110,110,103,114,115,126,119,96,98,91,98,91,98,85,93,102,95,102,95,102,96,97,102,92,89,100,82,90,84,91,102,94,95,92,93,78,106,90,98,92,102,103,100,100,103,93,103,103,112,105,105,107,116,100,107,99,102,104,106,95,78,89,83,91,72,96,81,84,84,96,92,94,98,96,90,90,91,94,106,107,101,96,96,92,86,83,96,96,92,86,90,98,98,94,88,80,98,103,96,104,96,83,98,103,96,104,96,97,100,105,98,106,98,98,90,88,88,81,92,90,110,100,99,111,112,97,107,93,99,88,91,110,109,94,115,125,125,118,127,124,124,117,126,108,108,112,122,122,115,124,96,107,121,115,115,108,87,100,88,100,86,87,96,91,105,99,75,76", "endOffsets": "258,493,952,1008,1068,1129,1194,1249,1399,1449,1502,1560,1608,1728,1776,1847,1919,1991,2064,2131,2180,2234,2271,2322,2382,2429,2485,2534,2592,2646,2707,2763,2814,2874,2930,2993,3042,3098,3154,3204,3263,3318,3380,3427,3481,3537,3589,3644,3698,3752,3806,3855,3913,3967,4024,4080,4127,4180,4236,4296,4359,4418,4480,4530,4584,4638,4686,4743,4796,4852,5412,5468,6308,6418,6474,6534,6587,6648,6727,6808,6880,6959,7039,7115,7193,7262,7338,7415,7486,7559,7635,7713,7782,7858,7935,7999,8070,8142,10543,10641,10696,10812,10865,10917,10967,11025,11090,11138,11189,16699,16765,16823,16892,16950,17019,17089,17162,17236,17304,17371,17441,17507,17580,17640,17716,17776,17836,17911,17979,18045,18113,18173,18232,18289,18355,18417,18474,18542,18615,18685,18747,18808,18876,18938,19008,19077,19133,19192,19254,19316,19383,19440,19501,19562,19623,19684,19740,19796,19852,19908,19966,20024,20082,20140,20197,20254,20311,20368,20427,20486,20544,20627,20710,20783,20837,20906,20962,21043,21124,21195,21324,21583,21641,21699,22440,22542,22602,22656,22726,22796,22861,22927,22992,23060,23129,23197,23327,23380,23439,23497,23549,23645,24033,24079,24129,24185,24232,24337,24395,24457,24520,24582,24641,24701,24766,24832,24897,24959,25021,25083,25145,25207,25269,25335,25402,25468,25531,25595,25658,25726,25787,25849,25911,25974,26038,26101,26165,26243,26302,26368,26448,26509,26562,26661,26712,27139,27496,27555,28878,31101,31172,31238,31312,31381,31452,31525,31596,31664,31737,31813,31883,31961,32029,32095,32156,32225,32289,32355,32423,32489,32552,32620,32691,32756,32829,32892,32973,33037,33103,33173,33243,33313,33383,33450,34269,34327,34386,34446,34505,34564,34623,34682,34741,34800,34859,34918,34977,35036,35096,35157,35219,35280,35341,35402,35463,35524,35585,35645,35706,35767,35827,35888,35949,36010,36071,36132,36193,36254,36315,36376,36437,36498,36566,36635,36705,36774,36843,36912,36981,37050,37119,37188,37257,37326,37395,37455,37516,37578,37639,37700,37761,37822,37883,37944,38005,38066,38127,38188,38250,38313,38377,38440,38503,38566,38629,38692,38755,38818,38881,38944,39007,39068,39130,39193,39255,39317,39379,39441,39503,39565,39627,39689,39751,39813,39870,39956,40036,40126,40221,40313,40405,40495,40578,40671,40758,40855,40946,41047,41134,41237,41326,41425,41517,41617,41701,41795,41883,41981,42064,42155,42249,42348,42450,42548,42648,42735,42835,42921,43017,43105,43186,43277,43373,43466,43559,43650,43735,43829,43918,44016,44109,44211,44299,44403,44494,44594,44687,44788,44873,44968,45057,45156,45241,45333,45428,45528,45631,45730,45833,45922,46023,46110,46207,46295,46391,46483,46583,46673,46771,46856,46945,47034,47127,47214,47305,48043,48119,48188,48267,48340,48420,48500,48577,48645,48723,48799,48870,48951,49024,49107,49182,49267,49340,49421,49502,49576,49660,49730,49808,49878,49958,50036,50108,50190,50260,50337,50417,50502,50590,50674,50761,50835,50913,50991,51062,51143,51234,51317,51413,51511,51618,51683,51749,51802,51878,51944,52031,52107,52183,64911,65136,65730,65809,65887,65960,66025,66088,66154,66225,66296,66366,66428,66497,66563,66623,66690,66757,66813,66864,66917,66969,67023,67094,67157,67216,67278,67337,67410,67477,67547,67607,67670,67745,67817,67913,67984,68040,68111,68168,68225,68291,68355,68426,68483,68536,68599,68651,68709,68776,70015,70081,70140,70223,70282,70339,70406,70476,70550,70612,70681,70751,70850,70947,71046,71132,71218,71299,71374,71463,71554,71638,71697,71743,71809,71866,71933,71990,72072,72137,72203,72326,72410,72531,72596,72658,72756,72830,72913,73002,73066,73145,73219,73281,73377,73442,73501,73557,73613,73673,73780,73827,73887,73948,74012,74073,74133,74191,74234,74283,74335,74386,74438,74487,74536,74601,74667,74727,74788,74844,74903,74952,75000,75058,75115,75217,75274,75349,75397,75448,75510,75575,75627,75701,75764,75827,75895,75945,76007,76067,76124,76184,76233,76301,76407,76509,76578,76649,76705,76754,76854,76925,77035,77124,77215,77297,77395,77451,77552,77662,77761,77824,77930,78007,78119,78246,78358,78485,78555,78669,78800,78897,78965,79083,79186,79304,79365,79439,79506,79611,79733,79807,79874,79984,80083,80156,80253,80375,80493,80611,80672,80794,80911,80979,81085,81187,81267,81338,81434,81501,81575,81649,81735,81823,81913,81991,82068,82168,82239,82360,82481,82545,82670,82744,82868,82992,83059,83168,83296,83408,83487,83565,83666,83737,83859,83981,84046,84172,84284,84390,84458,84557,84661,84724,84790,84874,84987,85100,85218,85296,85368,85504,85640,85725,85865,86003,86141,86283,86365,86474,86585,86713,86841,86973,87103,87233,87367,87429,87525,87592,87709,87830,87927,88009,88096,88183,88314,88445,88580,88657,88734,88845,88959,89033,89142,89254,89356,89452,89556,89623,89717,89789,89899,90005,90078,90169,90271,90374,90469,90576,90681,90803,90925,91051,91110,91168,91292,91416,91544,91662,91780,91902,91988,92085,92219,92353,92433,92571,92703,92835,92971,93046,93122,93225,93299,93412,93493,93550,93611,93670,93730,93788,93849,93907,93957,94006,94073,94132,94191,94240,94311,94395,94465,94536,94616,94685,94748,94816,94882,94950,95015,95081,95158,95236,95342,95448,95544,95673,95762,95889,95955,96024,96110,96176,96259,96357,96453,96549,96647,96756,96851,96940,97002,97062,97127,97184,97265,97319,97376,97473,97583,97644,97759,97880,97975,98067,98160,98262,98318,98377,98426,98518,98567,98621,98675,98729,98783,98837,98892,99002,99112,99220,99330,99440,99550,99660,99768,99874,99978,100082,100186,100281,100376,100469,100562,100666,100772,100876,100980,101073,101166,101259,101352,101460,101566,101672,101778,101875,101970,102065,102160,102266,102372,102478,102584,102682,102778,102874,102972,103037,103141,103199,103263,103324,103386,103446,103511,103573,103641,103699,103762,103825,103892,103967,104040,104106,104158,104211,104263,104320,104404,104499,104584,104665,104745,104822,104901,104978,105052,105126,105197,105277,105349,105424,105489,105550,105610,105685,105759,105832,105902,105974,106044,106117,106181,106251,106297,106366,106418,106503,106586,106643,106709,106776,106842,106923,106998,107054,107107,107168,107226,107276,107325,107374,107423,107485,107537,107582,107663,107714,107768,107821,107875,107926,107975,108041,108092,108153,108214,108276,108326,108367,108444,108503,108562,108621,108682,108738,108794,108861,108922,108987,109042,109107,109176,109244,109322,109391,109451,109522,109596,109661,109733,109803,109870,109954,110023,110090,110160,110223,110290,110358,110441,110520,110610,110687,110755,110822,110900,110957,111014,111082,111148,111204,111264,111323,111377,111427,111477,111525,111587,111638,111711,111791,111871,111935,112002,112073,112131,112192,112258,112317,112384,112444,112504,112567,112635,112696,112763,112841,112911,112960,113017,113086,113147,113235,113323,113411,113499,113586,113673,113760,113847,113905,113979,114049,114105,114176,114241,114303,114378,114451,114541,114607,114673,114734,114798,114860,114918,114989,115072,115131,115202,115268,115333,115394,115453,115524,115590,115655,115738,115814,115889,115970,116030,116099,116169,116238,116293,116349,116405,116466,116524,116580,116634,116689,116751,116808,116902,116971,117072,117123,117193,117256,117312,117370,117429,117483,117569,117653,117723,117792,117862,117977,118098,118165,118232,118307,118374,118433,118487,118541,118595,118648,118700,118774,120775,120915,124063,124113,124163,124346,124402,124460,124522,124577,124635,124759,124823,124882,124944,125010,125076,125570,125716,125761,125804,126790,126837,126882,126933,126984,127035,127086,127467,127983,128045,128225,128297,128354,128408,128463,128521,128576,128635,128691,128760,128829,128898,128968,129031,129094,129157,129220,129285,129350,129415,129480,129543,129607,129671,129735,129786,129864,129942,130013,130085,130158,130230,130296,130362,130430,130498,130564,130631,130705,130768,130825,130885,130950,131017,131082,131139,131200,131258,131362,131472,131581,131685,131763,131828,131895,131961,132031,132078,132130,132180,132363,132753,136289,136753,136937,137115,137353,137542,137711,138611,138726,138811,138927,141154,141219,141365,141522,141679,143138,143480,143687,144082,144178,144268,144364,144454,144620,144743,144866,145036,145142,145257,145372,145474,145580,145697,145812,150261,150434,150602,150750,150909,151064,151237,151354,151471,151639,151751,151865,152037,152213,152371,152504,152616,152762,152914,153046,153189,153311,153861,153997,154093,154229,154324,154491,154584,154676,154863,155019,155197,155361,155543,155860,156042,156224,156414,156646,156836,157013,157175,157332,157442,157625,157762,157966,158150,158334,158494,158652,158836,159063,159266,159437,159657,159879,160034,160234,160418,160521,160711,160852,161017,161188,161388,161592,161794,161959,162164,162363,162562,162759,162850,162999,163149,163233,163382,163527,163679,163820,163986,164147,165214,165515,165681,165836,165938,170468,170632,170818,171458,171583,174727,174999,175277,175522,175584,175869,178863,179319,179828,190604,191118,191555,191989,192432,196414,196535,196634,197039,197136,197253,197340,197463,197564,197970,198069,198188,198281,198388,198731,198838,199083,199204,199613,199861,199961,200066,200185,200694,200841,200960,201211,201344,201759,202013,202125,207475,207600,207917,208038,208266,208387,208520,208667,229298,229790,250170,250594,271270,271764,292189,292615,297456,302873,306964,312395,317137,322514,326498,330490,335881,336428,336861,337617,337847,338090,339223,340152,388297,388881,389354,390784,391528,392721,393775,394253,394546,394929,396444,397209,398352,398793,399234,399830,400104,400515,401531,401709,402462,402599,402690,404884,405150,405472,405682,405791,405910,406094,407212,407682,408433,411016,411111,413661,413889,414145,414404,414980,415334,415456,415595,415887,416147,417075,417361,417764,418166,418509,418721,418922,419135,419424,419709,430840,430927,431012,431111,443649,443755,443878,444010,444133,444263,444387,444520,444651,444776,444893,445013,445145,445273,445387,445505,445618,445739,445927,446114,446295,446478,446662,446827,447009,447129,447249,447357,447467,447579,447687,447797,447962,448128,448280,448445,448546,448666,448837,448998,449161,449322,449489,449608,449725,449905,450087,450268,450451,450606,450751,450873,451008,451171,451364,451490,451642,451784,451954,452110,452282,452573,459240,459332,459505,459667,459762,459931,460025,460114,460357,460446,460739,461155,461575,461996,462422,462839,463255,463672,464090,464504,464974,465447,465919,466330,466801,467273,467463,467669,467775,467883,467989,468101,468215,468327,468441,468557,468671,468779,468889,468997,469259,469638,470042,470189,470297,470407,470515,470629,471038,471452,471568,471986,472227,472657,473092,473502,473924,474334,474456,474865,475281,475403,475621,475805,478509,478853,478933,479289,479439,479583,479659,479771,479861,480123,480388,480496,480648,480756,480832,480944,481034,481136,481244,481352,481452,481560,481645,481749,481836,481914,482028,482120,482384,482651,482761,482914,483024,483108,483497,483595,483703,483797,483927,484035,484157,484293,484401,484521,484655,484777,484905,485047,485173,485313,485439,485557,485689,485787,485897,486197,486309,486427,486891,487007,487310,487436,487532,487933,488043,488167,488305,488415,488537,488849,488973,489103,489579,489707,490022,490160,490322,490538,490694,490898,496420,496504,496608,496811,497000,497201,497394,497599,497912,498124,498290,498406,498652,498868,499181,499607,500069,500306,500458,500718,500862,501004,504236,504350,504470,504586,504680,505001,505100,505218,505319,505598,505883,506162,506444,506697,506956,507209,507465,507889,507965,511215,512570,513014,514868,515443,515651,516661,517041,517207,517348,522368,522794,522906,523041,523194,523391,523562,523745,523920,524107,524379,524537,524621,524725,525212,525768,525926,526145,526376,526599,526834,527056,527322,527460,528059,528173,528311,528423,528547,529118,529613,530159,530304,530397,530489,532416,532986,533284,533473,533679,533872,534082,534966,535111,535503,535661,535878,536139,544366,545241,545861,546058,547006,547771,547894,548667,548888,549088,551065,551165,551255,551941,552694,553459,554222,554997,556210,556375,557988,558309,559372,559582,559752,560322,561217,561850,562016,563502,564118,564354,564575,565533,565798,566063,566310,566724,566960,568245,568694,568881,569130,569372,569548,569789,570022,570247,570842,571317,571841,572102,573453,573928,575154,575624,576672,577124,577368,577825,579070,579553,579703,580047,580193,580331,580467,580755,581259,581768,581884,582786,582908,583020,583197,583463,583733,583999,584267,584523,584783,585039,585297,585549,585805,586057,586311,586543,586779,587031,587287,587539,587793,588025,588259,588371,588796,588920,590012,590827,591023,591347,591736,592088,592329,592543,592842,593034,593349,593556,593902,594202,594603,594822,595235,595472,595842,596566,596921,597190,597330,597584,597728,598005,598997,599406,600038,600384,600752,601826,602189,602589,604097,604682,605000,607535,607729,607947,608173,608385,608584,608791,609995,610290,610847,611237,611869,612346,612591,612942,613188,613948,614212,614635,614826,615205,615293,615401,615509,615822,616147,616466,616797,619500,619688,619949,620198,622782,622974,623239,623492,624024,624432,624631,625215,625450,625574,625986,626200,626602,626705,626835,627010,627262,627458,627598,627792,628803,629872,630160,630290,631067,631724,631870,632576,632814,634354,634504,634921,635086,635772,636242,636438,636529,636613,636757,636991,637158,638086,638372,638532,639147,639306,639634,639861,640373,640735,640814,641153,641258,641623,641994,642355,644229,644858,645934,646358,646611,646763,647811,648548,648751,648997,649244,649462,649704,650025,650289,650594,650817,651128,651317,652032,652301,652795,653021,653461,653620,653904,654649,655014,655319,655477,655715,657034,657432,657660,657880,658022,659312,659418,659548,659686,659810,660098,660267,660367,660652,660766,661649,662404,662843,662967,663213,663406,663540,663731,664510,664728,665019,665298,665615,665837,666132,666415,666519,666860,667676,667992,668553,669059,669264,670050,670455,671116,671305,671856,672422,672542,672944,673478,797848,797941,798004,798086,798179,798272,798359,798457,798548,798639,798727,798811,798907,799011,799111,799217,799320,799421,799525,799631,799730,799836,799938,800045,800154,800265,800396,800516,800632,800750,800849,800956,801072,801191,801319,801408,801503,801580,801669,801760,801853,801927,802024,802119,802217,802316,802420,802516,802618,802721,802821,802924,803009,803110,803208,803298,803393,803480,803586,803688,803782,803873,803967,804043,804135,804224,804327,804438,804521,804607,804702,804799,804895,804983,805084,805185,805288,805394,805492,805589,805684,805782,805885,805985,806088,806193,806311,806427,806522,806615,806700,806796,806890,806982,807084,807191,807274,807378,807483,807583,807684,807789,807889,807990,808089,808191,808285,808392,808494,808597,808690,808786,808888,808991,809087,809189,809292,809389,809492,809590,809694,809799,809896,810004,810118,810233,810341,810455,810570,810672,810777,810885,810995,811111,811228,811323,811420,811519,811624,811730,811829,811934,812040,812140,812246,812347,812454,812573,812672,812777,812879,812981,813081,813184,813279,813383,813468,813572,813676,813774,813878,813984,814082,814187,814285,814398,814492,814581,814670,814753,814844,814927,815025,815115,815211,815300,815394,815482,815578,815663,815771,815872,815973,816071,816177,816268,816367,816464,816562,816658,816751,816861,816959,817054,817164,817256,817356,817455,817542,817646,817751,817850,817957,818064,818163,818272,818364,818475,818586,818697,818801,818916,819032,819159,819279,819376,819475,819567,819666,819758,819857,819943,820037,820140,820236,820339,820435,820538,820635,820733,820836,820929,821019,821120,821203,821294,821379,821471,821574,821669,821765,821858,821952,822031,822138,822229,822328,822421,822524,822628,822729,822830,822934,823028,823132,823236,823349,823455,823561,823669,823786,823887,823995,824095,824198,824303,824410,824506,824585,824675,824759,824851,824924,825021,825103,825188,825273,825370,825463,825558,825657,825754,825845,825936,826028,826123,826230,826338,826440,826537,826634,826727,826814,826898,826995,827092,827185,827272,827363,827462,827561,827656,827745,827826,827925,828029,828126,828231,828328,828412,828511,828615,828712,828817,828914,829012,829113,829219,829318,829425,829524,829623,829714,829803,829892,829974,830067,830158,830269,830370,830470,830582,830695,830793,830901,830995,831095,831184,831276,831387,831497,831592,831708,831834,831960,832079,832207,832332,832457,832575,832702,832811,832920,833033,833156,833279,833395,833520,833617,833725,833847,833963,834079,834188,834276,834377,834466,834567,834654,834742,834839,834931,835037,835137,835213,835290"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\5619827a7b7a666ff443af1afbf85287\\transformed\\preference-1.2.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "554,882,1787,1788,1789,1790,1791,1792,1793,1879,1880,1881,1996,1997,2092,2124,2343,2364,2461,2489,2490,5895,6194,6197,6203,6209,6212,6218,6222,6225,6232,6238,6241,6247,6252,6257,6264,6266,6272,6278,6286,6291,6298,6303,6309,6313,6320,6324,6330,6336,6339,6343,6344", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "28440,52548,119757,119821,119876,119944,120011,120076,120133,125191,125239,125287,132185,132248,139337,141224,164338,166095,172013,173585,173635,411116,432431,432536,432781,433119,433265,433605,433817,433980,434387,434725,434848,435187,435426,435683,436054,436114,436452,436738,437187,437479,437867,438172,438516,438761,439091,439298,439566,439839,439983,440184,440231", "endLines": "554,882,1787,1788,1789,1790,1791,1792,1793,1879,1880,1881,1996,1997,2092,2124,2343,2366,2461,2489,2490,5911,6196,6202,6208,6211,6217,6221,6224,6231,6237,6240,6246,6251,6256,6263,6265,6271,6277,6285,6290,6297,6302,6308,6312,6319,6323,6329,6335,6338,6342,6343,6344", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55", "endOffsets": "28508,52612,119816,119871,119939,120006,120071,120128,120185,125234,125282,125343,132243,132306,139370,141276,164377,166230,172147,173630,173678,412549,432531,432776,433114,433260,433600,433812,433975,434382,434720,434843,435182,435421,435678,436049,436109,436447,436733,437182,437474,437862,438167,438511,438756,439086,439293,439561,439834,439978,440179,440226,440282"}}, {"source": "D:\\development\\Codes\\MyApplication\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "571,576,630,637,877,878,879,880,881,883,884,887,894,895,896,897,900,901,906,907,908,909,910,911,912,913,914,919,920,921,924,925,943,998", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "29605,29969,33735,34176,52320,52368,52411,52456,52503,52617,52659,52860,53343,53393,53440,53487,53649,53693,54039,54091,54139,54191,54237,54286,54337,54383,54427,54755,54800,54845,55023,55067,56042,59335", "endColumns": "44,41,39,40,47,42,44,46,44,41,47,47,49,46,46,46,43,49,51,47,51,45,48,50,45,43,41,44,44,56,43,41,54,66", "endOffsets": "29645,30006,33770,34212,52363,52406,52451,52498,52543,52654,52702,52903,53388,53435,53482,53529,53688,53738,54086,54134,54186,54232,54281,54332,54378,54422,54464,54795,54840,54897,55062,55104,56092,59397"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\cd688a78b2d926833405c834f3e4e352\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2048", "startColumns": "4", "startOffsets": "135919", "endColumns": "82", "endOffsets": "135997"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\75ac900f6bf42e90e2b331a4ef619a90\\transformed\\work-runtime-2.9.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1"}, "to": {"startLines": "556,557,558,561", "startColumns": "4,4,4,4", "startOffsets": "28571,28636,28706,28883", "endColumns": "64,69,63,60", "endOffsets": "28631,28701,28765,28939"}}]}, {"outputFile": "com.timeflow.app-mergeDebugResources-85:/values/values.xml", "map": [{"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\269f815ecf39126ae6ebdbf9d5fd71ed\\transformed\\lifecycle-runtime-2.7.0\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "1924", "startColumns": "4", "startOffsets": "127472", "endColumns": "42", "endOffsets": "127510"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\443a6cfc0de72abe8b7a8ef4c7b7a3e3\\transformed\\core-1.12.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "419,569,570,585,586,875,876,1085,1086,1087,1088,1089,1090,1091,1772,1773,1774,1775,1776,1777,1778,1779,1780,1781,1782,1783,1784,1785,1786,1804,1805,1806,1856,1857,1894,1895,1896,1897,1898,1899,1900,1901,1902,1903,1904,1905,1906,1907,1908,1916,1999,2071,2072,2073,2074,2075,2076,2077,2460,6602,6603,6607,6608,6612,7810,7811", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "21704,29445,29517,30652,30717,52188,52257,65141,65211,65279,65351,65421,65482,65556,118779,118840,118901,118963,119027,119089,119150,119218,119318,119378,119444,119517,119586,119643,119695,120920,120992,121068,123949,123984,125920,125975,126038,126093,126151,126209,126270,126333,126390,126441,126491,126552,126609,126675,126709,127091,132368,137969,138036,138108,138177,138246,138320,138392,171955,458403,458520,458721,458831,459032,543911,543983", "endLines": "419,569,570,585,586,875,876,1085,1086,1087,1088,1089,1090,1091,1772,1773,1774,1775,1776,1777,1778,1779,1780,1781,1782,1783,1784,1785,1786,1804,1805,1806,1856,1857,1894,1895,1896,1897,1898,1899,1900,1901,1902,1903,1904,1905,1906,1907,1908,1916,1999,2071,2072,2073,2074,2075,2076,2077,2460,6602,6606,6607,6611,6612,7810,7811", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66", "endOffsets": "21759,29512,29600,30712,30778,52252,52315,65206,65274,65346,65416,65477,65551,65624,118835,118896,118958,119022,119084,119145,119213,119313,119373,119439,119512,119581,119638,119690,119752,120987,121063,121128,123979,124014,125970,126033,126088,126146,126204,126265,126328,126385,126436,126486,126547,126604,126670,126704,126739,127121,132433,138031,138103,138172,138241,138315,138387,138475,172021,458515,458716,458826,459027,459156,543978,544045"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\2e72a71af7cea86f1e691bad46c56138\\transformed\\appcompat-1.6.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "120,208,211,509,552,553,562,563,564,565,566,567,568,572,573,574,575,577,578,579,580,581,582,583,584,626,627,628,629,631,632,633,634,635,636,809,810,811,812,813,814,815,816,817,818,819,820,885,886,888,889,890,891,892,893,898,899,902,903,904,905,915,916,917,918,922,923,999,1000,1001,1002,1003,1004,1005,1006,1007,1008,1009,1010,1011,1012,1013,1014,1015,1016,1017,1018,1019,1020,1021,1022,1023,1024,1025,1026,1027,1028,1029,1030,1031,1032,1033,1034,1035,1036,1037,1038,1039,1040,1041,1042,1043,1044,1045,1046,1047,1048,1049,1050,1051,1052,1053,1054,1055,1056,1057,1058,1059,1060,1061,1062,1063,1064,1065,1066,1067,1068,1069,1070,1071,1072,1073,1074,1075,1076,1077,1078,1079,1141,1142,1146,1147,1148,1149,1150,1151,1152,1794,1795,1796,1797,1798,1799,1800,1801,1839,1840,1841,1842,1852,1882,1883,1893,1922,1930,1931,1934,1935,2004,2005,2006,2007,2008,2009,2010,2011,2012,2013,2014,2015,2016,2017,2018,2019,2020,2021,2022,2023,2024,2025,2026,2027,2028,2029,2030,2387,2500,2501,2502,2503,2504,2526,2534,2535,2539,2543,2554,2559,2588,2595,2599,2603,2608,2612,2616,2620,2624,2628,2632,2638,2642,2648,2652,2658,2662,2667,2671,2674,2678,2684,2688,2694,2698,2704,2707,2711,2715,2719,2723,2727,2728,2729,2730,2733,2736,2739,2742,2746,2747,2748,2749,2790,2793,2795,2797,2799,2804,2805,2809,2815,2819,2820,2822,2834,2835,2839,2845,2849,2926,2927,2931,2958,2962,2963,2967,4764,4936,4962,5133,5159,5190,5198,5204,5220,5242,5247,5252,5262,5271,5280,5284,5291,5310,5317,5318,5327,5330,5333,5337,5341,5345,5348,5349,5354,5359,5369,5374,5381,5387,5388,5391,5395,5400,5402,5404,5407,5410,5412,5416,5419,5426,5429,5432,5436,5438,5442,5444,5446,5448,5452,5460,5468,5480,5486,5495,5498,5509,5512,5513,5518,5519,6032,6101,6175,6176,6186,6195,6347,6349,6353,6356,6359,6362,6365,6368,6371,6374,6378,6381,6384,6387,6391,6394,6398,6546,6547,6548,6549,6550,6551,6552,6553,6554,6555,6556,6557,6558,6559,6560,6561,6562,6563,6564,6565,6566,6568,6570,6571,6572,6573,6574,6575,6576,6577,6579,6580,6582,6583,6585,6587,6588,6590,6591,6592,6593,6594,6595,6597,6598,6599,6600,6601,6886,6888,6890,6892,6893,6894,6895,6896,6897,6898,6899,6900,6901,6902,6903,6904,6906,6907,6908,6909,6910,6911,6912,6914,6918,7163,7164,7165,7166,7167,7168,7172,7173,7174,7715,7717,7719,7721,7723,7725,7726,7727,7728,7730,7732,7734,7735,7736,7737,7738,7739,7740,7741,7742,7743,7744,7745,7748,7749,7750,7751,7753,7755,7756,7758,7759,7761,7763,7765,7766,7767,7768,7769,7770,7771,7772,7773,7774,7775,7776,7778,7779,7780,7781,7783,7784,7785,7786,7787,7789,7791,7793,7795,7796,7797,7798,7799,7800,7801,7802,7803,7804,7805,7806,7807,7808,7809", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6094,10548,10701,26567,28323,28378,28944,29008,29078,29139,29214,29290,29367,29650,29735,29817,29893,30011,30088,30166,30272,30378,30457,30537,30594,33455,33529,33604,33669,33775,33835,33896,33968,34041,34108,47310,47369,47428,47487,47546,47605,47659,47713,47766,47820,47874,47928,52707,52781,52908,52981,53055,53126,53198,53270,53534,53591,53743,53816,53890,53964,54469,54541,54614,54684,54902,54962,59402,59471,59540,59610,59684,59760,59824,59901,59977,60054,60119,60188,60265,60340,60409,60477,60554,60620,60681,60778,60843,60912,61011,61082,61141,61199,61256,61315,61379,61450,61522,61594,61666,61738,61805,61873,61941,62000,62063,62127,62217,62308,62368,62434,62501,62567,62637,62701,62754,62821,62882,62949,63062,63120,63183,63248,63313,63388,63461,63533,63577,63624,63670,63719,63780,63841,63902,63964,64028,64092,64156,64221,64284,64344,64405,64471,64530,64590,64652,64723,64783,68781,68867,69117,69207,69294,69382,69464,69547,69637,120190,120242,120300,120345,120411,120475,120532,120589,123043,123100,123148,123197,123740,125348,125395,125874,127392,127796,127860,128050,128110,132758,132832,132902,132980,133034,133104,133189,133237,133283,133344,133407,133473,133537,133608,133671,133736,133800,133861,133922,133974,134047,134121,134190,134265,134339,134413,134554,167191,174124,174202,174292,174380,174476,175985,176567,176656,176903,177184,177850,178135,179944,180421,180643,180865,181141,181368,181598,181828,182058,182288,182515,182934,183160,183585,183815,184243,184462,184745,184953,185084,185311,185737,185962,186389,186610,187035,187155,187431,187732,188056,188347,188661,188798,188929,189034,189276,189443,189647,189855,190126,190238,190350,190455,192548,192762,192908,193048,193134,193482,193570,193816,194234,194483,194565,194663,195320,195420,195672,196096,196351,202241,202330,202567,204591,204833,204935,205188,340268,350949,352465,363160,364688,366445,367071,367491,368752,370017,370273,370509,371056,371550,372155,372353,372933,374301,374676,374794,375332,375489,375685,375958,376214,376384,376525,376589,376954,377321,377997,378261,378599,378952,379046,379232,379538,379800,379925,380052,380291,380502,380621,380814,380991,381446,381627,381749,382008,382121,382308,382410,382517,382646,382921,383429,383925,384802,385096,385666,385815,386547,386719,386803,387139,387231,420281,425512,431227,431289,431867,432451,440398,440511,440740,440900,441052,441223,441389,441558,441725,441888,442131,442301,442474,442645,442919,443118,443323,452689,452773,452869,452965,453063,453163,453265,453367,453469,453571,453673,453773,453869,453981,454110,454233,454364,454495,454593,454707,454801,454941,455075,455171,455283,455383,455499,455595,455707,455807,455947,456083,456247,456377,456535,456685,456826,456970,457105,457217,457367,457495,457623,457759,457891,458021,458151,458263,475921,476067,476211,476349,476415,476505,476581,476685,476775,476877,476985,477093,477193,477273,477365,477463,477573,477625,477703,477809,477901,478005,478115,478237,478400,495465,495545,495645,495735,495845,495935,496176,496270,496376,536255,536355,536467,536581,536697,536813,536907,537021,537133,537235,537355,537477,537559,537663,537783,537909,538007,538101,538189,538301,538417,538539,538651,538826,538942,539028,539120,539232,539356,539423,539549,539617,539745,539889,540017,540086,540181,540296,540409,540508,540617,540728,540839,540940,541045,541145,541275,541366,541489,541583,541695,541781,541885,541981,542069,542187,542291,542395,542521,542609,542717,542817,542907,543017,543101,543203,543287,543341,543405,543511,543597,543707,543791", "endLines": "120,208,211,509,552,553,562,563,564,565,566,567,568,572,573,574,575,577,578,579,580,581,582,583,584,626,627,628,629,631,632,633,634,635,636,809,810,811,812,813,814,815,816,817,818,819,820,885,886,888,889,890,891,892,893,898,899,902,903,904,905,915,916,917,918,922,923,999,1000,1001,1002,1003,1004,1005,1006,1007,1008,1009,1010,1011,1012,1013,1014,1015,1016,1017,1018,1019,1020,1021,1022,1023,1024,1025,1026,1027,1028,1029,1030,1031,1032,1033,1034,1035,1036,1037,1038,1039,1040,1041,1042,1043,1044,1045,1046,1047,1048,1049,1050,1051,1052,1053,1054,1055,1056,1057,1058,1059,1060,1061,1062,1063,1064,1065,1066,1067,1068,1069,1070,1071,1072,1073,1074,1075,1076,1077,1078,1079,1141,1142,1146,1147,1148,1149,1150,1151,1152,1794,1795,1796,1797,1798,1799,1800,1801,1839,1840,1841,1842,1852,1882,1883,1893,1922,1930,1931,1934,1935,2004,2005,2006,2007,2008,2009,2010,2011,2012,2013,2014,2015,2016,2017,2018,2019,2020,2021,2022,2023,2024,2025,2026,2027,2028,2029,2030,2387,2500,2501,2502,2503,2504,2533,2534,2538,2542,2546,2558,2564,2594,2598,2602,2607,2611,2615,2619,2623,2627,2631,2637,2641,2647,2651,2657,2661,2666,2670,2673,2677,2683,2687,2693,2697,2703,2706,2710,2714,2718,2722,2726,2727,2728,2729,2732,2735,2738,2741,2745,2746,2747,2748,2749,2792,2794,2796,2798,2803,2804,2808,2814,2818,2819,2821,2833,2834,2838,2844,2848,2849,2926,2930,2957,2961,2962,2966,2994,4935,4961,5132,5158,5189,5197,5203,5219,5241,5246,5251,5261,5270,5279,5283,5290,5309,5316,5317,5326,5329,5332,5336,5340,5344,5347,5348,5353,5358,5368,5373,5380,5386,5387,5390,5394,5399,5401,5403,5406,5409,5411,5415,5418,5425,5428,5431,5435,5437,5441,5443,5445,5447,5451,5459,5467,5479,5485,5494,5497,5508,5511,5512,5517,5518,5523,6100,6170,6175,6185,6194,6195,6348,6352,6355,6358,6361,6364,6367,6370,6373,6377,6380,6383,6386,6390,6393,6397,6401,6546,6547,6548,6549,6550,6551,6552,6553,6554,6555,6556,6557,6558,6559,6560,6561,6562,6563,6564,6565,6567,6569,6570,6571,6572,6573,6574,6575,6576,6578,6579,6581,6582,6584,6586,6587,6589,6590,6591,6592,6593,6594,6596,6597,6598,6599,6600,6601,6887,6889,6891,6892,6893,6894,6895,6896,6897,6898,6899,6900,6901,6902,6903,6905,6906,6907,6908,6909,6910,6911,6913,6917,6921,7163,7164,7165,7166,7167,7171,7172,7173,7174,7716,7718,7720,7722,7724,7725,7726,7727,7729,7731,7733,7734,7735,7736,7737,7738,7739,7740,7741,7742,7743,7744,7747,7748,7749,7750,7752,7754,7755,7757,7758,7760,7762,7764,7765,7766,7767,7768,7769,7770,7771,7772,7773,7774,7775,7777,7778,7779,7780,7782,7783,7784,7785,7786,7788,7790,7792,7794,7795,7796,7797,7798,7799,7800,7801,7802,7803,7804,7805,7806,7807,7808,7809", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119", "endOffsets": "6144,10588,10745,26603,28373,28435,29003,29073,29134,29209,29285,29362,29440,29730,29812,29888,29964,30083,30161,30267,30373,30452,30532,30589,30647,33524,33599,33664,33730,33830,33891,33963,34036,34103,34171,47364,47423,47482,47541,47600,47654,47708,47761,47815,47869,47923,47977,52776,52855,52976,53050,53121,53193,53265,53338,53586,53644,53811,53885,53959,54034,54536,54609,54679,54750,54957,55018,59466,59535,59605,59679,59755,59819,59896,59972,60049,60114,60183,60260,60335,60404,60472,60549,60615,60676,60773,60838,60907,61006,61077,61136,61194,61251,61310,61374,61445,61517,61589,61661,61733,61800,61868,61936,61995,62058,62122,62212,62303,62363,62429,62496,62562,62632,62696,62749,62816,62877,62944,63057,63115,63178,63243,63308,63383,63456,63528,63572,63619,63665,63714,63775,63836,63897,63959,64023,64087,64151,64216,64279,64339,64400,64466,64525,64585,64647,64718,64778,64846,68862,68949,69202,69289,69377,69459,69542,69632,69723,120237,120295,120340,120406,120470,120527,120584,120638,123095,123143,123192,123243,123769,125390,125439,125915,127419,127855,127917,128105,128162,132827,132897,132975,133029,133099,133184,133232,133278,133339,133402,133468,133532,133603,133666,133731,133795,133856,133917,133969,134042,134116,134185,134260,134334,134408,134549,134619,167239,174197,174287,174375,174471,174561,176562,176651,176898,177179,177431,178130,178523,180416,180638,180860,181136,181363,181593,181823,182053,182283,182510,182929,183155,183580,183810,184238,184457,184740,184948,185079,185306,185732,185957,186384,186605,187030,187150,187426,187727,188051,188342,188656,188793,188924,189029,189271,189438,189642,189850,190121,190233,190345,190450,190567,192757,192903,193043,193129,193477,193565,193811,194229,194478,194560,194658,195315,195415,195667,196091,196346,196440,202325,202562,204586,204828,204930,205183,207339,350944,352460,363155,364683,366440,367066,367486,368747,370012,370268,370504,371051,371545,372150,372348,372928,374296,374671,374789,375327,375484,375680,375953,376209,376379,376520,376584,376949,377316,377992,378256,378594,378947,379041,379227,379533,379795,379920,380047,380286,380497,380616,380809,380986,381441,381622,381744,382003,382116,382303,382405,382512,382641,382916,383424,383920,384797,385091,385661,385810,386542,386714,386798,387134,387226,387504,425507,430878,431284,431862,432446,432537,440506,440735,440895,441047,441218,441384,441553,441720,441883,442126,442296,442469,442640,442914,443113,443318,443648,452768,452864,452960,453058,453158,453260,453362,453464,453566,453668,453768,453864,453976,454105,454228,454359,454490,454588,454702,454796,454936,455070,455166,455278,455378,455494,455590,455702,455802,455942,456078,456242,456372,456530,456680,456821,456965,457100,457212,457362,457490,457618,457754,457886,458016,458146,458258,458398,476062,476206,476344,476410,476500,476576,476680,476770,476872,476980,477088,477188,477268,477360,477458,477568,477620,477698,477804,477896,478000,478110,478232,478395,478552,495540,495640,495730,495840,495930,496171,496265,496371,496463,536350,536462,536576,536692,536808,536902,537016,537128,537230,537350,537472,537554,537658,537778,537904,538002,538096,538184,538296,538412,538534,538646,538821,538937,539023,539115,539227,539351,539418,539544,539612,539740,539884,540012,540081,540176,540291,540404,540503,540612,540723,540834,540935,541040,541140,541270,541361,541484,541578,541690,541776,541880,541976,542064,542182,542286,542390,542516,542604,542712,542812,542902,543012,543096,543198,543282,543336,543400,543506,543592,543702,543786,543906"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\d17c3cc083b583ce71e54f6d227cea13\\transformed\\transition-1.4.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "1849,1850,1877,1887,1888,1917,1918,1919,1920,1921", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "123602,123642,125081,125575,125630,127126,127180,127232,127281,127342", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "123637,123684,125119,125625,125672,127175,127227,127276,127337,127387"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\5fb9f1b19747d5d55118ab1a0045d5ba\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "1854,1878", "startColumns": "4,4", "startOffsets": "123829,125124", "endColumns": "53,66", "endOffsets": "123878,125186"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\e8a1fad92796dfcc9d449130c7157984\\transformed\\lifecycle-viewmodel-2.7.0\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "1927", "startColumns": "4", "startOffsets": "127629", "endColumns": "49", "endOffsets": "127674"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\c844dc5032d7376f9b48ed94bcd5a918\\transformed\\recyclerview-1.1.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "437,1143,1144,1145,1153,1154,1155,1855", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "22445,68954,69013,69061,69728,69803,69879,123883", "endColumns": "55,58,47,55,74,75,71,65", "endOffsets": "22496,69008,69056,69112,69798,69874,69946,123944"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\4d4153ca3cac4b70eeadb3640318cf03\\transformed\\metrics-performance-1.0.0-beta01\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "1861,1862", "startColumns": "4,4", "startOffsets": "124168,124214", "endColumns": "45,47", "endOffsets": "124209,124257"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\9016448c9bf9cd5590d66215cffcbf2c\\transformed\\drawerlayout-1.1.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1", "startColumns": "-1,-1,-1", "startOffsets": "-1,-1,-1"}, "to": {"startLines": "121,124,1092", "startColumns": "4,4,4", "startOffsets": "6149,6313,65629", "endColumns": "55,47,51", "endOffsets": "6200,6356,65676"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\e8414bc356727da52467a60536d06b50\\transformed\\material3-release\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2182,2183,2184,2185,2186,2187,2188,2189,2190,2191,2194,2195,2196,2197,2198,2199,2200,2201,2202,2203,2204,2207,2208,2209,2210,2211,2212,2213,2214,2215,2216,2217,2218,2219,2220,2221,2222,2223,2224,2225,2226,2227,2228,2229,2230,2231,2232,2233,2234,2235,2236,2237,2238,2239,2240,2241", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "145817,145905,145991,146072,146156,146225,146290,146373,146479,146565,146685,146739,146808,146869,146938,147027,147122,147196,147293,147386,147484,147633,147724,147812,147908,148006,148070,148138,148225,148319,148386,148458,148530,148631,148740,148816,148885,148933,148999,149063,149120,149177,149249,149299,149353,149424,149495,149565,149634,149692,149768,149839,149913,149999,150049,150119", "endLines": "2182,2183,2184,2185,2186,2187,2188,2189,2190,2193,2194,2195,2196,2197,2198,2199,2200,2201,2202,2203,2206,2207,2208,2209,2210,2211,2212,2213,2214,2215,2216,2217,2218,2219,2220,2221,2222,2223,2224,2225,2226,2227,2228,2229,2230,2231,2232,2233,2234,2235,2236,2237,2238,2239,2240,2241", "endColumns": "87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64", "endOffsets": "145900,145986,146067,146151,146220,146285,146368,146474,146560,146680,146734,146803,146864,146933,147022,147117,147191,147288,147381,147479,147628,147719,147807,147903,148001,148065,148133,148220,148314,148381,148453,148525,148626,148735,148811,148880,148928,148994,149058,149115,149172,149244,149294,149348,149419,149490,149560,149629,149687,149763,149834,149908,149994,150044,150114,150179"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\ac812fd31ead2ff3fba2b67a79fdb627\\transformed\\window-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1"}, "to": {"startLines": "459,464,465,466,1844", "startColumns": "4,4,4,4,4", "startOffsets": "23650,23829,23889,23941,123322", "endLines": "463,464,465,466,1844", "endColumns": "11,59,51,44,59", "endOffsets": "23824,23884,23936,23981,123377"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\71256635af31e1aec955d3a558d276eb\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "2482,2483", "startColumns": "4,4", "startOffsets": "173186,173242", "endColumns": "55,54", "endOffsets": "173237,173292"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\be96a3cc4d9fb8c1f62d02a0f08f5225\\transformed\\coordinatorlayout-1.1.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "94,9797", "startColumns": "4,4", "startOffsets": "5016,673594", "endLines": "94,9799", "endColumns": "60,12", "endOffsets": "5072,673734"}}, {"source": "D:\\development\\Codes\\MyApplication\\app\\src\\main\\res\\values\\bools.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "555,559", "startColumns": "4,4", "startOffsets": "28513,28770", "endColumns": "57,50", "endOffsets": "28566,28816"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\bb69df46b629c4933421c332e21438f5\\transformed\\coil-base-2.4.0\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "1845", "startColumns": "4", "startOffsets": "123382", "endColumns": "49", "endOffsets": "123427"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\9f98cf38d23a9440ede43a1dc0784488\\transformed\\activity-1.8.2\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "1884,1925", "startColumns": "4,4", "startOffsets": "125444,127515", "endColumns": "41,59", "endOffsets": "125481,127570"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\c8644e9637794500baaef0ec63c3c74d\\transformed\\aws-android-sdk-auth-core-2.76.0\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2455", "startColumns": "4", "startOffsets": "171640", "endColumns": "90", "endOffsets": "171726"}}, {"source": "D:\\development\\Codes\\MyApplication\\app\\src\\main\\res\\values\\widget_colors.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "926,927,928,929,930,931,932,933,934,935,936,937,938,939,940,941,942,944,945,946,947,948,949,950,951,952,953,954,955,956,957,958,959,960,961,962,963,964,965,966,967,968,969,970,971,972,973,974,975,976,977,978,979,980,981,982,983,984,985,986,987,988,989,990,991,992,993,994,995,996,997", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55109,55162,55216,55271,55324,55379,55431,55484,55544,55602,55660,55714,55775,55828,55882,55937,55990,56097,56162,56228,56295,56363,56422,56483,56537,56597,56659,56720,56783,56844,56907,56963,57019,57076,57131,57185,57242,57292,57349,57402,57458,57512,57566,57618,57672,57728,57783,57841,57906,57965,58019,58071,58133,58194,58263,58334,58396,58460,58519,58579,58642,58706,58765,58825,58886,58948,59008,59069,59134,59200,59267", "endColumns": "52,53,54,52,54,51,52,59,57,57,53,60,52,53,54,52,51,64,65,66,67,58,60,53,59,61,60,62,60,62,55,55,56,54,53,56,49,56,52,55,53,53,51,53,55,54,57,64,58,53,51,61,60,68,70,61,63,58,59,62,63,58,59,60,61,59,60,64,65,66,67", "endOffsets": "55157,55211,55266,55319,55374,55426,55479,55539,55597,55655,55709,55770,55823,55877,55932,55985,56037,56157,56223,56290,56358,56417,56478,56532,56592,56654,56715,56778,56839,56902,56958,57014,57071,57126,57180,57237,57287,57344,57397,57453,57507,57561,57613,57667,57723,57778,57836,57901,57960,58014,58066,58128,58189,58258,58329,58391,58455,58514,58574,58637,58701,58760,58820,58881,58943,59003,59064,59129,59195,59262,59330"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\55566c834c8001acc9b240421f8a423e\\transformed\\navigation-runtime-2.7.7\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "1870", "startColumns": "4", "startOffsets": "124640", "endColumns": "52", "endOffsets": "124688"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\6245402d8a114e85318ed4c000100fb2\\transformed\\cardview-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "26,587,588,589,590,1081,1082,1083,2547,5914,5916,5919", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1254,30783,30844,30906,30968,64916,64975,65032,177436,412665,412729,412855", "endLines": "26,587,588,589,590,1081,1082,1083,2553,5915,5918,5921", "endColumns": "51,60,61,61,63,58,56,53,12,12,12,12", "endOffsets": "1301,30839,30901,30963,31027,64970,65027,65081,177845,412724,412850,412978"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\c1c22d75c8cbd1da7dd9b0cc24fa738b\\transformed\\fragment-1.5.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1", "startColumns": "-1,-1,-1", "startOffsets": "-1,-1,-1"}, "to": {"startLines": "1848,1892,1928", "startColumns": "4,4,4", "startOffsets": "123545,125809,127679", "endColumns": "56,64,63", "endOffsets": "123597,125869,127738"}}, {"source": "D:\\development\\Codes\\MyApplication\\app\\src\\main\\res\\values\\ids.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "1885", "startColumns": "4", "startOffsets": "125486", "endColumns": "45", "endOffsets": "125527"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\38fdf0035e0d96201bc1ef115272625c\\transformed\\constraintlayout-2.0.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,4,10,11,19,27,33,91,92,93,95,99,100,101,104,112,122,150,151,156,157,162,167,168,169,174,175,180,181,186,187,188,194,195,196,201,206,220,221,225,226,227,228,231,232,235,238,239,240,241,242,245,248,249,250,251,256,259,262,263,264,269,270,271,274,277,278,281,284,287,290,291,292,295,298,299,304,305,310,313,316,317,318,319,320,321,322,323,324,325,412,413,414,415,420,426,427,428,431,457,472,512,513,523,529,532,536,537,538,539,548,1863", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,263,498,559,850,1306,1613,4857,4909,4970,5077,5210,5262,5312,5473,5782,6205,8147,8206,8403,8460,8655,8837,8891,8948,9140,9198,9394,9450,9644,9701,9752,9974,10026,10081,10271,10447,11194,11250,11410,11471,11531,11601,11734,11802,11931,12057,12119,12184,12252,12319,12442,12567,12634,12699,12764,12945,13066,13187,13253,13320,13530,13599,13665,13790,13916,13983,14109,14236,14361,14488,14544,14609,14735,14858,14923,15131,15198,15378,15498,15618,15683,15745,15807,15869,15928,15988,16049,16110,16169,21329,21380,21429,21477,21764,21994,22041,22101,22207,23554,24237,26717,26769,27144,27382,27560,27699,27745,27800,27845,28186,124262", "endLines": "2,8,10,18,19,27,33,91,92,93,98,99,100,101,111,119,122,150,155,156,161,166,167,168,173,174,179,180,185,186,187,193,194,195,200,205,206,220,224,225,226,227,230,231,234,237,238,239,240,241,244,247,248,249,250,255,258,261,262,263,268,269,270,273,276,277,280,283,286,289,290,291,294,297,298,303,304,309,312,315,316,317,318,319,320,321,322,323,324,336,412,413,414,415,425,426,427,430,435,457,472,512,521,528,529,535,536,537,538,547,551,1863", "endColumns": "56,11,60,11,51,47,50,51,60,45,11,51,49,50,11,11,44,58,11,56,11,11,53,56,11,57,11,55,11,56,50,11,51,54,11,11,49,55,11,60,59,69,11,67,11,11,61,64,67,66,11,11,66,64,64,11,11,11,65,66,11,68,65,11,11,66,11,11,11,11,55,64,11,11,64,11,66,11,11,11,64,61,61,61,58,59,60,60,58,11,50,48,47,57,11,46,59,11,11,45,46,51,11,11,54,11,45,54,44,11,11,40", "endOffsets": "202,444,554,845,897,1349,1659,4904,4965,5011,5205,5257,5307,5358,5777,6089,6245,8201,8398,8455,8650,8832,8886,8943,9135,9193,9389,9445,9639,9696,9747,9969,10021,10076,10266,10442,10492,11245,11405,11466,11526,11596,11729,11797,11926,12052,12114,12179,12247,12314,12437,12562,12629,12694,12759,12940,13061,13182,13248,13315,13525,13594,13660,13785,13911,13978,14104,14231,14356,14483,14539,14604,14730,14853,14918,15126,15193,15373,15493,15613,15678,15740,15802,15864,15923,15983,16044,16105,16164,16632,21375,21424,21472,21530,21989,22036,22096,22202,22382,23595,24279,26764,27094,27377,27432,27694,27740,27795,27840,28181,28318,124298"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\86a8ee86ddb77f4af22b4f128e92bf76\\transformed\\savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "1926", "startColumns": "4", "startOffsets": "127575", "endColumns": "53", "endOffsets": "127624"}}, {"source": "D:\\development\\Codes\\MyApplication\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,276,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,277", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,13318,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,13370", "endColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,51,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,58", "endOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,13365,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,13424"}, "to": {"startLines": "2031,2032,2033,2034,2035,2036,2037,2038,2039,2040,2041,2042,2043,2044,2045,2046,2047,2049,2050,2051,2053,2054,2055,2056,2057,2058,2059,2066,2067,2068,2069,2070,2078,2082,2084,2087,2088,2089,2090,2091,2093,2094,2095,2096,2097,2098,2099,2102,2103,2104,2105,2106,2107,2108,2110,2111,2112,2113,2114,2115,2116,2117,2118,2119,2128,2129,2130,2131,2132,2133,2134,2135,2136,2137,2138,2139,2140,2141,2142,2143,2144,2145,2146,2147,2148,2149,2150,2151,2153,2154,2155,2156,2160,2162,2163,2164,2165,2264,2265,2266,2267,2268,2269,2270,2340,2341,2344,2345,2346,2347,2348,2349,2350,2351,2352,2354,2361,2362,2363,2367,2368,2369,2370,2371,2372,2373,2374,2375,2376,2379,2380,2381,2382,2383,2384,2385,2386,2388,2389,2390,2391,2392,2393,2394,2395,2396,2397,2398,2399,2400,2401,2402,2403,2404,2405,2406,2407,2408,2409,2410,2411,2412,2413,2414,2415,2416,2417,2418,2419,2420,2421,2422,2423,2424,2425,2426,2427,2428,2429,2430,2431,2432,2433,2434,2435,2436,2437,2438,2439,2445,2446,2448,2449,2450,2451,2452,2456,2457,2458,2459,2461,2463,2466,2467,2468,2470,2471,2472,2473,2474,2475,2476,2477,2478,2479,2480,2481,2484,2485,2486,2487,2488,2489,2492,2493,2494,2495,2496,2497,2498,2499", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "134624,134661,134696,134750,134803,134864,134907,134982,135064,135140,135210,135396,135461,135559,135627,135814,135878,136002,136048,136103,136294,136347,136384,136430,136466,136515,136574,137716,137756,137805,137859,137922,138480,138816,138932,139088,139134,139190,139229,139284,139375,139431,139479,139523,139566,139619,139673,139883,139921,139977,140043,140109,140174,140214,140308,140344,140427,140513,140600,140686,140770,140857,140901,140962,141684,141740,141793,141847,141917,141967,142022,142073,142123,142175,142235,142282,142320,142372,142423,142497,142544,142606,142665,142721,142771,142829,142867,142917,143143,143179,143224,143274,143597,143692,143755,143806,143857,153316,153370,153437,153497,153546,153594,153632,164214,164251,164382,164453,164536,164608,164688,164759,164839,164905,164982,165073,165943,165994,166045,166235,166276,166322,166378,166428,166467,166515,166568,166632,166688,166860,166902,166940,166977,167023,167070,167115,167155,167244,167304,167363,167420,167476,167536,167590,167648,167715,167780,167851,167908,167963,168020,168083,168139,168199,168268,168341,168410,168467,168528,168583,168638,168698,168756,168813,168873,168930,168995,169059,169127,169182,169238,169295,169352,169411,169466,169530,169593,169655,169721,169779,169833,169887,169947,170001,170066,170131,170198,170261,170315,170875,170924,171028,171087,171127,171181,171244,171731,171783,171834,171890,172026,172204,172325,172374,172411,172543,172591,172648,172696,172736,172772,172826,172882,172956,173001,173061,173116,173297,173350,173389,173441,173500,173570,173735,173774,173813,173854,173916,173989,174031,174065", "endColumns": "36,34,53,52,60,42,74,81,75,69,185,64,97,67,186,63,40,45,54,40,52,36,45,35,48,58,52,39,48,53,62,46,37,36,36,45,55,38,54,52,55,47,43,42,52,53,42,37,55,65,65,64,39,35,35,82,85,86,85,83,86,43,60,36,55,52,53,69,49,54,50,49,51,59,46,37,51,50,73,46,61,58,55,49,57,37,49,72,35,44,49,56,35,62,50,50,42,53,66,59,48,47,37,55,36,32,70,82,71,79,70,79,65,76,54,33,50,50,49,40,45,55,49,38,47,52,63,55,67,41,37,36,45,46,44,39,35,59,58,56,55,59,53,57,66,64,70,56,54,56,62,55,59,68,72,68,56,60,54,54,59,57,56,59,56,64,63,67,54,55,56,56,58,54,63,62,61,65,57,53,53,59,53,64,64,66,62,53,51,48,57,58,39,53,62,47,51,50,55,64,38,37,48,36,41,47,56,47,39,35,53,55,73,44,59,54,69,52,38,51,58,69,66,38,38,40,61,72,41,33,58", "endOffsets": "134656,134691,134745,134798,134859,134902,134977,135059,135135,135205,135391,135456,135554,135622,135809,135873,135914,136043,136098,136139,136342,136379,136425,136461,136510,136569,136622,137751,137800,137854,137917,137964,138513,138848,138964,139129,139185,139224,139279,139332,139426,139474,139518,139561,139614,139668,139711,139916,139972,140038,140104,140169,140209,140245,140339,140422,140508,140595,140681,140765,140852,140896,140957,140994,141735,141788,141842,141912,141962,142017,142068,142118,142170,142230,142277,142315,142367,142418,142492,142539,142601,142660,142716,142766,142824,142862,142912,142985,143174,143219,143269,143326,143628,143750,143801,143852,143895,153365,153432,153492,153541,153589,153627,153683,164246,164279,164448,164531,164603,164683,164754,164834,164900,164977,165032,165102,165989,166040,166090,166271,166317,166373,166423,166462,166510,166563,166627,166683,166751,166897,166935,166972,167018,167065,167110,167150,167186,167299,167358,167415,167471,167531,167585,167643,167710,167775,167846,167903,167958,168015,168078,168134,168194,168263,168336,168405,168462,168523,168578,168633,168693,168751,168808,168868,168925,168990,169054,169122,169177,169233,169290,169347,169406,169461,169525,169588,169650,169716,169774,169828,169882,169942,169996,170061,170126,170193,170256,170310,170362,170919,170977,171082,171122,171176,171239,171287,171778,171829,171885,171950,172060,172237,172369,172406,172448,172586,172643,172691,172731,172767,172821,172877,172951,172996,173056,173111,173181,173345,173384,173436,173495,173565,173632,173769,173808,173849,173911,173984,174026,174060,174119"}}, {"source": "D:\\development\\Codes\\MyApplication\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "6026,7080,7081,7095,7121,7137,7162", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "419825,491014,491078,491922,493389,494055,495403", "endLines": "6031,7080,7094,7120,7136,7161,7162", "endColumns": "12,63,12,12,12,12,61", "endOffsets": "420276,491073,491917,493384,494050,495398,495460"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\6e1bec7b61f7c5e14c57eb6146ea2199\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "1807,1808,1809,1810,1811,1812,1813,1814,1815,1816,1817,1818,1819,1820,1821,1822,1823,1824,1825,1826,1827,1828,1829,1830,1831,1832,1833,1834,1835,1836,1837,1838,1843,1846,1847,1851,1853,1929,2085,2086,2100,2101,2109,2158,2159,2339,2342,2353,2355,2377,2378,2447,2464,2465,2469,5922,5925,5928", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "121133,121192,121251,121311,121371,121431,121491,121551,121611,121671,121731,121791,121851,121910,121970,122030,122090,122150,122210,122270,122330,122390,122450,122510,122569,122629,122689,122748,122807,122866,122925,122984,123248,123432,123490,123689,123774,127743,138969,139034,139716,139782,140250,143485,143537,164152,164284,165037,165107,166756,166806,170982,172242,172289,172453,412983,413095,413206", "endLines": "1807,1808,1809,1810,1811,1812,1813,1814,1815,1816,1817,1818,1819,1820,1821,1822,1823,1824,1825,1826,1827,1828,1829,1830,1831,1832,1833,1834,1835,1836,1837,1838,1843,1846,1847,1851,1853,1929,2085,2086,2100,2101,2109,2158,2159,2339,2342,2353,2355,2377,2378,2447,2464,2465,2469,5924,5927,5931", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,35,33,49,53,45,46,35,89,12,12,12", "endOffsets": "121187,121246,121306,121366,121426,121486,121546,121606,121666,121726,121786,121846,121905,121965,122025,122085,122145,122205,122265,122325,122385,122445,122505,122564,122624,122684,122743,122802,122861,122920,122979,123038,123317,123485,123540,123735,123824,127791,139029,139083,139777,139878,140303,143532,143592,164209,164333,165068,165136,166801,166855,171023,172284,172320,172538,413090,413201,413396"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\8502c7d6e294f2825540546f57fbfcc0\\transformed\\material-1.11.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "3,9,20,21,22,23,24,25,28,29,30,31,32,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,102,103,123,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,207,209,210,212,213,214,215,216,217,218,219,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,416,417,418,436,438,439,440,441,442,443,444,445,446,447,448,449,453,454,455,456,458,467,468,469,470,471,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,510,511,522,530,531,560,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,638,639,640,641,642,643,644,645,646,647,648,649,650,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,700,701,702,703,704,705,706,707,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,777,778,779,780,781,782,783,784,785,786,787,788,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,804,805,806,807,808,821,822,823,824,825,826,827,828,829,830,831,832,833,834,835,836,837,838,839,840,841,842,843,844,845,846,847,848,849,850,851,852,853,854,855,856,857,858,859,860,861,862,863,864,865,866,867,868,869,870,871,872,873,874,1080,1084,1093,1094,1095,1096,1097,1098,1099,1100,1101,1102,1103,1104,1105,1106,1107,1108,1109,1110,1111,1112,1113,1114,1115,1116,1117,1118,1119,1120,1121,1122,1123,1124,1125,1126,1127,1128,1129,1130,1131,1132,1133,1134,1135,1136,1137,1138,1139,1140,1156,1157,1158,1159,1160,1161,1162,1163,1164,1165,1166,1167,1168,1169,1170,1171,1172,1173,1174,1175,1176,1177,1178,1179,1180,1181,1182,1183,1184,1185,1186,1187,1188,1189,1190,1191,1192,1193,1194,1195,1196,1197,1198,1199,1200,1201,1202,1203,1204,1205,1206,1207,1208,1209,1210,1211,1212,1213,1214,1215,1216,1217,1218,1219,1220,1221,1222,1223,1224,1225,1226,1227,1228,1229,1230,1231,1232,1233,1234,1235,1236,1237,1238,1239,1240,1241,1242,1243,1244,1245,1246,1247,1248,1249,1250,1251,1252,1253,1254,1255,1256,1257,1258,1259,1260,1261,1262,1263,1264,1265,1266,1267,1268,1269,1270,1271,1272,1273,1274,1275,1276,1277,1278,1279,1280,1281,1282,1283,1284,1285,1286,1287,1288,1289,1290,1291,1292,1293,1294,1295,1296,1297,1298,1299,1300,1301,1302,1303,1304,1305,1306,1307,1308,1309,1310,1311,1312,1313,1314,1315,1316,1317,1318,1319,1320,1321,1322,1323,1324,1325,1326,1327,1328,1329,1330,1331,1332,1333,1334,1335,1336,1337,1338,1339,1340,1341,1342,1343,1344,1345,1346,1347,1348,1349,1350,1351,1352,1353,1354,1355,1356,1357,1358,1359,1360,1361,1362,1363,1364,1365,1366,1367,1368,1369,1370,1371,1372,1373,1374,1375,1376,1377,1378,1379,1380,1381,1382,1383,1384,1385,1386,1387,1388,1389,1390,1391,1392,1393,1394,1395,1396,1397,1398,1399,1400,1401,1402,1403,1404,1405,1406,1407,1408,1409,1410,1411,1412,1413,1414,1415,1416,1417,1418,1419,1420,1421,1422,1423,1424,1425,1426,1427,1428,1429,1430,1431,1432,1433,1434,1435,1436,1437,1438,1439,1440,1441,1442,1443,1444,1445,1446,1447,1448,1449,1450,1451,1452,1453,1454,1455,1456,1457,1458,1459,1460,1461,1462,1463,1464,1465,1466,1467,1468,1469,1470,1471,1472,1473,1474,1475,1476,1477,1478,1479,1480,1481,1482,1483,1484,1485,1486,1487,1488,1489,1490,1491,1492,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1532,1533,1534,1535,1536,1537,1538,1539,1540,1541,1542,1543,1544,1545,1546,1547,1548,1549,1550,1551,1552,1553,1554,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1568,1569,1570,1571,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1586,1587,1588,1589,1590,1591,1592,1593,1594,1595,1596,1597,1598,1599,1600,1601,1602,1603,1604,1605,1606,1607,1608,1609,1610,1611,1612,1613,1614,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1627,1628,1629,1630,1631,1632,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1646,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1658,1659,1660,1661,1662,1663,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1692,1693,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1711,1712,1713,1714,1715,1716,1717,1718,1719,1720,1721,1722,1723,1724,1725,1726,1727,1728,1729,1730,1731,1732,1733,1734,1735,1736,1737,1738,1739,1740,1741,1742,1743,1744,1745,1746,1747,1748,1749,1750,1751,1752,1753,1754,1755,1756,1757,1758,1759,1760,1761,1762,1763,1764,1765,1766,1767,1768,1769,1770,1771,1802,1803,1858,1859,1860,1864,1865,1866,1867,1868,1869,1871,1872,1873,1874,1875,1876,1886,1889,1890,1891,1909,1910,1911,1912,1913,1914,1915,1923,1932,1933,1936,1937,1938,1939,1940,1941,1942,1943,1944,1945,1946,1947,1948,1949,1950,1951,1952,1953,1954,1955,1956,1957,1958,1959,1960,1961,1962,1963,1964,1965,1966,1967,1968,1969,1970,1971,1972,1973,1974,1975,1976,1977,1978,1979,1980,1981,1982,1983,1984,1985,1986,1987,1988,1989,1990,1991,1992,1993,1994,1995,1998,2000,2052,2060,2061,2062,2063,2064,2065,2079,2080,2081,2083,2120,2123,2125,2126,2127,2152,2157,2161,2166,2167,2168,2169,2170,2171,2172,2173,2174,2175,2176,2177,2178,2179,2180,2181,2242,2243,2244,2245,2246,2247,2248,2249,2250,2251,2252,2253,2254,2255,2256,2257,2258,2259,2260,2261,2262,2263,2271,2272,2273,2274,2275,2276,2277,2278,2279,2280,2283,2286,2289,2290,2291,2292,2293,2294,2295,2296,2297,2298,2299,2300,2301,2302,2303,2304,2305,2306,2307,2308,2309,2310,2311,2312,2313,2314,2315,2316,2317,2318,2319,2320,2321,2322,2323,2324,2325,2326,2327,2328,2329,2330,2331,2332,2333,2334,2335,2336,2337,2338,2356,2357,2358,2359,2360,2440,2443,2444,2453,2454,2505,2509,2513,2517,2521,2522,2565,2573,2580,2750,2753,2763,2772,2781,2850,2851,2852,2853,2859,2860,2861,2862,2863,2864,2870,2871,2872,2873,2874,2879,2880,2884,2885,2891,2895,2896,2897,2898,2908,2909,2910,2914,2915,2921,2925,2995,2998,2999,3003,3004,3007,3008,3009,3010,3273,3280,3540,3546,3809,3816,4076,4082,4145,4227,4279,4361,4423,4505,4569,4621,4703,4711,4717,4728,4732,4736,4749,5524,5540,5547,5553,5570,5583,5603,5620,5629,5634,5641,5661,5674,5691,5697,5703,5710,5714,5720,5734,5737,5747,5748,5749,5797,5801,5805,5809,5810,5811,5814,5830,5837,5851,5896,5932,5938,5942,5946,5951,5958,5964,5965,5968,5972,5977,5990,5994,5999,6004,6009,6012,6015,6018,6022,6171,6172,6173,6174,6402,6403,6404,6405,6406,6407,6408,6409,6410,6411,6412,6413,6414,6415,6416,6417,6418,6419,6420,6424,6428,6432,6436,6440,6444,6448,6449,6450,6451,6452,6453,6454,6455,6459,6463,6464,6468,6469,6472,6476,6479,6482,6485,6489,6492,6495,6499,6503,6507,6511,6514,6515,6516,6517,6520,6524,6527,6530,6533,6536,6539,6542,6613,6616,6617,6620,6623,6624,6627,6628,6629,6633,6634,6639,6646,6653,6660,6667,6674,6681,6688,6695,6702,6711,6720,6729,6736,6745,6754,6757,6760,6761,6762,6763,6764,6765,6766,6767,6768,6769,6770,6771,6772,6776,6781,6786,6789,6790,6791,6792,6793,6801,6809,6810,6818,6822,6830,6838,6846,6854,6862,6863,6871,6879,6880,6883,6922,6924,6929,6931,6936,6940,6944,6945,6946,6947,6951,6955,6956,6960,6961,6962,6963,6964,6965,6966,6967,6968,6969,6970,6971,6972,6973,6974,6975,6979,6983,6984,6988,6989,6990,6995,6996,6997,6998,6999,7000,7001,7002,7003,7004,7005,7006,7007,7008,7009,7010,7011,7012,7013,7014,7015,7019,7020,7021,7027,7028,7032,7034,7035,7040,7041,7042,7043,7044,7045,7049,7050,7051,7057,7058,7062,7064,7068,7072,7076,7175,7176,7177,7178,7181,7184,7187,7190,7193,7198,7202,7205,7206,7211,7215,7220,7226,7232,7237,7241,7246,7250,7254,7295,7296,7297,7298,7299,7303,7304,7305,7306,7310,7314,7318,7322,7326,7330,7334,7338,7344,7345,7386,7400,7405,7431,7438,7441,7452,7457,7460,7463,7518,7524,7525,7528,7531,7534,7537,7540,7543,7546,7550,7553,7554,7555,7563,7571,7574,7579,7584,7589,7594,7598,7602,7603,7611,7612,7613,7614,7615,7623,7628,7633,7634,7635,7636,7661,7667,7672,7675,7679,7682,7686,7696,7699,7704,7707,7711,7812,7820,7834,7847,7851,7866,7877,7880,7891,7896,7900,7935,7936,7937,7949,7957,7965,7973,7981,8001,8004,8031,8036,8056,8059,8062,8069,8082,8091,8094,8114,8124,8128,8132,8145,8149,8153,8157,8163,8167,8184,8192,8196,8200,8204,8207,8211,8215,8219,8229,8236,8243,8247,8273,8283,8308,8317,8337,8347,8351,8361,8386,8396,8399,8403,8404,8405,8406,8410,8416,8422,8423,8436,8437,8438,8441,8444,8447,8450,8453,8456,8459,8462,8465,8468,8471,8474,8477,8480,8483,8486,8489,8492,8495,8498,8501,8502,8507,8508,8521,8531,8535,8540,8545,8549,8552,8556,8560,8563,8567,8570,8574,8579,8584,8587,8594,8598,8602,8611,8616,8621,8622,8626,8629,8633,8646,8651,8659,8663,8667,8684,8688,8693,8711,8718,8722,8752,8755,8758,8761,8764,8767,8770,8789,8795,8803,8810,8822,8830,8835,8840,8844,8855,8859,8867,8870,8875,8876,8877,8878,8882,8886,8890,8894,8929,8932,8936,8940,8974,8977,8981,8985,8994,9000,9003,9013,9017,9018,9025,9029,9036,9037,9038,9041,9046,9051,9052,9056,9071,9090,9094,9095,9107,9117,9118,9130,9135,9159,9162,9168,9171,9180,9188,9192,9195,9198,9201,9205,9208,9225,9229,9232,9247,9250,9258,9263,9270,9275,9276,9281,9282,9288,9294,9300,9332,9343,9360,9367,9371,9374,9387,9396,9400,9405,9409,9413,9417,9421,9425,9429,9433,9438,9441,9453,9458,9467,9470,9477,9478,9482,9491,9497,9501,9502,9506,9527,9533,9537,9541,9542,9560,9561,9562,9563,9564,9569,9572,9573,9579,9580,9592,9604,9611,9612,9617,9622,9623,9627,9641,9646,9652,9658,9664,9669,9675,9681,9682,9688,9703,9708,9717,9726,9729,9743,9748,9759,9763,9772,9781,9782,9789,13298,13299,13300,13301,13302,13303,13304,13305,13306,13307,13308,13309,13310,13311,13312,13313,13314,13315,13316,13317,13318,13319,13320,13321,13322,13323,13324,13325,13326,13327,13328,13329,13330,13331,13332,13333,13334,13335,13336,13337,13338,13339,13340,13341,13342,13343,13344,13345,13346,13347,13348,13349,13350,13351,13352,13353,13354,13355,13356,13357,13358,13359,13360,13361,13362,13363,13364,13365,13366,13367,13368,13369,13370,13371,13372,13373,13374,13375,13376,13377,13378,13379,13380,13381,13382,13383,13384,13385,13386,13387,13388,13389,13390,13391,13392,13393,13394,13395,13396,13397,13398,13399,13400,13401,13402,13403,13404,13405,13406,13407,13408,13409,13410,13411,13412,13413,13414,13415,13416,13417,13418,13419,13420,13421,13422,13423,13424,13425,13426,13427,13428,13429,13430,13431,13432,13433,13434,13435,13436,13437,13438,13439,13440,13441,13442,13443,13444,13445,13446,13447,13448,13449,13450,13451,13452,13453,13454,13455,13456,13457,13458,13459,13460,13461,13462,13463,13464,13465,13466,13467,13468,13469,13470,13471,13472,13473,13474,13475,13476,13477,13478,13479,13480,13481,13482,13483,13484,13485,13486,13487,13488,13489,13490,13491,13492,13493,13494,13495,13496,13497,13498,13499,13500,13501,13502,13503,13504,13505,13506,13507,13508,13509,13510,13511,13512,13513,13514,13515,13516,13517,13518,13519,13520,13521,13522,13523,13524,13525,13526,13527,13528,13529,13530,13531,13532,13533,13534,13535,13536,13537,13538,13539,13540,13541,13542,13543,13544,13545,13546,13547,13548,13549,13550,13551,13552,13553,13554,13555,13556,13557,13558,13559,13560,13561,13562,13563,13564,13565,13566,13567,13568,13569,13570,13571,13572,13573,13574,13575,13576,13577,13578,13579,13580,13581,13582,13583,13584,13585,13586,13587,13588,13589,13590,13591,13592,13593,13594,13595,13596,13597,13598,13599,13600,13601,13602,13603,13604,13605,13606,13607,13608,13609,13610,13611,13612,13613,13614,13615,13616,13617,13618,13619,13620,13621,13622,13623,13624,13625,13626,13627,13628,13629,13630,13631,13632,13633,13634,13635,13636,13637,13638,13639,13640,13641,13642,13643,13644,13645,13646,13647,13648,13649,13650,13651,13652,13653,13654,13655,13656,13657,13658,13659,13660,13661,13662,13663,13664,13665,13666,13667,13668,13669,13670,13671,13672,13673", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "207,449,902,957,1013,1073,1134,1199,1354,1404,1454,1507,1565,1664,1733,1781,1852,1924,1996,2069,2136,2185,2239,2276,2327,2387,2434,2490,2539,2597,2651,2712,2768,2819,2879,2935,2998,3047,3103,3159,3209,3268,3323,3385,3432,3486,3542,3594,3649,3703,3757,3811,3860,3918,3972,4029,4085,4132,4185,4241,4301,4364,4423,4485,4535,4589,4643,4691,4748,4801,5363,5417,6250,6361,6423,6479,6539,6592,6653,6732,6813,6885,6964,7044,7120,7198,7267,7343,7420,7491,7564,7640,7718,7787,7863,7940,8004,8075,10497,10593,10646,10750,10817,10870,10922,10972,11030,11095,11143,16637,16704,16770,16828,16897,16955,17024,17094,17167,17241,17309,17376,17446,17512,17585,17645,17721,17781,17841,17916,17984,18050,18118,18178,18237,18294,18360,18422,18479,18547,18620,18690,18752,18813,18881,18943,19013,19082,19138,19197,19259,19321,19388,19445,19506,19567,19628,19689,19745,19801,19857,19913,19971,20029,20087,20145,20202,20259,20316,20373,20432,20491,20549,20632,20715,20788,20842,20911,20967,21048,21129,21200,21535,21588,21646,22387,22501,22547,22607,22661,22731,22801,22866,22932,22997,23065,23134,23202,23332,23385,23444,23502,23600,23986,24038,24084,24134,24190,24284,24342,24400,24462,24525,24587,24646,24706,24771,24837,24902,24964,25026,25088,25150,25212,25274,25340,25407,25473,25536,25600,25663,25731,25792,25854,25916,25979,26043,26106,26170,26248,26307,26373,26453,26514,26608,26666,27099,27437,27501,28821,31032,31106,31177,31243,31317,31386,31457,31530,31601,31669,31742,31818,31888,31966,32034,32100,32161,32230,32294,32360,32428,32494,32557,32625,32696,32761,32834,32897,32978,33042,33108,33178,33248,33318,33388,34217,34274,34332,34391,34451,34510,34569,34628,34687,34746,34805,34864,34923,34982,35041,35101,35162,35224,35285,35346,35407,35468,35529,35590,35650,35711,35772,35832,35893,35954,36015,36076,36137,36198,36259,36320,36381,36442,36503,36571,36640,36710,36779,36848,36917,36986,37055,37124,37193,37262,37331,37400,37460,37521,37583,37644,37705,37766,37827,37888,37949,38010,38071,38132,38193,38255,38318,38382,38445,38508,38571,38634,38697,38760,38823,38886,38949,39012,39073,39135,39198,39260,39322,39384,39446,39508,39570,39632,39694,39756,39818,39875,39961,40041,40131,40226,40318,40410,40500,40583,40676,40763,40860,40951,41052,41139,41242,41331,41430,41522,41622,41706,41800,41888,41986,42069,42160,42254,42353,42455,42553,42653,42740,42840,42926,43022,43110,43191,43282,43378,43471,43564,43655,43740,43834,43923,44021,44114,44216,44304,44408,44499,44599,44692,44793,44878,44973,45062,45161,45246,45338,45433,45533,45636,45735,45838,45927,46028,46115,46212,46300,46396,46488,46588,46678,46776,46861,46950,47039,47132,47219,47982,48048,48124,48193,48272,48345,48425,48505,48582,48650,48728,48804,48875,48956,49029,49112,49187,49272,49345,49426,49507,49581,49665,49735,49813,49883,49963,50041,50113,50195,50265,50342,50422,50507,50595,50679,50766,50840,50918,50996,51067,51148,51239,51322,51418,51516,51623,51688,51754,51807,51883,51949,52036,52112,64851,65086,65681,65735,65814,65892,65965,66030,66093,66159,66230,66301,66371,66433,66502,66568,66628,66695,66762,66818,66869,66922,66974,67028,67099,67162,67221,67283,67342,67415,67482,67552,67612,67675,67750,67822,67918,67989,68045,68116,68173,68230,68296,68360,68431,68488,68541,68604,68656,68714,69951,70020,70086,70145,70228,70287,70344,70411,70481,70555,70617,70686,70756,70855,70952,71051,71137,71223,71304,71379,71468,71559,71643,71702,71748,71814,71871,71938,71995,72077,72142,72208,72331,72415,72536,72601,72663,72761,72835,72918,73007,73071,73150,73224,73286,73382,73447,73506,73562,73618,73678,73785,73832,73892,73953,74017,74078,74138,74196,74239,74288,74340,74391,74443,74492,74541,74606,74672,74732,74793,74849,74908,74957,75005,75063,75120,75222,75279,75354,75402,75453,75515,75580,75632,75706,75769,75832,75900,75950,76012,76072,76129,76189,76238,76306,76412,76514,76583,76654,76710,76759,76859,76930,77040,77129,77220,77302,77400,77456,77557,77667,77766,77829,77935,78012,78124,78251,78363,78490,78560,78674,78805,78902,78970,79088,79191,79309,79370,79444,79511,79616,79738,79812,79879,79989,80088,80161,80258,80380,80498,80616,80677,80799,80916,80984,81090,81192,81272,81343,81439,81506,81580,81654,81740,81828,81918,81996,82073,82173,82244,82365,82486,82550,82675,82749,82873,82997,83064,83173,83301,83413,83492,83570,83671,83742,83864,83986,84051,84177,84289,84395,84463,84562,84666,84729,84795,84879,84992,85105,85223,85301,85373,85509,85645,85730,85870,86008,86146,86288,86370,86479,86590,86718,86846,86978,87108,87238,87372,87434,87530,87597,87714,87835,87932,88014,88101,88188,88319,88450,88585,88662,88739,88850,88964,89038,89147,89259,89361,89457,89561,89628,89722,89794,89904,90010,90083,90174,90276,90379,90474,90581,90686,90808,90930,91056,91115,91173,91297,91421,91549,91667,91785,91907,91993,92090,92224,92358,92438,92576,92708,92840,92976,93051,93127,93230,93304,93417,93498,93555,93616,93675,93735,93793,93854,93912,93962,94011,94078,94137,94196,94245,94316,94400,94470,94541,94621,94690,94753,94821,94887,94955,95020,95086,95163,95241,95347,95453,95549,95678,95767,95894,95960,96029,96115,96181,96264,96362,96458,96554,96652,96761,96856,96945,97007,97067,97132,97189,97270,97324,97381,97478,97588,97649,97764,97885,97980,98072,98165,98267,98323,98382,98431,98523,98572,98626,98680,98734,98788,98842,98897,99007,99117,99225,99335,99445,99555,99665,99773,99879,99983,100087,100191,100286,100381,100474,100567,100671,100777,100881,100985,101078,101171,101264,101357,101465,101571,101677,101783,101880,101975,102070,102165,102271,102377,102483,102589,102687,102783,102879,102977,103042,103146,103204,103268,103329,103391,103451,103516,103578,103646,103704,103767,103830,103897,103972,104045,104111,104163,104216,104268,104325,104409,104504,104589,104670,104750,104827,104906,104983,105057,105131,105202,105282,105354,105429,105494,105555,105615,105690,105764,105837,105907,105979,106049,106122,106186,106256,106302,106371,106423,106508,106591,106648,106714,106781,106847,106928,107003,107059,107112,107173,107231,107281,107330,107379,107428,107490,107542,107587,107668,107719,107773,107826,107880,107931,107980,108046,108097,108158,108219,108281,108331,108372,108449,108508,108567,108626,108687,108743,108799,108866,108927,108992,109047,109112,109181,109249,109327,109396,109456,109527,109601,109666,109738,109808,109875,109959,110028,110095,110165,110228,110295,110363,110446,110525,110615,110692,110760,110827,110905,110962,111019,111087,111153,111209,111269,111328,111382,111432,111482,111530,111592,111643,111716,111796,111876,111940,112007,112078,112136,112197,112263,112322,112389,112449,112509,112572,112640,112701,112768,112846,112916,112965,113022,113091,113152,113240,113328,113416,113504,113591,113678,113765,113852,113910,113984,114054,114110,114181,114246,114308,114383,114456,114546,114612,114678,114739,114803,114865,114923,114994,115077,115136,115207,115273,115338,115399,115458,115529,115595,115660,115743,115819,115894,115975,116035,116104,116174,116243,116298,116354,116410,116471,116529,116585,116639,116694,116756,116813,116907,116976,117077,117128,117198,117261,117317,117375,117434,117488,117574,117658,117728,117797,117867,117982,118103,118170,118237,118312,118379,118438,118492,118546,118600,118653,118705,120643,120780,124019,124068,124118,124303,124351,124407,124465,124527,124582,124693,124764,124828,124887,124949,125015,125532,125677,125721,125766,126744,126795,126842,126887,126938,126989,127040,127424,127922,127988,128167,128230,128302,128359,128413,128468,128526,128581,128640,128696,128765,128834,128903,128973,129036,129099,129162,129225,129290,129355,129420,129485,129548,129612,129676,129740,129791,129869,129947,130018,130090,130163,130235,130301,130367,130435,130503,130569,130636,130710,130773,130830,130890,130955,131022,131087,131144,131205,131263,131367,131477,131586,131690,131768,131833,131900,131966,132036,132083,132135,132311,132438,136144,136627,136758,136942,137120,137358,137547,138518,138616,138731,138853,140999,141159,141281,141370,141527,142990,143331,143633,143900,144087,144183,144273,144369,144459,144625,144748,144871,145041,145147,145262,145377,145479,145585,145702,150184,150266,150439,150607,150755,150914,151069,151242,151359,151476,151644,151756,151870,152042,152218,152376,152509,152621,152767,152919,153051,153194,153688,153866,154002,154098,154234,154329,154496,154589,154681,154868,155024,155202,155366,155548,155865,156047,156229,156419,156651,156841,157018,157180,157337,157447,157630,157767,157971,158155,158339,158499,158657,158841,159068,159271,159442,159662,159884,160039,160239,160423,160526,160716,160857,161022,161193,161393,161597,161799,161964,162169,162368,162567,162764,162855,163004,163154,163238,163387,163532,163684,163825,163991,165141,165219,165520,165686,165841,170367,170525,170689,171292,171515,174566,174843,175115,175393,175638,175700,178528,178979,179435,190572,190720,191234,191671,192105,196445,196530,196651,196750,197155,197252,197369,197456,197579,197680,198086,198185,198304,198397,198504,198847,198954,199199,199320,199729,199977,200077,200182,200301,200810,200957,201076,201327,201460,201875,202129,207344,207591,207716,208033,208154,208382,208503,208636,208783,229414,229906,250286,250710,271386,271880,292305,292731,297572,302989,307080,312511,317253,322630,326614,330606,335997,336544,336977,337733,337963,338206,339339,387509,388413,388997,389470,390900,391644,392837,393891,394369,394662,395045,396560,397325,398468,398909,399350,399946,400220,400631,401647,401825,402578,402715,402806,405000,405266,405588,405798,405907,406026,406210,407328,407798,408549,411132,413401,413777,414005,414261,414520,415096,415450,415572,415711,416003,416263,417191,417477,417880,418282,418625,418837,419038,419251,419540,430883,430956,431043,431128,443653,443765,443871,443994,444126,444249,444379,444503,444636,444767,444892,445009,445129,445261,445389,445503,445621,445734,445855,446043,446230,446411,446594,446778,446943,447125,447245,447365,447473,447583,447695,447803,447913,448078,448244,448396,448561,448662,448782,448953,449114,449277,449438,449605,449724,449841,450021,450203,450384,450567,450722,450867,450989,451124,451287,451480,451606,451758,451900,452070,452226,452398,459161,459356,459448,459621,459783,459878,460047,460141,460230,460473,460562,460855,461271,461691,462112,462538,462955,463371,463788,464206,464620,465090,465563,466035,466446,466917,467389,467579,467785,467891,467999,468105,468217,468331,468443,468557,468673,468787,468895,469005,469113,469375,469754,470158,470305,470413,470523,470631,470745,471154,471568,471684,472102,472343,472773,473208,473618,474040,474450,474572,474981,475397,475519,475737,478557,478625,478969,479049,479405,479555,479699,479775,479887,479977,480239,480504,480612,480764,480872,480948,481060,481150,481252,481360,481468,481568,481676,481761,481865,481952,482030,482144,482236,482500,482767,482877,483030,483140,483224,483613,483711,483819,483913,484043,484151,484273,484409,484517,484637,484771,484893,485021,485163,485289,485429,485555,485673,485805,485903,486013,486313,486425,486543,487007,487123,487426,487552,487648,488049,488159,488283,488421,488531,488653,488965,489089,489219,489695,489823,490138,490276,490438,490654,490810,496468,496536,496620,496724,496927,497116,497317,497510,497715,498028,498240,498406,498522,498768,498984,499297,499723,500185,500422,500574,500834,500978,501120,504352,504466,504586,504702,504796,505117,505216,505334,505435,505714,505999,506278,506560,506813,507072,507325,507581,508005,508081,511331,512686,513130,514984,515559,515767,516777,517157,517323,517464,522484,522910,523022,523157,523310,523507,523678,523861,524036,524223,524495,524653,524737,524841,525328,525884,526042,526261,526492,526715,526950,527172,527438,527576,528175,528289,528427,528539,528663,529234,529729,530275,530420,530513,530605,532532,533102,533400,533589,533795,533988,534198,535082,535227,535619,535777,535994,544050,544482,545357,545977,546174,547122,547887,548010,548783,549004,549204,551181,551281,551371,552057,552810,553575,554338,555113,556326,556491,558104,558425,559488,559698,559868,560438,561333,561966,562132,563618,564234,564470,564691,565649,565914,566179,566426,566840,567076,568361,568810,568997,569246,569488,569664,569905,570138,570363,570958,571433,571957,572218,573569,574044,575270,575740,576788,577240,577484,577941,579186,579669,579819,580163,580309,580447,580583,580871,581375,581884,582000,582902,583024,583136,583313,583579,583849,584115,584383,584639,584899,585155,585413,585665,585921,586173,586427,586659,586895,587147,587403,587655,587909,588141,588375,588487,588912,589036,590128,590943,591139,591463,591852,592204,592445,592659,592958,593150,593465,593672,594018,594318,594719,594938,595351,595588,595958,596682,597037,597306,597446,597700,597844,598121,599113,599522,600154,600500,600868,601942,602305,602705,604213,604798,605116,607651,607845,608063,608289,608501,608700,608907,610111,610406,610963,611353,611985,612462,612707,613058,613304,614064,614328,614751,614942,615321,615409,615517,615625,615938,616263,616582,616913,619616,619804,620065,620314,622898,623090,623355,623608,624140,624548,624747,625331,625566,625690,626102,626316,626718,626821,626951,627126,627378,627574,627714,627908,628919,629988,630276,630406,631183,631840,631986,632692,632930,634470,634620,635037,635202,635888,636358,636554,636645,636729,636873,637107,637274,638202,638488,638648,639263,639422,639750,639977,640489,640851,640930,641269,641374,641739,642110,642471,644345,644974,646050,646474,646727,646879,647927,648664,648867,649113,649360,649578,649820,650141,650405,650710,650933,651244,651433,652148,652417,652911,653137,653577,653736,654020,654765,655130,655435,655593,655831,657150,657548,657776,657996,658138,659428,659534,659664,659802,659926,660214,660383,660483,660768,660882,661765,662520,662959,663083,663329,663522,663656,663847,664626,664844,665135,665414,665731,665953,666248,666531,666635,666976,667792,668108,668669,669175,669380,670166,670571,671232,671421,671972,672538,672658,673060,797869,797964,798057,798120,798202,798295,798388,798475,798573,798664,798755,798843,798927,799023,799127,799227,799333,799436,799537,799641,799747,799846,799952,800054,800161,800270,800381,800512,800632,800748,800866,800965,801072,801188,801307,801435,801524,801619,801696,801785,801876,801969,802043,802140,802235,802333,802432,802536,802632,802734,802837,802937,803040,803125,803226,803324,803414,803509,803596,803702,803804,803898,803989,804083,804159,804251,804340,804443,804554,804637,804723,804818,804915,805011,805099,805200,805301,805404,805510,805608,805705,805800,805898,806001,806101,806204,806309,806427,806543,806638,806731,806816,806912,807006,807098,807200,807307,807390,807494,807599,807699,807800,807905,808005,808106,808205,808307,808401,808508,808610,808713,808806,808902,809004,809107,809203,809305,809408,809505,809608,809706,809810,809915,810012,810120,810234,810349,810457,810571,810686,810788,810893,811001,811111,811227,811344,811439,811536,811635,811740,811846,811945,812050,812156,812256,812362,812463,812570,812689,812788,812893,812995,813097,813197,813300,813395,813499,813584,813688,813792,813890,813994,814100,814198,814303,814401,814514,814608,814697,814786,814869,814960,815043,815141,815231,815327,815416,815510,815598,815694,815779,815887,815988,816089,816187,816293,816384,816483,816580,816678,816774,816867,816977,817075,817170,817280,817372,817472,817571,817658,817762,817867,817966,818073,818180,818279,818388,818480,818591,818702,818813,818917,819032,819148,819275,819395,819492,819591,819683,819782,819874,819973,820059,820153,820256,820352,820455,820551,820654,820751,820849,820952,821045,821135,821236,821319,821410,821495,821587,821690,821785,821881,821974,822068,822147,822254,822345,822444,822537,822640,822744,822845,822946,823050,823144,823248,823352,823465,823571,823677,823785,823902,824003,824111,824211,824314,824419,824526,824622,824701,824791,824875,824967,825040,825137,825219,825304,825389,825486,825579,825674,825773,825870,825961,826052,826144,826239,826346,826454,826556,826653,826750,826843,826930,827014,827111,827208,827301,827388,827479,827578,827677,827772,827861,827942,828041,828145,828242,828347,828444,828528,828627,828731,828828,828933,829030,829128,829229,829335,829434,829541,829640,829739,829830,829919,830008,830090,830183,830274,830385,830486,830586,830698,830811,830909,831017,831111,831211,831300,831392,831503,831613,831708,831824,831950,832076,832195,832323,832448,832573,832691,832818,832927,833036,833149,833272,833395,833511,833636,833733,833841,833963,834079,834195,834304,834392,834493,834582,834683,834770,834858,834955,835047,835153,835253,835329", "endLines": "3,9,20,21,22,23,24,25,28,29,30,31,32,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,102,103,123,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,207,209,210,212,213,214,215,216,217,218,219,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,411,416,417,418,436,438,439,440,441,442,443,444,445,446,447,448,452,453,454,455,456,458,467,468,469,470,471,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,510,511,522,530,531,560,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,638,639,640,641,642,643,644,645,646,647,648,649,650,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,700,701,702,703,704,705,706,707,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,777,778,779,780,781,782,783,784,785,786,787,788,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,804,805,806,807,808,821,822,823,824,825,826,827,828,829,830,831,832,833,834,835,836,837,838,839,840,841,842,843,844,845,846,847,848,849,850,851,852,853,854,855,856,857,858,859,860,861,862,863,864,865,866,867,868,869,870,871,872,873,874,1080,1084,1093,1094,1095,1096,1097,1098,1099,1100,1101,1102,1103,1104,1105,1106,1107,1108,1109,1110,1111,1112,1113,1114,1115,1116,1117,1118,1119,1120,1121,1122,1123,1124,1125,1126,1127,1128,1129,1130,1131,1132,1133,1134,1135,1136,1137,1138,1139,1140,1156,1157,1158,1159,1160,1161,1162,1163,1164,1165,1166,1167,1168,1169,1170,1171,1172,1173,1174,1175,1176,1177,1178,1179,1180,1181,1182,1183,1184,1185,1186,1187,1188,1189,1190,1191,1192,1193,1194,1195,1196,1197,1198,1199,1200,1201,1202,1203,1204,1205,1206,1207,1208,1209,1210,1211,1212,1213,1214,1215,1216,1217,1218,1219,1220,1221,1222,1223,1224,1225,1226,1227,1228,1229,1230,1231,1232,1233,1234,1235,1236,1237,1238,1239,1240,1241,1242,1243,1244,1245,1246,1247,1248,1249,1250,1251,1252,1253,1254,1255,1256,1257,1258,1259,1260,1261,1262,1263,1264,1265,1266,1267,1268,1269,1270,1271,1272,1273,1274,1275,1276,1277,1278,1279,1280,1281,1282,1283,1284,1285,1286,1287,1288,1289,1290,1291,1292,1293,1294,1295,1296,1297,1298,1299,1300,1301,1302,1303,1304,1305,1306,1307,1308,1309,1310,1311,1312,1313,1314,1315,1316,1317,1318,1319,1320,1321,1322,1323,1324,1325,1326,1327,1328,1329,1330,1331,1332,1333,1334,1335,1336,1337,1338,1339,1340,1341,1342,1343,1344,1345,1346,1347,1348,1349,1350,1351,1352,1353,1354,1355,1356,1357,1358,1359,1360,1361,1362,1363,1364,1365,1366,1367,1368,1369,1370,1371,1372,1373,1374,1375,1376,1377,1378,1379,1380,1381,1382,1383,1384,1385,1386,1387,1388,1389,1390,1391,1392,1393,1394,1395,1396,1397,1398,1399,1400,1401,1402,1403,1404,1405,1406,1407,1408,1409,1410,1411,1412,1413,1414,1415,1416,1417,1418,1419,1420,1421,1422,1423,1424,1425,1426,1427,1428,1429,1430,1431,1432,1433,1434,1435,1436,1437,1438,1439,1440,1441,1442,1443,1444,1445,1446,1447,1448,1449,1450,1451,1452,1453,1454,1455,1456,1457,1458,1459,1460,1461,1462,1463,1464,1465,1466,1467,1468,1469,1470,1471,1472,1473,1474,1475,1476,1477,1478,1479,1480,1481,1482,1483,1484,1485,1486,1487,1488,1489,1490,1491,1492,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1532,1533,1534,1535,1536,1537,1538,1539,1540,1541,1542,1543,1544,1545,1546,1547,1548,1549,1550,1551,1552,1553,1554,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1568,1569,1570,1571,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1586,1587,1588,1589,1590,1591,1592,1593,1594,1595,1596,1597,1598,1599,1600,1601,1602,1603,1604,1605,1606,1607,1608,1609,1610,1611,1612,1613,1614,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1627,1628,1629,1630,1631,1632,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1646,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1658,1659,1660,1661,1662,1663,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1692,1693,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1711,1712,1713,1714,1715,1716,1717,1718,1719,1720,1721,1722,1723,1724,1725,1726,1727,1728,1729,1730,1731,1732,1733,1734,1735,1736,1737,1738,1739,1740,1741,1742,1743,1744,1745,1746,1747,1748,1749,1750,1751,1752,1753,1754,1755,1756,1757,1758,1759,1760,1761,1762,1763,1764,1765,1766,1767,1768,1769,1770,1771,1802,1803,1858,1859,1860,1864,1865,1866,1867,1868,1869,1871,1872,1873,1874,1875,1876,1886,1889,1890,1891,1909,1910,1911,1912,1913,1914,1915,1923,1932,1933,1936,1937,1938,1939,1940,1941,1942,1943,1944,1945,1946,1947,1948,1949,1950,1951,1952,1953,1954,1955,1956,1957,1958,1959,1960,1961,1962,1963,1964,1965,1966,1967,1968,1969,1970,1971,1972,1973,1974,1975,1976,1977,1978,1979,1980,1981,1982,1983,1984,1985,1986,1987,1988,1989,1990,1991,1992,1993,1994,1995,1998,2003,2052,2060,2061,2062,2063,2064,2065,2079,2080,2081,2083,2122,2123,2125,2126,2127,2152,2157,2161,2166,2167,2168,2169,2170,2171,2172,2173,2174,2175,2176,2177,2178,2179,2180,2181,2242,2243,2244,2245,2246,2247,2248,2249,2250,2251,2252,2253,2254,2255,2256,2257,2258,2259,2260,2261,2262,2263,2271,2272,2273,2274,2275,2276,2277,2278,2279,2282,2285,2288,2289,2290,2291,2292,2293,2294,2295,2296,2297,2298,2299,2300,2301,2302,2303,2304,2305,2306,2307,2308,2309,2310,2311,2312,2313,2314,2315,2316,2317,2318,2319,2320,2321,2322,2323,2324,2325,2326,2327,2328,2329,2330,2331,2332,2333,2334,2335,2336,2337,2338,2356,2357,2358,2359,2360,2442,2443,2444,2453,2454,2508,2512,2516,2520,2521,2525,2572,2579,2587,2752,2762,2771,2780,2789,2850,2851,2852,2858,2859,2860,2861,2862,2863,2869,2870,2871,2872,2873,2878,2879,2883,2884,2890,2894,2895,2896,2897,2907,2908,2909,2913,2914,2920,2924,2925,2997,2998,3002,3003,3006,3007,3008,3009,3272,3279,3539,3545,3808,3815,4075,4081,4144,4226,4278,4360,4422,4504,4568,4620,4702,4710,4716,4727,4731,4735,4748,4763,5539,5546,5552,5569,5582,5602,5619,5628,5633,5640,5660,5673,5690,5696,5702,5709,5713,5719,5733,5736,5746,5747,5748,5796,5800,5804,5808,5809,5810,5813,5829,5836,5850,5895,5896,5937,5941,5945,5950,5957,5963,5964,5967,5971,5976,5989,5993,5998,6003,6008,6011,6014,6017,6021,6025,6171,6172,6173,6174,6402,6403,6404,6405,6406,6407,6408,6409,6410,6411,6412,6413,6414,6415,6416,6417,6418,6419,6423,6427,6431,6435,6439,6443,6447,6448,6449,6450,6451,6452,6453,6454,6458,6462,6463,6467,6468,6471,6475,6478,6481,6484,6488,6491,6494,6498,6502,6506,6510,6513,6514,6515,6516,6519,6523,6526,6529,6532,6535,6538,6541,6545,6615,6616,6619,6622,6623,6626,6627,6628,6632,6633,6638,6645,6652,6659,6666,6673,6680,6687,6694,6701,6710,6719,6728,6735,6744,6753,6756,6759,6760,6761,6762,6763,6764,6765,6766,6767,6768,6769,6770,6771,6775,6780,6785,6788,6789,6790,6791,6792,6800,6808,6809,6817,6821,6829,6837,6845,6853,6861,6862,6870,6878,6879,6882,6885,6923,6928,6930,6935,6939,6943,6944,6945,6946,6950,6954,6955,6959,6960,6961,6962,6963,6964,6965,6966,6967,6968,6969,6970,6971,6972,6973,6974,6978,6982,6983,6987,6988,6989,6994,6995,6996,6997,6998,6999,7000,7001,7002,7003,7004,7005,7006,7007,7008,7009,7010,7011,7012,7013,7014,7018,7019,7020,7026,7027,7031,7033,7034,7039,7040,7041,7042,7043,7044,7048,7049,7050,7056,7057,7061,7063,7067,7071,7075,7079,7175,7176,7177,7180,7183,7186,7189,7192,7197,7201,7204,7205,7210,7214,7219,7225,7231,7236,7240,7245,7249,7253,7294,7295,7296,7297,7298,7302,7303,7304,7305,7309,7313,7317,7321,7325,7329,7333,7337,7343,7344,7385,7399,7404,7430,7437,7440,7451,7456,7459,7462,7517,7523,7524,7527,7530,7533,7536,7539,7542,7545,7549,7552,7553,7554,7562,7570,7573,7578,7583,7588,7593,7597,7601,7602,7610,7611,7612,7613,7614,7622,7627,7632,7633,7634,7635,7660,7666,7671,7674,7678,7681,7685,7695,7698,7703,7706,7710,7714,7819,7833,7846,7850,7865,7876,7879,7890,7895,7899,7934,7935,7936,7948,7956,7964,7972,7980,8000,8003,8030,8035,8055,8058,8061,8068,8081,8090,8093,8113,8123,8127,8131,8144,8148,8152,8156,8162,8166,8183,8191,8195,8199,8203,8206,8210,8214,8218,8228,8235,8242,8246,8272,8282,8307,8316,8336,8346,8350,8360,8385,8395,8398,8402,8403,8404,8405,8409,8415,8421,8422,8435,8436,8437,8440,8443,8446,8449,8452,8455,8458,8461,8464,8467,8470,8473,8476,8479,8482,8485,8488,8491,8494,8497,8500,8501,8506,8507,8520,8530,8534,8539,8544,8548,8551,8555,8559,8562,8566,8569,8573,8578,8583,8586,8593,8597,8601,8610,8615,8620,8621,8625,8628,8632,8645,8650,8658,8662,8666,8683,8687,8692,8710,8717,8721,8751,8754,8757,8760,8763,8766,8769,8788,8794,8802,8809,8821,8829,8834,8839,8843,8854,8858,8866,8869,8874,8875,8876,8877,8881,8885,8889,8893,8928,8931,8935,8939,8973,8976,8980,8984,8993,8999,9002,9012,9016,9017,9024,9028,9035,9036,9037,9040,9045,9050,9051,9055,9070,9089,9093,9094,9106,9116,9117,9129,9134,9158,9161,9167,9170,9179,9187,9191,9194,9197,9200,9204,9207,9224,9228,9231,9246,9249,9257,9262,9269,9274,9275,9280,9281,9287,9293,9299,9331,9342,9359,9366,9370,9373,9386,9395,9399,9404,9408,9412,9416,9420,9424,9428,9432,9437,9440,9452,9457,9466,9469,9476,9477,9481,9490,9496,9500,9501,9505,9526,9532,9536,9540,9541,9559,9560,9561,9562,9563,9568,9571,9572,9578,9579,9591,9603,9610,9611,9616,9621,9622,9626,9640,9645,9651,9657,9663,9668,9674,9680,9681,9687,9702,9707,9716,9725,9728,9742,9747,9758,9762,9771,9780,9781,9788,9796,13298,13299,13300,13301,13302,13303,13304,13305,13306,13307,13308,13309,13310,13311,13312,13313,13314,13315,13316,13317,13318,13319,13320,13321,13322,13323,13324,13325,13326,13327,13328,13329,13330,13331,13332,13333,13334,13335,13336,13337,13338,13339,13340,13341,13342,13343,13344,13345,13346,13347,13348,13349,13350,13351,13352,13353,13354,13355,13356,13357,13358,13359,13360,13361,13362,13363,13364,13365,13366,13367,13368,13369,13370,13371,13372,13373,13374,13375,13376,13377,13378,13379,13380,13381,13382,13383,13384,13385,13386,13387,13388,13389,13390,13391,13392,13393,13394,13395,13396,13397,13398,13399,13400,13401,13402,13403,13404,13405,13406,13407,13408,13409,13410,13411,13412,13413,13414,13415,13416,13417,13418,13419,13420,13421,13422,13423,13424,13425,13426,13427,13428,13429,13430,13431,13432,13433,13434,13435,13436,13437,13438,13439,13440,13441,13442,13443,13444,13445,13446,13447,13448,13449,13450,13451,13452,13453,13454,13455,13456,13457,13458,13459,13460,13461,13462,13463,13464,13465,13466,13467,13468,13469,13470,13471,13472,13473,13474,13475,13476,13477,13478,13479,13480,13481,13482,13483,13484,13485,13486,13487,13488,13489,13490,13491,13492,13493,13494,13495,13496,13497,13498,13499,13500,13501,13502,13503,13504,13505,13506,13507,13508,13509,13510,13511,13512,13513,13514,13515,13516,13517,13518,13519,13520,13521,13522,13523,13524,13525,13526,13527,13528,13529,13530,13531,13532,13533,13534,13535,13536,13537,13538,13539,13540,13541,13542,13543,13544,13545,13546,13547,13548,13549,13550,13551,13552,13553,13554,13555,13556,13557,13558,13559,13560,13561,13562,13563,13564,13565,13566,13567,13568,13569,13570,13571,13572,13573,13574,13575,13576,13577,13578,13579,13580,13581,13582,13583,13584,13585,13586,13587,13588,13589,13590,13591,13592,13593,13594,13595,13596,13597,13598,13599,13600,13601,13602,13603,13604,13605,13606,13607,13608,13609,13610,13611,13612,13613,13614,13615,13616,13617,13618,13619,13620,13621,13622,13623,13624,13625,13626,13627,13628,13629,13630,13631,13632,13633,13634,13635,13636,13637,13638,13639,13640,13641,13642,13643,13644,13645,13646,13647,13648,13649,13650,13651,13652,13653,13654,13655,13656,13657,13658,13659,13660,13661,13662,13663,13664,13665,13666,13667,13668,13669,13670,13671,13672,13673", "endColumns": "55,48,54,55,59,60,64,54,49,49,52,57,47,68,47,70,71,71,72,66,48,53,36,50,59,46,55,48,57,53,60,55,50,59,55,62,48,55,55,49,58,54,61,46,53,55,51,54,53,53,53,48,57,53,56,55,46,52,55,59,62,58,61,49,53,53,47,56,52,55,53,55,62,61,55,59,52,60,78,80,71,78,79,75,77,68,75,76,70,72,75,77,68,75,76,63,70,71,50,52,54,66,52,51,49,57,64,47,50,66,65,57,68,57,68,69,72,73,67,66,69,65,72,59,75,59,59,74,67,65,67,59,58,56,65,61,56,67,72,69,61,60,67,61,69,68,55,58,61,61,66,56,60,60,60,60,55,55,55,55,57,57,57,57,56,56,56,56,58,58,57,82,82,72,53,68,55,80,80,70,9,52,57,57,57,45,59,53,69,69,64,65,64,67,68,67,9,52,58,57,51,49,51,45,49,55,46,57,57,61,62,61,58,59,64,65,64,61,61,61,61,61,61,65,66,65,62,63,62,67,60,61,61,62,63,62,63,77,58,65,79,60,52,57,50,44,63,58,61,73,70,65,73,68,70,72,70,67,72,75,69,77,67,65,60,68,63,65,67,65,62,67,70,64,72,62,80,63,65,69,69,69,69,66,56,57,58,59,58,58,58,58,58,58,58,58,58,58,59,60,61,60,60,60,60,60,60,59,60,60,59,60,60,60,60,60,60,60,60,60,60,60,67,68,69,68,68,68,68,68,68,68,68,68,68,59,60,61,60,60,60,60,60,60,60,60,60,60,61,62,63,62,62,62,62,62,62,62,62,62,62,60,61,62,61,61,61,61,61,61,61,61,61,61,56,85,79,89,94,91,91,89,82,92,86,96,90,100,86,102,88,98,91,99,83,93,87,97,82,90,93,98,101,97,99,86,99,85,95,87,80,90,95,92,92,90,84,93,88,97,92,101,87,103,90,99,92,100,84,94,88,98,84,91,94,99,102,98,102,88,100,86,96,87,95,91,99,89,97,84,88,88,92,86,90,65,75,68,78,72,79,79,76,67,77,75,70,80,72,82,74,84,72,80,80,73,83,69,77,69,79,77,71,81,69,76,79,84,87,83,86,73,77,77,70,80,90,82,95,97,106,64,65,52,75,65,86,75,75,64,54,53,78,77,72,64,62,65,70,70,69,61,68,65,59,66,66,55,50,52,51,53,70,62,58,61,58,72,66,69,59,62,74,71,95,70,55,70,56,56,65,63,70,56,52,62,51,57,66,68,65,58,82,58,56,66,69,73,61,68,69,98,96,98,85,85,80,74,88,90,83,58,45,65,56,66,56,81,64,65,122,83,120,64,61,97,73,82,88,63,78,73,61,95,64,58,55,55,59,106,46,59,60,63,60,59,57,42,48,51,50,51,48,48,64,65,59,60,55,58,48,47,57,56,101,56,74,47,50,61,64,51,73,62,62,67,49,61,59,56,59,48,67,105,101,68,70,55,48,99,70,109,88,90,81,97,55,100,109,98,62,105,76,111,126,111,126,69,113,130,96,67,117,102,117,60,73,66,104,121,73,66,109,98,72,96,121,117,117,60,121,116,67,105,101,79,70,95,66,73,73,85,87,89,77,76,99,70,120,120,63,124,73,123,123,66,108,127,111,78,77,100,70,121,121,64,125,111,105,67,98,103,62,65,83,112,112,117,77,71,135,135,84,139,137,137,141,81,108,110,127,127,131,129,129,133,61,95,66,116,120,96,81,86,86,130,130,134,76,76,110,113,73,108,111,101,95,103,66,93,71,109,105,72,90,101,102,94,106,104,121,121,125,58,57,123,123,127,117,117,121,85,96,133,133,79,137,131,131,135,74,75,102,73,112,80,56,60,58,59,57,60,57,49,48,66,58,58,48,70,83,69,70,79,68,62,67,65,67,64,65,76,77,105,105,95,128,88,126,65,68,85,65,82,97,95,95,97,108,94,88,61,59,64,56,80,53,56,96,109,60,114,120,94,91,92,101,55,58,48,91,48,53,53,53,53,53,54,109,109,107,109,109,109,109,107,105,103,103,103,94,94,92,92,103,105,103,103,92,92,92,92,107,105,105,105,96,94,94,94,105,105,105,105,97,95,95,97,64,103,57,63,60,61,59,64,61,67,57,62,62,66,74,72,65,51,52,51,56,83,94,84,80,79,76,78,76,73,73,70,79,71,74,64,60,59,74,73,72,69,71,69,72,63,69,45,68,51,84,82,56,65,66,65,80,74,55,52,60,57,49,48,48,48,61,51,44,80,50,53,52,53,50,48,65,50,60,60,61,49,40,76,58,58,58,60,55,55,66,60,64,54,64,68,67,77,68,59,70,73,64,71,69,66,83,68,66,69,62,66,67,82,78,89,76,67,66,77,56,56,67,65,55,59,58,53,49,49,47,61,50,72,79,79,63,66,70,57,60,65,58,66,59,59,62,67,60,66,77,69,48,56,68,60,87,87,87,87,86,86,86,86,57,73,69,55,70,64,61,74,72,89,65,65,60,63,61,57,70,82,58,70,65,64,60,58,70,65,64,82,75,74,80,59,68,69,68,54,55,55,60,57,55,53,54,61,56,93,68,100,50,69,62,55,57,58,53,85,83,69,68,69,114,120,66,66,74,66,58,53,53,53,52,51,73,136,139,48,49,49,47,55,57,61,54,57,70,63,58,61,65,65,42,43,44,42,50,46,44,50,50,50,50,47,65,61,62,71,56,53,54,57,54,58,55,68,68,68,69,62,62,62,62,64,64,64,64,62,63,63,63,50,77,77,70,71,72,71,65,65,67,67,65,66,73,62,56,59,64,66,64,56,60,57,103,109,108,103,77,64,66,65,69,46,51,49,56,12,149,130,183,177,237,188,168,97,114,84,78,11,64,88,156,156,152,153,58,186,95,89,95,89,165,122,122,169,105,114,114,101,105,116,114,81,172,167,147,158,154,172,116,116,167,111,113,171,175,157,132,111,145,151,131,142,121,177,135,95,135,94,166,92,91,186,11,11,11,181,316,181,181,189,231,189,176,161,156,109,182,136,203,183,183,159,157,183,226,202,170,219,221,154,199,183,102,189,140,164,170,199,203,201,164,204,198,198,196,90,148,149,83,148,144,151,140,165,160,77,300,165,154,101,11,163,185,222,124,10,10,10,10,61,10,10,10,10,10,10,10,10,10,84,120,98,10,96,116,86,122,100,10,98,118,92,106,10,106,10,120,10,10,99,104,118,10,146,118,10,132,10,10,111,10,124,10,120,10,120,132,146,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,136,90,10,10,10,10,108,118,10,10,10,10,10,94,10,10,10,10,10,10,121,10,10,10,10,10,10,10,10,10,10,10,10,10,72,86,84,98,111,105,122,131,122,129,123,132,130,124,116,119,131,127,113,117,112,120,10,10,10,10,10,10,10,119,119,107,109,111,107,109,10,10,151,10,100,10,10,10,10,10,10,10,10,10,10,10,10,10,144,121,134,10,10,10,10,10,10,10,10,10,10,91,10,10,94,10,93,88,10,88,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,105,107,105,111,113,111,113,115,113,107,109,107,10,10,10,10,107,109,107,113,10,10,115,10,10,10,10,10,10,10,121,10,10,121,10,10,10,10,10,10,10,10,75,111,89,10,10,107,10,107,75,111,89,101,107,107,99,107,84,103,86,77,113,91,10,10,109,10,109,83,10,97,107,93,129,107,121,135,107,119,133,121,127,141,125,139,125,117,131,97,109,10,111,117,10,115,10,10,95,10,109,123,137,109,121,10,123,129,10,127,10,10,10,10,10,10,67,83,103,10,10,10,10,10,10,10,10,115,10,10,10,10,10,10,10,10,10,10,10,113,119,115,93,10,98,117,100,10,10,10,10,10,10,10,10,10,75,10,10,10,10,10,10,10,10,10,10,10,10,111,10,10,10,10,10,10,10,10,10,83,103,10,10,10,10,10,10,10,10,10,137,10,113,137,111,123,10,10,10,144,92,91,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,99,89,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,145,137,135,10,10,10,115,10,121,111,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,111,10,123,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,139,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,87,107,107,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,123,10,10,10,102,129,10,10,10,139,10,10,10,10,129,10,10,145,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,78,10,104,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,158,10,10,10,10,157,10,10,10,10,10,141,10,105,129,137,123,10,10,99,10,113,10,10,10,123,10,10,133,10,10,10,10,10,10,10,10,10,103,10,10,10,10,10,10,10,10,10,10,10,10,119,10,10,94,92,62,81,92,92,86,97,90,90,87,83,95,103,99,105,102,100,103,105,98,105,101,106,108,110,130,119,115,117,98,106,115,118,127,88,94,76,88,90,92,73,96,94,97,98,103,95,101,102,99,102,84,100,97,89,94,86,105,101,93,90,93,75,91,88,102,110,82,85,94,96,95,87,100,100,102,105,97,96,94,97,102,99,102,104,117,115,94,92,84,95,93,91,101,106,82,103,104,99,100,104,99,100,98,101,93,106,101,102,92,95,101,102,95,101,102,96,102,97,103,104,96,107,113,114,107,113,114,101,104,107,109,115,116,94,96,98,104,105,98,104,105,99,105,100,106,118,98,104,101,101,99,102,94,103,84,103,103,97,103,105,97,104,97,112,93,88,88,82,90,82,97,89,95,88,93,87,95,84,107,100,100,97,105,90,98,96,97,95,92,109,97,94,109,91,99,98,86,103,104,98,106,106,98,108,91,110,110,110,103,114,115,126,119,96,98,91,98,91,98,85,93,102,95,102,95,102,96,97,102,92,89,100,82,90,84,91,102,94,95,92,93,78,106,90,98,92,102,103,100,100,103,93,103,103,112,105,105,107,116,100,107,99,102,104,106,95,78,89,83,91,72,96,81,84,84,96,92,94,98,96,90,90,91,94,106,107,101,96,96,92,86,83,96,96,92,86,90,98,98,94,88,80,98,103,96,104,96,83,98,103,96,104,96,97,100,105,98,106,98,98,90,88,88,81,92,90,110,100,99,111,112,97,107,93,99,88,91,110,109,94,115,125,125,118,127,124,124,117,126,108,108,112,122,122,115,124,96,107,121,115,115,108,87,100,88,100,86,87,96,91,105,99,75,76", "endOffsets": "258,493,952,1008,1068,1129,1194,1249,1399,1449,1502,1560,1608,1728,1776,1847,1919,1991,2064,2131,2180,2234,2271,2322,2382,2429,2485,2534,2592,2646,2707,2763,2814,2874,2930,2993,3042,3098,3154,3204,3263,3318,3380,3427,3481,3537,3589,3644,3698,3752,3806,3855,3913,3967,4024,4080,4127,4180,4236,4296,4359,4418,4480,4530,4584,4638,4686,4743,4796,4852,5412,5468,6308,6418,6474,6534,6587,6648,6727,6808,6880,6959,7039,7115,7193,7262,7338,7415,7486,7559,7635,7713,7782,7858,7935,7999,8070,8142,10543,10641,10696,10812,10865,10917,10967,11025,11090,11138,11189,16699,16765,16823,16892,16950,17019,17089,17162,17236,17304,17371,17441,17507,17580,17640,17716,17776,17836,17911,17979,18045,18113,18173,18232,18289,18355,18417,18474,18542,18615,18685,18747,18808,18876,18938,19008,19077,19133,19192,19254,19316,19383,19440,19501,19562,19623,19684,19740,19796,19852,19908,19966,20024,20082,20140,20197,20254,20311,20368,20427,20486,20544,20627,20710,20783,20837,20906,20962,21043,21124,21195,21324,21583,21641,21699,22440,22542,22602,22656,22726,22796,22861,22927,22992,23060,23129,23197,23327,23380,23439,23497,23549,23645,24033,24079,24129,24185,24232,24337,24395,24457,24520,24582,24641,24701,24766,24832,24897,24959,25021,25083,25145,25207,25269,25335,25402,25468,25531,25595,25658,25726,25787,25849,25911,25974,26038,26101,26165,26243,26302,26368,26448,26509,26562,26661,26712,27139,27496,27555,28878,31101,31172,31238,31312,31381,31452,31525,31596,31664,31737,31813,31883,31961,32029,32095,32156,32225,32289,32355,32423,32489,32552,32620,32691,32756,32829,32892,32973,33037,33103,33173,33243,33313,33383,33450,34269,34327,34386,34446,34505,34564,34623,34682,34741,34800,34859,34918,34977,35036,35096,35157,35219,35280,35341,35402,35463,35524,35585,35645,35706,35767,35827,35888,35949,36010,36071,36132,36193,36254,36315,36376,36437,36498,36566,36635,36705,36774,36843,36912,36981,37050,37119,37188,37257,37326,37395,37455,37516,37578,37639,37700,37761,37822,37883,37944,38005,38066,38127,38188,38250,38313,38377,38440,38503,38566,38629,38692,38755,38818,38881,38944,39007,39068,39130,39193,39255,39317,39379,39441,39503,39565,39627,39689,39751,39813,39870,39956,40036,40126,40221,40313,40405,40495,40578,40671,40758,40855,40946,41047,41134,41237,41326,41425,41517,41617,41701,41795,41883,41981,42064,42155,42249,42348,42450,42548,42648,42735,42835,42921,43017,43105,43186,43277,43373,43466,43559,43650,43735,43829,43918,44016,44109,44211,44299,44403,44494,44594,44687,44788,44873,44968,45057,45156,45241,45333,45428,45528,45631,45730,45833,45922,46023,46110,46207,46295,46391,46483,46583,46673,46771,46856,46945,47034,47127,47214,47305,48043,48119,48188,48267,48340,48420,48500,48577,48645,48723,48799,48870,48951,49024,49107,49182,49267,49340,49421,49502,49576,49660,49730,49808,49878,49958,50036,50108,50190,50260,50337,50417,50502,50590,50674,50761,50835,50913,50991,51062,51143,51234,51317,51413,51511,51618,51683,51749,51802,51878,51944,52031,52107,52183,64911,65136,65730,65809,65887,65960,66025,66088,66154,66225,66296,66366,66428,66497,66563,66623,66690,66757,66813,66864,66917,66969,67023,67094,67157,67216,67278,67337,67410,67477,67547,67607,67670,67745,67817,67913,67984,68040,68111,68168,68225,68291,68355,68426,68483,68536,68599,68651,68709,68776,70015,70081,70140,70223,70282,70339,70406,70476,70550,70612,70681,70751,70850,70947,71046,71132,71218,71299,71374,71463,71554,71638,71697,71743,71809,71866,71933,71990,72072,72137,72203,72326,72410,72531,72596,72658,72756,72830,72913,73002,73066,73145,73219,73281,73377,73442,73501,73557,73613,73673,73780,73827,73887,73948,74012,74073,74133,74191,74234,74283,74335,74386,74438,74487,74536,74601,74667,74727,74788,74844,74903,74952,75000,75058,75115,75217,75274,75349,75397,75448,75510,75575,75627,75701,75764,75827,75895,75945,76007,76067,76124,76184,76233,76301,76407,76509,76578,76649,76705,76754,76854,76925,77035,77124,77215,77297,77395,77451,77552,77662,77761,77824,77930,78007,78119,78246,78358,78485,78555,78669,78800,78897,78965,79083,79186,79304,79365,79439,79506,79611,79733,79807,79874,79984,80083,80156,80253,80375,80493,80611,80672,80794,80911,80979,81085,81187,81267,81338,81434,81501,81575,81649,81735,81823,81913,81991,82068,82168,82239,82360,82481,82545,82670,82744,82868,82992,83059,83168,83296,83408,83487,83565,83666,83737,83859,83981,84046,84172,84284,84390,84458,84557,84661,84724,84790,84874,84987,85100,85218,85296,85368,85504,85640,85725,85865,86003,86141,86283,86365,86474,86585,86713,86841,86973,87103,87233,87367,87429,87525,87592,87709,87830,87927,88009,88096,88183,88314,88445,88580,88657,88734,88845,88959,89033,89142,89254,89356,89452,89556,89623,89717,89789,89899,90005,90078,90169,90271,90374,90469,90576,90681,90803,90925,91051,91110,91168,91292,91416,91544,91662,91780,91902,91988,92085,92219,92353,92433,92571,92703,92835,92971,93046,93122,93225,93299,93412,93493,93550,93611,93670,93730,93788,93849,93907,93957,94006,94073,94132,94191,94240,94311,94395,94465,94536,94616,94685,94748,94816,94882,94950,95015,95081,95158,95236,95342,95448,95544,95673,95762,95889,95955,96024,96110,96176,96259,96357,96453,96549,96647,96756,96851,96940,97002,97062,97127,97184,97265,97319,97376,97473,97583,97644,97759,97880,97975,98067,98160,98262,98318,98377,98426,98518,98567,98621,98675,98729,98783,98837,98892,99002,99112,99220,99330,99440,99550,99660,99768,99874,99978,100082,100186,100281,100376,100469,100562,100666,100772,100876,100980,101073,101166,101259,101352,101460,101566,101672,101778,101875,101970,102065,102160,102266,102372,102478,102584,102682,102778,102874,102972,103037,103141,103199,103263,103324,103386,103446,103511,103573,103641,103699,103762,103825,103892,103967,104040,104106,104158,104211,104263,104320,104404,104499,104584,104665,104745,104822,104901,104978,105052,105126,105197,105277,105349,105424,105489,105550,105610,105685,105759,105832,105902,105974,106044,106117,106181,106251,106297,106366,106418,106503,106586,106643,106709,106776,106842,106923,106998,107054,107107,107168,107226,107276,107325,107374,107423,107485,107537,107582,107663,107714,107768,107821,107875,107926,107975,108041,108092,108153,108214,108276,108326,108367,108444,108503,108562,108621,108682,108738,108794,108861,108922,108987,109042,109107,109176,109244,109322,109391,109451,109522,109596,109661,109733,109803,109870,109954,110023,110090,110160,110223,110290,110358,110441,110520,110610,110687,110755,110822,110900,110957,111014,111082,111148,111204,111264,111323,111377,111427,111477,111525,111587,111638,111711,111791,111871,111935,112002,112073,112131,112192,112258,112317,112384,112444,112504,112567,112635,112696,112763,112841,112911,112960,113017,113086,113147,113235,113323,113411,113499,113586,113673,113760,113847,113905,113979,114049,114105,114176,114241,114303,114378,114451,114541,114607,114673,114734,114798,114860,114918,114989,115072,115131,115202,115268,115333,115394,115453,115524,115590,115655,115738,115814,115889,115970,116030,116099,116169,116238,116293,116349,116405,116466,116524,116580,116634,116689,116751,116808,116902,116971,117072,117123,117193,117256,117312,117370,117429,117483,117569,117653,117723,117792,117862,117977,118098,118165,118232,118307,118374,118433,118487,118541,118595,118648,118700,118774,120775,120915,124063,124113,124163,124346,124402,124460,124522,124577,124635,124759,124823,124882,124944,125010,125076,125570,125716,125761,125804,126790,126837,126882,126933,126984,127035,127086,127467,127983,128045,128225,128297,128354,128408,128463,128521,128576,128635,128691,128760,128829,128898,128968,129031,129094,129157,129220,129285,129350,129415,129480,129543,129607,129671,129735,129786,129864,129942,130013,130085,130158,130230,130296,130362,130430,130498,130564,130631,130705,130768,130825,130885,130950,131017,131082,131139,131200,131258,131362,131472,131581,131685,131763,131828,131895,131961,132031,132078,132130,132180,132363,132753,136289,136753,136937,137115,137353,137542,137711,138611,138726,138811,138927,141154,141219,141365,141522,141679,143138,143480,143687,144082,144178,144268,144364,144454,144620,144743,144866,145036,145142,145257,145372,145474,145580,145697,145812,150261,150434,150602,150750,150909,151064,151237,151354,151471,151639,151751,151865,152037,152213,152371,152504,152616,152762,152914,153046,153189,153311,153861,153997,154093,154229,154324,154491,154584,154676,154863,155019,155197,155361,155543,155860,156042,156224,156414,156646,156836,157013,157175,157332,157442,157625,157762,157966,158150,158334,158494,158652,158836,159063,159266,159437,159657,159879,160034,160234,160418,160521,160711,160852,161017,161188,161388,161592,161794,161959,162164,162363,162562,162759,162850,162999,163149,163233,163382,163527,163679,163820,163986,164147,165214,165515,165681,165836,165938,170520,170684,170870,171510,171635,174838,175110,175388,175633,175695,175980,178974,179430,179939,190715,191229,191666,192100,192543,196525,196646,196745,197150,197247,197364,197451,197574,197675,198081,198180,198299,198392,198499,198842,198949,199194,199315,199724,199972,200072,200177,200296,200805,200952,201071,201322,201455,201870,202124,202236,207586,207711,208028,208149,208377,208498,208631,208778,229409,229901,250281,250705,271381,271875,292300,292726,297567,302984,307075,312506,317248,322625,326609,330601,335992,336539,336972,337728,337958,338201,339334,340263,388408,388992,389465,390895,391639,392832,393886,394364,394657,395040,396555,397320,398463,398904,399345,399941,400215,400626,401642,401820,402573,402710,402801,404995,405261,405583,405793,405902,406021,406205,407323,407793,408544,411127,411222,413772,414000,414256,414515,415091,415445,415567,415706,415998,416258,417186,417472,417875,418277,418620,418832,419033,419246,419535,419820,430951,431038,431123,431222,443760,443866,443989,444121,444244,444374,444498,444631,444762,444887,445004,445124,445256,445384,445498,445616,445729,445850,446038,446225,446406,446589,446773,446938,447120,447240,447360,447468,447578,447690,447798,447908,448073,448239,448391,448556,448657,448777,448948,449109,449272,449433,449600,449719,449836,450016,450198,450379,450562,450717,450862,450984,451119,451282,451475,451601,451753,451895,452065,452221,452393,452684,459351,459443,459616,459778,459873,460042,460136,460225,460468,460557,460850,461266,461686,462107,462533,462950,463366,463783,464201,464615,465085,465558,466030,466441,466912,467384,467574,467780,467886,467994,468100,468212,468326,468438,468552,468668,468782,468890,469000,469108,469370,469749,470153,470300,470408,470518,470626,470740,471149,471563,471679,472097,472338,472768,473203,473613,474035,474445,474567,474976,475392,475514,475732,475916,478620,478964,479044,479400,479550,479694,479770,479882,479972,480234,480499,480607,480759,480867,480943,481055,481145,481247,481355,481463,481563,481671,481756,481860,481947,482025,482139,482231,482495,482762,482872,483025,483135,483219,483608,483706,483814,483908,484038,484146,484268,484404,484512,484632,484766,484888,485016,485158,485284,485424,485550,485668,485800,485898,486008,486308,486420,486538,487002,487118,487421,487547,487643,488044,488154,488278,488416,488526,488648,488960,489084,489214,489690,489818,490133,490271,490433,490649,490805,491009,496531,496615,496719,496922,497111,497312,497505,497710,498023,498235,498401,498517,498763,498979,499292,499718,500180,500417,500569,500829,500973,501115,504347,504461,504581,504697,504791,505112,505211,505329,505430,505709,505994,506273,506555,506808,507067,507320,507576,508000,508076,511326,512681,513125,514979,515554,515762,516772,517152,517318,517459,522479,522905,523017,523152,523305,523502,523673,523856,524031,524218,524490,524648,524732,524836,525323,525879,526037,526256,526487,526710,526945,527167,527433,527571,528170,528284,528422,528534,528658,529229,529724,530270,530415,530508,530600,532527,533097,533395,533584,533790,533983,534193,535077,535222,535614,535772,535989,536250,544477,545352,545972,546169,547117,547882,548005,548778,548999,549199,551176,551276,551366,552052,552805,553570,554333,555108,556321,556486,558099,558420,559483,559693,559863,560433,561328,561961,562127,563613,564229,564465,564686,565644,565909,566174,566421,566835,567071,568356,568805,568992,569241,569483,569659,569900,570133,570358,570953,571428,571952,572213,573564,574039,575265,575735,576783,577235,577479,577936,579181,579664,579814,580158,580304,580442,580578,580866,581370,581879,581995,582897,583019,583131,583308,583574,583844,584110,584378,584634,584894,585150,585408,585660,585916,586168,586422,586654,586890,587142,587398,587650,587904,588136,588370,588482,588907,589031,590123,590938,591134,591458,591847,592199,592440,592654,592953,593145,593460,593667,594013,594313,594714,594933,595346,595583,595953,596677,597032,597301,597441,597695,597839,598116,599108,599517,600149,600495,600863,601937,602300,602700,604208,604793,605111,607646,607840,608058,608284,608496,608695,608902,610106,610401,610958,611348,611980,612457,612702,613053,613299,614059,614323,614746,614937,615316,615404,615512,615620,615933,616258,616577,616908,619611,619799,620060,620309,622893,623085,623350,623603,624135,624543,624742,625326,625561,625685,626097,626311,626713,626816,626946,627121,627373,627569,627709,627903,628914,629983,630271,630401,631178,631835,631981,632687,632925,634465,634615,635032,635197,635883,636353,636549,636640,636724,636868,637102,637269,638197,638483,638643,639258,639417,639745,639972,640484,640846,640925,641264,641369,641734,642105,642466,644340,644969,646045,646469,646722,646874,647922,648659,648862,649108,649355,649573,649815,650136,650400,650705,650928,651239,651428,652143,652412,652906,653132,653572,653731,654015,654760,655125,655430,655588,655826,657145,657543,657771,657991,658133,659423,659529,659659,659797,659921,660209,660378,660478,660763,660877,661760,662515,662954,663078,663324,663517,663651,663842,664621,664839,665130,665409,665726,665948,666243,666526,666630,666971,667787,668103,668664,669170,669375,670161,670566,671227,671416,671967,672533,672653,673055,673589,797959,798052,798115,798197,798290,798383,798470,798568,798659,798750,798838,798922,799018,799122,799222,799328,799431,799532,799636,799742,799841,799947,800049,800156,800265,800376,800507,800627,800743,800861,800960,801067,801183,801302,801430,801519,801614,801691,801780,801871,801964,802038,802135,802230,802328,802427,802531,802627,802729,802832,802932,803035,803120,803221,803319,803409,803504,803591,803697,803799,803893,803984,804078,804154,804246,804335,804438,804549,804632,804718,804813,804910,805006,805094,805195,805296,805399,805505,805603,805700,805795,805893,805996,806096,806199,806304,806422,806538,806633,806726,806811,806907,807001,807093,807195,807302,807385,807489,807594,807694,807795,807900,808000,808101,808200,808302,808396,808503,808605,808708,808801,808897,808999,809102,809198,809300,809403,809500,809603,809701,809805,809910,810007,810115,810229,810344,810452,810566,810681,810783,810888,810996,811106,811222,811339,811434,811531,811630,811735,811841,811940,812045,812151,812251,812357,812458,812565,812684,812783,812888,812990,813092,813192,813295,813390,813494,813579,813683,813787,813885,813989,814095,814193,814298,814396,814509,814603,814692,814781,814864,814955,815038,815136,815226,815322,815411,815505,815593,815689,815774,815882,815983,816084,816182,816288,816379,816478,816575,816673,816769,816862,816972,817070,817165,817275,817367,817467,817566,817653,817757,817862,817961,818068,818175,818274,818383,818475,818586,818697,818808,818912,819027,819143,819270,819390,819487,819586,819678,819777,819869,819968,820054,820148,820251,820347,820450,820546,820649,820746,820844,820947,821040,821130,821231,821314,821405,821490,821582,821685,821780,821876,821969,822063,822142,822249,822340,822439,822532,822635,822739,822840,822941,823045,823139,823243,823347,823460,823566,823672,823780,823897,823998,824106,824206,824309,824414,824521,824617,824696,824786,824870,824962,825035,825132,825214,825299,825384,825481,825574,825669,825768,825865,825956,826047,826139,826234,826341,826449,826551,826648,826745,826838,826925,827009,827106,827203,827296,827383,827474,827573,827672,827767,827856,827937,828036,828140,828237,828342,828439,828523,828622,828726,828823,828928,829025,829123,829224,829330,829429,829536,829635,829734,829825,829914,830003,830085,830178,830269,830380,830481,830581,830693,830806,830904,831012,831106,831206,831295,831387,831498,831608,831703,831819,831945,832071,832190,832318,832443,832568,832686,832813,832922,833031,833144,833267,833390,833506,833631,833728,833836,833958,834074,834190,834299,834387,834488,834577,834678,834765,834853,834950,835042,835148,835248,835324,835401"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\5619827a7b7a666ff443af1afbf85287\\transformed\\preference-1.2.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "554,882,1787,1788,1789,1790,1791,1792,1793,1879,1880,1881,1996,1997,2092,2124,2343,2364,2462,2490,2491,5897,6196,6199,6205,6211,6214,6220,6224,6227,6234,6240,6243,6249,6254,6259,6266,6268,6274,6280,6288,6293,6300,6305,6311,6315,6322,6326,6332,6338,6341,6345,6346", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "28440,52548,119757,119821,119876,119944,120011,120076,120133,125191,125239,125287,132185,132248,139337,141224,164338,166095,172065,173637,173687,411227,432542,432647,432892,433230,433376,433716,433928,434091,434498,434836,434959,435298,435537,435794,436165,436225,436563,436849,437298,437590,437978,438283,438627,438872,439202,439409,439677,439950,440094,440295,440342", "endLines": "554,882,1787,1788,1789,1790,1791,1792,1793,1879,1880,1881,1996,1997,2092,2124,2343,2366,2462,2490,2491,5913,6198,6204,6210,6213,6219,6223,6226,6233,6239,6242,6248,6253,6258,6265,6267,6273,6279,6287,6292,6299,6304,6310,6314,6321,6325,6331,6337,6340,6344,6345,6346", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55", "endOffsets": "28508,52612,119816,119871,119939,120006,120071,120128,120185,125234,125282,125343,132243,132306,139370,141276,164377,166230,172199,173682,173730,412660,432642,432887,433225,433371,433711,433923,434086,434493,434831,434954,435293,435532,435789,436160,436220,436558,436844,437293,437585,437973,438278,438622,438867,439197,439404,439672,439945,440089,440290,440337,440393"}}, {"source": "D:\\development\\Codes\\MyApplication\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "571,576,630,637,877,878,879,880,881,883,884,887,894,895,896,897,900,901,906,907,908,909,910,911,912,913,914,919,920,921,924,925,943,998", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "29605,29969,33735,34176,52320,52368,52411,52456,52503,52617,52659,52860,53343,53393,53440,53487,53649,53693,54039,54091,54139,54191,54237,54286,54337,54383,54427,54755,54800,54845,55023,55067,56042,59335", "endColumns": "44,41,39,40,47,42,44,46,44,41,47,47,49,46,46,46,43,49,51,47,51,45,48,50,45,43,41,44,44,56,43,41,54,66", "endOffsets": "29645,30006,33770,34212,52363,52406,52451,52498,52543,52654,52702,52903,53388,53435,53482,53529,53688,53738,54086,54134,54186,54232,54281,54332,54378,54422,54464,54795,54840,54897,55062,55104,56092,59397"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\cd688a78b2d926833405c834f3e4e352\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2048", "startColumns": "4", "startOffsets": "135919", "endColumns": "82", "endOffsets": "135997"}}, {"source": "D:\\development\\Android\\gradle\\caches\\8.11.1\\transforms\\75ac900f6bf42e90e2b331a4ef619a90\\transformed\\work-runtime-2.9.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1"}, "to": {"startLines": "556,557,558,561", "startColumns": "4,4,4,4", "startOffsets": "28571,28636,28706,28883", "endColumns": "64,69,63,60", "endOffsets": "28631,28701,28765,28939"}}]}]}