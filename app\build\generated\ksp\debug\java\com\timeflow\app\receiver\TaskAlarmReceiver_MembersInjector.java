package com.timeflow.app.receiver;

import com.timeflow.app.utils.TimeFlowNotificationManager;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class TaskAlarmReceiver_MembersInjector implements MembersInjector<TaskAlarmReceiver> {
  private final Provider<TimeFlowNotificationManager> notificationManagerProvider;

  public TaskAlarmReceiver_MembersInjector(
      Provider<TimeFlowNotificationManager> notificationManagerProvider) {
    this.notificationManagerProvider = notificationManagerProvider;
  }

  public static MembersInjector<TaskAlarmReceiver> create(
      Provider<TimeFlowNotificationManager> notificationManagerProvider) {
    return new TaskAlarmReceiver_MembersInjector(notificationManagerProvider);
  }

  @Override
  public void injectMembers(TaskAlarmReceiver instance) {
    injectNotificationManager(instance, notificationManagerProvider.get());
  }

  @InjectedFieldSignature("com.timeflow.app.receiver.TaskAlarmReceiver.notificationManager")
  public static void injectNotificationManager(TaskAlarmReceiver instance,
      TimeFlowNotificationManager notificationManager) {
    instance.notificationManager = notificationManager;
  }
}
