package com.timeflow.app.util;

import com.timeflow.app.data.repository.GoalRepository;
import com.timeflow.app.data.repository.HabitRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class HabitGoalManager_Factory implements Factory<HabitGoalManager> {
  private final Provider<HabitRepository> habitRepositoryProvider;

  private final Provider<GoalRepository> goalRepositoryProvider;

  public HabitGoalManager_Factory(Provider<HabitRepository> habitRepositoryProvider,
      Provider<GoalRepository> goalRepositoryProvider) {
    this.habitRepositoryProvider = habitRepositoryProvider;
    this.goalRepositoryProvider = goalRepositoryProvider;
  }

  @Override
  public HabitGoalManager get() {
    return newInstance(habitRepositoryProvider.get(), goalRepositoryProvider.get());
  }

  public static HabitGoalManager_Factory create(Provider<HabitRepository> habitRepositoryProvider,
      Provider<GoalRepository> goalRepositoryProvider) {
    return new HabitGoalManager_Factory(habitRepositoryProvider, goalRepositoryProvider);
  }

  public static HabitGoalManager newInstance(HabitRepository habitRepository,
      GoalRepository goalRepository) {
    return new HabitGoalManager(habitRepository, goalRepository);
  }
}
