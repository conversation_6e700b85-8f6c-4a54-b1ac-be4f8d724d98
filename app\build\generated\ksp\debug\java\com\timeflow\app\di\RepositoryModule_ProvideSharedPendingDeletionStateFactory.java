package com.timeflow.app.di;

import android.content.Context;
import com.timeflow.app.data.repository.SharedPendingDeletionState;
import com.timeflow.app.data.repository.TaskRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class RepositoryModule_ProvideSharedPendingDeletionStateFactory implements Factory<SharedPendingDeletionState> {
  private final Provider<Context> contextProvider;

  private final Provider<TaskRepository> taskRepositoryProvider;

  public RepositoryModule_ProvideSharedPendingDeletionStateFactory(
      Provider<Context> contextProvider, Provider<TaskRepository> taskRepositoryProvider) {
    this.contextProvider = contextProvider;
    this.taskRepositoryProvider = taskRepositoryProvider;
  }

  @Override
  public SharedPendingDeletionState get() {
    return provideSharedPendingDeletionState(contextProvider.get(), taskRepositoryProvider.get());
  }

  public static RepositoryModule_ProvideSharedPendingDeletionStateFactory create(
      Provider<Context> contextProvider, Provider<TaskRepository> taskRepositoryProvider) {
    return new RepositoryModule_ProvideSharedPendingDeletionStateFactory(contextProvider, taskRepositoryProvider);
  }

  public static SharedPendingDeletionState provideSharedPendingDeletionState(Context context,
      TaskRepository taskRepository) {
    return Preconditions.checkNotNullFromProvides(RepositoryModule.INSTANCE.provideSharedPendingDeletionState(context, taskRepository));
  }
}
