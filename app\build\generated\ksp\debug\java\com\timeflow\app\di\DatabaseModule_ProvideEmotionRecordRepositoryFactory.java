package com.timeflow.app.di;

import com.timeflow.app.data.dao.EmotionRecordDao;
import com.timeflow.app.data.repository.EmotionRecordRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DatabaseModule_ProvideEmotionRecordRepositoryFactory implements Factory<EmotionRecordRepository> {
  private final Provider<EmotionRecordDao> emotionRecordDaoProvider;

  public DatabaseModule_ProvideEmotionRecordRepositoryFactory(
      Provider<EmotionRecordDao> emotionRecordDaoProvider) {
    this.emotionRecordDaoProvider = emotionRecordDaoProvider;
  }

  @Override
  public EmotionRecordRepository get() {
    return provideEmotionRecordRepository(emotionRecordDaoProvider.get());
  }

  public static DatabaseModule_ProvideEmotionRecordRepositoryFactory create(
      Provider<EmotionRecordDao> emotionRecordDaoProvider) {
    return new DatabaseModule_ProvideEmotionRecordRepositoryFactory(emotionRecordDaoProvider);
  }

  public static EmotionRecordRepository provideEmotionRecordRepository(
      EmotionRecordDao emotionRecordDao) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.provideEmotionRecordRepository(emotionRecordDao));
  }
}
