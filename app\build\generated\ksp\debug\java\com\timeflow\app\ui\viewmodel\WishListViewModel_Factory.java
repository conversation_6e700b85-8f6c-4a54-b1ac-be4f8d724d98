package com.timeflow.app.ui.viewmodel;

import com.timeflow.app.data.repository.GoalRepository;
import com.timeflow.app.data.repository.WishRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class WishListViewModel_Factory implements Factory<WishListViewModel> {
  private final Provider<WishRepository> wishRepositoryProvider;

  private final Provider<GoalRepository> goalRepositoryProvider;

  public WishListViewModel_Factory(Provider<WishRepository> wishRepositoryProvider,
      Provider<GoalRepository> goalRepositoryProvider) {
    this.wishRepositoryProvider = wishRepositoryProvider;
    this.goalRepositoryProvider = goalRepositoryProvider;
  }

  @Override
  public WishListViewModel get() {
    return newInstance(wishRepositoryProvider.get(), goalRepositoryProvider.get());
  }

  public static WishListViewModel_Factory create(Provider<WishRepository> wishRepositoryProvider,
      Provider<GoalRepository> goalRepositoryProvider) {
    return new WishListViewModel_Factory(wishRepositoryProvider, goalRepositoryProvider);
  }

  public static WishListViewModel newInstance(WishRepository wishRepository,
      GoalRepository goalRepository) {
    return new WishListViewModel(wishRepository, goalRepository);
  }
}
