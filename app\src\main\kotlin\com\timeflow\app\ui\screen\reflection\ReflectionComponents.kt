package com.timeflow.app.ui.screen.reflection

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material.icons.outlined.Star
import androidx.compose.material.icons.outlined.Tag
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.unit.sp
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.res.stringResource
import coil.compose.AsyncImage
import coil.request.ImageRequest
import java.time.Instant
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import androidx.compose.ui.graphics.Color
import androidx.compose.foundation.border
import androidx.compose.foundation.BorderStroke
import com.timeflow.app.data.model.MoodType
import com.timeflow.app.data.model.Reflection
import com.timeflow.app.data.model.TimeFilter
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import com.timeflow.app.ui.screen.reflection.model.TimeDimension
import com.timeflow.app.ui.screen.reflection.model.DayViewData
import com.timeflow.app.ui.screen.reflection.model.MonthViewData
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.material.icons.automirrored.filled.ArrowForward
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.ui.unit.dp
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import kotlinx.coroutines.delay
import java.io.File
import com.timeflow.app.R

/**
 * 格式化日期时间
 */
fun formatDateTime(date: java.time.Instant?): String {
    if (date == null) return ""
    val localDateTime = date.atZone(ZoneId.systemDefault()).toLocalDateTime()
    val formatter = DateTimeFormatter.ofPattern("yyyy.MM.dd HH:mm")
    return localDateTime.format(formatter)
}

/**
 * 格式化日期
 */
fun formatDate(date: java.time.Instant?): String {
    if (date == null) return ""
    val localDate = date.atZone(ZoneId.systemDefault()).toLocalDate()
    val formatter = DateTimeFormatter.ofPattern("M月dd日")
    return formatter.format(localDate)
}

/**
 * 获取星期
 */
fun getDayOfWeek(date: java.time.Instant?): String {
    if (date == null) return ""
    val localDate = date.atZone(ZoneId.systemDefault()).toLocalDate()
    val dayOfWeek = localDate.dayOfWeek.value
    return when(dayOfWeek) {
        1 -> "周一"
        2 -> "周二"
        3 -> "周三"
        4 -> "周四"
        5 -> "周五"
        6 -> "周六"
        7 -> "周日"
        else -> ""
    }
}

/**
 * 格式化时间
 */
fun formatTime(date: java.time.Instant?): String {
    if (date == null) return ""
    val localTime = date.atZone(ZoneId.systemDefault()).toLocalTime()
    val formatter = DateTimeFormatter.ofPattern("HH:mm:ss")
    return formatter.format(localTime)
}

/**
 * 获取年份
 */
fun getYear(date: java.time.Instant?): String {
    if (date == null) return ""
    return date.atZone(ZoneId.systemDefault()).year.toString()
}

/**
 * 获取类型对应的图标
 */
fun getIconForType(type: ReflectionType): Int {
    return type.iconRes
}

/**
 * 公共自定义芯片组件
 */
@Composable
fun Chip(
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    label: @Composable () -> Unit
) {
    SuggestionChip(
        onClick = onClick,
        label = label,
        modifier = modifier,
        enabled = enabled
    )
}

/**
 * 公共自定义轮廓芯片组件
 */
@Composable
fun OutlinedSuggestionChip(
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    label: @Composable () -> Unit
) {
    SuggestionChip(
        onClick = onClick,
        label = label,
        modifier = modifier,
        enabled = enabled,
        colors = SuggestionChipDefaults.suggestionChipColors(
            containerColor = Color.Transparent,
            labelColor = MaterialTheme.colorScheme.onSurface
        ),
        border = BorderStroke(
            width = 1.dp,
            color = MaterialTheme.colorScheme.outline
        )
    )
}

/**
 * 智能搜索面板组件
 */
@Composable
fun SmartSearchPanel(
    query: String,
    recentSearches: List<String>,
    popularTags: List<String>,
    onQueryChange: (String) -> Unit,
    onSearch: () -> Unit,
    onFilterToggle: () -> Unit
) {
    // 获取键盘控制器以隐藏键盘
    val keyboardController = LocalSoftwareKeyboardController.current
    // 获取焦点管理器以清除焦点
    val focusManager = LocalFocusManager.current
    
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 8.dp)
    ) {
            // 搜索输入框
            Row(
            modifier = Modifier
                .fillMaxWidth()
                .height(56.dp)
                .clip(RoundedCornerShape(28.dp))
                .background(MaterialTheme.colorScheme.surfaceVariant)
                .padding(horizontal = 16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = Icons.Default.Search,
                contentDescription = "搜索",
                tint = MaterialTheme.colorScheme.onSurfaceVariant
            )
            
            Spacer(modifier = Modifier.width(8.dp))
            
            TextField(
                    value = query,
                    onValueChange = onQueryChange,
                    placeholder = { Text("搜索感想、标签或关键词") },
                    modifier = Modifier
                        .weight(1f)
                        .fillMaxHeight(),
                    keyboardOptions = KeyboardOptions(imeAction = ImeAction.Search),
                    keyboardActions = KeyboardActions(
                        onSearch = { 
                            // 执行搜索，隐藏键盘并清除焦点
                            onSearch()
                            keyboardController?.hide()
                            focusManager.clearFocus()
                        }
                    ),
                    colors = TextFieldDefaults.colors(
                    focusedContainerColor = Color.Transparent,
                    unfocusedContainerColor = Color.Transparent,
                    disabledContainerColor = Color.Transparent,
                    focusedIndicatorColor = Color.Transparent,
                    unfocusedIndicatorColor = Color.Transparent
                    ),
                    singleLine = true
                )
                
            if (query.isNotEmpty()) {
                IconButton(onClick = { onQueryChange("") }) {
                    Icon(
                        imageVector = Icons.Default.Close,
                        contentDescription = "清除",
                        tint = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
            
            IconButton(onClick = onFilterToggle) {
                Icon(
                    imageVector = Icons.Default.FilterList,
                    contentDescription = "筛选",
                    tint = MaterialTheme.colorScheme.primary
            )
            }
        }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 最近搜索
            if (recentSearches.isNotEmpty()) {
                Text(
                text = "最近搜索",
                style = MaterialTheme.typography.titleSmall,
                fontWeight = FontWeight.Bold
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            LazyRow(
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(recentSearches) { recentSearch ->
                    Chip(
                    onClick = { 
                            onQueryChange(recentSearch)
                        onSearch()
                    },
                        label = { Text(recentSearch) }
                )
            }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
        }
            
        // 热门标签
        if (popularTags.isNotEmpty()) {
            Text(
                text = "热门标签",
                style = MaterialTheme.typography.titleSmall,
                fontWeight = FontWeight.Bold
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            LazyRow(
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(popularTags) { tag ->
                    FilterChip(
                        selected = false,
                        onClick = { 
                            onQueryChange("#$tag")
                            onSearch()
                        },
                        label = { Text("#$tag") }
                    )
                }
            }
        }
        
        Spacer(modifier = Modifier.height(8.dp))
        
        // 搜索按钮
        Button(
            onClick = onSearch,
            modifier = Modifier.fillMaxWidth(),
            enabled = query.isNotEmpty()
        ) {
            Text("搜索")
        }
    }
}

/**
 * 多维检索系统组件
 */
@Composable
fun MultiDimensionalFilterPanel(
    timeFilter: TimeFilter,
    moodFilters: List<String>,
    typeFilters: List<String>,
    advancedFilters: Map<String, Boolean>,
    onTimeFilterChanged: (TimeFilter) -> Unit,
    onMoodFilterChanged: (String, Boolean) -> Unit,
    onTypeFilterChanged: (String, Boolean) -> Unit,
    onAdvancedFilterChanged: (String, Boolean) -> Unit,
    startDate: String,
    endDate: String,
    onStartDateClick: () -> Unit,
    onEndDateClick: () -> Unit,
    onClearDateRange: () -> Unit,
    onApplyDateRange: () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp)
    ) {
        // 时间筛选
        Text(
            text = "时间范围",
            style = MaterialTheme.typography.titleSmall,
            fontWeight = FontWeight.Bold
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        // 时间筛选选项
            Row(
                modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            FilterChip(
                selected = timeFilter == TimeFilter.TODAY,
                onClick = { onTimeFilterChanged(TimeFilter.TODAY) },
                label = { Text("今天") }
            )
            
            FilterChip(
                selected = timeFilter == TimeFilter.YESTERDAY,
                onClick = { onTimeFilterChanged(TimeFilter.YESTERDAY) },
                label = { Text("昨天") }
            )
            
            FilterChip(
                selected = timeFilter == TimeFilter.THIS_WEEK,
                onClick = { onTimeFilterChanged(TimeFilter.THIS_WEEK) },
                label = { Text("本周") }
            )
            
            FilterChip(
                selected = timeFilter == TimeFilter.THIS_MONTH,
                onClick = { onTimeFilterChanged(TimeFilter.THIS_MONTH) },
                label = { Text("本月") }
            )
            
            FilterChip(
                selected = timeFilter == TimeFilter.CUSTOM_RANGE,
                onClick = { onTimeFilterChanged(TimeFilter.CUSTOM_RANGE) },
                label = { Text("自定义") }
                )
            }
            
        // 自定义日期范围
        if (timeFilter == TimeFilter.CUSTOM_RANGE) {
            Spacer(modifier = Modifier.height(8.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 开始日期选择
                OutlinedCard(
                    modifier = Modifier
                        .weight(1f)
                        .clickable { onStartDateClick() },
                    shape = RoundedCornerShape(8.dp)
                ) {
                    Column(
                        modifier = Modifier.padding(8.dp)
                    ) {
                        Text(
                            text = "开始日期",
                            style = MaterialTheme.typography.labelSmall
                        )
                    Text(
                            text = startDate.ifEmpty { "选择日期" },
                        style = MaterialTheme.typography.bodyMedium
                    )
                    }
                }
                
                Spacer(modifier = Modifier.width(16.dp))
                
                // 结束日期选择
                OutlinedCard(
                    modifier = Modifier
                        .weight(1f)
                        .clickable { onEndDateClick() },
                    shape = RoundedCornerShape(8.dp)
                ) {
                    Column(
                        modifier = Modifier.padding(8.dp)
                    ) {
                        Text(
                            text = "结束日期",
                            style = MaterialTheme.typography.labelSmall
                        )
                        Text(
                            text = endDate.ifEmpty { "选择日期" },
                            style = MaterialTheme.typography.bodyMedium
                        )
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // 日期范围操作按钮
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.End
            ) {
                TextButton(onClick = onClearDateRange) {
                    Text("清除")
                }
                
                Button(onClick = onApplyDateRange) {
                    Text("应用")
                }
            }
        }
                
                Divider(
                    modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 8.dp)
        )
        
        // 心情筛选
        Text(
            text = "心情",
            style = MaterialTheme.typography.titleSmall,
            fontWeight = FontWeight.Bold
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        // 心情筛选选项
        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            val allMoods = listOf("开心", "平静", "惊讶", "悲伤", "愤怒", "焦虑")
            items(allMoods) { mood ->
                FilterChip(
                    selected = moodFilters.contains(mood),
                    onClick = { onMoodFilterChanged(mood, !moodFilters.contains(mood)) },
                    label = { Text(mood) }
                )
            }
            }
            
        Spacer(modifier = Modifier.height(8.dp))
            
            // 类型筛选
                Text(
            text = "类型",
                    style = MaterialTheme.typography.titleSmall,
            fontWeight = FontWeight.Bold
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        // 类型筛选选项
        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            val allTypes = listOf("日常", "工作", "学习", "家庭", "社交", "其他")
            items(allTypes) { type ->
                FilterChip(
                    selected = typeFilters.contains(type),
                    onClick = { onTypeFilterChanged(type, !typeFilters.contains(type)) },
                    label = { Text(type) }
                )
            }
            }
            
        Spacer(modifier = Modifier.height(8.dp))
            
            // 高级筛选
                Text(
            text = "高级筛选",
                    style = MaterialTheme.typography.titleSmall,
            fontWeight = FontWeight.Bold
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        // 高级筛选选项
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
            FilterChip(
                selected = advancedFilters["hasAttachment"] == true,
                onClick = { onAdvancedFilterChanged("hasAttachment", !(advancedFilters["hasAttachment"] ?: false)) },
                label = { Text("带附件") }
            )
            
            FilterChip(
                selected = advancedFilters["hasTag"] == true,
                onClick = { onAdvancedFilterChanged("hasTag", !(advancedFilters["hasTag"] ?: false)) },
                label = { Text("带标签") }
            )
            
            FilterChip(
                selected = advancedFilters["isFavorite"] == true,
                onClick = { onAdvancedFilterChanged("isFavorite", !(advancedFilters["isFavorite"] ?: false)) },
                label = { Text("已收藏") }
            )
        }
    }
}

/**
 * 情景化搜索建议组件
 */
@Composable
fun ContextualSearchSuggestions(
    suggestions: List<String>,
    extendedSuggestions: List<String>,
    onSuggestionSelected: (String) -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp)
    ) {
            Text(
            text = stringResource(R.string.search_suggestions),
                style = MaterialTheme.typography.titleSmall,
            fontWeight = FontWeight.Bold
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        // 常规建议
        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
            items(suggestions) { suggestion ->
                SuggestionChip(
                    onClick = { onSuggestionSelected(suggestion) },
                    label = { Text(suggestion) }
                )
            }
        }
        
        // 扩展建议
        if (extendedSuggestions.isNotEmpty()) {
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = stringResource(R.string.you_might_be_looking_for),
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            
            Spacer(modifier = Modifier.height(4.dp))
            
            LazyRow(
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(extendedSuggestions) { suggestion ->
                    OutlinedSuggestionChip(
                    onClick = { onSuggestionSelected(suggestion) },
                    label = { Text(suggestion) }
                )
                }
            }
        }
    }
}

/**
 * 感想卡片组件 - data.model.Reflection 版本
 */
@Composable
fun DataReflectionCard(
    reflection: com.timeflow.app.data.model.Reflection,
    onClick: () -> Unit,
    onFavoriteToggle: () -> Unit = {},
    modifier: Modifier = Modifier
) {
    val dateFormatter = DateTimeFormatter.ofPattern("yyyy年MM月dd日 HH:mm")
    
    Card(
        modifier = modifier
            .fillMaxWidth()
            .clickable(onClick = onClick),
        shape = RoundedCornerShape(20.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 日期和心情
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(verticalAlignment = Alignment.CenterVertically) {
                    Box(
                        modifier = Modifier
                            .size(28.dp)
                            .clip(CircleShape)
                            .background(reflection.moodColor.copy(alpha = 0.15f))
                            .border(0.5.dp, reflection.moodColor.copy(alpha = 0.5f), CircleShape),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = reflection.moodEmoji,
                            style = MaterialTheme.typography.bodyLarge
                        )
                    }
                    
                    Spacer(modifier = Modifier.width(8.dp))
                    
                    Text(
                        text = reflection.createdTime.format(dateFormatter),
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
                
                if (reflection.isFavorite) {
                    Icon(
                        imageVector = Icons.Default.Favorite,
                        contentDescription = "已收藏",
                        tint = Color(0xFFE57373),
                        modifier = Modifier.size(18.dp)
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 内容预览
            Text(
                text = reflection.content,
                style = MaterialTheme.typography.bodyMedium,
                maxLines = 3,
                overflow = TextOverflow.Ellipsis,
                color = MaterialTheme.colorScheme.onSurface
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 标签
            if (reflection.tags.isNotEmpty()) {
                LazyRow(
                    horizontalArrangement = Arrangement.spacedBy(6.dp)
                ) {
                    items(reflection.tags.take(3)) { tag ->
                        Surface(
                            shape = RoundedCornerShape(50),
                            color = MaterialTheme.colorScheme.secondaryContainer.copy(alpha = 0.5f),
                            modifier = Modifier.height(24.dp)
                        ) {
                            Text(
                                text = "#$tag",
                                style = MaterialTheme.typography.labelSmall,
                                color = MaterialTheme.colorScheme.onSecondaryContainer,
                                modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
                            )
                        }
                    }
                    
                    if (reflection.tags.size > 3) {
                        item {
                            Surface(
                                shape = RoundedCornerShape(50),
                                color = MaterialTheme.colorScheme.secondaryContainer.copy(alpha = 0.3f),
                                modifier = Modifier.height(24.dp)
                            ) {
                                Text(
                                    text = "+${reflection.tags.size - 3}",
                                    style = MaterialTheme.typography.labelSmall,
                                    color = MaterialTheme.colorScheme.onSecondaryContainer,
                                    modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
                                )
                            }
                        }
                    }
                }
            }
        }
    }
}

/**
 * 感想卡片组件 - ui.screen.reflection.Reflection 版本
 */
@Composable
fun ReflectionCard(
    reflection: com.timeflow.app.ui.screen.reflection.Reflection,
    onClick: () -> Unit,
    onFavoriteToggle: () -> Unit = {},
    modifier: Modifier = Modifier
) {
    val dateFormatter = DateTimeFormatter.ofPattern("yyyy年MM月dd日 HH:mm")
    
    Card(
        modifier = modifier
            .fillMaxWidth()
            .clickable(onClick = onClick),
        shape = RoundedCornerShape(20.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 日期和心情
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(verticalAlignment = Alignment.CenterVertically) {
                    Box(
                        modifier = Modifier
                            .size(28.dp)
                            .clip(CircleShape)
                            .background(reflection.mood.color.copy(alpha = 0.15f))
                            .border(0.5.dp, reflection.mood.color.copy(alpha = 0.5f), CircleShape),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = reflection.mood.emoji,
                            style = MaterialTheme.typography.bodyLarge
                        )
                    }
                    
                    Spacer(modifier = Modifier.width(8.dp))
                    
                    val localDate = reflection.date.atZone(ZoneId.systemDefault()).toLocalDateTime()
                    Text(
                        text = localDate.format(dateFormatter),
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 标题和内容预览
            Text(
                text = reflection.title,
                style = MaterialTheme.typography.titleSmall,
                color = MaterialTheme.colorScheme.onSurface
            )
            
            Spacer(modifier = Modifier.height(4.dp))
            
            // 内容预览
            Text(
                text = reflection.content,
                style = MaterialTheme.typography.bodyMedium,
                maxLines = 3,
                overflow = TextOverflow.Ellipsis,
                color = MaterialTheme.colorScheme.onSurface
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 标签
            if (reflection.tags.isNotEmpty()) {
                LazyRow(
                    horizontalArrangement = Arrangement.spacedBy(6.dp)
                ) {
                    items(reflection.tags.take(3)) { tag ->
                        Surface(
                            shape = RoundedCornerShape(50),
                            color = MaterialTheme.colorScheme.secondaryContainer.copy(alpha = 0.5f),
                            modifier = Modifier.height(24.dp)
                        ) {
                            Text(
                                text = "#$tag",
                                style = MaterialTheme.typography.labelSmall,
                                color = MaterialTheme.colorScheme.onSecondaryContainer,
                                modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
                            )
                        }
                    }
                    
                    if (reflection.tags.size > 3) {
                        item {
                            Surface(
                                shape = RoundedCornerShape(50),
                                color = MaterialTheme.colorScheme.secondaryContainer.copy(alpha = 0.3f),
                                modifier = Modifier.height(24.dp)
                            ) {
                                Text(
                                    text = "+${reflection.tags.size - 3}",
                                    style = MaterialTheme.typography.labelSmall,
                                    color = MaterialTheme.colorScheme.onSecondaryContainer,
                                    modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
                                )
                            }
                        }
                    }
                }
            }
        }
    }
}

/**
 * 芯片组，用于替代FlowRow
 */
@Composable
fun <T> ChipGroup(
    items: List<T>,
    content: @Composable (T) -> Unit
) {
    Column {
        var currentRowItems = mutableListOf<T>()
        var currentRowWidth = 0f
        val maxWidth = 1000f // 估计的最大宽度

        for (item in items) {
            if (currentRowWidth + 100 > maxWidth) { // 100是估计的每个芯片的宽度
                LazyRow(
                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                    content = {
                        items(currentRowItems) { rowItem ->
                            content(rowItem)
                        }
                    }
                )
                Spacer(modifier = Modifier.height(8.dp))
                currentRowItems = mutableListOf()
                currentRowWidth = 0f
            }
            currentRowItems.add(item)
            currentRowWidth += 100 // 假设每个芯片平均宽度为100
        }

        if (currentRowItems.isNotEmpty()) {
            LazyRow(
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                content = {
                    items(currentRowItems) { rowItem ->
                        content(rowItem)
                    }
                }
            )
        }
    }
}

/**
 * 时间维度选择器
 * 用于切换周/月/年视图模式
 */
@Composable
fun TimeDimensionSelector(
    selectedDimension: TimeDimension,
    onDimensionSelected: (TimeDimension) -> Unit,
    onCalendarClick: () -> Unit
) {
    // 🔧 使用主题色而不是硬编码颜色
    val primaryColor = MaterialTheme.colorScheme.primary
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 4.dp), // 🎯 缩小垂直间距
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 分段选择器
        Row(
            modifier = Modifier
                .weight(1f)
                .clip(RoundedCornerShape(28.dp))
                .background(Color(0xFFF3F2F4)) // 🎯 更改背景色为 #f3f2f4
                .padding(4.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            TimeDimension.values().forEach { dimension ->
                val isSelected = dimension == selectedDimension
                Box(
                    modifier = Modifier
                        .weight(1f)
                        .height(36.dp) // 🎯 稍微缩小高度
                        .clip(RoundedCornerShape(18.dp))
                        .background(
                            if (isSelected) primaryColor // 🔧 使用主题色
                            else Color.Transparent
                        )
                        .clickable { onDimensionSelected(dimension) },
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = dimension.displayName,
                        color = if (isSelected) 
                            Color.White // 🎯 选中文字颜色为白色
                        else 
                            Color(0xFF666666), // 🎯 未选中文字颜色为深灰
                        style = MaterialTheme.typography.labelLarge.copy(
                            fontWeight = if (isSelected) FontWeight.Medium else FontWeight.Normal
                        )
                    )
                }
            }
        }
        
        // 自定义日期按钮
        IconButton(onClick = onCalendarClick) {
            Icon(
                imageVector = Icons.Default.DateRange,
                contentDescription = "自定义日期",
                tint = primaryColor // 🔧 使用主题色
            )
        }
    }
}

/**
 * 时间导航控制栏
 */
@Composable
fun TimeNavigationBar(
    title: String,
    onPrevious: () -> Unit,
    onNext: () -> Unit,
    onToday: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 4.dp), // 🎯 缩小垂直间距
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 左侧导航箭头和标题
        Row(
            verticalAlignment = Alignment.CenterVertically
        ) {
            IconButton(onClick = onPrevious) {
                Icon(
                    imageVector = Icons.Default.KeyboardArrowLeft,
                    contentDescription = "上一个",
                    tint = MaterialTheme.colorScheme.onSurface
                )
            }
            
            Text(
                text = title,
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            
            IconButton(onClick = onNext) {
                Icon(
                    imageVector = Icons.Default.KeyboardArrowRight,
                    contentDescription = "下一个",
                    tint = MaterialTheme.colorScheme.onSurface
                )
            }
        }
        
        // 今天按钮
        OutlinedButton(
            onClick = onToday,
            contentPadding = PaddingValues(horizontal = 12.dp, vertical = 4.dp),
            shape = RoundedCornerShape(16.dp)
        ) {
            Text("今天", style = MaterialTheme.typography.labelMedium)
        }
    }
}

/**
 * 周视图组件
 */
@Composable
fun WeekView(
    weekDays: List<DayViewData>,
    onDaySelected: (DayViewData) -> Unit,
    selectedDay: DayViewData? = null,
    reflections: List<com.timeflow.app.ui.screen.reflection.Reflection> = emptyList()
) {
    Column(
        modifier = Modifier.fillMaxWidth()
    ) {
        // 周视图头部
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 8.dp),
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            val dayNames = listOf("日", "一", "二", "三", "四", "五", "六")
            dayNames.forEach { day ->
                Box(
                    modifier = Modifier.weight(1f),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = day,
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }
        
        // 周视图日期格子
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 4.dp, vertical = 8.dp),
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            weekDays.forEach { dayData ->
                DayCell(
                    dayData = dayData,
                    isSelected = dayData == selectedDay,
                    onClick = { onDaySelected(dayData) },
                    modifier = Modifier.weight(1f)
                )
            }
        }
        
        // 选中日期的感想列表
        selectedDay?.let { day ->
            Divider(
                modifier = Modifier.padding(vertical = 8.dp),
                color = MaterialTheme.colorScheme.surfaceVariant
            )
            
            Text(
                text = "${day.date.month.value}月${day.date.dayOfMonth}日（${getDayOfWeek(day.timestamp)}）",
                style = MaterialTheme.typography.titleMedium,
                modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)
            )
            
            val dayReflections = reflections.filter { reflection ->
                val reflectionDate = reflection.date.atZone(ZoneId.systemDefault()).toLocalDate()
                reflectionDate == day.date
            }
            
            if (dayReflections.isNotEmpty()) {
                LazyColumn(
                    modifier = Modifier
                        .fillMaxWidth()
                        .heightIn(max = 400.dp), // 限制最大高度以支持滑动
                    verticalArrangement = Arrangement.spacedBy(8.dp),
                    contentPadding = PaddingValues(horizontal = 16.dp, vertical = 8.dp)
                ) {
                    items(dayReflections) { reflection ->
                        SuperEnhancedReflectionCard(
                            reflection = reflection,
                            onClick = {},
                            onLongClick = {},
                            modifier = Modifier.fillMaxWidth()
                        )
                    }
                }
            } else {
                EmptyStateMessage(
                    message = "这一天还没有记录感想",
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(32.dp)
                )
            }
        }
    }
}

/**
 * 日期单元格
 */
@Composable
fun DayCell(
    dayData: DayViewData,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    val isCurrentMonth = dayData.isCurrentMonth
    val hasContent = dayData.reflectionCount > 0
    // 🔧 使用主题色而不是硬编码颜色
    val primaryColor = MaterialTheme.colorScheme.primary
    
    Box(
        modifier = modifier
            .aspectRatio(1f)
            .padding(4.dp)
            .clip(CircleShape)
            .background(
                when {
                    isSelected -> primaryColor // 🔧 使用主题色
                    else -> Color.Transparent
                }
            )
            .border(
                width = 1.dp,
                color = when {
                    isSelected -> Color.Transparent
                    dayData.isToday -> primaryColor // 🔧 使用主题色
                    else -> Color.Transparent
                },
                shape = CircleShape
            )
            .clickable(enabled = isCurrentMonth) { onClick() },
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = dayData.date.dayOfMonth.toString(),
                style = MaterialTheme.typography.bodyMedium,
                color = when {
                    isSelected -> Color.White // 选中时文字为白色
                    !isCurrentMonth -> MaterialTheme.colorScheme.outline
                    dayData.isToday -> primaryColor // 🔧 使用主题色
                    else -> MaterialTheme.colorScheme.onSurface
                }
            )
            
            if (hasContent) {
                Box(
                    modifier = Modifier
                        .padding(top = 4.dp)
                        .size(6.dp)
                        .clip(CircleShape)
                        .background(
                            when {
                                isSelected -> Color.White // 选中时小圆点为白色
                                else -> primaryColor // 🔧 使用主题色
                            }
                        )
                )
            }
        }
    }
}

/**
 * 月视图组件
 */
@Composable
fun MonthView(
    monthDays: List<DayViewData>,
    onDaySelected: (DayViewData) -> Unit
) {
    Column(
        modifier = Modifier.fillMaxWidth()
    ) {
        // 月视图头部
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 8.dp),
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            val dayNames = listOf("日", "一", "二", "三", "四", "五", "六")
            dayNames.forEach { day ->
                Box(
                    modifier = Modifier.weight(1f),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = day,
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }
        
        // 月视图日期网格
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 4.dp, vertical = 8.dp)
        ) {
            // 将日期分组为每行7天
            monthDays.chunked(7).forEach { weekDays ->
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceEvenly
                ) {
                    weekDays.forEach { dayData ->
                        MonthDayCell(
                            dayData = dayData,
                            onClick = { onDaySelected(dayData) },
                            modifier = Modifier.weight(1f)
                        )
                    }
                    
                    // 如果一行不足7天，补充空白
                    repeat(7 - weekDays.size) {
                        Box(
                            modifier = Modifier
                                .weight(1f)
                                .aspectRatio(1f)
                                .padding(4.dp)
                        )
                    }
                }
            }
        }
    }
}

/**
 * 月视图日期单元格
 */
@Composable
fun MonthDayCell(
    dayData: DayViewData,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    val isCurrentMonth = dayData.isCurrentMonth
    val reflectionCount = dayData.reflectionCount
    // 🔧 使用主题色而不是硬编码颜色
    val primaryColor = MaterialTheme.colorScheme.primary
    
    Box(
        modifier = modifier
            .aspectRatio(1f)
            .padding(4.dp)
            .clip(RoundedCornerShape(8.dp))
            .background(
                if (dayData.isToday)
                    primaryColor.copy(alpha = 0.1f) // 🔧 使用主题色
                else
                    Color.Transparent
            )
            .border(
                width = 1.dp,
                color = if (dayData.isToday)
                    primaryColor // 🔧 使用主题色
                else
                    Color.Transparent,
                shape = RoundedCornerShape(8.dp)
            )
            .clickable(enabled = isCurrentMonth) { onClick() },
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier.padding(4.dp)
        ) {
            Text(
                text = dayData.date.dayOfMonth.toString(),
                style = MaterialTheme.typography.bodyMedium,
                color = when {
                    !isCurrentMonth -> MaterialTheme.colorScheme.outline
                    dayData.isToday -> primaryColor // 🔧 使用主题色
                    else -> MaterialTheme.colorScheme.onSurface
                }
            )
            
            // 热力条 - 代表内容数量
            if (reflectionCount > 0 && isCurrentMonth) {
                Spacer(modifier = Modifier.height(4.dp))
                Box(
                    modifier = Modifier
                        .width((reflectionCount * 5).coerceAtMost(24).dp) 
                        .height(3.dp)
                        .clip(RoundedCornerShape(1.5.dp))
                        .background(getHeatmapColor(reflectionCount))
                )
            }
        }
    }
}

/**
 * 年视图组件
 */
@Composable
fun YearView(
    yearMonths: List<MonthViewData>,
    onMonthSelected: (MonthViewData) -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 8.dp)
    ) {
        // 第一排：1-6月
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 8.dp),
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            (1..6).forEach { month ->
                val monthData = yearMonths.find { it.month == month }
                if (monthData != null) {
                    CompactMonthCell(
                        monthData = monthData,
                        onClick = { onMonthSelected(monthData) },
                        modifier = Modifier.weight(1f)
                    )
                } else {
                    Box(
                        modifier = Modifier
                            .weight(1f)
                            .height(60.dp)
                            .padding(2.dp)
                    )
                }
            }
        }
        
        // 第二排：7-12月
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 8.dp),
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            (7..12).forEach { month ->
                val monthData = yearMonths.find { it.month == month }
                if (monthData != null) {
                    CompactMonthCell(
                        monthData = monthData,
                        onClick = { onMonthSelected(monthData) },
                        modifier = Modifier.weight(1f)
                    )
                } else {
                    Box(
                        modifier = Modifier
                            .weight(1f)
                            .height(60.dp)
                            .padding(2.dp)
                    )
                }
            }
        }
    }
}

/**
 * 年视图月份单元格
 */
@Composable
fun MonthCell(
    monthData: MonthViewData,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    val isCurrentMonth = monthData.isCurrentMonth
    
    Box(
        modifier = modifier
            .aspectRatio(1f)
            .padding(4.dp)
            .clip(RoundedCornerShape(8.dp))
            .background(getHeatmapColor(monthData.reflectionCount, maxAlpha = 0.3f))
            .border(
                width = 1.dp,
                color = if (isCurrentMonth) 
                    MaterialTheme.colorScheme.primary
                else 
                    MaterialTheme.colorScheme.surfaceVariant,
                shape = RoundedCornerShape(8.dp)
            )
            .clickable { onClick() },
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = "${monthData.month}月",
                style = MaterialTheme.typography.titleSmall,
                color = if (isCurrentMonth) 
                    MaterialTheme.colorScheme.primary
                else 
                    MaterialTheme.colorScheme.onSurface
            )
            
            if (monthData.reflectionCount > 0) {
                Text(
                    text = "${monthData.reflectionCount}",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

/**
 * 紧凑版年视图月份单元格
 */
@Composable
fun CompactMonthCell(
    monthData: MonthViewData,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    val isCurrentMonth = monthData.isCurrentMonth
    
    Box(
        modifier = modifier
            .height(60.dp)
            .padding(2.dp)
            .clip(RoundedCornerShape(8.dp))
            .background(getHeatmapColor(monthData.reflectionCount, maxAlpha = 0.3f))
            .border(
                width = 1.dp,
                color = if (isCurrentMonth) 
                    MaterialTheme.colorScheme.primary
                else 
                    MaterialTheme.colorScheme.surfaceVariant,
                shape = RoundedCornerShape(8.dp)
            )
            .clickable { onClick() },
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = "${monthData.month}月",
                style = MaterialTheme.typography.labelMedium,
                color = if (isCurrentMonth) 
                    MaterialTheme.colorScheme.primary
                else 
                    MaterialTheme.colorScheme.onSurface
            )
            
            if (monthData.reflectionCount > 0) {
                Text(
                    text = "${monthData.reflectionCount}",
                    style = MaterialTheme.typography.labelSmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

/**
 * 获取热力图颜色
 */
@Composable
fun getHeatmapColor(count: Int, maxAlpha: Float = 1.0f): Color {
    val primaryColor = MaterialTheme.colorScheme.primary
    return when {
        count <= 0 -> Color.Transparent
        count == 1 -> primaryColor.copy(alpha = 0.2f * maxAlpha)
        count <= 3 -> primaryColor.copy(alpha = 0.4f * maxAlpha)
        count <= 5 -> primaryColor.copy(alpha = 0.6f * maxAlpha)
        count <= 8 -> primaryColor.copy(alpha = 0.8f * maxAlpha)
        else -> primaryColor.copy(alpha = maxAlpha)
    }
}

/**
 * 空状态消息组件
 */
@Composable
fun EmptyStateMessage(
    message: String,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Text(
            text = message,
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            textAlign = TextAlign.Center
        )
    }
}

/**
 * 🎨 美化感想卡片组件 - 参考知名日记应用设计
 * 支持任务标签同步和更丰富的视觉呈现
 */
@OptIn(ExperimentalLayoutApi::class)
@Composable
fun EnhancedReflectionCard(
    reflection: com.timeflow.app.ui.screen.reflection.Reflection,
    onClick: () -> Unit,
    onFavoriteToggle: () -> Unit = {},
    modifier: Modifier = Modifier
) {
    val dateFormatter = DateTimeFormatter.ofPattern("M月d日")
    val timeFormatter = DateTimeFormatter.ofPattern("HH:mm")
    val localDateTime = reflection.date.atZone(ZoneId.systemDefault()).toLocalDateTime()
    
    // 检查是否是任务完成的感想
    val isTaskReflection = reflection.title.startsWith("✓ ")
    val taskTitle = if (isTaskReflection) {
        reflection.title.removePrefix("✓ ").trim()
    } else {
        reflection.title
    }
    
    // 从metrics中获取任务相关信息
    val taskTags = reflection.metrics["taskTags"]?.split(",")?.filter { it.isNotBlank() } ?: emptyList()
    val taskPriority = reflection.metrics["taskPriority"]
    
    Card(
        modifier = modifier
            .fillMaxWidth()
            .clickable(onClick = onClick),
        shape = RoundedCornerShape(20.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = 2.dp,
            pressedElevation = 8.dp
        )
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            // 📅 顶部：日期时间和心情
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 左侧：日期时间
                Column {
                    Text(
                        text = localDateTime.format(dateFormatter),
                        style = MaterialTheme.typography.titleSmall.copy(
                            fontWeight = FontWeight.Bold
                        ),
                        color = MaterialTheme.colorScheme.primary
                    )
                    Text(
                        text = localDateTime.format(timeFormatter),
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.8f)
                    )
                }
                
                // 右侧：心情指示器
                Box(
                    modifier = Modifier
                        .size(56.dp)
                        .clip(CircleShape)
                        .background(
                            brush = Brush.radialGradient(
                                colors = listOf(
                                    reflection.mood.color.copy(alpha = 0.2f),
                                    reflection.mood.color.copy(alpha = 0.05f)
                                )
                            )
                        )
                        .border(
                            width = 2.dp,
                            color = reflection.mood.color.copy(alpha = 0.3f),
                            shape = CircleShape
                        ),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = reflection.mood.emoji,
                        style = MaterialTheme.typography.headlineMedium,
                        fontSize = 24.sp
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 🎯 标题部分
            if (isTaskReflection) {
                // 任务完成感想的特殊样式
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Icon(
                        imageVector = Icons.Default.CheckCircle,
                        contentDescription = null,
                        tint = Color(0xFF4CAF50),
                        modifier = Modifier.size(20.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = taskTitle,
                        style = MaterialTheme.typography.titleMedium.copy(
                            fontWeight = FontWeight.SemiBold
                        ),
                        color = MaterialTheme.colorScheme.onSurface,
                        maxLines = 2,
                        overflow = TextOverflow.Ellipsis
                    )
                }
                
                // 任务优先级指示器
                if (taskPriority != null && taskPriority != "未设置") {
                    Spacer(modifier = Modifier.height(8.dp))
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        val priorityColor = when (taskPriority) {
                            "URGENT" -> Color(0xFFE53E3E)
                            "HIGH" -> Color(0xFFFF8C00)
                            "MEDIUM" -> Color(0xFF3182CE)
                            "LOW" -> Color(0xFF38A169)
                            else -> Color(0xFF718096)
                        }
                        
                        val priorityText = when (taskPriority) {
                            "URGENT" -> "紧急"
                            "HIGH" -> "高优先级"
                            "MEDIUM" -> "普通优先级"
                            "LOW" -> "低优先级"
                            else -> taskPriority
                        }
                        
                        Surface(
                            shape = RoundedCornerShape(12.dp),
                            color = priorityColor.copy(alpha = 0.1f),
                            modifier = Modifier.height(24.dp)
                        ) {
                            Row(
                                verticalAlignment = Alignment.CenterVertically,
                                modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
                            ) {
                                Box(
                                    modifier = Modifier
                                        .size(6.dp)
                                        .clip(CircleShape)
                                        .background(priorityColor)
                                )
                                Spacer(modifier = Modifier.width(4.dp))
                                Text(
                                    text = priorityText,
                                    style = MaterialTheme.typography.labelSmall,
                                    color = priorityColor,
                                    fontWeight = FontWeight.Medium
                                )
                            }
                        }
                    }
                }
            } else {
                // 普通感想的标题
                Text(
                    text = reflection.title,
                    style = MaterialTheme.typography.titleMedium.copy(
                        fontWeight = FontWeight.SemiBold
                    ),
                    color = MaterialTheme.colorScheme.onSurface,
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis
                )
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 📝 内容预览
            Text(
                text = reflection.content,
                style = MaterialTheme.typography.bodyMedium.copy(
                    lineHeight = 20.sp
                ),
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.85f),
                maxLines = 3,
                overflow = TextOverflow.Ellipsis
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 🏷️ 标签区域 - 区分任务标签和感想标签
            val allTags = reflection.tags
            val reflectionTags = allTags.filter { it !in listOf("任务完成", "反馈") && it !in taskTags }
            
            if (allTags.isNotEmpty()) {
                Column(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    // 任务标签（如果存在）
                    if (taskTags.isNotEmpty()) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            modifier = Modifier.fillMaxWidth()
                        ) {
                            Icon(
                                imageVector = Icons.Default.Assignment,
                                contentDescription = null,
                                tint = MaterialTheme.colorScheme.primary.copy(alpha = 0.7f),
                                modifier = Modifier.size(14.dp)
                            )
                            Spacer(modifier = Modifier.width(6.dp))
                            Text(
                                text = "任务标签",
                                style = MaterialTheme.typography.labelSmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant,
                                fontWeight = FontWeight.Medium
                            )
                        }
                        
                        Spacer(modifier = Modifier.height(6.dp))
                        
                        FlowRow(
                            horizontalArrangement = Arrangement.spacedBy(6.dp),
                            verticalArrangement = Arrangement.spacedBy(4.dp)
                        ) {
                            taskTags.take(4).forEach { tag ->
                                Surface(
                                    shape = RoundedCornerShape(16.dp),
                                    color = Color(0xFF3182CE).copy(alpha = 0.1f),
                                    modifier = Modifier.height(28.dp)
                                ) {
                                    Text(
                                        text = "#$tag",
                                        style = MaterialTheme.typography.labelSmall,
                                        color = Color(0xFF3182CE),
                                        fontWeight = FontWeight.Medium,
                                        modifier = Modifier.padding(horizontal = 10.dp, vertical = 6.dp)
                                    )
                                }
                            }
                            
                            if (taskTags.size > 4) {
                                Surface(
                                    shape = RoundedCornerShape(16.dp),
                                    color = Color(0xFF3182CE).copy(alpha = 0.05f),
                                    modifier = Modifier.height(28.dp)
                                ) {
                                    Text(
                                        text = "+${taskTags.size - 4}",
                                        style = MaterialTheme.typography.labelSmall,
                                        color = Color(0xFF3182CE).copy(alpha = 0.7f),
                                        modifier = Modifier.padding(horizontal = 10.dp, vertical = 6.dp)
                                    )
                                }
                            }
                        }
                        
                        if (reflectionTags.isNotEmpty()) {
                            Spacer(modifier = Modifier.height(8.dp))
                        }
                    }
                    
                    // 感想标签
                    if (reflectionTags.isNotEmpty()) {
                        if (taskTags.isNotEmpty()) {
                            Row(
                                verticalAlignment = Alignment.CenterVertically,
                                modifier = Modifier.fillMaxWidth()
                            ) {
                                Icon(
                                    imageVector = Icons.Default.LocalOffer,
                                    contentDescription = null,
                                    tint = MaterialTheme.colorScheme.tertiary.copy(alpha = 0.7f),
                                    modifier = Modifier.size(14.dp)
                                )
                                Spacer(modifier = Modifier.width(6.dp))
                                Text(
                                    text = "感想标签",
                                    style = MaterialTheme.typography.labelSmall,
                                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                                    fontWeight = FontWeight.Medium
                                )
                            }
                            
                            Spacer(modifier = Modifier.height(6.dp))
                        }
                        
                        FlowRow(
                            horizontalArrangement = Arrangement.spacedBy(6.dp),
                            verticalArrangement = Arrangement.spacedBy(4.dp)
                        ) {
                            reflectionTags.take(3).forEach { tag ->
                                Surface(
                                    shape = RoundedCornerShape(16.dp),
                                    color = MaterialTheme.colorScheme.secondaryContainer.copy(alpha = 0.6f),
                                    modifier = Modifier.height(28.dp)
                                ) {
                                    Text(
                                        text = "#$tag",
                                        style = MaterialTheme.typography.labelSmall,
                                        color = MaterialTheme.colorScheme.onSecondaryContainer,
                                        fontWeight = FontWeight.Medium,
                                        modifier = Modifier.padding(horizontal = 10.dp, vertical = 6.dp)
                                    )
                                }
                            }
                            
                            if (reflectionTags.size > 3) {
                                Surface(
                                    shape = RoundedCornerShape(16.dp),
                                    color = MaterialTheme.colorScheme.secondaryContainer.copy(alpha = 0.3f),
                                    modifier = Modifier.height(28.dp)
                                ) {
                                    Text(
                                        text = "+${reflectionTags.size - 3}",
                                        style = MaterialTheme.typography.labelSmall,
                                        color = MaterialTheme.colorScheme.onSecondaryContainer.copy(alpha = 0.7f),
                                        modifier = Modifier.padding(horizontal = 10.dp, vertical = 6.dp)
                                    )
                                }
                            }
                        }
                    }
                }
            }
            
            // 底部分割线和互动提示
            if (isTaskReflection || reflection.tags.isNotEmpty()) {
                Spacer(modifier = Modifier.height(12.dp))
                
                Divider(
                    color = MaterialTheme.colorScheme.outlineVariant.copy(alpha = 0.3f),
                    thickness = 0.5.dp
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "点击查看详情",
                        style = MaterialTheme.typography.labelSmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.6f)
                    )
                    
                    Icon(
                        imageVector = Icons.Default.ChevronRight,
                        contentDescription = null,
                        tint = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.4f),
                        modifier = Modifier.size(16.dp)
                    )
                }
            }
        }
    }
}

/**
 * 🎨 超级美化感想卡片 - 参考现代日记应用设计
 * 支持点击详情、长按删除、撤销功能
 */
@OptIn(ExperimentalFoundationApi::class, ExperimentalLayoutApi::class)
@Composable
fun SuperEnhancedReflectionCard(
    reflection: com.timeflow.app.ui.screen.reflection.Reflection,
    onClick: () -> Unit,
    onLongClick: () -> Unit = {},
    onDelete: () -> Unit = {},
    modifier: Modifier = Modifier
) {
    val dateFormatter = remember { DateTimeFormatter.ofPattern("M月d日") }
    val timeFormatter = remember { DateTimeFormatter.ofPattern("HH:mm") }
    val localDateTime = remember(reflection.date) {
        reflection.date.atZone(ZoneId.systemDefault()).toLocalDateTime()
    }
    
    // 检查是否是任务完成的感想
    val isTaskReflection = reflection.title.startsWith("✓ ")
    val taskTitle = if (isTaskReflection) {
        reflection.title.removePrefix("✓ ").trim()
    } else {
        reflection.title
    }
    
    // 从metrics中获取任务相关信息
    val taskTags = reflection.metrics["taskTags"]?.split(",")?.filter { it.isNotBlank() } ?: emptyList()
    val taskPriority = reflection.metrics["taskPriority"]
    
    // 🎨 纯色背景
    val backgroundColor = Color(0xFFFBFAFB)
    
    Card(
        modifier = modifier
            .fillMaxWidth()
            .pointerInput(Unit) {
                detectTapGestures(
                    onTap = { onClick() },
                    onLongPress = { onLongClick() }
                )
            },
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.Transparent
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = 0.dp,
            pressedElevation = 0.dp
        )
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .background(backgroundColor)
                .padding(12.dp)
        ) {
            Column {
                // 📅 顶部：日期时间和心情
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 左侧：日期时间同排显示
                    Row(
                        verticalAlignment = Alignment.Bottom
                    ) {
                        Text(
                            text = localDateTime.format(dateFormatter),
                            style = MaterialTheme.typography.titleSmall.copy(
                                fontWeight = FontWeight.Bold,
                                fontSize = 14.sp
                            ),
                            color = Color(0xFF333333) // 深灰色
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = localDateTime.format(timeFormatter),
                            style = MaterialTheme.typography.bodySmall.copy(
                                fontSize = 11.sp
                            ),
                            color = Color(0xFF666666) // 中灰色
                        )
                    }
                    
                    // 右侧：心情指示器
                    Box(
                        modifier = Modifier
                            .size(40.dp)
                            .clip(CircleShape)
                            .background(
                                brush = Brush.radialGradient(
                                    colors = listOf(
                                        reflection.mood.color.copy(alpha = 0.15f),
                                        reflection.mood.color.copy(alpha = 0.03f),
                                        Color.Transparent
                                    )
                                )
                            )
                            .border(
                                width = 1.5.dp,
                                color = reflection.mood.color.copy(alpha = 0.25f),
                                shape = CircleShape
                            ),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = reflection.mood.emoji,
                            fontSize = 18.sp
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(12.dp))
                
                // 🎯 标题部分 - 加粗、字体加大
                if (isTaskReflection) {
                    // 任务完成感想的特殊样式
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        // 任务完成图标
                        Box(
                            modifier = Modifier
                                .size(24.dp)
                                .clip(CircleShape)
                                .background(Color(0xFF4CAF50).copy(alpha = 0.1f)),
                            contentAlignment = Alignment.Center
                        ) {
                            Icon(
                                imageVector = Icons.Default.CheckCircle,
                                contentDescription = null,
                                tint = Color(0xFF4CAF50),
                                modifier = Modifier.size(16.dp)
                            )
                        }
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = taskTitle,
                            style = MaterialTheme.typography.titleMedium.copy(
                                fontWeight = FontWeight.Bold,
                                fontSize = 16.sp,
                                lineHeight = 22.sp
                            ),
                            color = Color(0xFF5D4037), // 深棕色，参考图2
                            maxLines = 2,
                            overflow = TextOverflow.Ellipsis
                        )
                    }
                    
                    // 任务优先级指示器
                                            if (taskPriority != null && taskPriority != "未设置") {
                        Spacer(modifier = Modifier.height(8.dp))
                        Row(
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            val priorityColor = when (taskPriority) {
                                "URGENT" -> Color(0xFFE53E3E)
                                "HIGH" -> Color(0xFFFF8C00)
                                "MEDIUM" -> Color(0xFF3182CE)
                                "LOW" -> Color(0xFF38A169)
                                else -> Color(0xFF718096)
                            }
                            
                            val priorityText = when (taskPriority) {
                                "URGENT" -> "紧急"
                                "HIGH" -> "高优先级"
                                "MEDIUM" -> "普通优先级"
                                "LOW" -> "低优先级"
                                else -> taskPriority
                            }
                            
                            Surface(
                                shape = RoundedCornerShape(12.dp),
                                color = priorityColor.copy(alpha = 0.15f),
                                modifier = Modifier.height(24.dp)
                            ) {
                                Row(
                                    verticalAlignment = Alignment.CenterVertically,
                                    modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
                                ) {
                                    Box(
                                        modifier = Modifier
                                            .size(6.dp)
                                            .clip(CircleShape)
                                            .background(priorityColor)
                                    )
                                    Spacer(modifier = Modifier.width(4.dp))
                                    Text(
                                        text = priorityText,
                                        style = MaterialTheme.typography.labelSmall,
                                        color = priorityColor,
                                        fontWeight = FontWeight.SemiBold
                                    )
                                }
                            }
                        }
                    }
                } else {
                    // 普通感想的标题
                    Text(
                        text = reflection.title,
                        style = MaterialTheme.typography.titleMedium.copy(
                            fontWeight = FontWeight.Bold,
                            fontSize = 16.sp,
                            lineHeight = 22.sp
                        ),
                        color = MaterialTheme.colorScheme.primary,
                        maxLines = 2,
                        overflow = TextOverflow.Ellipsis
                    )
                }
                
                Spacer(modifier = Modifier.height(10.dp))
                
                // 📝 内容预览
                if (reflection.content.isNotBlank()) {
                    Text(
                        text = reflection.content,
                        style = MaterialTheme.typography.bodyMedium.copy(
                            fontSize = 12.sp,
                            lineHeight = 16.sp
                        ),
                        color = Color(0xFF666666), // 中灰色
                        maxLines = 2,
                        overflow = TextOverflow.Ellipsis
                    )
                    Spacer(modifier = Modifier.height(10.dp))
                }
                
                // 🖼️ 图片缩略图网格
                if (reflection.richContent.any { it.type == "image" }) {
                    ReflectionImageGrid(
                        richContent = reflection.richContent,
                        maxImages = 3,
                        modifier = Modifier.fillMaxWidth()
                    )
                    Spacer(modifier = Modifier.height(10.dp))
                }
                
                // 🏷️ 标签区域
                val allTags = (taskTags + reflection.tags).distinct().take(4)
                if (allTags.isNotEmpty()) {
                    FlowRow(
                        horizontalArrangement = Arrangement.spacedBy(8.dp),
                        verticalArrangement = Arrangement.spacedBy(6.dp)
                    ) {
                        allTags.forEach { tag ->
                            val isTaskTag = tag in taskTags
                            val tagColor = if (isTaskTag) {
                                Color(0xFF3182CE) // 任务标签：蓝色
                            } else {
                                Color(0xFF8D6E63) // 感想标签：棕色
                            }
                            
                            Surface(
                                shape = RoundedCornerShape(16.dp),
                                color = tagColor.copy(alpha = 0.12f),
                                modifier = Modifier.height(24.dp)
                            ) {
                                Row(
                                    verticalAlignment = Alignment.CenterVertically,
                                    modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
                                ) {
                                    Icon(
                                        imageVector = if (isTaskTag) Icons.Default.Assignment else Icons.Default.LocalOffer,
                                        contentDescription = null,
                                        tint = tagColor,
                                        modifier = Modifier.size(10.dp)
                                    )
                                    Spacer(modifier = Modifier.width(3.dp))
                                    Text(
                                        text = "#$tag",
                                        style = MaterialTheme.typography.labelSmall.copy(
                                            fontSize = 8.sp
                                        ),
                                        color = tagColor,
                                        fontWeight = FontWeight.Medium
                                    )
                                }
                            }
                        }
                        
                        // 如果标签超过4个，显示"+"
                        val remainingCount = (taskTags + reflection.tags).distinct().size - 4
                        if (remainingCount > 0) {
                            Surface(
                                shape = RoundedCornerShape(16.dp),
                                color = Color(0xFF8D6E63).copy(alpha = 0.12f),
                                modifier = Modifier.height(24.dp)
                            ) {
                                Box(
                                    modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp),
                                    contentAlignment = Alignment.Center
                                ) {
                                    Text(
                                        text = "+$remainingCount",
                                        style = MaterialTheme.typography.labelSmall.copy(
                                            fontSize = 8.sp
                                        ),
                                        color = Color(0xFF8D6E63),
                                        fontWeight = FontWeight.Medium
                                    )
                                }
                            }
                        }
                    }
                }
                
                // 底部引导提示
                Spacer(modifier = Modifier.height(8.dp))
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.End,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "轻触查看详情",
                        style = MaterialTheme.typography.labelSmall,
                        color = Color(0xFF999999).copy(alpha = 0.8f),
                        fontWeight = FontWeight.Medium
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Icon(
                        imageVector = Icons.AutoMirrored.Filled.ArrowForward,
                        contentDescription = null,
                        tint = Color(0xFF999999).copy(alpha = 0.8f),
                        modifier = Modifier.size(14.dp)
                    )
                }
            }
        }
    }
}

/**
 * 删除确认对话框
 */
@Composable
fun DeleteReflectionDialog(
    onConfirm: () -> Unit,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = "删除感想",
                fontWeight = FontWeight.Bold
            )
        },
        text = {
            Text("确定要删除这条感想吗？删除后可在1分钟内撤销。")
        },
        confirmButton = {
            TextButton(
                onClick = onConfirm,
                colors = ButtonDefaults.textButtonColors(
                    contentColor = Color(0xFFE53E3E)
                )
            ) {
                Text(
                    text = "删除",
                    fontWeight = FontWeight.SemiBold
                )
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("取消")
            }
        },
        shape = RoundedCornerShape(20.dp),
        containerColor = Color.White
    )
}

/**
 * 撤销删除的Snackbar
 */
@Composable
fun UndoDeleteSnackbar(
    onUndo: () -> Unit,
    onDismiss: () -> Unit,
    reflectionTitle: String
) {
    val context = LocalContext.current
    
    LaunchedEffect(Unit) {
        // 60秒后自动消失
        delay(60000)
        onDismiss()
    }
    
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color(0xFF323232)
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = 8.dp
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = "已删除感想",
                    style = MaterialTheme.typography.bodyMedium,
                    color = Color.White,
                    fontWeight = FontWeight.Medium
                )
                Text(
                    text = reflectionTitle.take(20) + if (reflectionTitle.length > 20) "..." else "",
                    style = MaterialTheme.typography.bodySmall,
                    color = Color.White.copy(alpha = 0.7f)
                )
            }
            
            TextButton(
                onClick = onUndo,
                colors = ButtonDefaults.textButtonColors(
                    contentColor = Color(0xFF4CAF50)
                )
            ) {
                Text(
                    text = "撤销",
                    fontWeight = FontWeight.Bold
                )
            }
        }
    }
}

/**
 * 🎯 可折叠的周视图 - 参考知名app设计
 */
@Composable
fun CollapsibleWeekView(
    weekDays: List<DayViewData>,
    onDaySelected: (DayViewData) -> Unit,
    selectedDay: DayViewData? = null,
    reflections: List<com.timeflow.app.ui.screen.reflection.Reflection> = emptyList(),
    onNavigateToDetail: (String) -> Unit,
    onDeleteReflection: (com.timeflow.app.ui.screen.reflection.Reflection) -> Unit
) {
    val listState = rememberLazyListState()
    
    // 监听滑动状态，当向上滑动时隐藏日历
    val isScrollingUp by remember {
        derivedStateOf {
            listState.firstVisibleItemScrollOffset > 0 || listState.firstVisibleItemIndex > 0
        }
    }
    
    // 动画控制日历高度
    val calendarHeight by animateFloatAsState(
        targetValue = if (isScrollingUp) 0.3f else 1f,
        animationSpec = tween(300),
        label = "calendar_height"
    )
    
    Column(modifier = Modifier.fillMaxSize()) {
        // 可折叠的日历区域
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height((120 * calendarHeight).coerceAtLeast(30f).dp) // 最小高度30dp
                .background(Color(0xFFFAFAFA))
        ) {
            if (calendarHeight > 0.5f) {
                // 完整显示
                WeekViewCalendar(
                    weekDays = weekDays,
                    onDaySelected = onDaySelected,
                    selectedDay = selectedDay,
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(horizontal = 8.dp, vertical = 4.dp)
                )
            } else {
                // 紧凑显示
                CompactWeekHeader(
                    selectedDay = selectedDay,
                    weekDays = weekDays,
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(horizontal = 16.dp, vertical = 4.dp)
                )
            }
        }
        
        // 感想列表
        LazyColumn(
            state = listState,
            modifier = Modifier
                .fillMaxSize()
                .background(Color.White),
            contentPadding = PaddingValues(start = 16.dp, top = 8.dp, end = 16.dp, bottom = 80.dp), // 🎯 增加底部空白80dp
            verticalArrangement = Arrangement.spacedBy(8.dp) // 🎯 减少间距
        ) {
            selectedDay?.let { day ->
                val dayReflections = reflections.filter { reflection ->
                    val reflectionDate = reflection.date.atZone(ZoneId.systemDefault()).toLocalDate()
                    reflectionDate == day.date
                }
                
                if (dayReflections.isNotEmpty()) {
                    items(dayReflections) { reflection ->
                        SuperEnhancedReflectionCard(
                            reflection = reflection,
                            onClick = { onNavigateToDetail(reflection.id) },
                            onLongClick = { onDeleteReflection(reflection) },
                            modifier = Modifier.fillMaxWidth()
                        )
                    }
                } else {
                    item {
                        EmptyStateMessage(
                            message = "这一天还没有记录感想",
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(32.dp)
                        )
                    }
                }
            }
        }
    }
}

/**
 * 紧凑的周视图日历
 */
@Composable
private fun WeekViewCalendar(
    weekDays: List<DayViewData>,
    onDaySelected: (DayViewData) -> Unit,
    selectedDay: DayViewData?,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier) {
        // 星期标题
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            val dayNames = listOf("日", "一", "二", "三", "四", "五", "六")
            dayNames.forEach { day ->
                Box(
                    modifier = Modifier.weight(1f),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = day,
                        style = MaterialTheme.typography.bodySmall,
                        color = Color(0xFF666666),
                        fontSize = 10.sp
                    )
                }
            }
        }
        
        Spacer(modifier = Modifier.height(8.dp))
        
        // 日期行
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            weekDays.forEach { dayData ->
                CompactDayCell(
                    dayData = dayData,
                    isSelected = dayData == selectedDay,
                    onClick = { onDaySelected(dayData) },
                    modifier = Modifier.weight(1f)
                )
            }
        }
    }
}

/**
 * 紧凑的日期单元格
 */
@Composable
private fun CompactDayCell(
    dayData: DayViewData,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    val isCurrentMonth = dayData.isCurrentMonth
    val hasContent = dayData.reflectionCount > 0
    // 🔧 使用主题色而不是硬编码颜色
    val primaryColor = MaterialTheme.colorScheme.primary
    
    Box(
        modifier = modifier
            .height(36.dp)
            .padding(2.dp)
            .clip(CircleShape)
            .background(
                when {
                    isSelected -> primaryColor // 🔧 使用主题色
                    else -> Color.Transparent
                }
            )
            .clickable(enabled = isCurrentMonth) { onClick() },
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = dayData.date.dayOfMonth.toString(),
                style = MaterialTheme.typography.bodySmall,
                fontSize = 12.sp,
                color = when {
                    isSelected -> Color.White
                    !isCurrentMonth -> Color(0xFFCCCCCC)
                    dayData.isToday -> primaryColor // 🔧 使用主题色
                    else -> Color(0xFF333333)
                }
            )
            
            if (hasContent) {
                Box(
                    modifier = Modifier
                        .padding(top = 2.dp)
                        .size(3.dp)
                        .clip(CircleShape)
                        .background(MaterialTheme.colorScheme.primary)
                )
            }
        }
    }
}

/**
 * 紧凑的头部显示
 */
@Composable
private fun CompactWeekHeader(
    selectedDay: DayViewData?,
    weekDays: List<DayViewData>,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        selectedDay?.let { day ->
            Text(
                text = "${day.date.month.value}月${day.date.dayOfMonth}日",
                style = MaterialTheme.typography.titleSmall,
                color = Color(0xFF333333),
                fontWeight = FontWeight.SemiBold
            )
        }
        
        // 显示本周的感想总数
        val totalReflections = weekDays.sumOf { it.reflectionCount }
        if (totalReflections > 0) {
            Text(
                text = "本周 $totalReflections 条感想",
                style = MaterialTheme.typography.bodySmall,
                color = Color(0xFF666666),
                fontSize = 10.sp
            )
        }
    }
}

/**
 * 🎯 可折叠的月视图 - 参考知名app设计
 */
@Composable
fun CollapsibleMonthView(
    monthDays: List<DayViewData>,
    reflections: List<com.timeflow.app.ui.screen.reflection.Reflection>,
    onDaySelected: (DayViewData) -> Unit,
    onNavigateToDetail: (String) -> Unit,
    onDeleteReflection: (com.timeflow.app.ui.screen.reflection.Reflection) -> Unit
) {
    val listState = rememberLazyListState()
    
    // 监听滑动状态
    val isScrollingUp by remember {
        derivedStateOf {
            listState.firstVisibleItemScrollOffset > 0 || listState.firstVisibleItemIndex > 0
        }
    }
    
    // 动画控制日历高度
    val calendarHeight by animateFloatAsState(
        targetValue = if (isScrollingUp) 0.4f else 1f,
        animationSpec = tween(300),
        label = "month_calendar_height"
    )
    
    Column(modifier = Modifier.fillMaxSize()) {
        // 可折叠的月历区域
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height((280 * calendarHeight).coerceAtLeast(60f).dp) // 最小高度60dp
                .background(Color(0xFFFAFAFA))
        ) {
            if (calendarHeight > 0.6f) {
                // 完整显示
                CompactMonthView(
                    monthDays = monthDays,
                    onDaySelected = onDaySelected,
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(8.dp)
                )
            } else {
                // 紧凑显示
                CompactMonthHeader(
                    monthDays = monthDays,
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(horizontal = 16.dp, vertical = 8.dp)
                )
            }
        }
        
        // 感想列表
        LazyColumn(
            state = listState,
            modifier = Modifier
                .fillMaxSize()
                .background(Color.White),
            contentPadding = PaddingValues(start = 16.dp, top = 8.dp, end = 16.dp, bottom = 80.dp), // 🎯 增加底部空白80dp
            verticalArrangement = Arrangement.spacedBy(8.dp) // 🎯 减少间距
        ) {
            if (reflections.isNotEmpty()) {
                item {
                    Text(
                        text = "本月感想 (${reflections.size})",
                        style = MaterialTheme.typography.titleMedium,
                        color = Color(0xFF333333),
                        fontWeight = FontWeight.SemiBold,
                        modifier = Modifier.padding(bottom = 8.dp)
                    )
                }
                
                items(reflections) { reflection ->
                    SuperEnhancedReflectionCard(
                        reflection = reflection,
                        onClick = { onNavigateToDetail(reflection.id) },
                        onLongClick = { onDeleteReflection(reflection) },
                        modifier = Modifier.fillMaxWidth()
                    )
                }
            } else {
                item {
                    EmptyStateMessage(
                        message = "本月还没有记录感想",
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(32.dp)
                    )
                }
            }
        }
    }
}

/**
 * 紧凑的月视图
 */
@Composable
private fun CompactMonthView(
    monthDays: List<DayViewData>,
    onDaySelected: (DayViewData) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier) {
        // 星期标题
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            val dayNames = listOf("日", "一", "二", "三", "四", "五", "六")
            dayNames.forEach { day ->
                Box(
                    modifier = Modifier.weight(1f),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = day,
                        style = MaterialTheme.typography.bodySmall,
                        color = Color(0xFF666666),
                        fontSize = 10.sp
                    )
                }
            }
        }
        
        Spacer(modifier = Modifier.height(8.dp))
        
        // 日期网格
        Column(
            verticalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            monthDays.chunked(7).forEach { weekDays ->
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceEvenly
                ) {
                    weekDays.forEach { dayData ->
                        CompactMonthDayCell(
                            dayData = dayData,
                            onClick = { onDaySelected(dayData) },
                            modifier = Modifier.weight(1f)
                        )
                    }
                    
                    // 补充空白
                    repeat(7 - weekDays.size) {
                        Spacer(modifier = Modifier.weight(1f))
                    }
                }
            }
        }
    }
}

/**
 * 紧凑的月视图日期单元格
 */
@Composable
private fun CompactMonthDayCell(
    dayData: DayViewData,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    val isCurrentMonth = dayData.isCurrentMonth
    val hasContent = dayData.reflectionCount > 0
    
    Box(
        modifier = modifier
            .aspectRatio(1f)
            .padding(2.dp)
            .clip(RoundedCornerShape(6.dp))
            .background(
                if (dayData.isToday) 
                    MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.3f)
                else 
                    Color.Transparent
            )
            .clickable(enabled = isCurrentMonth) { onClick() },
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = dayData.date.dayOfMonth.toString(),
                style = MaterialTheme.typography.bodySmall,
                fontSize = 11.sp,
                color = when {
                    !isCurrentMonth -> Color(0xFFCCCCCC)
                    dayData.isToday -> MaterialTheme.colorScheme.primary
                    else -> Color(0xFF333333)
                }
            )
            
            if (hasContent) {
                Box(
                    modifier = Modifier
                        .padding(top = 2.dp)
                        .size(3.dp)
                        .clip(CircleShape)
                        .background(MaterialTheme.colorScheme.primary)
                )
            }
        }
    }
}

/**
 * 紧凑的月份头部显示
 */
@Composable
private fun CompactMonthHeader(
    monthDays: List<DayViewData>,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        monthDays.firstOrNull()?.let { day ->
            Text(
                text = "${day.date.year}年${day.date.month.value}月",
                style = MaterialTheme.typography.titleMedium,
                color = Color(0xFF333333),
                fontWeight = FontWeight.Bold
            )
        }
        
        // 显示本月的感想总数
        val totalReflections = monthDays.sumOf { it.reflectionCount }
        if (totalReflections > 0) {
            Text(
                text = "本月 $totalReflections 条感想",
                style = MaterialTheme.typography.bodySmall,
                color = Color(0xFF666666),
                fontSize = 11.sp
            )
        }
    }
}

/**
 * 感想内容显示组件
 * 支持显示文本和图片的富文本内容
 */
@Composable
fun ReflectionContentDisplay(
    richContent: List<ContentBlock>,
    modifier: Modifier = Modifier,
    onImageClick: ((Any) -> Unit)? = null
) {
    Column(
        modifier = modifier,
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        richContent.forEach { contentBlock ->
            when (contentBlock.type) {
                "text" -> {
                    Text(
                        text = contentBlock.value,
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.onSurface
                    )
                }
                "image" -> {
                    ReflectionImageCard(
                        imagePath = contentBlock.value,
                        onClick = onImageClick
                    )
                }
            }
        }
    }
}

/**
 * 感想图片卡片组件
 */
@Composable
fun ReflectionImageCard(
    imagePath: String,
    modifier: Modifier = Modifier,
    onClick: ((Any) -> Unit)? = null
) {
    val imageModel = if (imagePath.startsWith("/")) {
        File(imagePath)
    } else {
        imagePath
    }
    
    Card(
        modifier = modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp)
            .then(
                if (onClick != null) {
                    Modifier.clickable { onClick(imageModel) }
                } else {
                    Modifier
                }
            ),
        shape = RoundedCornerShape(12.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        AsyncImage(
            model = imageModel,
            contentDescription = "感想图片",
            contentScale = ContentScale.Crop,
            modifier = Modifier
                .fillMaxWidth()
                .heightIn(min = 150.dp, max = 300.dp)
        )
    }
}

/**
 * 感想图片网格显示组件
 * 用于在列表中显示多张图片的缩略图
 */
@Composable
fun ReflectionImageGrid(
    richContent: List<ContentBlock>,
    maxImages: Int = 3,
    modifier: Modifier = Modifier,
    onImageClick: ((Any) -> Unit)? = null
) {
    val imageBlocks = richContent.filter { it.type == "image" }.take(maxImages)
    
    if (imageBlocks.isNotEmpty()) {
        LazyRow(
            modifier = modifier,
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            items(imageBlocks) { imageBlock ->
                ReflectionImageThumbnail(
                    imageBlock = imageBlock,
                    onImageClick = onImageClick
                )
            }
            
            // 如果还有更多图片，显示数量
            if (richContent.filter { it.type == "image" }.size > maxImages) {
                item {
                    val remainingCount = richContent.filter { it.type == "image" }.size - maxImages
                    Card(
                        modifier = Modifier.size(80.dp),
                        shape = RoundedCornerShape(8.dp),
                        colors = CardDefaults.cardColors(
                            containerColor = MaterialTheme.colorScheme.surfaceVariant
                        )
                    ) {
                        Box(
                            modifier = Modifier.fillMaxSize(),
                            contentAlignment = Alignment.Center
                        ) {
                            Text(
                                text = "+$remainingCount",
                                style = MaterialTheme.typography.labelMedium,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    }
                }
            }
        }
    }
}

/**
 * 感想图片缩略图组件
 */
@Composable
private fun ReflectionImageThumbnail(
    imageBlock: ContentBlock,
    onImageClick: ((Any) -> Unit)? = null
) {
    val imageModel = if (imageBlock.value.startsWith("/")) {
        File(imageBlock.value)
    } else {
        imageBlock.value
    }
    
    Card(
        modifier = Modifier
            .size(80.dp)
            .then(
                if (onImageClick != null) {
                    Modifier.clickable { onImageClick(imageModel) }
                } else {
                    Modifier
                }
            ),
        shape = RoundedCornerShape(8.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 1.dp)
    ) {
        AsyncImage(
            model = imageModel,
            contentDescription = "感想图片缩略图",
            contentScale = ContentScale.Crop,
            modifier = Modifier.fillMaxSize()
        )
    }
}

/**
 * 🎨 年视图紧凑型感想卡片 - 参考日视图设计，优化文字显示
 * 专为年视图设计的紧凑型卡片，解决文字遮挡问题
 */
@OptIn(ExperimentalFoundationApi::class, ExperimentalLayoutApi::class)
@Composable
fun YearViewCompactReflectionCard(
    reflection: com.timeflow.app.ui.screen.reflection.Reflection,
    onClick: () -> Unit,
    onLongClick: () -> Unit = {},
    modifier: Modifier = Modifier
) {
    val dateFormatter = remember { DateTimeFormatter.ofPattern("M月d日") }
    val timeFormatter = remember { DateTimeFormatter.ofPattern("HH:mm") }
    val localDateTime = remember(reflection.date) {
        reflection.date.atZone(ZoneId.systemDefault()).toLocalDateTime()
    }
    
    // 检查是否是任务完成的感想
    val isTaskReflection = reflection.title.startsWith("✓ ")
    val taskTitle = if (isTaskReflection) {
        reflection.title.removePrefix("✓ ").trim()
    } else {
        reflection.title
    }
    
    // 从metrics中获取任务相关信息
    val taskTags = reflection.metrics["taskTags"]?.split(",")?.filter { it.isNotBlank() } ?: emptyList()
    val taskPriority = reflection.metrics["taskPriority"]
    
    Card(
        modifier = modifier
            .fillMaxWidth()
            .pointerInput(Unit) {
                detectTapGestures(
                    onTap = { onClick() },
                    onLongPress = { onLongClick() }
                )
            },
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = 2.dp,
            pressedElevation = 4.dp
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            // 📅 顶部：日期时间和心情
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 左侧：日期时间
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = localDateTime.format(dateFormatter),
                        style = MaterialTheme.typography.titleSmall.copy(
                            fontWeight = FontWeight.SemiBold,
                            fontSize = 14.sp
                        ),
                        color = Color(0xFF333333)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = localDateTime.format(timeFormatter),
                        style = MaterialTheme.typography.bodySmall.copy(
                            fontSize = 12.sp
                        ),
                        color = Color(0xFF666666)
                    )
                }
                
                // 右侧：心情指示器
                Box(
                    modifier = Modifier
                        .size(32.dp)
                        .clip(CircleShape)
                        .background(
                            reflection.mood.color.copy(alpha = 0.1f)
                        )
                        .border(
                            width = 1.dp,
                            color = reflection.mood.color.copy(alpha = 0.3f),
                            shape = CircleShape
                        ),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = reflection.mood.emoji,
                        fontSize = 16.sp
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 🎯 标题部分 - 参考日视图样式
            if (isTaskReflection) {
                // 任务完成感想的特殊样式
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.fillMaxWidth()
                ) {
                    // 任务完成图标
                    Box(
                        modifier = Modifier
                            .size(20.dp)
                            .clip(CircleShape)
                            .background(Color(0xFF4CAF50).copy(alpha = 0.15f)),
                        contentAlignment = Alignment.Center
                    ) {
                        Icon(
                            imageVector = Icons.Default.CheckCircle,
                            contentDescription = null,
                            tint = Color(0xFF4CAF50),
                            modifier = Modifier.size(14.dp)
                        )
                    }
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = taskTitle,
                        style = MaterialTheme.typography.titleMedium.copy(
                            fontWeight = FontWeight.SemiBold,
                            fontSize = 15.sp,
                            lineHeight = 20.sp
                        ),
                        color = Color(0xFF1A1A1A),
                        maxLines = 2,
                        overflow = TextOverflow.Ellipsis
                    )
                }
            } else {
                // 普通感想的标题
                Text(
                    text = reflection.title,
                    style = MaterialTheme.typography.titleMedium.copy(
                        fontWeight = FontWeight.SemiBold,
                        fontSize = 15.sp,
                        lineHeight = 20.sp
                    ),
                    color = MaterialTheme.colorScheme.primary,
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis
                )
            }
            
            // 📝 内容预览 - 调整行高和字体大小
            if (reflection.content.isNotBlank()) {
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = reflection.content,
                    style = MaterialTheme.typography.bodyMedium.copy(
                        fontSize = 13.sp,
                        lineHeight = 18.sp
                    ),
                    color = Color(0xFF666666),
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis
                )
            }
            
            // 🖼️ 图片指示器（如果有图片）
            if (reflection.richContent.any { it.type == "image" }) {
                Spacer(modifier = Modifier.height(8.dp))
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.Image,
                        contentDescription = null,
                        tint = Color(0xFF9E9E9E),
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(
                        text = "${reflection.richContent.count { it.type == "image" }}张图片",
                        style = MaterialTheme.typography.labelSmall.copy(
                            fontSize = 11.sp
                        ),
                        color = Color(0xFF9E9E9E)
                    )
                }
            }
            
            // 🏷️ 标签区域 - 精简显示
            val allTags = (taskTags + reflection.tags).distinct().take(3)
            if (allTags.isNotEmpty()) {
                Spacer(modifier = Modifier.height(10.dp))
                FlowRow(
                    horizontalArrangement = Arrangement.spacedBy(6.dp),
                    verticalArrangement = Arrangement.spacedBy(4.dp)
                ) {
                    allTags.forEach { tag ->
                        val isTaskTag = tag in taskTags
                        val tagColor = if (isTaskTag) {
                            Color(0xFF3182CE) // 任务标签：蓝色
                        } else {
                            Color(0xFF8D6E63) // 感想标签：棕色
                        }
                        
                        Surface(
                            shape = RoundedCornerShape(12.dp),
                            color = tagColor.copy(alpha = 0.1f),
                            modifier = Modifier.height(20.dp)
                        ) {
                            Text(
                                text = "#$tag",
                                style = MaterialTheme.typography.labelSmall.copy(
                                    fontSize = 10.sp
                                ),
                                color = tagColor,
                                fontWeight = FontWeight.Medium,
                                modifier = Modifier.padding(horizontal = 6.dp, vertical = 2.dp)
                            )
                        }
                    }
                    
                    // 如果标签超过3个，显示"+"
                    val remainingCount = (taskTags + reflection.tags).distinct().size - 3
                    if (remainingCount > 0) {
                        Surface(
                            shape = RoundedCornerShape(12.dp),
                            color = Color(0xFF8D6E63).copy(alpha = 0.1f),
                            modifier = Modifier.height(20.dp)
                        ) {
                            Text(
                                text = "+$remainingCount",
                                style = MaterialTheme.typography.labelSmall.copy(
                                    fontSize = 10.sp
                                ),
                                color = Color(0xFF8D6E63),
                                fontWeight = FontWeight.Medium,
                                modifier = Modifier.padding(horizontal = 6.dp, vertical = 2.dp)
                            )
                        }
                    }
                }
            }
        }
    }
} 