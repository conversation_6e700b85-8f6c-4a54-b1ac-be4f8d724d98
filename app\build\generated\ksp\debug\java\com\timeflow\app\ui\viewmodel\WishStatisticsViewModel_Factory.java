package com.timeflow.app.ui.viewmodel;

import com.timeflow.app.data.repository.WishRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class WishStatisticsViewModel_Factory implements Factory<WishStatisticsViewModel> {
  private final Provider<WishRepository> wishRepositoryProvider;

  public WishStatisticsViewModel_Factory(Provider<WishRepository> wishRepositoryProvider) {
    this.wishRepositoryProvider = wishRepositoryProvider;
  }

  @Override
  public WishStatisticsViewModel get() {
    return newInstance(wishRepositoryProvider.get());
  }

  public static WishStatisticsViewModel_Factory create(
      Provider<WishRepository> wishRepositoryProvider) {
    return new WishStatisticsViewModel_Factory(wishRepositoryProvider);
  }

  public static WishStatisticsViewModel newInstance(WishRepository wishRepository) {
    return new WishStatisticsViewModel(wishRepository);
  }
}
