package com.timeflow.app.data.repository;

import com.timeflow.app.data.dao.WishDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class WishRepositoryImpl_Factory implements Factory<WishRepositoryImpl> {
  private final Provider<WishDao> wishDaoProvider;

  public WishRepositoryImpl_Factory(Provider<WishDao> wishDaoProvider) {
    this.wishDaoProvider = wishDaoProvider;
  }

  @Override
  public WishRepositoryImpl get() {
    return newInstance(wishDaoProvider.get());
  }

  public static WishRepositoryImpl_Factory create(Provider<WishDao> wishDaoProvider) {
    return new WishRepositoryImpl_Factory(wishDaoProvider);
  }

  public static WishRepositoryImpl newInstance(WishDao wishDao) {
    return new WishRepositoryImpl(wishDao);
  }
}
