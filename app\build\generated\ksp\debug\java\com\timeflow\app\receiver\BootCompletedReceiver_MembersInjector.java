package com.timeflow.app.receiver;

import com.timeflow.app.data.repository.HabitRepository;
import com.timeflow.app.data.repository.TaskRepository;
import com.timeflow.app.service.DailyReviewScheduler;
import com.timeflow.app.service.TaskReminderScheduler;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class BootCompletedReceiver_MembersInjector implements MembersInjector<BootCompletedReceiver> {
  private final Provider<HabitRepository> habitRepositoryProvider;

  private final Provider<TaskRepository> taskRepositoryProvider;

  private final Provider<TaskReminderScheduler> taskReminderSchedulerProvider;

  private final Provider<DailyReviewScheduler> dailyReviewSchedulerProvider;

  public BootCompletedReceiver_MembersInjector(Provider<HabitRepository> habitRepositoryProvider,
      Provider<TaskRepository> taskRepositoryProvider,
      Provider<TaskReminderScheduler> taskReminderSchedulerProvider,
      Provider<DailyReviewScheduler> dailyReviewSchedulerProvider) {
    this.habitRepositoryProvider = habitRepositoryProvider;
    this.taskRepositoryProvider = taskRepositoryProvider;
    this.taskReminderSchedulerProvider = taskReminderSchedulerProvider;
    this.dailyReviewSchedulerProvider = dailyReviewSchedulerProvider;
  }

  public static MembersInjector<BootCompletedReceiver> create(
      Provider<HabitRepository> habitRepositoryProvider,
      Provider<TaskRepository> taskRepositoryProvider,
      Provider<TaskReminderScheduler> taskReminderSchedulerProvider,
      Provider<DailyReviewScheduler> dailyReviewSchedulerProvider) {
    return new BootCompletedReceiver_MembersInjector(habitRepositoryProvider, taskRepositoryProvider, taskReminderSchedulerProvider, dailyReviewSchedulerProvider);
  }

  @Override
  public void injectMembers(BootCompletedReceiver instance) {
    injectHabitRepository(instance, habitRepositoryProvider.get());
    injectTaskRepository(instance, taskRepositoryProvider.get());
    injectTaskReminderScheduler(instance, taskReminderSchedulerProvider.get());
    injectDailyReviewScheduler(instance, dailyReviewSchedulerProvider.get());
  }

  @InjectedFieldSignature("com.timeflow.app.receiver.BootCompletedReceiver.habitRepository")
  public static void injectHabitRepository(BootCompletedReceiver instance,
      HabitRepository habitRepository) {
    instance.habitRepository = habitRepository;
  }

  @InjectedFieldSignature("com.timeflow.app.receiver.BootCompletedReceiver.taskRepository")
  public static void injectTaskRepository(BootCompletedReceiver instance,
      TaskRepository taskRepository) {
    instance.taskRepository = taskRepository;
  }

  @InjectedFieldSignature("com.timeflow.app.receiver.BootCompletedReceiver.taskReminderScheduler")
  public static void injectTaskReminderScheduler(BootCompletedReceiver instance,
      TaskReminderScheduler taskReminderScheduler) {
    instance.taskReminderScheduler = taskReminderScheduler;
  }

  @InjectedFieldSignature("com.timeflow.app.receiver.BootCompletedReceiver.dailyReviewScheduler")
  public static void injectDailyReviewScheduler(BootCompletedReceiver instance,
      DailyReviewScheduler dailyReviewScheduler) {
    instance.dailyReviewScheduler = dailyReviewScheduler;
  }
}
