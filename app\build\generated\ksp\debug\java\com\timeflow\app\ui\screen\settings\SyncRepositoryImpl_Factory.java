package com.timeflow.app.ui.screen.settings;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SyncRepositoryImpl_Factory implements Factory<SyncRepositoryImpl> {
  @Override
  public SyncRepositoryImpl get() {
    return newInstance();
  }

  public static SyncRepositoryImpl_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static SyncRepositoryImpl newInstance() {
    return new SyncRepositoryImpl();
  }

  private static final class InstanceHolder {
    private static final SyncRepositoryImpl_Factory INSTANCE = new SyncRepositoryImpl_Factory();
  }
}
