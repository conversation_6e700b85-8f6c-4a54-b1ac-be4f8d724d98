package com.timeflow.app.ui.screen.goal

import android.app.Activity
import android.util.Log
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.animateContentSize
import androidx.compose.animation.core.tween
import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.fadeIn
import androidx.compose.animation.slideInVertically
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material.icons.outlined.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import com.timeflow.app.R
import com.timeflow.app.utils.SystemBarManager
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import androidx.compose.foundation.BorderStroke
import com.timeflow.app.ui.viewmodel.GoalViewModel
import com.timeflow.app.ui.viewmodel.GoalUiState
import androidx.compose.ui.graphics.drawscope.inset
import androidx.compose.ui.text.TextStyle
import java.time.YearMonth
import java.util.Locale
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.graphics.nativeCanvas
import androidx.compose.animation.core.Spring
import androidx.compose.animation.core.animateDpAsState
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.ui.draw.scale
import androidx.compose.ui.draw.shadow
// 直接导入当前包中的MockGoalViewModel

/**
 * 目标完成分析页面
 * 提供对已完成的目标的年、月、季度目标的综合集合性复盘分析
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun GoalCompletionAnalysisScreen(
    navController: NavController,
    viewModel: Any = hiltViewModel<GoalViewModel>()
) {
    // 转换viewModel为内部使用的格式
    val actualViewModel = when (viewModel) {
        is GoalViewModel -> viewModel
        is MockGoalViewModel -> viewModel
        else -> throw IllegalArgumentException("不支持的ViewModel类型")
    }
    
    // 获取context和activity
    val context = LocalContext.current
    val activity = remember { context as? Activity }
    
    // 获取当前目标
    val uiState = when (viewModel) {
        is GoalViewModel -> viewModel.uiState.value
        is MockGoalViewModel -> viewModel.uiState.value
        else -> GoalUiState.Error("不支持的ViewModel类型")
    }
    
    val yearText = stringResource(R.string.year)
    val quarterText = stringResource(R.string.quarter)
    val monthText = stringResource(R.string.month)

    var selectedTimeRange by remember { mutableStateOf(yearText) }
    val timeRanges = listOf(yearText, quarterText, monthText)
    var selectedAnalysisType by remember { mutableStateOf(timeDimensionText) }
    val analysisTypes = listOf(timeDimensionText, domainDimensionText, difficultyDimensionText)
    
    // 从ViewModel获取实际数据
    val totalCompletedGoals = when (viewModel) {
        is GoalViewModel -> viewModel.totalCompletedGoals.value
        is MockGoalViewModel -> viewModel.totalCompletedGoals.value
        else -> 0
    }
    
    val inProgressGoals = when (viewModel) {
        is GoalViewModel -> viewModel.inProgressGoals.value
        is MockGoalViewModel -> viewModel.inProgressGoals.value
        else -> 0
    }
    
    val avgCompletionRate = when (viewModel) {
        is GoalViewModel -> viewModel.avgCompletionRate.value
        is MockGoalViewModel -> viewModel.avgCompletionRate.value
        else -> 0f
    }
    
    val goalsByTimeRange = when (viewModel) {
        is GoalViewModel -> viewModel.goalsByTimeRange.value
        is MockGoalViewModel -> viewModel.goalsByTimeRange.value
        else -> emptyMap()
    }
    
    val goalsByDomain = when (viewModel) {
        is GoalViewModel -> viewModel.goalsByDomain.value
        is MockGoalViewModel -> viewModel.goalsByDomain.value
        else -> emptyMap()
    }
    
    val goalsByDifficulty = when (viewModel) {
        is GoalViewModel -> viewModel.goalsByDifficulty.value
        is MockGoalViewModel -> viewModel.goalsByDifficulty.value
        else -> emptyMap()
    }
    
    // 加载所有已完成的目标
    LaunchedEffect(Unit) {
        // 初始加载全部目标数据
        when (viewModel) {
            is GoalViewModel -> viewModel.loadCompletedGoals()
            is MockGoalViewModel -> viewModel.loadCompletedGoals()
        }
        // 立即加载默认时间范围的数据
        when (viewModel) {
            is GoalViewModel -> viewModel.loadCompletedGoalsByTimeRange(selectedTimeRange)
            is MockGoalViewModel -> viewModel.loadCompletedGoalsByTimeRange(selectedTimeRange)
        }
    }
    
    // 监听时间范围变化
    LaunchedEffect(selectedTimeRange) {
        if (selectedTimeRange.isNotEmpty()) {
            when (viewModel) {
                is GoalViewModel -> viewModel.loadCompletedGoalsByTimeRange(selectedTimeRange)
                is MockGoalViewModel -> viewModel.loadCompletedGoalsByTimeRange(selectedTimeRange)
            }
        }
    }
    
    // 清理资源
    DisposableEffect(Unit) {
        onDispose {
            // 清理资源或重置状态
            when (viewModel) {
                is GoalViewModel -> viewModel.resetAnalysisData()
                is MockGoalViewModel -> viewModel.resetAnalysisData()
            }
        }
    }
    
    // 动画状态
    var showContent by remember { mutableStateOf(false) }
    LaunchedEffect(Unit) {
        kotlinx.coroutines.delay(100)
        showContent = true
    }
    
    // 界面颜色
    val backgroundColor = Color(0xFFF8F9FA)
    val surfaceColor = Color.White
    val textPrimaryColor = MaterialTheme.colorScheme.primary
    val textSecondaryColor = Color(0xFF666666)
    val accentColor = Color(0xFF7B66FF)
    
    // 状态颜色
    val successColor = Color(0xFF10B981)
    val warningColor = Color(0xFFF59E0B)
    val neutralColor = Color(0xFF7097A8)
    
    // 状态栏处理
    SideEffect {
        activity?.let { 
            SystemBarManager.forceOpaqueStatusBar(it)
        }
    }
    
    // 创建实际的数据对象
    val goalStats = remember(totalCompletedGoals, inProgressGoals, avgCompletionRate) {
        mapOf(
            stringResource(R.string.completed_goals) to totalCompletedGoals,
            stringResource(R.string.in_progress_goals) to inProgressGoals,
            stringResource(R.string.average_completion_rate) to "${(avgCompletionRate * 100).toInt()}%"
        )
    }
    
    // 假设这些数据仍然是模拟的，未来可以扩展ViewModel提供
    val timeStats = remember {
        mapOf(
            stringResource(R.string.fastest_completion) to stringResource(R.string.fastest_completion_example),
            stringResource(R.string.longest_goal) to stringResource(R.string.longest_goal_example),
            stringResource(R.string.best_persistence) to stringResource(R.string.best_persistence_example)
        )
    }
    
    val achievements = remember {
        listOf(
            stringResource(R.string.achievement_perseverance),
            stringResource(R.string.achievement_efficiency),
            stringResource(R.string.achievement_breakthrough)
        )
    }
    
    // 根据选择的分析类型获取相应的数据
    val timeDimensionText = stringResource(R.string.time_dimension)
    val domainDimensionText = stringResource(R.string.domain_dimension)
    val difficultyDimensionText = stringResource(R.string.difficulty_dimension)

    val analysisData = remember(selectedAnalysisType, goalsByTimeRange, goalsByDomain, goalsByDifficulty, timeDimensionText, domainDimensionText, difficultyDimensionText) {
        when (selectedAnalysisType) {
            timeDimensionText -> goalsByTimeRange
            domainDimensionText -> goalsByDomain
            difficultyDimensionText -> goalsByDifficulty
            else -> emptyMap()
        }
    }
    
    // 记忆点
    var growthMoments by remember { mutableStateOf(List(3) { "" }) }
    
    Scaffold(
        modifier = Modifier
            .fillMaxSize()
            .background(backgroundColor)
            .padding(top = SystemBarManager.getFixedStatusBarHeight()),
        containerColor = backgroundColor,
        topBar = {
            TopAppBar(
                title = { 
                    Text(
                        text = "目标成就全景",
                        fontSize = 20.sp,
                        fontWeight = FontWeight.Bold,
                        color = textPrimaryColor
                    )
                },
                navigationIcon = {
                    IconButton(onClick = { navController.popBackStack() }) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "返回"
                        )
                    }
                },
                actions = {
                    IconButton(onClick = { /* 导出报告 */ }) {
                        Icon(
                            imageVector = Icons.Default.Share,
                            contentDescription = "分享"
                        )
                    }
                    IconButton(onClick = { /* 保存为PDF */ }) {
                        Icon(
                            imageVector = Icons.Default.Download,
                            contentDescription = "导出"
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = backgroundColor,
                    titleContentColor = textPrimaryColor
                )
            )
        }
    ) { paddingValues ->
        // 处理加载状态
        if (uiState is GoalUiState.Loading) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator()
            }
        } else {
            // 显示主内容
            AnimatedVisibility(
                visible = showContent,
                enter = fadeIn(initialAlpha = 0.3f) + 
                        slideInVertically(
                            initialOffsetY = { it / 3 },
                            animationSpec = tween(durationMillis = 300)
                        )
            ) {
                LazyColumn(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(paddingValues)
                        .padding(horizontal = 16.dp),
                    verticalArrangement = Arrangement.spacedBy(16.dp),
                    contentPadding = PaddingValues(bottom = 24.dp)
                ) {
                    // 时间范围选择器 - 改进版
                    item {
                        Column(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(vertical = 8.dp)
                        ) {
                            Text(
                                text = stringResource(R.string.select_time_dimension),
                                fontSize = 14.sp,
                                fontWeight = FontWeight.Medium,
                                color = textSecondaryColor,
                                modifier = Modifier.padding(bottom = 8.dp, start = 4.dp)
                            )
                            
                            // 改进的选择器UI
                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(vertical = 4.dp),
                                horizontalArrangement = Arrangement.spacedBy(12.dp)
                            ) {
                                timeRanges.forEach { range ->
                                    val isSelected = selectedTimeRange == range
                                    
                                    // 定义动画值
                                    val animatedElevation by animateDpAsState(
                                        targetValue = if (isSelected) 8.dp else 2.dp,
                                        animationSpec = tween(durationMillis = 300)
                                    )
                                    
                                    val animatedScale by animateFloatAsState(
                                        targetValue = if (isSelected) 1.05f else 1f,
                                        animationSpec = tween(durationMillis = 300)
                                    )
                                    
                                    val backgroundColor = if (isSelected) accentColor else Color.White
                                    val contentColor = if (isSelected) Color.White else textPrimaryColor
                                    
                                    // 绘制带有图标的卡片按钮
                                    Card(
                                        modifier = Modifier
                                            .weight(1f)
                                            .height(48.dp)
                                            .scale(animatedScale)
                                            .shadow(
                                                elevation = animatedElevation,
                                                shape = RoundedCornerShape(24.dp)
                                            )
                                            .clickable { 
                                                if (selectedTimeRange != range) {
                                                    selectedTimeRange = range
                                                }
                                            },
                                        shape = RoundedCornerShape(24.dp),
                                        colors = CardDefaults.cardColors(
                                            containerColor = backgroundColor
                                        ),
                                        elevation = CardDefaults.cardElevation(
                                            defaultElevation = 0.dp,
                                            pressedElevation = 0.dp
                                        )
                                    ) {
                                        Box(
                                            modifier = Modifier.fillMaxSize(),
                                            contentAlignment = Alignment.Center
                                        ) {
                                            Row(
                                                horizontalArrangement = Arrangement.Center,
                                                verticalAlignment = Alignment.CenterVertically
                                            ) {
                                                // 为每个范围添加对应的图标
                                                val icon = when (range) {
                                                    "年" -> Icons.Outlined.DateRange
                                                    "季度" -> Icons.Outlined.EventNote
                                                    "月" -> Icons.Outlined.CalendarToday
                                                    else -> Icons.Outlined.CalendarToday
                                                }
                                                
                                                Icon(
                                                    imageVector = icon,
                                                    contentDescription = null,
                                                    tint = contentColor,
                                                    modifier = Modifier.size(18.dp)
                                                )
                                                
                                                Spacer(modifier = Modifier.width(6.dp))
                                                
                                                Text(
                                                    text = range,
                                                    fontSize = 14.sp,
                                                    fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Medium,
                                                    color = contentColor
                                                )
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    
                    // 成就全景卡片
                    item {
                        Card(
                            modifier = Modifier.fillMaxWidth(),
                            shape = RoundedCornerShape(16.dp),
                            colors = CardDefaults.cardColors(containerColor = surfaceColor),
                            elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
                        ) {
                            Column(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(20.dp)
                            ) {
                                Text(
                                    text = "🌟 ${LocalDateTime.now().year}年度目标成就全景",
                                    fontSize = 18.sp,
                                    fontWeight = FontWeight.Bold,
                                    color = textPrimaryColor
                                )
                                
                                Divider(
                                    modifier = Modifier.padding(vertical = 12.dp),
                                    color = Color(0xFFEEEEEE)
                                )
                                
                                // 完成情况
                                Text(
                                    text = "🔢 完成情况",
                                    fontSize = 16.sp,
                                    fontWeight = FontWeight.Bold,
                                    color = textPrimaryColor
                                )
                                
                                Spacer(modifier = Modifier.height(8.dp))
                                
                                goalStats.forEach { (key, value) ->
                                    Row(
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .padding(vertical = 4.dp)
                                    ) {
                                        Text(
                                            text = "• $key: ",
                                            fontSize = 14.sp,
                                            color = textPrimaryColor
                                        )
                                        
                                        val displayValue = when {
                                            value is Int -> "${value}个"
                                            else -> value.toString()
                                        }
                                        
                                        Text(
                                            text = displayValue,
                                            fontSize = 14.sp,
                                            fontWeight = FontWeight.Medium,
                                            color = textPrimaryColor
                                        )
                                    }
                                }
                                
                                Spacer(modifier = Modifier.height(16.dp))
                                
                                // 时间分布
                                Text(
                                    text = "⏱️ 时间分布",
                                    fontSize = 16.sp,
                                    fontWeight = FontWeight.Bold,
                                    color = textPrimaryColor
                                )
                                
                                Spacer(modifier = Modifier.height(8.dp))
                                
                                timeStats.forEach { (key, value) ->
                                    Row(
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .padding(vertical = 4.dp)
                                    ) {
                                        Text(
                                            text = "• $key: ",
                                            fontSize = 14.sp,
                                            color = textPrimaryColor
                                        )
                                        
                                        Text(
                                            text = value,
                                            fontSize = 14.sp,
                                            fontWeight = FontWeight.Medium,
                                            color = textPrimaryColor
                                        )
                                    }
                                }
                                
                                Spacer(modifier = Modifier.height(16.dp))
                                
                                // 成就徽章
                                Text(
                                    text = "🏆 成就徽章",
                                    fontSize = 16.sp,
                                    fontWeight = FontWeight.Bold,
                                    color = textPrimaryColor
                                )
                                
                                Spacer(modifier = Modifier.height(8.dp))
                                
                                // 使用优化后的徽章组件
                                AchievementBadges(
                                    achievements = achievements,
                                    onClick = { achievement ->
                                        // 可以添加徽章点击事件处理
                                    }
                                )
                            }
                        }
                    }
                    
                    // 分析视角选择器
                    item {
                        Column(
                            modifier = Modifier.fillMaxWidth()
                        ) {
                            Text(
                                text = "切换分析视角:",
                                fontSize = 14.sp,
                                color = textSecondaryColor,
                                modifier = Modifier.padding(vertical = 8.dp)
                            )
                            
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalArrangement = Arrangement.spacedBy(16.dp)
                            ) {
                                analysisTypes.forEach { type ->
                                    Row(
                                        verticalAlignment = Alignment.CenterVertically,
                                        modifier = Modifier.clickable { selectedAnalysisType = type }
                                    ) {
                                        RadioButton(
                                            selected = selectedAnalysisType == type,
                                            onClick = { selectedAnalysisType = type },
                                            colors = RadioButtonDefaults.colors(
                                                selectedColor = accentColor
                                            ),
                                            modifier = Modifier.padding(end = 4.dp)
                                        )
                                        Text(
                                            text = type,
                                            fontSize = 14.sp,
                                            color = textPrimaryColor,
                                            maxLines = 1,
                                            overflow = TextOverflow.Visible
                                        )
                                    }
                                }
                            }
                        }
                    }
                    
                    // 分析图表区域
                    item {
                        Card(
                            modifier = Modifier.fillMaxWidth(),
                            shape = RoundedCornerShape(16.dp),
                            colors = CardDefaults.cardColors(containerColor = surfaceColor),
                            elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
                        ) {
                            Column(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(20.dp)
                            ) {
                                val data = when (selectedAnalysisType) {
                                    "时间维度" -> goalsByTimeRange
                                    "领域维度" -> goalsByDomain
                                    "难度维度" -> goalsByDifficulty
                                    else -> emptyMap()
                                }
                                
                                // 根据当前选择的时间范围更新标题
                                val timeRangeTitle = when (selectedTimeRange) {
                                    yearText -> stringResource(R.string.yearly)
                                    quarterText -> stringResource(R.string.quarterly)
                                    monthText -> stringResource(R.string.monthly)
                                    else -> ""
                                }
                                
                                Text(
                                    text = "📊 ${selectedAnalysisType}${timeRangeTitle}${stringResource(R.string.analysis)}",
                                    fontSize = 16.sp,
                                    fontWeight = FontWeight.Bold,
                                    color = textPrimaryColor
                                )
                                
                                Spacer(modifier = Modifier.height(16.dp))
                                
                                // 如果在加载中，显示加载指示器
                                if (uiState is GoalUiState.Loading) {
                                    Box(
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .height(200.dp),
                                        contentAlignment = Alignment.Center
                                    ) {
                                        CircularProgressIndicator(
                                            color = accentColor,
                                            modifier = Modifier.size(48.dp)
                                        )
                                    }
                                } else {
                                    // 根据选择的分析类型展示不同图表
                                    when (selectedAnalysisType) {
                                        "时间维度" -> TimelineDimensionChart(
                                            data = data.map { entry -> entry.key to entry.value },
                                            timeScale = selectedTimeRange,
                                            modifier = Modifier
                                                .fillMaxWidth()
                                                .height(200.dp)
                                        )
                                        "领域维度" -> DomainDimensionChart(
                                            data = data.map { entry -> entry.key to entry.value },
                                            modifier = Modifier
                                                .fillMaxWidth()
                                                .height(200.dp)
                                        )
                                        "难度维度" -> DifficultyDimensionChart(
                                            data = data.map { entry -> entry.key to entry.value },
                                            modifier = Modifier
                                                .fillMaxWidth()
                                                .height(200.dp)
                                        )
                                        else -> {
                                            // 其他维度暂时保留原始占位显示
                                            Box(
                                                modifier = Modifier
                                                    .fillMaxWidth()
                                                    .height(200.dp)
                                                    .background(Color(0xFFF5F5F5), RoundedCornerShape(8.dp))
                                                    .padding(16.dp),
                                                contentAlignment = Alignment.Center
                                            ) {
                                                val displayText = if (data.isEmpty()) {
                                                    "没有${selectedAnalysisType}相关的数据"
                                                } else {
                                                    "此处显示${selectedAnalysisType}图表\n" + 
                                                    data.entries.joinToString("\n") { "${it.key}: ${it.value}" }
                                                }
                                                
                                                Text(
                                                    text = displayText,
                                                    textAlign = TextAlign.Center,
                                                    fontSize = 14.sp,
                                                    color = textSecondaryColor
                                                )
                                            }
                                        }
                                    }
                                }
                                
                                Spacer(modifier = Modifier.height(16.dp))
                                
                                // 洞察分析
                                Text(
                                    text = "🔍 关键洞察",
                                    fontSize = 16.sp,
                                    fontWeight = FontWeight.Bold,
                                    color = textPrimaryColor
                                )
                                
                                Spacer(modifier = Modifier.height(8.dp))
                                
                                // 模拟洞察内容
                                val insights = when (selectedAnalysisType) {
                                    "时间维度" -> listOf(
                                        "Q1季度是目标完成最高效的时期",
                                        "全年保持了稳定的目标完成节奏",
                                        "四季度有赶工趋势，建议均衡规划"
                                    )
                                    "领域维度" -> listOf(
                                        "学习领域目标完成占比最高(42%)",
                                        "健康领域目标坚持度最高(95%)",
                                        "工作领域目标质量评分最高(4.7/5)"
                                    )
                                    "难度维度" -> listOf(
                                        "中等难度目标占比最高(50%)",
                                        "困难目标完成率提升最多(+15%)",
                                        "简单目标平均提前2.5天完成"
                                    )
                                    else -> listOf()
                                }
                                
                                insights.forEach { insight ->
                                    Row(
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .padding(vertical = 4.dp)
                                    ) {
                                        Text(
                                            text = "• $insight",
                                            fontSize = 14.sp,
                                            color = textSecondaryColor
                                        )
                                    }
                                }
                            }
                        }
                    }
                    
                    // 记忆点标记
                    item {
                        Card(
                            modifier = Modifier.fillMaxWidth(),
                            shape = RoundedCornerShape(16.dp),
                            colors = CardDefaults.cardColors(containerColor = surfaceColor),
                            elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
                        ) {
                            Column(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(20.dp)
                            ) {
                                Text(
                                    text = "🌈 标记本年度的3个关键成长瞬间:",
                                    fontSize = 16.sp,
                                    fontWeight = FontWeight.Bold,
                                    color = textPrimaryColor
                                )
                                
                                Spacer(modifier = Modifier.height(16.dp))
                                
                                // 3个输入框
                                growthMoments.forEachIndexed { index, text ->
                                    OutlinedTextField(
                                        value = text,
                                        onValueChange = { 
                                            val newList = growthMoments.toMutableList()
                                            newList[index] = it
                                            growthMoments = newList
                                        },
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .padding(vertical = 8.dp),
                                        placeholder = {
                                            Text("点击输入关键成长瞬间 #${index + 1}")
                                        },
                                        colors = OutlinedTextFieldDefaults.colors(
                                            focusedBorderColor = accentColor,
                                            unfocusedBorderColor = Color(0xFFDDDDDD)
                                        )
                                    )
                                }
                                
                                Spacer(modifier = Modifier.height(16.dp))
                                
                                Button(
                                    onClick = { /* 保存记忆点 */ },
                                    modifier = Modifier.align(Alignment.End),
                                    colors = ButtonDefaults.buttonColors(
                                        containerColor = accentColor
                                    )
                                ) {
                                    Icon(
                                        imageVector = Icons.Default.Save,
                                        contentDescription = null,
                                        modifier = Modifier.size(18.dp)
                                    )
                                    Spacer(modifier = Modifier.width(8.dp))
                                    Text("保存记忆点")
                                }
                            }
                        }
                    }
                    
                    // 底部操作按钮
                    item {
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(vertical = 16.dp),
                            horizontalArrangement = Arrangement.spacedBy(16.dp)
                        ) {
                            OutlinedButton(
                                onClick = { /* 深度分析 */ },
                                modifier = Modifier.weight(1f),
                                border = BorderStroke(1.dp, accentColor)
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Analytics,
                                    contentDescription = null,
                                    tint = accentColor,
                                    modifier = Modifier.size(18.dp)
                                )
                                Spacer(modifier = Modifier.width(8.dp))
                                Text(
                                    "深度分析",
                                    color = accentColor
                                )
                            }
                            
                            Button(
                                onClick = { /* 生成PDF报告 */ },
                                modifier = Modifier.weight(1f),
                                colors = ButtonDefaults.buttonColors(
                                    containerColor = accentColor
                                )
                            ) {
                                Icon(
                                    imageVector = Icons.Default.PictureAsPdf,
                                    contentDescription = null,
                                    modifier = Modifier.size(18.dp)
                                )
                                Spacer(modifier = Modifier.width(8.dp))
                                Text("生成报告")
                            }
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun PerformanceIndicator(
    label: String,
    value: Float,
    color: Color
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = label,
            modifier = Modifier.width(100.dp),
            fontSize = 14.sp,
            color = Color(0xFF666666)
        )
        
        Spacer(modifier = Modifier.width(8.dp))
        
        // 进度条
        Box(
            modifier = Modifier
                .weight(1f)
                .height(8.dp)
                .clip(RoundedCornerShape(4.dp))
                .background(Color(0xFFEEEEEE))
        ) {
            Box(
                modifier = Modifier
                    .fillMaxHeight()
                    .fillMaxWidth(value)
                    .clip(RoundedCornerShape(4.dp))
                    .background(color)
            )
        }
        
        Spacer(modifier = Modifier.width(8.dp))
        
        // 数值
        Text(
            text = "${(value * 100).toInt()}%",
            fontSize = 14.sp,
            fontWeight = FontWeight.Medium,
            color = color,
            modifier = Modifier.width(40.dp)
        )
    }
}

/**
 * 时间维度图表组件 - 增强版
 */
@Composable
fun TimelineDimensionChart(
    data: List<Pair<String, Int>>,
    timeScale: String,
    modifier: Modifier = Modifier
) {
    val primaryColor = Color(0xFF7B66FF)
    val secondaryColor = Color(0xFFB8B5FF)
    val highlightColor = Color(0xFFFF6B6B)
    val textColor = Color(0xFF666666)
    val gridColor = Color(0xFFEEEEEE)
    
    // 根据时间尺度获取相应数据
    val timeScaleData = getTimeScaleData(timeScale)
    
    // 合并时间范围和实际数据
    val displayData = if (data.isEmpty()) {
        // 如果没有数据，使用时间范围默认值
        timeScaleData.rangeValues.map { it to 0 }
    } else {
        // 合并实际数据，确保所有时间范围都有值
        val dataMap = data.toMap()
        timeScaleData.rangeValues.map { key ->
            key to (dataMap[key] ?: 0)
        }
    }
    
    // 找出最大值用于缩放
    val maxValue = displayData.maxOfOrNull { it.second } ?: 0
    val safeMaxValue = if (maxValue == 0) 1 else maxValue
    
    // 添加悬停状态
    var hoveredItemIndex by remember { mutableStateOf(-1) }
    
    Column(modifier = modifier) {
        // 图表标题
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 12.dp, start = 8.dp, end = 8.dp),
            horizontalArrangement = Arrangement.Center,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "${timeScaleData.label}${timeScaleData.displayName}目标完成情况",
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium,
                color = textColor,
                maxLines = 1,
                overflow = TextOverflow.Visible
            )
        }
        
        // 图表区域
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(210.dp)
                .background(Color(0xFFFCFCFC), RoundedCornerShape(12.dp))
                .border(1.dp, gridColor, RoundedCornerShape(12.dp))
        ) {
            // 图表内容区
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(
                        top = 24.dp,
                        bottom = 40.dp,
                        start = 48.dp,
                        end = 16.dp
                    )
            ) {
                Canvas(
                    modifier = Modifier.fillMaxSize()
                ) {
                    val barWidth = if (displayData.isEmpty()) 0f else (size.width - 16.dp.toPx()) / displayData.size
                    val maxHeight = size.height - 30.dp.toPx()
                    
                    // 绘制网格线 - 用虚线美化
                    val gridLineCount = 4
                    for (i in 0..gridLineCount) {
                        val y = size.height - (i * (size.height / gridLineCount))
                        
                        // 绘制虚线网格
                        val dashWidth = 4.dp.toPx()
                        val spaceWidth = 4.dp.toPx()
                        var startX = 0f
                        
                        while (startX < size.width) {
                            drawLine(
                                color = gridColor,
                                start = Offset(startX, y),
                                end = Offset(startX + dashWidth, y),
                                strokeWidth = 1.dp.toPx()
                            )
                            startX += dashWidth + spaceWidth
                        }
                        
                        // Y轴标签
                        val value = ((i * safeMaxValue) / 4)
                        drawContext.canvas.nativeCanvas.apply {
                            drawText(
                                value.toString(),
                                -16.dp.toPx(),
                                y + 4.dp.toPx(),
                                android.graphics.Paint().apply {
                                    textSize = 12.sp.toPx()
                                    color = textColor.toArgb()
                                    textAlign = android.graphics.Paint.Align.RIGHT
                                    isFakeBoldText = false
                                }
                            )
                        }
                    }
                    
                    // 绘制柱状图和连接线
                    displayData.forEachIndexed { index, (label, value) ->
                        val barHeight = (value.toFloat() / safeMaxValue) * maxHeight
                        val x = index * barWidth + barWidth * 0.2f
                        val y = size.height - barHeight
                        val isHighlighted = index == hoveredItemIndex
                        
                        // 绘制连接曲线（在前几个和后几个点之间）
                        if (index > 0 && index < displayData.size) {
                            val prevBarHeight = (displayData[index - 1].second.toFloat() / safeMaxValue) * maxHeight
                            val prevX = (index - 1) * barWidth + barWidth * 0.2f
                            val prevY = size.height - prevBarHeight
                            
                            // 绘制曲线连接线
                            drawLine(
                                color = secondaryColor.copy(alpha = 0.5f),
                                start = Offset(prevX + barWidth * 0.3f, prevY),
                                end = Offset(x + barWidth * 0.3f, y),
                                strokeWidth = 2.dp.toPx(),
                                cap = StrokeCap.Round
                            )
                        }
                        
                        // 计算柱状图颜色 - 添加渐变效果
                        val barColor = if (isHighlighted) highlightColor else primaryColor
                        
                        // 绘制柱状 - 添加圆角
                        val cornerRadius = 4.dp.toPx()
                        
                        // 绘制柱状图底座 - 增加3D效果
                        if (barHeight > 0) {
                            // 底部投影
                            drawRoundRect(
                                color = barColor.copy(alpha = 0.3f),
                                topLeft = Offset(x, size.height - 2.dp.toPx()),
                                size = Size(barWidth * 0.6f, 2.dp.toPx()),
                                cornerRadius = CornerRadius(cornerRadius, cornerRadius)
                            )
                        }
                        
                        // 主柱状图
                        drawRoundRect(
                            color = barColor.copy(alpha = if (isHighlighted) 1f else 0.8f),
                            topLeft = Offset(x, y),
                            size = Size(barWidth * 0.6f, barHeight),
                            cornerRadius = CornerRadius(cornerRadius, cornerRadius)
                        )
                        
                        // 柱状图上方高光 - 增强3D效果
                        drawRoundRect(
                            color = Color.White.copy(alpha = 0.3f),
                            topLeft = Offset(x, y),
                            size = Size(barWidth * 0.6f, 4.dp.toPx()),
                            cornerRadius = CornerRadius(cornerRadius, cornerRadius)
                        )
                        
                        // 顶部圆点标记
                        drawCircle(
                            color = if (isHighlighted) highlightColor else primaryColor,
                            radius = if (isHighlighted) 6.dp.toPx() else 4.dp.toPx(),
                            center = Offset(x + barWidth * 0.3f, y)
                        )
                        
                        // 添加白色边框
                        drawCircle(
                            color = Color.White,
                            radius = if (isHighlighted) 6.dp.toPx() else 4.dp.toPx(),
                            center = Offset(x + barWidth * 0.3f, y),
                            style = Stroke(width = 1.5f.dp.toPx())
                        )
                        
                        // 数值标签 - 在柱子顶部显示实际值
                        if (value > 0) {
                            val textColor = if (isHighlighted) highlightColor else primaryColor
                            drawContext.canvas.nativeCanvas.apply {
                                drawText(
                                    value.toString(),
                                    x + barWidth * 0.3f,
                                    y - 12.dp.toPx(),
                                    android.graphics.Paint().apply {
                                        textSize = 12.sp.toPx()
                                        color = textColor.toArgb()
                                        textAlign = android.graphics.Paint.Align.CENTER
                                        isFakeBoldText = true
                                        setShadowLayer(2f, 0f, 0f, Color.White.toArgb())
                                    }
                                )
                            }
                        }
                        
                        // X轴标签
                        val xLabelColor = if (isHighlighted) primaryColor else textColor
                        drawContext.canvas.nativeCanvas.apply {
                            drawText(
                                label,
                                x + barWidth * 0.3f,
                                size.height + 20.dp.toPx(),
                                android.graphics.Paint().apply {
                                    textSize = 12.sp.toPx()
                                    color = xLabelColor.toArgb()
                                    textAlign = android.graphics.Paint.Align.CENTER
                                    isFakeBoldText = isHighlighted
                                }
                            )
                        }
                    }
                    
                    // 添加更明显的坐标轴
                    drawLine(
                        color = Color(0xFF999999),
                        start = Offset(0f, size.height),
                        end = Offset(size.width, size.height),
                        strokeWidth = 1.5f.dp.toPx()
                    )
                    
                    drawLine(
                        color = Color(0xFF999999),
                        start = Offset(0f, 0f),
                        end = Offset(0f, size.height),
                        strokeWidth = 1.5f.dp.toPx()
                    )
                }
            }
            
            // 添加交互区域 - 修复版
            Row(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(
                        top = 24.dp,
                        bottom = 40.dp,
                        start = 48.dp,
                        end = 16.dp
                    ),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                displayData.forEachIndexed { index, _ ->
                    Box(
                        modifier = Modifier
                            .fillMaxHeight()
                            .weight(1f)
                            .pointerInput(Unit) {
                                detectTapGestures(
                                    onPress = { 
                                        hoveredItemIndex = index
                                        awaitRelease()
                                        hoveredItemIndex = -1
                                    }
                                )
                            }
                    )
                }
            }
        }
        
        // 添加图例
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 8.dp),
            horizontalArrangement = Arrangement.Center,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Box(
                modifier = Modifier
                    .size(8.dp)
                    .background(primaryColor, CircleShape)
            )
            Spacer(modifier = Modifier.width(4.dp))
            Text(
                text = "目标数量",
                fontSize = 12.sp,
                color = textColor
            )
            
            Spacer(modifier = Modifier.width(16.dp))
            
            Box(
                modifier = Modifier
                    .size(8.dp)
                    .background(secondaryColor, CircleShape)
            )
            Spacer(modifier = Modifier.width(4.dp))
            Text(
                text = "趋势线",
                fontSize = 12.sp,
                color = textColor
            )
            
            Spacer(modifier = Modifier.width(16.dp))
            
            Box(
                modifier = Modifier
                    .size(8.dp)
                    .background(highlightColor, CircleShape)
            )
            Spacer(modifier = Modifier.width(4.dp))
            Text(
                text = "点击高亮",
                fontSize = 12.sp,
                color = textColor
            )
        }
    }
}

/**
 * 领域维度图表组件 - 饼图实现
 */
@Composable
fun DomainDimensionChart(
    data: List<Pair<String, Int>>,
    modifier: Modifier = Modifier
) {
    val domainColors = mapOf(
        "学习" to Color(0xFF42A5F5),  // 蓝色
        "工作" to Color(0xFFFF7043),  // 橙色
        "健康" to Color(0xFF66BB6A),  // 绿色
        "个人" to Color(0xFF9C27B0),  // 紫色
        "家庭" to Color(0xFFFFCA28),  // 黄色
        "财务" to Color(0xFF78909C),  // 蓝灰色
        "社交" to Color(0xFFEC407A),  // 粉色
        "爱好" to Color(0xFF8D6E63)   // 棕色
    )
    
    // 如果没有数据，显示空态
    if (data.isEmpty()) {
        Box(
            modifier = modifier
                .background(Color(0xFFF5F5F5), RoundedCornerShape(8.dp)),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = stringResource(R.string.no_domain_data),
                fontSize = 14.sp,
                color = Color(0xFF999999)
            )
        }
        return
    }
    
    // 计算总数
    val total = data.sumOf { it.second }
    
    Column(modifier = modifier) {
        // 饼图区域
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(180.dp)
                .background(Color(0xFFF5F5F5), RoundedCornerShape(8.dp))
                .padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxSize(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 左侧饼图
                Box(
                    modifier = Modifier
                        .size(140.dp)
                        .padding(8.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Canvas(modifier = Modifier.fillMaxSize()) {
                        var startAngle = 0f
                        
                        data.forEach { (domain, count) ->
                            val sweepAngle = 360f * count / total
                            val color = domainColors[domain] ?: Color(0xFF9E9E9E)
                            
                            drawArc(
                                color = color,
                                startAngle = startAngle,
                                sweepAngle = sweepAngle,
                                useCenter = true,
                                topLeft = Offset(16.dp.toPx(), 16.dp.toPx()),
                                size = Size(size.width - 32.dp.toPx(), size.height - 32.dp.toPx())
                            )
                            
                            startAngle += sweepAngle
                        }
                    }
                    
                    // 中心文字
                    Text(
                        text = "${total}个",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color(0xFF333333)
                    )
                }
                
                // 右侧图例
                Column(
                    modifier = Modifier
                        .weight(1f)
                        .padding(start = 16.dp)
                ) {
                    data.forEach { (domain, count) ->
                        val color = domainColors[domain] ?: Color(0xFF9E9E9E)
                        val percentage = (count * 100f / total).toInt()
                        
                        Row(
                            modifier = Modifier.padding(vertical = 4.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            // 颜色方块
                            Box(
                                modifier = Modifier
                                    .size(12.dp)
                                    .background(color, RoundedCornerShape(2.dp))
                            )
                            
                            Spacer(modifier = Modifier.width(8.dp))
                            
                            // 领域名称
                            Text(
                                text = domain,
                                fontSize = 12.sp,
                                color = Color(0xFF666666),
                                modifier = Modifier.weight(1f)
                            )
                            
                            // 数量
                            Text(
                                text = "$count ($percentage%)",
                                fontSize = 12.sp,
                                fontWeight = FontWeight.Medium,
                                color = Color(0xFF333333)
                            )
                        }
                    }
                }
            }
        }
        
        // 图表标题
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 8.dp, start = 8.dp, end = 8.dp),
            horizontalArrangement = Arrangement.Center
        ) {
            Text(
                text = "目标领域分布",
                fontSize = 12.sp,
                color = Color(0xFF666666),
                maxLines = 1,
                overflow = TextOverflow.Visible
            )
        }
    }
}

/**
 * 难度维度图表组件 - 水平条形图实现
 */
@Composable
fun DifficultyDimensionChart(
    data: List<Pair<String, Int>>,
    modifier: Modifier = Modifier
) {
    val difficultyColors = mapOf(
        "简单" to Color(0xFF4CAF50),  // 绿色
        "一般" to Color(0xFF2196F3),  // 蓝色
        "中等" to Color(0xFFFFC107),  // 黄色
        "困难" to Color(0xFFFF9800),  // 橙色
        "极难" to Color(0xFFF44336)   // 红色
    )
    
    // 难度的固定顺序
    val difficultyOrder = listOf("简单", "一般", "中等", "困难", "极难")
    
    // 按难度排序后的数据
    val sortedData = if (data.isEmpty()) {
        emptyList()
    } else {
        // 创建一个包含所有难度级别的Map，确保显示所有难度级别
        val dataMap = data.toMap().toMutableMap()
        difficultyOrder.forEach { difficulty ->
            if (!dataMap.containsKey(difficulty)) {
                dataMap[difficulty] = 0
            }
        }
        // 按难度顺序排序
        difficultyOrder.filter { dataMap.containsKey(it) }.map { it to (dataMap[it] ?: 0) }
    }
    
    // 模拟的完成率数据 (实际应用中应从 ViewModel 获取)
    val completionRates = mapOf(
        "简单" to 0.95f,
        "一般" to 0.85f,
        "中等" to 0.75f,
        "困难" to 0.65f,
        "极难" to 0.40f
    )
    
    // 如果没有数据，显示空态
    if (sortedData.isEmpty()) {
        Box(
            modifier = modifier
                .background(Color(0xFFF5F5F5), RoundedCornerShape(8.dp)),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = stringResource(R.string.no_difficulty_data),
                fontSize = 14.sp,
                color = Color(0xFF999999)
            )
        }
        return
    }
    
    // 找出最大值以便缩放图表
    val maxValue = sortedData.maxOfOrNull { it.second } ?: 0
    val safeMaxValue = if (maxValue == 0) 1 else maxValue
    
    Column(modifier = modifier) {
        // 图表区域
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(180.dp)
                .background(Color(0xFFF5F5F5), RoundedCornerShape(8.dp))
                .padding(
                    top = 16.dp,
                    bottom = 16.dp,
                    start = 16.dp,
                    end = 32.dp
                )
        ) {
            Column(
                modifier = Modifier.fillMaxSize(),
                verticalArrangement = Arrangement.SpaceEvenly
            ) {
                sortedData.forEach { (difficulty, count) ->
                    val color = difficultyColors[difficulty] ?: Color(0xFF9E9E9E)
                    val completionRate = completionRates[difficulty] ?: 0f
                    val barWidth = (count.toFloat() / safeMaxValue) * 0.7f // 最大宽度为70%
                    
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(24.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        // 难度标签
                        Text(
                            text = difficulty,
                            fontSize = 12.sp,
                            fontWeight = FontWeight.Medium,
                            color = Color(0xFF555555),
                            modifier = Modifier.width(40.dp)
                        )
                        
                        Spacer(modifier = Modifier.width(8.dp))
                        
                        // 条形图容器
                        Box(
                            modifier = Modifier
                                .weight(1f)
                                .height(16.dp)
                                .clip(RoundedCornerShape(8.dp))
                                .background(Color(0xFFEEEEEE))
                        ) {
                            // 数量条
                            Box(
                                modifier = Modifier
                                    .fillMaxHeight()
                                    .fillMaxWidth(barWidth)
                                    .clip(RoundedCornerShape(8.dp))
                                    .background(color.copy(alpha = 0.7f))
                            )
                            
                            // 完成率指示
                            Box(
                                modifier = Modifier
                                    .fillMaxHeight()
                                    .fillMaxWidth(completionRate)
                                    .clip(RoundedCornerShape(8.dp))
                                    .background(color)
                                    .align(Alignment.CenterStart)
                            )
                        }
                    }
                }
            }
            
            // 图例
            Row(
                modifier = Modifier
                    .align(Alignment.BottomEnd)
                    .padding(top = 8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Box(
                    modifier = Modifier
                        .size(8.dp)
                        .background(Color(0xFF666666), RoundedCornerShape(1.dp))
                )
                Spacer(modifier = Modifier.width(4.dp))
                Text(
                    text = "目标数量",
                    fontSize = 10.sp,
                    color = Color(0xFF666666)
                )
                
                Spacer(modifier = Modifier.width(12.dp))
                
                Box(
                    modifier = Modifier
                        .size(8.dp)
                        .background(Color(0xFF333333), RoundedCornerShape(1.dp))
                )
                Spacer(modifier = Modifier.width(4.dp))
                Text(
                    text = "完成率",
                    fontSize = 10.sp,
                    color = Color(0xFF666666)
                )
            }
        }
        
        // 图表标题
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 8.dp, start = 8.dp, end = 8.dp),
            horizontalArrangement = Arrangement.Center
        ) {
            Text(
                text = stringResource(R.string.goal_difficulty_distribution_completion_rate),
                fontSize = 12.sp,
                color = Color(0xFF666666),
                maxLines = 1,
                overflow = TextOverflow.Visible
            )
        }
    }
}

/**
 * 徽章显示组件 - 优化版
 */
@Composable
fun AchievementBadges(
    achievements: List<String>,
    onClick: (String) -> Unit = {}
) {
    val perseveranceText = stringResource(R.string.achievement_perseverance)
    val efficiencyText = stringResource(R.string.achievement_efficiency)
    val breakthroughText = stringResource(R.string.achievement_breakthrough)
    val focusText = stringResource(R.string.achievement_focus)
    val balanceText = stringResource(R.string.achievement_balance)

    val badgeColors = mapOf(
        perseveranceText to Color(0xFF9C27B0),
        efficiencyText to Color(0xFF2196F3),
        breakthroughText to Color(0xFFFF9800),
        focusText to Color(0xFF4CAF50),
        balanceText to Color(0xFF795548)
    )
    
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp),
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        achievements.forEach { achievement ->
            val badgeColor = badgeColors[achievement] ?: Color(0xFF7B66FF)
            
            Surface(
                modifier = Modifier
                    .clip(RoundedCornerShape(16.dp))
                    .clickable { onClick(achievement) },
                color = badgeColor.copy(alpha = 0.15f)
            ) {
                Row(
                    modifier = Modifier.padding(horizontal = 12.dp, vertical = 6.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.EmojiEvents,
                        contentDescription = null,
                        tint = badgeColor,
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(
                        text = achievement,
                        fontSize = 12.sp,
                        color = badgeColor,
                        fontWeight = FontWeight.Medium
                    )
                }
            }
        }
    }
}

/**
 * 时间尺度数据类
 */
data class TimeScaleData(
    val label: String,
    val displayName: String,
    val rangeValues: List<String>
)

/**
 * 根据当前选择的时间尺度生成时间范围值
 */
fun getTimeScaleData(timeScale: String): TimeScaleData {
    val currentDateTime = LocalDateTime.now()
    val currentYear = currentDateTime.year
    val currentMonth = currentDateTime.monthValue
    
    return when (timeScale) {
        "年" -> {
            TimeScaleData(
                label = "${currentYear}年",
                displayName = "年度",
                rangeValues = listOf("Q1", "Q2", "Q3", "Q4")
            )
        }
        "季度" -> {
            val currentQuarter = (currentMonth - 1) / 3 + 1
            
            // 根据季度获取对应的月份
            val months = when (currentQuarter) {
                1 -> listOf("1月", "2月", "3月")
                2 -> listOf("4月", "5月", "6月")
                3 -> listOf("7月", "8月", "9月")
                else -> listOf("10月", "11月", "12月")
            }
            
            TimeScaleData(
                label = "${currentYear}年Q$currentQuarter",
                displayName = "季度",
                rangeValues = months
            )
        }
        "月" -> {
            // 月度视图固定使用4周
            val weeks = (1..4).map { weekNum ->
                "第${weekNum}周"
            }
            
            TimeScaleData(
                label = "${currentYear}年${currentMonth}月",
                displayName = "月度",
                rangeValues = weeks
            )
        }
        else -> TimeScaleData("", "", emptyList())
    }
} 