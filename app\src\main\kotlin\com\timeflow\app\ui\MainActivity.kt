package com.timeflow.app.ui

import android.content.ComponentCallbacks2
import android.content.Intent
import android.os.Bundle
import android.util.Log
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.runtime.staticCompositionLocalOf
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.core.view.WindowCompat
import kotlinx.coroutines.delay
import androidx.navigation.compose.currentBackStackEntryAsState
import androidx.navigation.compose.rememberNavController
import com.google.accompanist.systemuicontroller.rememberSystemUiController
import com.timeflow.app.ui.TimeFlowApp
import com.timeflow.app.ui.theme.AppTheme
import com.timeflow.app.ui.theme.AppBackground
import com.timeflow.app.utils.RenderUtils
import com.timeflow.app.data.db.AppDatabase
import com.timeflow.app.utils.SampleDataGenerator
import dagger.hilt.android.AndroidEntryPoint
import androidx.metrics.performance.JankStats
import androidx.compose.runtime.CompositionLocalProvider
import androidx.metrics.performance.PerformanceMetricsState
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import javax.inject.Inject
import androidx.lifecycle.lifecycleScope
import java.time.LocalDateTime
import java.util.UUID
import com.timeflow.app.utils.SafeParcelHelper
import com.timeflow.app.utils.applyAllOptimizations
import com.timeflow.app.utils.configureEmergencyRecovery
import com.timeflow.app.utils.ImageLoader
import com.timeflow.app.utils.RenderOptimizer
import android.view.WindowManager
import android.os.Build
import com.timeflow.app.utils.HwcLutsErrorFixer
import android.view.View
import android.widget.Toast
import timber.log.Timber
import android.graphics.PixelFormat
import android.graphics.Color
import android.view.Choreographer
import android.content.Context
import android.content.res.Configuration
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.core.view.WindowInsetsControllerCompat
import androidx.compose.ui.graphics.Color as ComposeColor
import android.view.ViewGroup
import android.os.Handler
import android.os.Looper
import com.timeflow.app.utils.BinderTransactionHelper
import com.timeflow.app.utils.PerformanceMonitor
import com.timeflow.app.ui.theme.TimeFlowTheme
import androidx.compose.ui.platform.LocalView
import androidx.core.view.ViewCompat
import android.view.WindowInsets
import android.app.Activity
import androidx.compose.foundation.layout.systemBarsPadding
import androidx.core.graphics.ColorUtils
import android.util.LayoutDirection
import com.timeflow.app.utils.SystemBarManager
import com.timeflow.app.R
import androidx.compose.runtime.LaunchedEffect
import com.timeflow.app.ui.theme.ThemeManager
import com.timeflow.app.ui.language.LanguageManager
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.activity.OnBackPressedCallback
import android.window.OnBackInvokedDispatcher
import android.window.OnBackInvokedCallback
import androidx.navigation.NavHostController
import com.timeflow.app.utils.NotificationPermissionHelper
import com.timeflow.app.utils.NotificationPermissionDialog
import com.timeflow.app.utils.rememberNotificationPermission
import com.timeflow.app.di.DataStoreModule.ThemeDataStore

// 定义MainActivity的TAG常量
private const val MAIN_ACTIVITY_TAG = "MainActivity"

// 创建一个性能监控的CompositionLocal
val LocalJankStatsMonitor = staticCompositionLocalOf<JankStats.OnFrameListener?> { null }

/**
 * 主Activity
 * 增加了JankStats性能监控和渲染优化
 */
@AndroidEntryPoint
class MainActivity : ComponentActivity() {
    companion object {
        private const val EXTRA_IS_CRASH_RESTART = "is_crash_restart"
    }

    private var jankStats: JankStats? = null
    private var currentFrameTime = 0L
    private var frameCount = 0
    private var jankFrameCount = 0
    
    // 注入数据库
    @Inject
    lateinit var appDatabase: AppDatabase
    
    // 注入示例数据生成器
    @Inject
    lateinit var sampleDataGenerator: SampleDataGenerator
    
    // 在类的开始部分添加日志监控相关变量
    private val TAG = "MainActivity"
    private var logMonitorThread: Thread? = null
    private var isLogMonitorRunning = false
    
    @Inject
    lateinit var renderOptimizer: RenderOptimizer
    
    @Inject
    lateinit var systemBarManager: SystemBarManager
    
    @Inject
    @ThemeDataStore
    lateinit var themeDataStore: DataStore<Preferences>
    
    // 添加共享NavController引用
    private var navController: androidx.navigation.NavHostController? = null
    // 添加双击返回退出应用的时间记录
    private var lastBackPressTime: Long = 0
    
    @Inject
    lateinit var notificationPermissionHelper: NotificationPermissionHelper
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 设置全局异常处理器
        setupGlobalExceptionHandler()
        
        // 检查通知权限
        checkNotificationPermission()
        
        // 添加新的返回处理API支持
        if (android.os.Build.VERSION.SDK_INT >= 33) {
            try {
                val onBackInvokedDispatcher = if (android.os.Build.VERSION.SDK_INT >= 33) {
                    onBackInvokedDispatcher
                } else null
                
                onBackInvokedDispatcher?.registerOnBackInvokedCallback(
                    0, // PRIORITY_DEFAULT equivalent
                    object : android.window.OnBackInvokedCallback {
                        override fun onBackInvoked() {
                            handleBackPress()
                        }
                    }
                )
            } catch (e: Exception) {
                Log.e(TAG, "Failed to register OnBackInvokedCallback: ${e.message}")
            }
        }
        
        // 应用渲染优化
        applyAllOptimizations() // 恢复优化
        
        // 配置系统栏
        SystemBarManager.setupActivitySystemBars(this) // 恢复系统栏管理
        
        // 初始化语言管理器
        LanguageManager.initialize(applicationContext, themeDataStore)

        // 初始化主题管理器
        com.timeflow.app.ui.theme.ThemeManager.initialize(applicationContext, themeDataStore)
        
        // 采用完全沉浸式模式，确保内容可以延伸到状态栏区域
        window.setFlags(
            WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS,
            WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS
        )
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            window.attributes.layoutInDisplayCutoutMode = 
                WindowManager.LayoutParams.LAYOUT_IN_DISPLAY_CUTOUT_MODE_SHORT_EDGES
        }
        
        // 强制设置状态栏完全透明
        window.statusBarColor = android.graphics.Color.TRANSPARENT
        window.navigationBarColor = android.graphics.Color.TRANSPARENT
        
        // 将窗口延伸到系统栏区域 (KEEP THIS for Compose insets)
        WindowCompat.setDecorFitsSystemWindows(window, false)
        
        // 设置Compose内容
        setContent {
            val navControllerInstance = rememberNavController()
            // 保存NavController引用以便在Activity级别使用
            navController = navControllerInstance
            
            // 通知权限管理
            val permissionState = rememberNotificationPermission(notificationPermissionHelper)
            
            NotificationPermissionDialog(
                showDialog = permissionState.showDialog,
                onDismiss = permissionState.dismissDialog,
                onConfirm = permissionState.requestPermission,
                onOpenSettings = permissionState.openSettings
            )
            
            // 初始化UI组件后安全设置系统栏行为
            DisposableEffect(Unit) {
                // 在Compose完全初始化后设置系统栏行为
                try {
                    // 获取当前是否为深色模式
                    val isDarkTheme = isNightMode(this@MainActivity)
                    
                    // 使用SystemBarManager设置状态栏颜色
                    SystemBarManager.setStatusBarColorByTheme(
                        activity = this@MainActivity,
                        isDarkTheme = isDarkTheme
                    )
                    
                    // 设置系统栏行为
                    val controller = WindowInsetsControllerCompat(window, window.decorView)
                    controller.systemBarsBehavior = WindowInsetsControllerCompat.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
                } catch (e: Exception) {
                    Log.e(MAIN_ACTIVITY_TAG, "设置系统栏行为失败: ${e.message}")
                }
                
                onDispose {}
            }
            
            // 在首次组合后异步执行渲染优化初始化
            LaunchedEffect(Unit) {
                if (!isFinishing && !isDestroyed) {
                     try {
                        initializeRenderingOptimizations() // 恢复初始化渲染优化
                     } catch (e: Exception) {
                         Log.e(MAIN_ACTIVITY_TAG, "执行 initializeRenderingOptimizations 失败: ${e.message}")
                     }
                }
            }

            // 完全沉浸式内容布局 - 使用一致的背景色覆盖整个屏幕
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    // 确保背景颜色应用到整个屏幕
                    .background(AppBackground) // Keep background
            ) {
                TimeFlowAppContent(navController = navControllerInstance)
            }
        }
        
        // 检查是否为紧急重启
        val isEmergencyRestart = SafeParcelHelper.checkEmergencyRestartIntent(intent)
        if (isEmergencyRestart) {
            Log.w(MAIN_ACTIVITY_TAG, "检测到应用紧急重启，应用特殊稳定模式")
            // 应用额外的稳定性措施
            window.setFlags(0, WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED)
        }
        
        // 检查是否是崩溃后重启
        if (intent.hasExtra(EXTRA_IS_CRASH_RESTART)) {
            Timber.w("应用程序在崩溃后重启")
            Toast.makeText(this, "应用程序从错误中恢复", Toast.LENGTH_SHORT).show()
        }

        // 处理来自小组件的操作
        handleWidgetActions(intent)
        
        // 监控HwcComposer错误并设置窗口优化
        // setupHwcErrorMonitoring() // 移除启动时调用
        
        // 应用RenderOptimizer窗口优化
        // RenderOptimizer.setupWindowOptimizations(this) // 移除启动时调用
        
        // 检测HWC能力并应用特殊渲染措施
        // checkHwcCapabilityAndApplyFix() // 移除启动时调用，避免 StrictMode 违规
        
        // 捕获并处理timerslack错误
        try {
            android.os.Process.setThreadPriority(android.os.Process.THREAD_PRIORITY_DISPLAY)
        } catch (e: Exception) {
            Log.w(MAIN_ACTIVITY_TAG, "设置线程优先级失败: ${e.message}")
        }
        
        // 初始化性能相关属性
        frameCount = 0
        jankFrameCount = 0
        
        // 应用所有优化设置
        // applyAllOptimizations() // 移除启动时调用
        
        // 配置系统崩溃时的紧急恢复
        // configureEmergencyRecovery(this) // 移除启动时调用
        
        // 添加渲染错误检测
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            // setupRenderWatchdog() // 移除启动时调用
        }
        
        // 注册系统内存监听
        registerComponentCallbacks(object : ComponentCallbacks2 {
            override fun onTrimMemory(level: Int) {
                when (level) {
                    ComponentCallbacks2.TRIM_MEMORY_RUNNING_CRITICAL,
                    ComponentCallbacks2.TRIM_MEMORY_COMPLETE -> {
                        Log.w("MainActivity", "内存严重不足，执行紧急清理")
                        // 紧急清理内存
                        performEmergencyCleanup()
                    }
                    else -> {
                        // 其他内存状态处理
                    }
                }
            }
            
            override fun onConfigurationChanged(newConfig: android.content.res.Configuration) {
                // 处理配置变更
            }
            
            override fun onLowMemory() {
                Log.w("MainActivity", "系统内存不足，清理缓存")
                // 清理缓存
                performMemoryCleanup()
            }
        })
        
        // 设置JankStats监控
        setupJankMonitoring()
        
        // 启动日志监控
        // startErrorLogMonitoring() // 移除启动时调用
    }
    
    override fun onResume() {
        super.onResume()
        RenderOptimizer.resetOptimizerState()
        
        // 重置HwcLutsErrorFixer状态
        HwcLutsErrorFixer.resetFixState()
        
        // Safely access jankStats only if initialized
        jankStats?.let { stats ->
            stats.isTrackingEnabled = true
        }
    }
    
    override fun onPause() {
        super.onPause()
        
        // 使用SystemBarManager重置系统栏状态
        com.timeflow.app.utils.SystemBarManager.resetActivitySystemBars(this)
        
        // Safely access jankStats only if initialized
        jankStats?.let { stats ->
            stats.isTrackingEnabled = false
        }
        
        // 记录会话性能数据
        if (frameCount > 0) {
            val jankRate = jankFrameCount * 100 / frameCount
            Log.d(MAIN_ACTIVITY_TAG, "会话性能统计: 总帧数=$frameCount, 卡顿帧数=$jankFrameCount, 卡顿率=$jankRate%")
        }
    }
    
    /**
     * 设置全局异常处理
     * 捕获未处理的异常并尝试恢复
     */
    private fun setupUncaughtExceptionHandler() {
        val defaultHandler = Thread.getDefaultUncaughtExceptionHandler()
        
        Thread.setDefaultUncaughtExceptionHandler { thread, throwable ->
            Log.e("MainActivity", "未捕获的异常: ${throwable.message}", throwable)
            
            try {
                // 尝试紧急恢复
                runOnUiThread {
                    try {
                        configureEmergencyRecovery(this@MainActivity)
                    } catch (e: Exception) {
                        Log.e("MainActivity", "恢复失败: ${e.message}")
                    }
                }
                
                // 延迟一秒后再传给默认处理器
                Thread.sleep(1000)
            } catch (e: Exception) {
                Log.e("MainActivity", "异常处理失败: ${e.message}")
            }
            
            // 如果恢复失败，交给默认处理器
            defaultHandler?.uncaughtException(thread, throwable)
        }
    }
    
    /**
     * 处理Intent安全问题
     */
    override fun onNewIntent(intent: Intent?) {
        // 安全处理新的Intent，防止崩溃
        val safeIntent = intent ?: return
        try {
            // 创建完全安全的Intent副本
            val secureIntent = SafeParcelHelper.getSafeIntent(safeIntent)
            
            // 检查是否为紧急重启Intent
            if (SafeParcelHelper.checkEmergencyRestartIntent(secureIntent)) {
                Log.w(MAIN_ACTIVITY_TAG, "收到紧急重启Intent")
                // 应用额外的稳定性措施
                HwcLutsErrorFixer.applyBasicFix(window)
            }
            
            // 获取安全的extras
            val extras = SafeParcelHelper.safeGetIntentExtras(secureIntent)

            // 处理来自小组件的操作
            handleWidgetActions(secureIntent)

            // 使用安全Intent
            super.onNewIntent(secureIntent)
        } catch (e: Exception) {
            Log.e(MAIN_ACTIVITY_TAG, "处理新Intent时出错: ${e.message}")
            super.onNewIntent(Intent())  // 使用空Intent防止崩溃
        }
    }

    /**
     * 处理来自小组件的操作
     */
    private fun handleWidgetActions(intent: Intent) {
        try {
            val navigateTo = intent.getStringExtra("navigate_to")
            val action = intent.getStringExtra("action")

            Log.d(MAIN_ACTIVITY_TAG, "处理小组件操作: navigateTo=$navigateTo, action=$action")

            // 如果需要导航到时间追踪页面
            if (navigateTo == "timer") {
                // 延迟执行导航，确保Activity完全初始化
                lifecycleScope.launch {
                    delay(500) // 等待500ms确保UI完全加载

                    try {
                        navController?.navigate("time_tracking") {
                            // 清除回退栈，直接跳转到时间追踪页面
                            popUpTo("home") { inclusive = false }
                            launchSingleTop = true
                        }

                        // 处理具体的计时器操作
                        when (action) {
                            "start_timer" -> {
                                Log.d(MAIN_ACTIVITY_TAG, "小组件请求启动计时器")
                                // 这里可以通过ViewModel或其他方式启动计时器
                            }
                            "pause_timer" -> {
                                Log.d(MAIN_ACTIVITY_TAG, "小组件请求暂停计时器")
                                // 这里可以通过ViewModel或其他方式暂停计时器
                            }
                            "resume_timer" -> {
                                Log.d(MAIN_ACTIVITY_TAG, "小组件请求恢复计时器")
                                // 这里可以通过ViewModel或其他方式恢复计时器
                            }
                            "stop_timer" -> {
                                Log.d(MAIN_ACTIVITY_TAG, "小组件请求停止计时器")
                                // 这里可以通过ViewModel或其他方式停止计时器
                            }
                            "toggle_timer" -> {
                                Log.d(MAIN_ACTIVITY_TAG, "小组件请求切换计时器状态")
                                // 这里可以通过ViewModel或其他方式切换计时器状态
                            }
                        }
                    } catch (e: Exception) {
                        Log.e(MAIN_ACTIVITY_TAG, "处理小组件导航失败: ${e.message}")
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(MAIN_ACTIVITY_TAG, "处理小组件操作失败: ${e.message}")
        }
    }

    /**
     * 设置渲染监视器，在发生渲染问题时自动恢复
     */
    private fun setupRenderWatchdog() {
        // 创建渲染监控线程
        Thread {
            try {
                while (!isFinishing) {
                    // 检查是否有UI挂起的迹象
                    val startTime = System.currentTimeMillis()
                    var result = false
                    
                    // 在主线程上尝试获取视图状态，检测主线程是否阻塞
                    runOnUiThread {
                        result = true
                    }
                    
                    // 等待短暂时间看主线程是否响应
                    Thread.sleep(500)
                    
                    // 如果5秒后主线程还没响应，可能存在UI卡死
                    if (!result && System.currentTimeMillis() - startTime > 5000) {
                        Log.e(MAIN_ACTIVITY_TAG, "检测到UI渲染卡死，尝试恢复")
                        
                        // 尝试在主线程应用紧急修复
                        runOnUiThread {
                            try {
                                // 应用HwcLuts错误修复
                                HwcLutsErrorFixer.applyCriticalModeFix(window)
                            } catch (e: Exception) {
                                Log.e(MAIN_ACTIVITY_TAG, "应用紧急修复失败: ${e.message}")
                            }
                        }
                    }
                    
                    // 每5秒检查一次
                    Thread.sleep(5000)
                }
            } catch (e: Exception) {
                Log.e(MAIN_ACTIVITY_TAG, "渲染监视器异常: ${e.message}")
            }
        }.apply {
            isDaemon = true
            name = "render-watchdog"
            priority = Thread.MIN_PRIORITY
            start()
        }
    }
    
    /**
     * 执行紧急内存清理
     */
    private fun performEmergencyCleanup() {
        // 清理可能导致泄漏的引用
        Runtime.getRuntime().gc()
        // 清理图片缓存
        ImageLoader.getInstance().clearMemoryCache()
        // 通知系统进行内存回收
        Runtime.getRuntime().runFinalization()
    }
    
    /**
     * 执行常规内存清理
     */
    private fun performMemoryCleanup() {
        // 清理不必要的缓存
        ImageLoader.getInstance().clearMemoryCache()
    }
    
    /**
     * 监控和管理内存
     */
    override fun onTrimMemory(level: Int) {
        super.onTrimMemory(level)
        
        // 当内存不足时，主动释放资源
        if (level >= ComponentCallbacks2.TRIM_MEMORY_RUNNING_LOW) {
            Log.w("MainActivity", "内存不足，正在释放资源")
            // 清理缓存和未使用的资源
            Runtime.getRuntime().gc()
        }
    }
    
    /**
     * 防止窗口泄漏
     */
    override fun onDestroy() {
        try {
            // 清理可能导致泄漏的引用
            window?.callback = null
            
            // 取消所有挂起的操作
            
        } catch (e: Exception) {
            Log.e("MainActivity", "销毁时出错: ${e.message}")
        } finally {
            super.onDestroy()
        }
        
        // 停止日志监控
        isLogMonitorRunning = false
        logMonitorThread?.interrupt()
        logMonitorThread = null
    }
    
    /**
     * 初始化数据库数据
     */
    private suspend fun initializeDatabaseData() {
        try {
            // 检查看板列是否存在
            val columnCount = appDatabase.kanbanColumnDao().getColumnCount()
            Log.d(MAIN_ACTIVITY_TAG, "检查看板列数量: $columnCount")
            
            if (columnCount == 0) {
                Log.d(MAIN_ACTIVITY_TAG, "未检测到任何看板列，开始创建默认数据...")
                
                // 清空所有表，避免任何外键约束问题
                appDatabase.clearAllTables()
                Log.d(MAIN_ACTIVITY_TAG, "清空数据库表，准备创建全新数据")
                
                // 创建默认看板
                val defaultBoardId = UUID.randomUUID().toString()
                val defaultBoard = com.timeflow.app.data.entity.KanbanBoard(
                    id = defaultBoardId,
                    title = "默认看板",
                    description = "系统自动创建的默认看板",
                    createdAt = LocalDateTime.now(),
                    updatedAt = LocalDateTime.now(),
                    position = 0
                )
                appDatabase.kanbanBoardDao().insert(defaultBoard)
                Log.d(MAIN_ACTIVITY_TAG, "创建默认看板: ${defaultBoard.title}, ID=${defaultBoard.id}")
                
                // 创建默认列
                val defaultColumns = listOf(
                    com.timeflow.app.data.entity.KanbanColumn(
                        id = UUID.randomUUID().toString(),
                        title = "待办",
                        description = "待开始的任务",
                        boardId = defaultBoardId,
                        position = 0,
                        createdAt = LocalDateTime.now(),
                        updatedAt = LocalDateTime.now()
                    ),
                    com.timeflow.app.data.entity.KanbanColumn(
                        id = UUID.randomUUID().toString(),
                        title = "进行中",
                        description = "正在进行的任务",
                        boardId = defaultBoardId,
                        position = 1,
                        createdAt = LocalDateTime.now(),
                        updatedAt = LocalDateTime.now()
                    ),
                    com.timeflow.app.data.entity.KanbanColumn(
                        id = UUID.randomUUID().toString(),
                        title = "已完成",
                        description = "已完成的任务",
                        boardId = defaultBoardId,
                        position = 2,
                        createdAt = LocalDateTime.now(),
                        updatedAt = LocalDateTime.now()
                    )
                )
                
                appDatabase.kanbanColumnDao().insertAll(defaultColumns)
                
                // 记录创建的列信息
                defaultColumns.forEachIndexed { index, column ->
                    Log.d(MAIN_ACTIVITY_TAG, "创建默认列 #${index+1}: ${column.title}, ID=${column.id}")
                }
                
                Log.d(MAIN_ACTIVITY_TAG, "默认看板和列创建完成")
            } else {
                Log.d(MAIN_ACTIVITY_TAG, "数据库中已存在 $columnCount 个看板列，无需创建默认数据")
            }
        } catch (e: Exception) {
            Log.e(MAIN_ACTIVITY_TAG, "初始化数据库数据失败: ${e.message}", e)
            // 不抛出异常，确保应用仍能继续运行
        }
    }
    
    /**
     * 安全检查HWC能力并应用相应修复
     */
    private fun checkHwcCapabilityAndApplyFix() {
        // 此函数因 StrictMode 违规和启动性能问题，暂时不在 onCreate 中调用
        try {
            // 使用线程延迟检查以避免阻塞UI初始化
            // 改为 IO 线程执行
            lifecycleScope.launch(Dispatchers.IO) {
                if (!isFinishing && !isDestroyed) {
                    // 在系统日志中查找HWC相关错误
                    val process = Runtime.getRuntime().exec("logcat -d -v threadtime HwcComposer:E HWComposer:E SurfaceFlinger:E *:S")
                    val reader = process.inputStream.bufferedReader()
                    
                    var line: String?
                    var hwcErrorsFound = false
                    
                    while (reader.readLine().also { line = it } != null) {
                        val errorLine = line ?: ""
                        if (errorLine.contains("getLuts failed") || 
                            (errorLine.contains("HWComposer") && errorLine.contains("UNSUPPORTED"))) {
                            hwcErrorsFound = true
                            Log.w(MAIN_ACTIVITY_TAG, "在日志中检测到HWC错误，应用预防性修复")
                            break
                        }
                    }
                    
                    if (hwcErrorsFound) {
                        // 应用HwcLutsErrorFixer增强修复
                        HwcLutsErrorFixer.applyUnsupported8ErrorFix(window)
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(MAIN_ACTIVITY_TAG, "检查HWC能力时出错: ${e.message}")
        }
    }
    
    /**
     * 检查是否是夜间模式
     */
    private fun isNightMode(context: Context): Boolean {
        val nightModeFlags = context.resources.configuration.uiMode and 
                Configuration.UI_MODE_NIGHT_MASK
        return nightModeFlags == Configuration.UI_MODE_NIGHT_YES
    }
    
    /**
     * 应用HWC错误处理
     */
    private fun applyHwcErrorHandling() {
        try {
            // 在应用启动时应用安全的窗口配置
            window.statusBarColor = Color.TRANSPARENT
            window.navigationBarColor = Color.TRANSPARENT
            
            // 安全检查window.decorView是否已初始化
            if (window.decorView != null) {
                // 应用修复前先确保窗口已正确初始化
                window.decorView.post {
                    // 应用完整修复而不触发错误
                    HwcLutsErrorFixer.applyBasicFix(window)
                    
                    // 查找是否有之前的HWC错误记录
                    val prefs = getSharedPreferences("hwc_error_prefs", Context.MODE_PRIVATE)
                    val hadPreviousErrors = prefs.getBoolean("had_hwc_errors", false)
                    
                    if (hadPreviousErrors) {
                        Log.d(MAIN_ACTIVITY_TAG, "检测到之前存在HWC错误，主动应用修复")
                        HwcLutsErrorFixer.applyUnsupported8ErrorFix(window)
                    }
                }
            } else {
                // 窗口尚未初始化，安排延迟操作
                Handler(Looper.getMainLooper()).postDelayed({
                    if (!isFinishing && !isDestroyed) {
                        // 重新尝试
                        applyHwcErrorHandling()
                    }
                }, 100)
            }
        } catch (e: Exception) {
            Log.e(MAIN_ACTIVITY_TAG, "应用HWC错误处理时发生异常: ${e.message}")
        }
    }
    
    /**
     * 检查UNSUPPORTED (8)错误
     */
    private fun checkForUnsupported8Errors() {
        try {
            Handler(Looper.getMainLooper()).postDelayed({
                if (!isFinishing && !isDestroyed) {
                    try {
                        // 安全检查window.decorView是否已初始化
                        if (window.decorView != null) {
                            // 在window.decorView可用后再安全地应用修复
                            HwcLutsErrorFixer.applyEnhancedUnsupported8Fix(window)
                        } else {
                            // 如果decorView仍然不可用，再次延迟尝试
                            Log.d(MAIN_ACTIVITY_TAG, "窗口DecorView尚未初始化，延迟应用修复")
                            Handler(Looper.getMainLooper()).postDelayed({
                                if (!isFinishing && !isDestroyed) {
                                    checkForUnsupported8Errors()
                                }
                            }, 500)
                        }
                    } catch (e: Exception) {
                        Log.e(MAIN_ACTIVITY_TAG, "应用UNSUPPORTED错误修复时出错: ${e.message}")
                    }
                }
            }, 1000) // 延迟1秒执行，确保窗口已完全初始化
        } catch (e: Exception) {
            Log.e(MAIN_ACTIVITY_TAG, "检查UNSUPPORTED(8)错误时发生异常: ${e.message}")
        }
    }

    /**
     * 设置HwcComposer错误监控
     * 主动检测和处理HwcComposer getLuts错误
     */
    private fun setupHwcErrorMonitoring() {
        // 此函数因启动性能问题，暂时不在 onCreate 中调用
        try {
            // 设置窗口格式为RGBA_8888，这是最兼容的格式
            window.setFormat(PixelFormat.RGBA_8888)
            
            // 设置窗口背景为黑色，避免透明度处理
            window.decorView.setBackgroundColor(Color.BLACK)
            
            // 设置状态栏和导航栏颜色
            window.statusBarColor = Color.BLACK
            window.navigationBarColor = Color.BLACK
            
            // 设置窗口参数，避免半透明和模糊效果
            val attrs = window.attributes
            attrs.alpha = 1.0f  // 完全不透明
            attrs.dimAmount = 0f  // 禁用背景变暗
            window.attributes = attrs
            
            // 添加帧回调以监控渲染质量
            // setupFrameCallback() // 移除启动时调用
            
            Timber.d("HwcComposer错误监控已启用")
        } catch (e: Exception) {
            Timber.e(e, "设置HwcComposer错误监控时出错")
        }
    }

    /**
     * 设置帧回调监控渲染问题
     */
    private fun setupFrameCallback() {
        // 此函数因启动性能问题，暂时不在 setupHwcErrorMonitoring 中调用
        try {
            val choreographer = Choreographer.getInstance()
            choreographer.postFrameCallback(object : Choreographer.FrameCallback {
                private var lastFrameTime = 0L
                private var frameDropCount = 0
                
                override fun doFrame(frameTimeNanos: Long) {
                    try {
                        // 检测帧延迟
                        if (lastFrameTime > 0) {
                            val frameDurationMs = (frameTimeNanos - lastFrameTime) / 1_000_000
                            
                            // 检测异常渲染 (>50ms为明显卡顿)
                            if (frameDurationMs > 50) {
                                frameDropCount++
                                Timber.w("检测到渲染延迟: ${frameDurationMs}ms")
                                
                                // 连续多次检测到问题，应用额外的修复
                                if (frameDropCount > 3) {
                                    // 通知HwcLutsErrorFixer应用更严格的修复
                                    HwcLutsErrorFixer.applyStrictModeFix(window)
                                    
                                    // 如果问题严重，切换到关键模式
                                    if (frameDurationMs > 100) {
                                        HwcLutsErrorFixer.applyCriticalModeFix(window)
                                    }
                                    
                                    // 执行紧急GC，减轻内存压力
                                    System.gc()
                                    
                                    frameDropCount = 0
                                }
                            } else {
                                // 正常帧，逐渐减少计数
                                if (frameDropCount > 0) frameDropCount--
                            }
                        }
                        
                        lastFrameTime = frameTimeNanos
                        
                    } catch (e: Exception) {
                        Timber.e(e, "帧回调处理错误")
                    } finally {
                        // 继续监听下一帧
                        choreographer.postFrameCallback(this)
                    }
                }
            })
        } catch (e: Exception) {
            Timber.e(e, "设置帧回调失败")
        }
    }

    // 应用主内容
    @Composable
    fun TimeFlowAppContent(navController: androidx.navigation.NavHostController) {
        // 应用主题但不使用任何过渡动画
        TimeFlowTheme(
            darkTheme = isNightMode(this)
        ) {
            // 添加性能监控组件
            PerformanceMonitor.MonitorSetup()
            
            // 使用主应用组件（移除任何可能的动画）
            com.timeflow.app.ui.TimeFlowApp(navController = navController)
        }
    }

    /**
     * 设置实时错误监控
     * 监控RippleDrawable和HWC错误
     */
    private fun setupErrorMonitoring() {
        val monitorRunnable = Runnable {
            try {
                val cmd = "logcat -v threadtime HwcComposer:E RippleDrawable:E *:S"
                val process = Runtime.getRuntime().exec(cmd)
                val reader = process.inputStream.bufferedReader()
                
                var hwcErrorCount = 0
                var rippleErrorCount = 0
                var lastHwcErrorTime = 0L
                var lastRippleErrorTime = 0L
                
                // 持续监控日志输出
                var line: String?
                while (reader.readLine().also { line = it } != null) {
                    val log = line ?: ""
                    val currentTime = System.currentTimeMillis()
                    
                    // 检测HWC getLuts错误
                    if (log.contains("getLuts failed") && log.contains("UNSUPPORTED (8)")) {
                        // 防止短时间内重复处理
                        if (currentTime - lastHwcErrorTime > 5000) {
                            hwcErrorCount++
                            lastHwcErrorTime = currentTime
                            
                            // 应用修复
                            runOnUiThread {
                                if (!isFinishing && !isDestroyed) {
                                    // 当错误次数超过阈值时应用增强修复
                                    if (hwcErrorCount > 3) {
                                        Log.w(TAG, "检测到多次HWC UNSUPPORTED(8)错误，应用增强修复")
                                        HwcLutsErrorFixer.applyEnhancedUnsupported8Fix(window)
                                        
                                        // 记录错误状态
                                        val prefs = getSharedPreferences("hwc_error_prefs", Context.MODE_PRIVATE)
                                        prefs.edit().putBoolean("had_hwc_errors", true).apply()
                                    } else {
                                        Log.d(TAG, "检测到HWC错误，但频率低于阈值，继续监控")
                                    }
                                }
                            }
                        }
                    }
                    
                    // 检测RippleDrawable错误
                    if (log.contains("RippleDrawable") && 
                        log.contains("not supported for a non-hardware accelerated Canvas")) {
                        
                        // 防止短时间内重复处理
                        if (currentTime - lastRippleErrorTime > 5000) {
                            rippleErrorCount++
                            lastRippleErrorTime = currentTime
                            
                            // 应用修复
                            runOnUiThread {
                                if (!isFinishing && !isDestroyed) {
                                    if (rippleErrorCount > 3) {
                                        Log.w(TAG, "检测到多次RippleDrawable错误，应用专项修复")
                                        // 针对RippleDrawable问题进行修复
                                        window.decorView.post {
                                            try {
                                                HwcLutsErrorFixer.reapplyHardwareAccelerationForInteractiveViews(window.decorView)
                                            } catch (e: Exception) {
                                                Log.e(TAG, "应用RippleDrawable修复失败: ${e.message}")
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "错误监控失败: ${e.message}")
            }
        }
        
        // 以后台线程启动监控
        Thread(monitorRunnable).apply {
            isDaemon = true
            name = "error-monitor"
            priority = Thread.MIN_PRIORITY
            start()
        }
    }

    // 添加新方法：启动日志监控
    private fun startErrorLogMonitoring() {
        // 此函数因启动性能问题，暂时不在 onCreate 中调用
        if (isLogMonitorRunning) return
        
        isLogMonitorRunning = true
        
        logMonitorThread = Thread {
            try {
                // 监控HWC和RippleDrawable相关错误
                val process = Runtime.getRuntime().exec("logcat -v threadtime HwcComposer:E HWComposer:E RippleDrawable:E *:S")
                val reader = process.inputStream.bufferedReader()
                
                var hwcErrorCount = 0
                var rippleErrorCount = 0
                val errorCooldown = HashMap<String, Long>()
                
                var line: String?
                while (reader.readLine().also { line = it } != null) {
                    val errorLine = line ?: ""
                    
                    // 检查是否有HWC getLuts错误
                    if (errorLine.contains("getLuts failed") || 
                        (errorLine.contains("HWComposer") && errorLine.contains("UNSUPPORTED"))) {
                        
                        // 避免重复处理同样的错误
                        val now = System.currentTimeMillis()
                        val lastReportTime = errorCooldown.getOrDefault("hwc", 0L)
                        if (now - lastReportTime > 5000) { // 每5秒最多处理一次
                            hwcErrorCount++
                            errorCooldown["hwc"] = now
                            
                            // 在UI线程应用优化
                            runOnUiThread {
                                if (!isFinishing && !isDestroyed) {
                                    applyOptimizationsBasedOnErrors(hwcErrorCount, rippleErrorCount)
                                }
                            }
                        }
                    }
                    
                    // 检查是否有RippleDrawable错误
                    if (errorLine.contains("RippleDrawable") && 
                        errorLine.contains("not supported for a non-hardware accelerated Canvas")) {
                        
                        val now = System.currentTimeMillis()
                        val lastReportTime = errorCooldown.getOrDefault("ripple", 0L)
                        if (now - lastReportTime > 5000) { // 每5秒最多处理一次
                            rippleErrorCount++
                            errorCooldown["ripple"] = now
                            
                            // 在UI线程应用优化
                            runOnUiThread {
                                if (!isFinishing && !isDestroyed) {
                                    applyOptimizationsBasedOnErrors(hwcErrorCount, rippleErrorCount)
                                }
                            }
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "日志监控失败: ${e.message}")
            } finally {
                isLogMonitorRunning = false
            }
        }.apply {
            isDaemon = true
            name = "error-log-monitor"
            priority = Thread.MIN_PRIORITY
            start()
        }
        
        Log.d(TAG, "错误日志监控已启动")
    }
    
    // 添加新方法：根据错误应用优化
    private fun applyOptimizationsBasedOnErrors(hwcErrorCount: Int, rippleErrorCount: Int) {
        try {
            // 如果同时检测到HWC错误和RippleDrawable错误
            if (hwcErrorCount > 0 && rippleErrorCount > 0) {
                Log.w(TAG, "同时检测到HWC错误和RippleDrawable错误，应用智能混合渲染策略")
                // 新版TaskListOptimizer不支持updateFromErrorLogs，使用替代方法
                com.timeflow.app.ui.task.TaskListOptimizer.setOptimizationLevel(
                    com.timeflow.app.ui.task.TaskListOptimizer.OptimizationLevel.MEDIUM
                )
                
                // 如果错误非常频繁，应用更激进的优化
                if (hwcErrorCount > 10 || rippleErrorCount > 10) {
                    // 应用紧急优化但保留对RippleDrawable的支持
                    com.timeflow.app.utils.HwcLutsErrorFixer.applyUnsupported8ErrorFix(window)
                }
            } 
            // 只有HWC错误
            else if (hwcErrorCount > 0) {
                Log.w(TAG, "检测到HWC getLuts错误，应用相应优化")
                // 新版TaskListOptimizer不支持updateFromErrorLogs，使用替代方法
                com.timeflow.app.ui.task.TaskListOptimizer.setOptimizationLevel(
                    com.timeflow.app.ui.task.TaskListOptimizer.OptimizationLevel.LIGHT
                )
                
                // 如果HWC错误持续发生，应用更强的优化
                if (hwcErrorCount > 5) {
                    com.timeflow.app.utils.HwcLutsErrorFixer.applyUnsupported8ErrorFix(window)
                }
            }
            // 只有RippleDrawable错误
            else if (rippleErrorCount > 0) {
                Log.w(TAG, "检测到RippleDrawable错误，调整RippleDrawable支持")
                // 不再需要调用updateFromErrorLogs，可直接应用window对象的修复
                window.decorView.post {
                    com.timeflow.app.utils.HwcLutsErrorFixer.handleRippleCompatibility(window.decorView)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "应用优化策略失败: ${e.message}")
        }
    }
    
    // 添加新方法：为可点击View启用硬件加速
    private fun findAndEnableHardwareAccelerationForClickableViews(view: View) {
        if (view.isClickable || view.background is android.graphics.drawable.RippleDrawable) {
            view.setLayerType(View.LAYER_TYPE_HARDWARE, null)
        }
        
        if (view is ViewGroup) {
            for (i in 0 until view.childCount) {
                findAndEnableHardwareAccelerationForClickableViews(view.getChildAt(i))
            }
        }
    }

    /**
     * 初始化渲染优化和HWC错误处理
     * 综合解决状态栏黑色问题和HwcComposer getLuts错误
     */
    private fun initializeRenderingOptimizations() {
        try {
            // 设置基本渲染优化 - 保留核心调用
            RenderOptimizer.setupWindowOptimizations(this)
            
            // 修复timerslack相关错误 - 暂时移除
            // RenderOptimizer.fixTimerSlackIssues()
            
            // 专门处理Binder和Parcel相关错误 - 暂时移除
            // initializeIpcErrorHandler()
            
            Log.d(MAIN_ACTIVITY_TAG, "已初始化渲染优化 (简化版)")
        } catch (e: Exception) {
            Log.e(MAIN_ACTIVITY_TAG, "初始化渲染优化失败: ${e.message}")
        }
    }
    
    /**
     * 初始化IPC错误处理器
     * 专门用于处理Binder事务失败和Parcel读取NULL错误
     */
    private fun initializeIpcErrorHandler() {
        try {
            // 只保留必要的配置，减少反射操作
            SafeParcelHelper.initialize()
            
            // 减少系统服务调用，使用更保守的策略处理Binder事务
            BinderTransactionHelper.setConservativeBinderPolicy()
            
            // 不再尝试监控日志，减少系统资源占用
            // startBinderErrorMonitoring() - 移除
            
            // 简化处理方式，避免使用反射访问系统服务
            handleParcelNullStringErrors()
            
            // 使用较轻量级的方式修复Binder事务问题
            fixBinderTransactionIssuesLite()
            
            Log.d(MAIN_ACTIVITY_TAG, "已初始化IPC错误处理")
        } catch (e: Exception) {
            Log.e(MAIN_ACTIVITY_TAG, "初始化IPC错误处理失败: ${e.message}")
        }
    }
    
    /**
     * 处理Parcel NULL字符串读取错误，轻量化处理
     */
    private fun handleParcelNullStringErrors() {
        try {
            // 启用安全的Parcel读取，不使用反射
            SafeParcelHelper.enableSafeReads()
            Log.d(MAIN_ACTIVITY_TAG, "已启用安全Parcel读取")
        } catch (e: Exception) {
            Log.e(MAIN_ACTIVITY_TAG, "启用安全Parcel读取失败: ${e.message}")
        }
    }
    
    /**
     * 轻量级修复Binder事务问题的方法
     * 避免使用反射和系统API调用
     */
    private fun fixBinderTransactionIssuesLite() {
        try {
            // 应用UI渲染优化，减少Binder负担
            RenderOptimizer.applyGlobalRenderingOptimization(window)
            
            // 在UI线程中清理不必要的对象引用
            Handler(Looper.getMainLooper()).post {
                // 触发一次GC请求，但不强制执行，避免性能问题
                Runtime.getRuntime().gc()
                Log.d(MAIN_ACTIVITY_TAG, "已请求轻量级内存回收")
            }
            
            Log.d(MAIN_ACTIVITY_TAG, "已应用轻量级Binder优化")
        } catch (e: Exception) {
            Log.e(MAIN_ACTIVITY_TAG, "轻量级Binder优化失败: ${e.message}")
        }
    }
    
    /**
     * 禁用强制深色模式
     * 防止系统强制应用深色模式导致UI问题
     */
    private fun disableForceDarkMode() {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                window.decorView.findViewById<View>(android.R.id.content)?.let { contentView ->
                    try {
                        val viewClass = contentView.javaClass
                        val setForceDarkMethod = viewClass.getMethod("setForceDarkAllowed", Boolean::class.java)
                        setForceDarkMethod.invoke(contentView, false)
                        Log.d(TAG, "已禁用强制深色模式")
                    } catch (e: Exception) {
                        Log.e(TAG, "禁用强制深色模式失败: ${e.message}")
                    }
                }
            }
        } catch (e: Exception) {
            // 忽略不支持的设备上的错误
        }
    }

    // 加载示例数据
    private fun loadSampleDataIfNeeded() {
        // 初始化数据库数据
        lifecycleScope.launch(Dispatchers.IO) {
            try {
                Log.d(MAIN_ACTIVITY_TAG, "开始初始化应用数据")
                
                // 先确保默认看板和列创建完成
                initializeDatabaseData()
                
                // 再生成示例数据
                sampleDataGenerator.generateSampleDataIfNeeded()
                
                Log.d(MAIN_ACTIVITY_TAG, "应用数据初始化完成")
            } catch (e: Exception) {
                Log.e(MAIN_ACTIVITY_TAG, "应用数据初始化失败: ${e.message}", e)
            }
        }
    }

    // 当窗口焦点改变时
    override fun onWindowFocusChanged(hasFocus: Boolean) {
        super.onWindowFocusChanged(hasFocus)
        if (hasFocus) {
            // 当窗口获得焦点时，尝试应用兼容性修复，可能解决某些渲染问题
            // com.timeflow.app.utils.HwcLutsErrorFixer.handleRippleCompatibility(window.decorView) // DISABLED: Performance tuning
        }
    }

    // 重写返回按钮处理方法
    @Deprecated("Deprecated in Java")
    override fun onBackPressed() {
        handleBackPress()
    }

    // 统一的返回按钮处理逻辑
    private fun handleBackPress() {
        val currentDestination = navController?.currentDestination?.route
        
        // 检查当前是否在首页
        if (currentDestination == com.timeflow.app.ui.navigation.AppDestinations.UNIFIED_HOME_ROUTE || 
            currentDestination == com.timeflow.app.ui.navigation.AppDestinations.HOME_ROUTE ||
            currentDestination == com.timeflow.app.ui.navigation.AppDestinations.TASK_ROUTE) {
            // 当前在首页，执行双击退出逻辑
            val currentTime = System.currentTimeMillis()
            if (currentTime - lastBackPressTime < 2000) { // 2秒内点击两次返回按钮退出
                finish()
            } else {
                Toast.makeText(this, "再按一次返回键退出应用", Toast.LENGTH_SHORT).show()
                lastBackPressTime = currentTime
            }
        } else {
            // 不在首页，执行默认导航返回
            navController?.popBackStack()
        }
    }

    /**
     * 检查通知权限
     */
    private fun checkNotificationPermission() {
        if (!notificationPermissionHelper.hasNotificationPermission()) {
            Log.d("MainActivity", "通知权限未授予，将请求权限")
            // 延迟请求权限，让界面先加载
            Handler(Looper.getMainLooper()).postDelayed({
                notificationPermissionHelper.requestNotificationPermission(this) { granted ->
                    if (granted) {
                        Log.d("MainActivity", "通知权限已授予")
                    } else {
                        Log.w("MainActivity", "通知权限被拒绝")
                    }
                }
            }, 1000)
        } else {
            Log.d("MainActivity", "通知权限已授予")
        }
    }
    
    /**
     * 设置全局异常处理器
     * 捕获图像渲染相关的异常并提供降级处理
     */
    private fun setupGlobalExceptionHandler() {
        try {
            val defaultHandler = Thread.getDefaultUncaughtExceptionHandler()
            
            Thread.setDefaultUncaughtExceptionHandler { thread, throwable ->
                try {
                    // 检查是否为图像渲染相关异常
                    val isImageRenderingError = when {
                        throwable.message?.contains("Software rendering doesn't support hardware bitmaps") == true ||
                        throwable.message?.contains("IllegalArgumentException") == true ||
                        throwable.message?.contains("Software rendering") == true ||
                        throwable.message?.contains("hardware bitmaps") == true -> true
                        else -> false
                    }
                    
                    if (isImageRenderingError) {
                        Log.e("GlobalExceptionHandler", "捕获到图像渲染异常: ${throwable.message}")
                        
                        // 立即重新配置Coil为最安全的设置
                        Handler(Looper.getMainLooper()).post {
                            try {
                                val emergencyLoader = coil.ImageLoader.Builder(this@MainActivity)
                                    .allowHardware(false)
                                    .bitmapConfig(android.graphics.Bitmap.Config.RGB_565)
                                    .allowRgb565(true)
                                    .crossfade(false) // 禁用动画减少复杂性
                                    .build()
                                coil.Coil.setImageLoader(emergencyLoader)
                                Log.d("GlobalExceptionHandler", "已切换到紧急安全图像加载器")
                            } catch (e: Exception) {
                                Log.e("GlobalExceptionHandler", "切换紧急图像加载器失败: ${e.message}")
                            }
                        }
                        
                        // 记录错误但不崩溃
                        Log.e("GlobalExceptionHandler", "图像渲染异常已处理", throwable)
                        return@setDefaultUncaughtExceptionHandler
                    }
                    
                    // 其他类型的异常继续使用默认处理器
                    defaultHandler?.uncaughtException(thread, throwable)
                } catch (e: Exception) {
                    Log.e("GlobalExceptionHandler", "异常处理器本身出错: ${e.message}")
                    // 如果异常处理器失败，仍然使用默认处理器
                    defaultHandler?.uncaughtException(thread, throwable)
                }
            }
            
            Log.d("MainActivity", "全局异常处理器设置完成")
        } catch (e: Exception) {
            Log.e("MainActivity", "设置全局异常处理器失败: ${e.message}")
        }
    }

    // 设置JankStats监控
    private fun setupJankMonitoring() {
        // JankStats 暂时禁用以简化启动
        try {
            // 初始化性能监控 - 安全方式创建JankStats或返回null
            /*
            val metricsState = PerformanceMetricsState.getHolderForHierarchy(window.decorView)
            jankStats = try {
                JankStats.createAndTrack(window) { frameData ->
                    frameCount++
                    if (frameData.isJank) {
                        jankFrameCount++
                        val durationMs = frameData.frameDurationUiNanos / 1_000_000
                        Log.w(MAIN_ACTIVITY_TAG, "检测到UI卡顿: ${durationMs}ms，原因: ${frameData.states}")
                    }
                }
            } catch (e: Exception) {
                Log.e(MAIN_ACTIVITY_TAG, "创建JankStats失败: ${e.message}")
                null
            }
            */
            Log.d(MAIN_ACTIVITY_TAG, "性能监控设置完成（JankStats已禁用）")
        } catch (e: Exception) {
            Log.e(MAIN_ACTIVITY_TAG, "设置性能监控失败: ${e.message}")
        }
    }

    /**
     * 应用语言设置到Context
     */
    override fun attachBaseContext(newBase: Context?) {
        val context = newBase ?: return

        try {
            // 应用语言设置
            val localizedContext = LanguageManager.applyLanguageToContext(context)
            super.attachBaseContext(localizedContext)
        } catch (e: Exception) {
            Log.e(MAIN_ACTIVITY_TAG, "应用语言设置失败: ${e.message}", e)
            super.attachBaseContext(context)
        }
    }
}

/**
 * 性能监控包装器组件
 * 提供JankStats监控功能到内部组件
 */
@Composable
fun TrackPerformance(content: @Composable () -> Unit) {
    // 提供性能监控工具给子组件
    CompositionLocalProvider(
        LocalJankStatsMonitor provides null,
        content = content
    )
}

@Composable
private fun SetSystemBarsColor() {
    val systemUiController = rememberSystemUiController()
    val useDarkIcons = !isSystemInDarkTheme()
    
    DisposableEffect(systemUiController, useDarkIcons) {
        // 更新所有系统栏颜色为新背景色
        systemUiController.setSystemBarsColor(
            color = AppBackground,
            darkIcons = useDarkIcons,
            isNavigationBarContrastEnforced = false
        )
        
        onDispose {}
    }
}