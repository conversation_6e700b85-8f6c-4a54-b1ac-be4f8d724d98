package com.timeflow.app.service;

import com.timeflow.app.utils.NotificationHelper;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class TimeTrackingService_MembersInjector implements MembersInjector<TimeTrackingService> {
  private final Provider<NotificationHelper> notificationHelperProvider;

  public TimeTrackingService_MembersInjector(
      Provider<NotificationHelper> notificationHelperProvider) {
    this.notificationHelperProvider = notificationHelperProvider;
  }

  public static MembersInjector<TimeTrackingService> create(
      Provider<NotificationHelper> notificationHelperProvider) {
    return new TimeTrackingService_MembersInjector(notificationHelperProvider);
  }

  @Override
  public void injectMembers(TimeTrackingService instance) {
    injectNotificationHelper(instance, notificationHelperProvider.get());
  }

  @InjectedFieldSignature("com.timeflow.app.service.TimeTrackingService.notificationHelper")
  public static void injectNotificationHelper(TimeTrackingService instance,
      NotificationHelper notificationHelper) {
    instance.notificationHelper = notificationHelper;
  }
}
