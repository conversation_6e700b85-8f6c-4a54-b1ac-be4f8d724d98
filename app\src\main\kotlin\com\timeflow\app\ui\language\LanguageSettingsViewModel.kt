package com.timeflow.app.ui.language

import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 语言设置ViewModel
 */
@HiltViewModel
class LanguageSettingsViewModel @Inject constructor() : ViewModel() {
    
    companion object {
        private const val TAG = "LanguageSettingsViewModel"
    }
    
    // UI状态
    data class UiState(
        val currentLanguage: LanguageManager.SupportedLanguage = LanguageManager.SupportedLanguage.SYSTEM,
        val useSystemLanguage: Boolean = true,
        val supportedLanguages: List<LanguageManager.SupportedLanguage> = emptyList(),
        val isLoading: Boolean = false,
        val showRestartDialog: Boolean = false,
        val error: String? = null
    )
    
    private val _uiState = MutableStateFlow(UiState())
    val uiState: StateFlow<UiState> = _uiState.asStateFlow()
    
    init {
        loadLanguageSettings()
    }
    
    /**
     * 加载语言设置
     */
    private fun loadLanguageSettings() {
        viewModelScope.launch {
            try {
                Log.d(TAG, "加载语言设置")
                
                // 获取支持的语言列表
                val supportedLanguages = LanguageManager.getSupportedLanguages()
                
                // 监听当前语言变化
                LanguageManager.currentLanguage.collect { currentLanguage ->
                    _uiState.value = _uiState.value.copy(
                        currentLanguage = currentLanguage,
                        supportedLanguages = supportedLanguages,
                        isLoading = false
                    )
                    Log.d(TAG, "当前语言: ${currentLanguage.displayName}")
                }
            } catch (e: Exception) {
                Log.e(TAG, "加载语言设置失败: ${e.message}", e)
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = e.message
                )
            }
        }
        
        // 监听系统语言设置变化
        viewModelScope.launch {
            LanguageManager.useSystemLanguage.collect { useSystemLanguage ->
                _uiState.value = _uiState.value.copy(
                    useSystemLanguage = useSystemLanguage
                )
            }
        }
    }
    
    /**
     * 设置语言
     */
    fun setLanguage(language: LanguageManager.SupportedLanguage) {
        viewModelScope.launch {
            try {
                Log.d(TAG, "设置语言: ${language.displayName}")
                _uiState.value = _uiState.value.copy(isLoading = true, error = null)
                
                // 检查是否需要重启应用
                val needRestart = _uiState.value.currentLanguage != language
                
                // 设置语言
                LanguageManager.setLanguage(language)
                
                _uiState.value = _uiState.value.copy(
                    currentLanguage = language,
                    useSystemLanguage = (language == LanguageManager.SupportedLanguage.SYSTEM),
                    isLoading = false,
                    showRestartDialog = needRestart
                )
                
                Log.d(TAG, "✅ 语言设置成功: ${language.displayName}")
            } catch (e: Exception) {
                Log.e(TAG, "设置语言失败: ${e.message}", e)
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = e.message
                )
            }
        }
    }
    
    /**
     * 获取语言显示名称
     */
    fun getLanguageDisplayName(language: LanguageManager.SupportedLanguage): String {
        return when (language) {
            LanguageManager.SupportedLanguage.SYSTEM -> {
                when (LanguageManager.getCurrentLocale().language) {
                    "zh" -> "跟随系统"
                    "en" -> "Follow System"
                    else -> "跟随系统"
                }
            }
            LanguageManager.SupportedLanguage.SIMPLIFIED_CHINESE -> "简体中文"
            LanguageManager.SupportedLanguage.TRADITIONAL_CHINESE -> "繁體中文"
            LanguageManager.SupportedLanguage.ENGLISH -> "English"
        }
    }
    
    /**
     * 显示重启对话框
     */
    fun showRestartDialog() {
        _uiState.value = _uiState.value.copy(showRestartDialog = true)
    }
    
    /**
     * 隐藏重启对话框
     */
    fun hideRestartDialog() {
        _uiState.value = _uiState.value.copy(showRestartDialog = false)
    }
    
    /**
     * 清除错误信息
     */
    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }
    
    /**
     * 获取当前选中的语言
     */
    fun getCurrentLanguage(): LanguageManager.SupportedLanguage {
        return _uiState.value.currentLanguage
    }
    
    /**
     * 检查是否是当前选中的语言
     */
    fun isCurrentLanguage(language: LanguageManager.SupportedLanguage): Boolean {
        return _uiState.value.currentLanguage == language
    }
    
    /**
     * 获取支持的语言列表
     */
    fun getSupportedLanguages(): List<LanguageManager.SupportedLanguage> {
        return _uiState.value.supportedLanguages
    }
}
