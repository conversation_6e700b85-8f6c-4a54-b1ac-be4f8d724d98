package com.timeflow.app.ui.screen.analytics;

import com.timeflow.app.data.repository.GoalRepository;
import com.timeflow.app.data.repository.HabitRepository;
import com.timeflow.app.data.repository.TaskRepository;
import com.timeflow.app.data.repository.TimeAnalyticsRepository;
import com.timeflow.app.data.repository.TimeSessionRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AnalyticsDataService_Factory implements Factory<AnalyticsDataService> {
  private final Provider<TaskRepository> taskRepositoryProvider;

  private final Provider<GoalRepository> goalRepositoryProvider;

  private final Provider<HabitRepository> habitRepositoryProvider;

  private final Provider<TimeSessionRepository> timeSessionRepositoryProvider;

  private final Provider<TimeAnalyticsRepository> timeAnalyticsRepositoryProvider;

  public AnalyticsDataService_Factory(Provider<TaskRepository> taskRepositoryProvider,
      Provider<GoalRepository> goalRepositoryProvider,
      Provider<HabitRepository> habitRepositoryProvider,
      Provider<TimeSessionRepository> timeSessionRepositoryProvider,
      Provider<TimeAnalyticsRepository> timeAnalyticsRepositoryProvider) {
    this.taskRepositoryProvider = taskRepositoryProvider;
    this.goalRepositoryProvider = goalRepositoryProvider;
    this.habitRepositoryProvider = habitRepositoryProvider;
    this.timeSessionRepositoryProvider = timeSessionRepositoryProvider;
    this.timeAnalyticsRepositoryProvider = timeAnalyticsRepositoryProvider;
  }

  @Override
  public AnalyticsDataService get() {
    return newInstance(taskRepositoryProvider.get(), goalRepositoryProvider.get(), habitRepositoryProvider.get(), timeSessionRepositoryProvider.get(), timeAnalyticsRepositoryProvider.get());
  }

  public static AnalyticsDataService_Factory create(Provider<TaskRepository> taskRepositoryProvider,
      Provider<GoalRepository> goalRepositoryProvider,
      Provider<HabitRepository> habitRepositoryProvider,
      Provider<TimeSessionRepository> timeSessionRepositoryProvider,
      Provider<TimeAnalyticsRepository> timeAnalyticsRepositoryProvider) {
    return new AnalyticsDataService_Factory(taskRepositoryProvider, goalRepositoryProvider, habitRepositoryProvider, timeSessionRepositoryProvider, timeAnalyticsRepositoryProvider);
  }

  public static AnalyticsDataService newInstance(TaskRepository taskRepository,
      GoalRepository goalRepository, HabitRepository habitRepository,
      TimeSessionRepository timeSessionRepository,
      TimeAnalyticsRepository timeAnalyticsRepository) {
    return new AnalyticsDataService(taskRepository, goalRepository, habitRepository, timeSessionRepository, timeAnalyticsRepository);
  }
}
