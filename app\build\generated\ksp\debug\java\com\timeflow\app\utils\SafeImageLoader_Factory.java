package com.timeflow.app.utils;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SafeImageLoader_Factory implements Factory<SafeImageLoader> {
  private final Provider<Context> contextProvider;

  public SafeImageLoader_Factory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public SafeImageLoader get() {
    return newInstance(contextProvider.get());
  }

  public static SafeImageLoader_Factory create(Provider<Context> contextProvider) {
    return new SafeImageLoader_Factory(contextProvider);
  }

  public static SafeImageLoader newInstance(Context context) {
    return new SafeImageLoader(context);
  }
}
