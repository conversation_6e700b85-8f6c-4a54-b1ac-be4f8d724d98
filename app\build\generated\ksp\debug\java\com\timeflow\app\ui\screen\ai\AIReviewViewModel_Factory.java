package com.timeflow.app.ui.screen.ai;

import android.content.Context;
import com.timeflow.app.data.repository.AiTaskRepository;
import com.timeflow.app.data.repository.GoalRepository;
import com.timeflow.app.data.repository.TaskRepository;
import com.timeflow.app.data.repository.TimeAnalyticsRepository;
import com.timeflow.app.data.repository.TimeSessionRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AIReviewViewModel_Factory implements Factory<AIReviewViewModel> {
  private final Provider<TaskRepository> taskRepositoryProvider;

  private final Provider<TimeAnalyticsRepository> timeAnalyticsRepositoryProvider;

  private final Provider<TimeSessionRepository> timeSessionRepositoryProvider;

  private final Provider<GoalRepository> goalRepositoryProvider;

  private final Provider<AiTaskRepository> aiTaskRepositoryProvider;

  private final Provider<Context> contextProvider;

  public AIReviewViewModel_Factory(Provider<TaskRepository> taskRepositoryProvider,
      Provider<TimeAnalyticsRepository> timeAnalyticsRepositoryProvider,
      Provider<TimeSessionRepository> timeSessionRepositoryProvider,
      Provider<GoalRepository> goalRepositoryProvider,
      Provider<AiTaskRepository> aiTaskRepositoryProvider, Provider<Context> contextProvider) {
    this.taskRepositoryProvider = taskRepositoryProvider;
    this.timeAnalyticsRepositoryProvider = timeAnalyticsRepositoryProvider;
    this.timeSessionRepositoryProvider = timeSessionRepositoryProvider;
    this.goalRepositoryProvider = goalRepositoryProvider;
    this.aiTaskRepositoryProvider = aiTaskRepositoryProvider;
    this.contextProvider = contextProvider;
  }

  @Override
  public AIReviewViewModel get() {
    return newInstance(taskRepositoryProvider.get(), timeAnalyticsRepositoryProvider.get(), timeSessionRepositoryProvider.get(), goalRepositoryProvider.get(), aiTaskRepositoryProvider.get(), contextProvider.get());
  }

  public static AIReviewViewModel_Factory create(Provider<TaskRepository> taskRepositoryProvider,
      Provider<TimeAnalyticsRepository> timeAnalyticsRepositoryProvider,
      Provider<TimeSessionRepository> timeSessionRepositoryProvider,
      Provider<GoalRepository> goalRepositoryProvider,
      Provider<AiTaskRepository> aiTaskRepositoryProvider, Provider<Context> contextProvider) {
    return new AIReviewViewModel_Factory(taskRepositoryProvider, timeAnalyticsRepositoryProvider, timeSessionRepositoryProvider, goalRepositoryProvider, aiTaskRepositoryProvider, contextProvider);
  }

  public static AIReviewViewModel newInstance(TaskRepository taskRepository,
      TimeAnalyticsRepository timeAnalyticsRepository, TimeSessionRepository timeSessionRepository,
      GoalRepository goalRepository, AiTaskRepository aiTaskRepository, Context context) {
    return new AIReviewViewModel(taskRepository, timeAnalyticsRepository, timeSessionRepository, goalRepository, aiTaskRepository, context);
  }
}
