package com.timeflow.app.ui.screen.settings

import android.content.Context
import android.content.Intent
import android.content.pm.PackageInfo
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material.icons.outlined.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.hapticfeedback.HapticFeedbackType
import androidx.compose.ui.platform.LocalClipboardManager
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.platform.LocalHapticFeedback
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import com.timeflow.app.R
import com.timeflow.app.ui.theme.*
import com.timeflow.app.ui.screen.calendar.CalendarViewModel
import com.timeflow.app.utils.SystemBarManager
import android.app.Activity
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalView
import kotlinx.coroutines.delay
import java.text.SimpleDateFormat
import java.util.*

/**
 * 关于页面
 * 参照微信、QQ、抖音等知名app的设计风格
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AboutScreen(
    navController: NavController,
    calendarViewModel: CalendarViewModel = hiltViewModel()
) {
    val context = LocalContext.current
    val clipboardManager = LocalClipboardManager.current
    val hapticFeedback = LocalHapticFeedback.current
    val scrollState = rememberLazyListState()
    
    // 获取用户颜色偏好
    val userColorPreference by calendarViewModel.userColorPreference.collectAsState()
    val backgroundColor = remember(userColorPreference) {
        Color(userColorPreference.settingsPageBackground)
    }
    
    // 动画状态
    var logoVisible by remember { mutableStateOf(false) }
    var contentVisible by remember { mutableStateOf(false) }
    
    // 获取应用信息
    val appInfo = remember { getAppInfo(context) }
    
    // 启动动画
    LaunchedEffect(Unit) {
        logoVisible = true
        delay(300)
        contentVisible = true
    }
    
    // 处理状态栏
    val activity = LocalContext.current as? Activity
    SideEffect {
        activity?.let { 
            SystemBarManager.forceOpaqueStatusBar(it)
        }
    }
    
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(backgroundColor)
    ) {
        LazyColumn(
            state = scrollState,
            modifier = Modifier
                .fillMaxSize()
                .padding(top = SystemBarManager.getFixedStatusBarHeight()),
            contentPadding = PaddingValues(bottom = 80.dp),
            verticalArrangement = Arrangement.spacedBy(2.dp)
        ) {
            // 顶部导航栏
            item {
                TopAppBar(
                    title = { 
                        Text(
                            "关于TimeFlow",
                            color = MaterialTheme.colorScheme.primary,
                            fontWeight = FontWeight.Bold
                        ) 
                    },
                    navigationIcon = {
                        IconButton(onClick = { navController.popBackStack() }) {
                            Icon(
                                Icons.Filled.ArrowBack, 
                                contentDescription = "返回",
                                tint = MaterialTheme.colorScheme.primary
                            )
                        }
                    },
                    colors = TopAppBarDefaults.topAppBarColors(
                        containerColor = Color.Transparent
                    )
                )
            }
            
            // 应用Logo和基本信息
            item {
                AppHeaderSection(
                    appInfo = appInfo,
                    logoVisible = logoVisible,
                    contentVisible = contentVisible
                )
            }
            
            // 应用介绍
            item {
                AnimatedVisibility(
                    visible = contentVisible,
                    enter = slideInVertically(
                        initialOffsetY = { it },
                        animationSpec = tween(600, delayMillis = 100)
                    ) + fadeIn(tween(600, delayMillis = 100))
                ) {
                    AppDescriptionSection()
                }
            }
            
            // 功能亮点
            item {
                AnimatedVisibility(
                    visible = contentVisible,
                    enter = slideInVertically(
                        initialOffsetY = { it },
                        animationSpec = tween(600, delayMillis = 200)
                    ) + fadeIn(tween(600, delayMillis = 200))
                ) {
                    FeatureHighlightsSection()
                }
            }
            
            // 版本信息
            item {
                AnimatedVisibility(
                    visible = contentVisible,
                    enter = slideInVertically(
                        initialOffsetY = { it },
                        animationSpec = tween(600, delayMillis = 300)
                    ) + fadeIn(tween(600, delayMillis = 300))
                ) {
                    VersionInfoSection(
                        appInfo = appInfo,
                        onCopyVersionInfo = {
                            val versionInfo = buildString {
                                appendLine("TimeFlow 版本信息")
                                appendLine("应用版本: ${appInfo.versionName}")
                                appendLine("版本代码: ${appInfo.versionCode}")
                                appendLine("系统版本: Android ${Build.VERSION.RELEASE}")
                                appendLine("设备型号: ${Build.MODEL}")
                                appendLine("设备厂商: ${Build.MANUFACTURER}")
                                appendLine("CPU架构: ${Build.CPU_ABI}")
                            }
                            clipboardManager.setText(AnnotatedString(versionInfo))
                            hapticFeedback.performHapticFeedback(HapticFeedbackType.LongPress)
                        }
                    )
                }
            }
            
            // 团队信息
            item {
                AnimatedVisibility(
                    visible = contentVisible,
                    enter = slideInVertically(
                        initialOffsetY = { it },
                        animationSpec = tween(600, delayMillis = 400)
                    ) + fadeIn(tween(600, delayMillis = 400))
                ) {
                    TeamInfoSection()
                }
            }
            
            // 用户协议和隐私政策
            item {
                AnimatedVisibility(
                    visible = contentVisible,
                    enter = slideInVertically(
                        initialOffsetY = { it },
                        animationSpec = tween(600, delayMillis = 500)
                    ) + fadeIn(tween(600, delayMillis = 500))
                ) {
                    LegalSection(context = context)
                }
            }
            
            // 联系方式
            item {
                AnimatedVisibility(
                    visible = contentVisible,
                    enter = slideInVertically(
                        initialOffsetY = { it },
                        animationSpec = tween(600, delayMillis = 600)
                    ) + fadeIn(tween(600, delayMillis = 600))
                ) {
                    ContactSection(context = context)
                }
            }
            
            // 版权信息
            item {
                AnimatedVisibility(
                    visible = contentVisible,
                    enter = slideInVertically(
                        initialOffsetY = { it },
                        animationSpec = tween(600, delayMillis = 700)
                    ) + fadeIn(tween(600, delayMillis = 700))
                ) {
                    CopyrightSection()
                }
            }
        }
        
        // 悬浮分享按钮
        AnimatedVisibility(
            visible = contentVisible,
            enter = scaleIn(tween(300, delayMillis = 1000)) + fadeIn(tween(300, delayMillis = 1000)),
            modifier = Modifier.align(Alignment.BottomEnd)
        ) {
            ShareFab(
                onShare = {
                    shareApp(context)
                },
                modifier = Modifier.padding(16.dp)
            )
        }
    }
}

/**
 * 应用头部信息
 */
@Composable
private fun AppHeaderSection(
    appInfo: AppInfo,
    logoVisible: Boolean,
    contentVisible: Boolean
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 8.dp),
        shape = RoundedCornerShape(20.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .background(
                    Brush.verticalGradient(
                        colors = listOf(
                            DustyLavender.copy(alpha = 0.1f),
                            Color.White
                        )
                    )
                )
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(32.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // 应用Logo
                AnimatedVisibility(
                    visible = logoVisible,
                    enter = scaleIn(
                        animationSpec = spring(
                            dampingRatio = Spring.DampingRatioMediumBouncy,
                            stiffness = Spring.StiffnessLow
                        )
                    ) + fadeIn(tween(500))
                ) {
                    Box(
                        modifier = Modifier
                            .size(100.dp)
                            .clip(RoundedCornerShape(24.dp))
                            .background(
                                Brush.radialGradient(
                                    colors = listOf(
                                        DustyLavender,
                                        DustyLavender.copy(alpha = 0.8f)
                                    )
                                )
                            ),
                        contentAlignment = Alignment.Center
                    ) {
                        Icon(
                            imageVector = Icons.Filled.AccessTime,
                            contentDescription = "TimeFlow Logo",
                            tint = Color.White,
                            modifier = Modifier.size(60.dp)
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(20.dp))
                
                // 应用名称
                AnimatedVisibility(
                    visible = contentVisible,
                    enter = slideInVertically(
                        initialOffsetY = { it },
                        animationSpec = tween(500, delayMillis = 200)
                    ) + fadeIn(tween(500, delayMillis = 200))
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Text(
                            text = "TimeFlow",
                            style = MaterialTheme.typography.headlineMedium,
                            fontWeight = FontWeight.Bold,
                            color = MaterialTheme.colorScheme.primary
                        )
                        
                        Spacer(modifier = Modifier.height(8.dp))
                        
                        Text(
                            text = "智能时间管理助手",
                            style = MaterialTheme.typography.bodyLarge,
                            color = MaterialTheme.colorScheme.onSurfaceVariant,
                            textAlign = TextAlign.Center
                        )
                        
                        Spacer(modifier = Modifier.height(12.dp))
                        
                        // 版本号
                        Surface(
                            color = DustyLavender.copy(alpha = 0.1f),
                            shape = RoundedCornerShape(16.dp)
                        ) {
                            Text(
                                text = "版本 ${appInfo.versionName}",
                                modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp),
                                style = MaterialTheme.typography.bodyMedium,
                                color = DustyLavender,
                                fontWeight = FontWeight.Medium
                            )
                        }
                    }
                }
            }
        }
    }
}

/**
 * 应用描述部分
 */
@Composable
private fun AppDescriptionSection() {
    InfoCard(
        title = "产品介绍",
        icon = Icons.Outlined.Info
    ) {
        Text(
            text = "TimeFlow是一款融合AI技术的智能时间管理应用，致力于帮助用户提升工作效率和生活品质。通过智能任务拆分、自动时间追踪、个性化主题定制等功能，让时间管理变得更加轻松高效。",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            lineHeight = 20.sp
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 产品理念
        Text(
            text = "产品理念",
            style = MaterialTheme.typography.titleSmall,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.primary
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Text(
            text = "「让每一分钟都有价值」- 我们相信通过智能化的时间管理工具，每个人都能找到属于自己的高效节奏，在忙碌中保持从容，在快节奏中找到生活的平衡。",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            lineHeight = 20.sp
        )
    }
}

/**
 * 功能亮点部分
 */
@Composable
private fun FeatureHighlightsSection() {
    InfoCard(
        title = stringResource(R.string.core_features),
        icon = Icons.Outlined.Star
    ) {
        val features = listOf(
            FeatureItem("🤖", stringResource(R.string.feature_ai_split), stringResource(R.string.feature_ai_split_desc)),
            FeatureItem("⏰", stringResource(R.string.feature_time_tracking), stringResource(R.string.feature_time_tracking_desc)),
            FeatureItem("🎨", stringResource(R.string.feature_personalized_theme), stringResource(R.string.feature_personalized_theme_desc)),
            FeatureItem("📊", stringResource(R.string.feature_data_analysis), stringResource(R.string.feature_data_analysis_desc)),
            FeatureItem("🗓️", stringResource(R.string.feature_calendar_sync), stringResource(R.string.feature_calendar_sync_desc)),
            FeatureItem("💭", stringResource(R.string.feature_emotion_record), stringResource(R.string.feature_emotion_record_desc)),
            FeatureItem("🎯", stringResource(R.string.feature_goal_tracking), stringResource(R.string.feature_goal_tracking_desc)),
            FeatureItem("📱", stringResource(R.string.feature_kanban_management), stringResource(R.string.feature_kanban_management_desc))
        )
        
        features.forEach { feature ->
            FeatureRow(feature = feature)
            if (feature != features.last()) {
                Spacer(modifier = Modifier.height(12.dp))
            }
        }
    }
}

/**
 * 功能项
 */
data class FeatureItem(
    val emoji: String,
    val title: String,
    val description: String
)

/**
 * 功能行组件
 */
@Composable
private fun FeatureRow(feature: FeatureItem) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.Top
    ) {
        Text(
            text = feature.emoji,
            fontSize = 20.sp,
            modifier = Modifier.padding(end = 12.dp, top = 2.dp)
        )
        
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = feature.title,
                style = MaterialTheme.typography.bodyLarge,
                fontWeight = FontWeight.Medium,
                color = MaterialTheme.colorScheme.primary
            )
            
            Text(
                text = feature.description,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                lineHeight = 18.sp
            )
        }
    }
}

/**
 * 版本信息部分
 */
@Composable
private fun VersionInfoSection(
    appInfo: AppInfo,
    onCopyVersionInfo: () -> Unit
) {
    InfoCard(
        title = "版本信息",
        icon = Icons.Outlined.Code,
        action = {
            IconButton(onClick = onCopyVersionInfo) {
                Icon(
                    Icons.Outlined.ContentCopy,
                    contentDescription = "复制版本信息",
                    tint = DustyLavender
                )
            }
        }
    ) {
        VersionInfoRow("应用版本", appInfo.versionName)
        VersionInfoRow("版本代码", appInfo.versionCode.toString())
        VersionInfoRow("编译时间", appInfo.buildTime)
        VersionInfoRow("最小系统版本", "Android 8.0 (API 26)")
        VersionInfoRow("目标系统版本", "Android 14 (API 34)")
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Text(
            text = "长按可复制版本信息",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.6f),
            modifier = Modifier.padding(top = 8.dp)
        )
    }
}

/**
 * 版本信息行
 */
@Composable
private fun VersionInfoRow(label: String, value: String) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = label,
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        
        Text(
            text = value,
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.primary,
            fontWeight = FontWeight.Medium
        )
    }
}

/**
 * 团队信息部分
 */
@Composable
private fun TeamInfoSection() {
    InfoCard(
        title = "开发团队",
        icon = Icons.Outlined.Group
    ) {
        Column {
            TeamMemberCard(
                name = "Lyaan",
                role = "产品设计师 & 开发者",
                description = "专注于用户体验设计和产品功能开发，致力于创造简洁美观的时间管理解决方案。",
                avatar = "👨‍💻"
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Text(
                text = "感谢所有为TimeFlow贡献想法和反馈的用户们，你们的建议让这个产品变得更好！",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                lineHeight = 20.sp,
                fontStyle = androidx.compose.ui.text.font.FontStyle.Italic
            )
        }
    }
}

/**
 * 团队成员卡片
 */
@Composable
private fun TeamMemberCard(
    name: String,
    role: String,
    description: String,
    avatar: String
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.Top
    ) {
        Box(
            modifier = Modifier
                .size(50.dp)
                .clip(CircleShape)
                .background(DustyLavender.copy(alpha = 0.1f)),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = avatar,
                fontSize = 24.sp
            )
        }
        
        Spacer(modifier = Modifier.width(16.dp))
        
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = name,
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.primary
            )
            
            Text(
                text = role,
                style = MaterialTheme.typography.bodyMedium,
                color = DustyLavender,
                fontWeight = FontWeight.Medium
            )
            
            Spacer(modifier = Modifier.height(4.dp))
            
            Text(
                text = description,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                lineHeight = 16.sp
            )
        }
    }
}

/**
 * 法律信息部分
 */
@Composable
private fun LegalSection(context: Context) {
    InfoCard(
        title = "法律信息",
        icon = Icons.Outlined.Gavel
    ) {
        LegalItem(
            title = "用户协议",
            description = "查看TimeFlow用户服务协议",
            onClick = { openUrl(context, "https://timeflow.app/terms") }
        )
        
        Spacer(modifier = Modifier.height(12.dp))
        
        LegalItem(
            title = "隐私政策",
            description = "了解我们如何保护您的隐私",
            onClick = { openUrl(context, "https://timeflow.app/privacy") }
        )
        
        Spacer(modifier = Modifier.height(12.dp))
        
        LegalItem(
            title = "第三方许可",
            description = "查看使用的开源组件许可信息",
            onClick = { openUrl(context, "https://timeflow.app/licenses") }
        )
    }
}

/**
 * 法律信息项
 */
@Composable
private fun LegalItem(
    title: String,
    description: String,
    onClick: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() }
            .padding(vertical = 8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.bodyLarge,
                fontWeight = FontWeight.Medium,
                color = MaterialTheme.colorScheme.primary
            )
            
            Text(
                text = description,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
        
        Icon(
            Icons.Filled.ChevronRight,
            contentDescription = null,
            tint = MaterialTheme.colorScheme.onSurfaceVariant,
            modifier = Modifier.size(20.dp)
        )
    }
}

/**
 * 联系方式部分
 */
@Composable
private fun ContactSection(context: Context) {
    InfoCard(
        title = "联系我们",
        icon = Icons.Outlined.ContactMail
    ) {
        ContactItem(
            icon = Icons.Outlined.Email,
            title = "邮箱反馈",
            content = "<EMAIL>",
            onClick = { sendEmail(context, "<EMAIL>") }
        )
        
        Spacer(modifier = Modifier.height(12.dp))
        
        ContactItem(
            icon = Icons.Outlined.Code,
            title = "GitHub",
            content = "github.com/timeflow/app",
            onClick = { openUrl(context, "https://github.com/timeflow/app") }
        )
        
        Spacer(modifier = Modifier.height(12.dp))
        
        ContactItem(
            icon = Icons.Outlined.BugReport,
            title = "问题反馈",
            content = "反馈Bug或建议",
            onClick = { openUrl(context, "https://github.com/timeflow/app/issues") }
        )
    }
}

/**
 * 联系方式项
 */
@Composable
private fun ContactItem(
    icon: ImageVector,
    title: String,
    content: String,
    onClick: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() }
            .padding(vertical = 8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = icon,
            contentDescription = title,
            tint = DustyLavender,
            modifier = Modifier.size(24.dp)
        )
        
        Spacer(modifier = Modifier.width(16.dp))
        
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.bodyLarge,
                fontWeight = FontWeight.Medium,
                color = MaterialTheme.colorScheme.primary
            )
            
            Text(
                text = content,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
        
        Icon(
            Icons.Filled.ChevronRight,
            contentDescription = null,
            tint = MaterialTheme.colorScheme.onSurfaceVariant,
            modifier = Modifier.size(20.dp)
        )
    }
}





/**
 * 版权信息部分
 */
@Composable
private fun CopyrightSection() {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 8.dp),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(24.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = "© 2024 TimeFlow Team",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.primary,
                fontWeight = FontWeight.Medium
            )
            
            Spacer(modifier = Modifier.height(4.dp))
            
            Text(
                text = "All Rights Reserved",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = "Made with ❤️ for better time management",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                textAlign = TextAlign.Center
            )
        }
    }
}

/**
 * 信息卡片组件
 */
@Composable
private fun InfoCard(
    title: String,
    icon: ImageVector,
    action: @Composable (() -> Unit)? = null,
    content: @Composable () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 8.dp),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(20.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = icon,
                        contentDescription = title,
                        tint = DustyLavender,
                        modifier = Modifier.size(24.dp)
                    )
                    
                    Spacer(modifier = Modifier.width(12.dp))
                    
                    Text(
                        text = title,
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.primary
                    )
                }
                
                action?.invoke()
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            content()
        }
    }
}

/**
 * 分享悬浮按钮
 */
@Composable
private fun ShareFab(
    onShare: () -> Unit,
    modifier: Modifier = Modifier
) {
    FloatingActionButton(
        onClick = onShare,
        modifier = modifier,
        containerColor = DustyLavender,
        contentColor = Color.White
    ) {
        Icon(
            imageVector = Icons.Filled.Share,
            contentDescription = "分享应用"
        )
    }
}

/**
 * 应用信息数据类
 */
data class AppInfo(
    val versionName: String,
    val versionCode: Long,
    val buildTime: String
)

/**
 * 获取应用信息
 */
private fun getAppInfo(context: Context): AppInfo {
    return try {
        val packageInfo: PackageInfo = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            context.packageManager.getPackageInfo(
                context.packageName,
                PackageManager.PackageInfoFlags.of(0)
            )
        } else {
            @Suppress("DEPRECATION")
            context.packageManager.getPackageInfo(context.packageName, 0)
        }
        
        val versionCode = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            packageInfo.longVersionCode
        } else {
            @Suppress("DEPRECATION")
            packageInfo.versionCode.toLong()
        }
        
        // 获取构建时间
        val buildTime = try {
            val applicationInfo = packageInfo.applicationInfo
            val apkFile = java.io.File(applicationInfo.sourceDir)
            val buildDate = Date(apkFile.lastModified())
            SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault()).format(buildDate)
        } catch (e: Exception) {
            "未知"
        }
        
        AppInfo(
            versionName = packageInfo.versionName ?: "1.0.0",
            versionCode = versionCode,
            buildTime = buildTime
        )
    } catch (e: Exception) {
        AppInfo(
            versionName = "1.0.0",
            versionCode = 1,
            buildTime = "未知"
        )
    }
}

/**
 * 打开URL
 */
private fun openUrl(context: Context, url: String) {
    try {
        val intent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
        context.startActivity(intent)
    } catch (e: Exception) {
        // 处理无法打开的情况
    }
}

/**
 * 发送邮件
 */
private fun sendEmail(context: Context, email: String) {
    try {
        val intent = Intent(Intent.ACTION_SENDTO).apply {
            data = Uri.parse("mailto:$email")
            putExtra(Intent.EXTRA_SUBJECT, "TimeFlow 用户反馈")
            putExtra(Intent.EXTRA_TEXT, "请在此输入您的反馈内容...")
        }
        context.startActivity(intent)
    } catch (e: Exception) {
        // 处理无法发送邮件的情况
    }
}

/**
 * 分享应用
 */
private fun shareApp(context: Context) {
    try {
        val intent = Intent(Intent.ACTION_SEND).apply {
            type = "text/plain"
            putExtra(Intent.EXTRA_SUBJECT, "推荐一款时间管理应用")
            putExtra(
                Intent.EXTRA_TEXT,
                "我在使用TimeFlow时间管理应用，功能很强大，推荐给你！\n\n" +
                "主要功能：\n" +
                "• AI智能任务拆分\n" +
                "• 自动时间追踪\n" +
                "• 个性化主题\n" +
                "• 数据统计分析\n\n" +
                "下载地址：https://timeflow.app"
            )
        }
        context.startActivity(Intent.createChooser(intent, "分享TimeFlow"))
    } catch (e: Exception) {
        // 处理分享失败的情况
    }
} 