package com.timeflow.app.ui.task;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SharedFilterState_Factory implements Factory<SharedFilterState> {
  @Override
  public SharedFilterState get() {
    return newInstance();
  }

  public static SharedFilterState_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static SharedFilterState newInstance() {
    return new SharedFilterState();
  }

  private static final class InstanceHolder {
    private static final SharedFilterState_Factory INSTANCE = new SharedFilterState_Factory();
  }
}
