package com.timeflow.app.worker;

import android.content.Context;
import androidx.work.WorkerParameters;
import com.timeflow.app.utils.DatabaseBackupManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AutoBackupWorker_Factory {
  private final Provider<DatabaseBackupManager> backupManagerProvider;

  public AutoBackupWorker_Factory(Provider<DatabaseBackupManager> backupManagerProvider) {
    this.backupManagerProvider = backupManagerProvider;
  }

  public AutoBackupWorker get(Context context, WorkerParameters workerParams) {
    return newInstance(context, workerParams, backupManagerProvider.get());
  }

  public static AutoBackupWorker_Factory create(
      Provider<DatabaseBackupManager> backupManagerProvider) {
    return new AutoBackupWorker_Factory(backupManagerProvider);
  }

  public static AutoBackupWorker newInstance(Context context, WorkerParameters workerParams,
      DatabaseBackupManager backupManager) {
    return new AutoBackupWorker(context, workerParams, backupManager);
  }
}
