package com.timeflow.app.di;

import com.timeflow.app.data.dao.ReflectionDao;
import com.timeflow.app.data.db.AppDatabase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DatabaseModule_ProvideReflectionDaoFactory implements Factory<ReflectionDao> {
  private final Provider<AppDatabase> databaseProvider;

  public DatabaseModule_ProvideReflectionDaoFactory(Provider<AppDatabase> databaseProvider) {
    this.databaseProvider = databaseProvider;
  }

  @Override
  public ReflectionDao get() {
    return provideReflectionDao(databaseProvider.get());
  }

  public static DatabaseModule_ProvideReflectionDaoFactory create(
      Provider<AppDatabase> databaseProvider) {
    return new DatabaseModule_ProvideReflectionDaoFactory(databaseProvider);
  }

  public static ReflectionDao provideReflectionDao(AppDatabase database) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.provideReflectionDao(database));
  }
}
