package com.timeflow.app.data.repository;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class EventRepositoryImpl_Factory implements Factory<EventRepositoryImpl> {
  @Override
  public EventRepositoryImpl get() {
    return newInstance();
  }

  public static EventRepositoryImpl_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static EventRepositoryImpl newInstance() {
    return new EventRepositoryImpl();
  }

  private static final class InstanceHolder {
    private static final EventRepositoryImpl_Factory INSTANCE = new EventRepositoryImpl_Factory();
  }
}
