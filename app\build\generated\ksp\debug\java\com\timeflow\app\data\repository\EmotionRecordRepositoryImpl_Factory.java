package com.timeflow.app.data.repository;

import com.timeflow.app.data.dao.EmotionRecordDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class EmotionRecordRepositoryImpl_Factory implements Factory<EmotionRecordRepositoryImpl> {
  private final Provider<EmotionRecordDao> emotionRecordDaoProvider;

  public EmotionRecordRepositoryImpl_Factory(Provider<EmotionRecordDao> emotionRecordDaoProvider) {
    this.emotionRecordDaoProvider = emotionRecordDaoProvider;
  }

  @Override
  public EmotionRecordRepositoryImpl get() {
    return newInstance(emotionRecordDaoProvider.get());
  }

  public static EmotionRecordRepositoryImpl_Factory create(
      Provider<EmotionRecordDao> emotionRecordDaoProvider) {
    return new EmotionRecordRepositoryImpl_Factory(emotionRecordDaoProvider);
  }

  public static EmotionRecordRepositoryImpl newInstance(EmotionRecordDao emotionRecordDao) {
    return new EmotionRecordRepositoryImpl(emotionRecordDao);
  }
}
