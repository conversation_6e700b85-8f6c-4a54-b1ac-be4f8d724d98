package com.timeflow.app.di;

import com.timeflow.app.data.dao.KanbanBoardDao;
import com.timeflow.app.data.db.AppDatabase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DatabaseModule_ProvideKanbanBoardDaoFactory implements Factory<KanbanBoardDao> {
  private final Provider<AppDatabase> databaseProvider;

  public DatabaseModule_ProvideKanbanBoardDaoFactory(Provider<AppDatabase> databaseProvider) {
    this.databaseProvider = databaseProvider;
  }

  @Override
  public KanbanBoardDao get() {
    return provideKanbanBoardDao(databaseProvider.get());
  }

  public static DatabaseModule_ProvideKanbanBoardDaoFactory create(
      Provider<AppDatabase> databaseProvider) {
    return new DatabaseModule_ProvideKanbanBoardDaoFactory(databaseProvider);
  }

  public static KanbanBoardDao provideKanbanBoardDao(AppDatabase database) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.provideKanbanBoardDao(database));
  }
}
