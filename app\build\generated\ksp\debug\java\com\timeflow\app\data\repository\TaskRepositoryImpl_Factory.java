package com.timeflow.app.data.repository;

import com.timeflow.app.data.dao.TaskDao;
import com.timeflow.app.data.db.AppDatabase;
import com.timeflow.app.service.TaskReminderScheduler;
import com.timeflow.app.utils.TaskReminderUtils;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class TaskRepositoryImpl_Factory implements Factory<TaskRepositoryImpl> {
  private final Provider<TaskDao> taskDaoProvider;

  private final Provider<AppDatabase> taskDatabaseProvider;

  private final Provider<TaskReminderScheduler> taskReminderSchedulerProvider;

  private final Provider<TaskReminderUtils> taskReminderUtilsProvider;

  public TaskRepositoryImpl_Factory(Provider<TaskDao> taskDaoProvider,
      Provider<AppDatabase> taskDatabaseProvider,
      Provider<TaskReminderScheduler> taskReminderSchedulerProvider,
      Provider<TaskReminderUtils> taskReminderUtilsProvider) {
    this.taskDaoProvider = taskDaoProvider;
    this.taskDatabaseProvider = taskDatabaseProvider;
    this.taskReminderSchedulerProvider = taskReminderSchedulerProvider;
    this.taskReminderUtilsProvider = taskReminderUtilsProvider;
  }

  @Override
  public TaskRepositoryImpl get() {
    return newInstance(taskDaoProvider.get(), taskDatabaseProvider.get(), taskReminderSchedulerProvider, taskReminderUtilsProvider.get());
  }

  public static TaskRepositoryImpl_Factory create(Provider<TaskDao> taskDaoProvider,
      Provider<AppDatabase> taskDatabaseProvider,
      Provider<TaskReminderScheduler> taskReminderSchedulerProvider,
      Provider<TaskReminderUtils> taskReminderUtilsProvider) {
    return new TaskRepositoryImpl_Factory(taskDaoProvider, taskDatabaseProvider, taskReminderSchedulerProvider, taskReminderUtilsProvider);
  }

  public static TaskRepositoryImpl newInstance(TaskDao taskDao, AppDatabase taskDatabase,
      Provider<TaskReminderScheduler> taskReminderSchedulerProvider,
      TaskReminderUtils taskReminderUtils) {
    return new TaskRepositoryImpl(taskDao, taskDatabase, taskReminderSchedulerProvider, taskReminderUtils);
  }
}
