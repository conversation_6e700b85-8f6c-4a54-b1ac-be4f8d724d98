package com.timeflow.app.ui.settings

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Check
import androidx.compose.material.icons.filled.ColorLens
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material.icons.filled.KeyboardArrowUp
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material.icons.filled.KeyboardArrowRight
import androidx.compose.material.icons.filled.Palette
import androidx.compose.material.icons.filled.Dashboard
import androidx.compose.material.icons.filled.FormatColorFill
import androidx.compose.material.icons.filled.PriorityHigh
import androidx.compose.material.icons.filled.Preview
import androidx.compose.material.icons.filled.CheckCircle
import androidx.compose.material.icons.filled.MoreVert
import androidx.compose.material.icons.filled.Sync
import androidx.compose.material.icons.filled.Layers
import androidx.compose.material.icons.filled.Style
import androidx.compose.material.icons.filled.ContentCopy
import androidx.compose.material.icons.outlined.CheckCircle
import androidx.compose.material.icons.outlined.ColorLens
import androidx.compose.material.icons.filled.BrightnessAuto
import androidx.compose.material.icons.filled.LightMode
import androidx.compose.material.icons.filled.DarkMode
import androidx.compose.material.icons.filled.Settings as SettingsSuggest
import androidx.compose.material.icons.filled.ExpandMore
import androidx.compose.material.icons.filled.FileUpload
import androidx.compose.material.icons.filled.FontDownload
import androidx.compose.material.icons.filled.Delete
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.text.selection.SelectionContainer
import androidx.compose.material3.*
import androidx.compose.material3.TabRowDefaults.tabIndicatorOffset
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.graphics.luminance
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import com.timeflow.app.data.model.Priority
import com.timeflow.app.ui.screen.calendar.CalendarViewModel
import com.timeflow.app.ui.screen.calendar.UserColorPreference
import com.timeflow.app.utils.RenderOptimizer
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.spring
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.expandVertically
import androidx.compose.animation.shrinkVertically
import androidx.compose.animation.core.tween
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.animateDpAsState
import android.os.Build
import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.app.Activity
import android.util.Log
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.ui.platform.LocalHapticFeedback
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.hapticfeedback.HapticFeedbackType
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import com.timeflow.app.utils.SystemBarManager
import androidx.compose.animation.core.updateTransition
import androidx.compose.ui.platform.LocalContext
import android.widget.Toast
import androidx.compose.animation.animateContentSize
import androidx.compose.ui.geometry.Offset
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.ui.res.stringResource
import androidx.navigation.NavController
import com.timeflow.app.R
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Card
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.OutlinedTextFieldDefaults
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.input.KeyboardType
import kotlin.math.pow
import java.io.ByteArrayOutputStream

/**
 * 根据背景色亮度获取适合的文本颜色
 */
fun getAppropriateTextColor(backgroundColor: Color): Color {
    return if (backgroundColor.luminance() > 0.5f) {
        Color.Black
    } else {
        Color.White
    }
}

/**
 * 主题模式枚举
 */
enum class ThemeMode {
    LIGHT, DARK, FOLLOW_SYSTEM
}

/**
 * 应用颜色定义
 */
object AppColors {
    val primaryColors = listOf(
        Color(0xFFf5f4f6), // 默认紫色
        Color(0xFF3700B3), // 深紫色
        Color(0xFF03DAC6), // 青色
        Color(0xFF018786), // 深青色
        Color(0xFFFF0266), // 粉红色
        Color(0xFF2196F3), // 蓝色
        Color(0xFF4CAF50), // 绿色
        Color(0xFFFFC107), // 琥珀色
        Color(0xFFFF5722), // 深橙色
        Color(0xFF795548), // 棕色
        Color(0xFF607D8B), // 蓝灰色
        Color(0xFF9C27B0)  // 紫色
    )
}

/**
 * 主题设置界面 - 美化版本
 * 参照Google Photos、Spotify等知名应用的设计风格
 * 使用莫兰迪、莫奈配色方案
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ThemeSettingsScreen(
    navController: NavController,
    viewModel: ThemeSettingsViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    var showColorPicker by remember { mutableStateOf(false) }
    var currentColorType by remember { mutableStateOf(ColorType.PRIMARY) }
    
    val selectedColor = when (currentColorType) {
        ColorType.PRIMARY -> uiState.customPrimaryColor
        ColorType.BACKGROUND -> uiState.customBackgroundColor
        ColorType.HOME -> uiState.homePageColor
        ColorType.CALENDAR -> uiState.calendarPageColor
        ColorType.STATISTICS -> uiState.statisticsPageColor
        ColorType.PROFILE -> uiState.profilePageColor
        ColorType.SETTINGS -> uiState.settingsPageColor
    }
    
    // 获取活动上下文用于状态栏处理
    val context = LocalContext.current
    val activity = remember { context as? Activity }
    
    // 状态栏处理
    SideEffect {
        activity?.let { 
            SystemBarManager.forceOpaqueStatusBar(it)
        }
    }
    
    activity?.let { act ->
        DisposableEffect(key1 = Unit) {
            val window = act.window
            val originalStatusBarColor = window.statusBarColor
            SystemBarManager.forceOpaqueStatusBar(act)
            
            onDispose {
                window.statusBarColor = originalStatusBarColor
                Log.d("ThemeSettingsScreen", "ThemeSettingsScreen disposed")
            }
        }
    }
    
    // 背景色 - 使用莫兰迪米色
    val backgroundColor = Color(0xFFF7F5F2)
    
    Scaffold(
        topBar = {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(backgroundColor)
                    .padding(top = SystemBarManager.getFixedStatusBarHeight())
            ) {
                // 现代化的顶部栏设计
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 20.dp, vertical = 12.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 返回按钮 - 圆形设计
                    Box(
                        modifier = Modifier
                            .size(36.dp)
                            .clip(CircleShape)
                            .background(Color(0xFFE8E4E0))
                            .clickable { navController.popBackStack() },
                        contentAlignment = Alignment.Center
                    ) {
                        Icon(
                            imageVector = Icons.Filled.ArrowBack,
                            contentDescription = stringResource(R.string.back),
                            tint = MaterialTheme.colorScheme.primary,
                            modifier = Modifier.size(18.dp)
                        )
                    }
                    
                    Spacer(modifier = Modifier.width(16.dp))
                    
                    // 标题 - 缩小字体
                    Text(
                        text = stringResource(R.string.theme_settings_title),
                            fontSize = 20.sp,
                            fontWeight = FontWeight.SemiBold,
                        color = MaterialTheme.colorScheme.primary
                    )
                    
                    Spacer(modifier = Modifier.weight(1f))
                    
                    // 右侧操作按钮
                    Box(
                        modifier = Modifier
                            .size(36.dp)
                            .clip(CircleShape)
                            .background(Color(0xFFE8E4E0))
                            .clickable { 
                                // 重置主题
                                viewModel.resetToDefault()
                },
                        contentAlignment = Alignment.Center
                    ) {
                        Icon(
                            imageVector = Icons.Filled.Refresh,
                            contentDescription = stringResource(R.string.reset),
                            tint = MaterialTheme.colorScheme.primary,
                            modifier = Modifier.size(18.dp)
                    )
                    }
                }
            }
        }
    ) { innerPadding ->
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .background(backgroundColor)
                .padding(innerPadding)
                .padding(horizontal = 20.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            // 欢迎卡片
            item {
                ModernWelcomeCard()
            }
            
            // 动态颜色设置
            item {
                ModernMaterialYouCard(
                enabled = uiState.useDynamicColor,
                onEnabledChange = { viewModel.updateUseDynamicColor(it) },
                    title = stringResource(R.string.material_you),
                    subtitle = stringResource(R.string.material_you_subtitle),
                    icon = Icons.Default.Palette
                )
            }
            
            // 统一背景设置
            item {
                ModernMaterialYouCard(
                enabled = uiState.useUnifiedBackground,
                onEnabledChange = { viewModel.updateUseUnifiedBackground(it) },
                    title = stringResource(R.string.unified_background),
                    subtitle = stringResource(R.string.unified_background_desc),
                    icon = Icons.Default.Layers
                )
            }
            
            // 自定义字体设置
            item {
                ModernSectionHeader(stringResource(R.string.font_settings))
            }
            
            item {
                CustomFontSection(
                    uiState = uiState,
                    viewModel = viewModel
                )
            }
            
            // 自定义颜色设置
            if (!uiState.useDynamicColor) {
                item {
                    ModernSectionHeader(stringResource(R.string.custom_colors))
                }
                
                // 主色调
                item {
                    ModernColorCard(
                        title = stringResource(R.string.primary_color),
                        subtitle = stringResource(R.string.primary_color_desc),
                    color = uiState.customPrimaryColor,
                    onClick = {
                        currentColorType = ColorType.PRIMARY
                        showColorPicker = true
                    }
                )
                }
                
                // 背景色设置
                if (uiState.useUnifiedBackground) {
                    item {
                        ModernColorCard(
                            title = stringResource(R.string.unified_background_color),
                            subtitle = stringResource(R.string.unified_background_color_desc),
                        color = uiState.customBackgroundColor,
                        onClick = {
                            currentColorType = ColorType.BACKGROUND
                            showColorPicker = true
                        }
                    )
                    }
                } else {
                    // 分页面背景色
                    item {
                        ModernPageColorsSection(
                            uiState = uiState,
                            onColorClick = { colorType ->
                                currentColorType = colorType
                            showColorPicker = true
                        }
                    )
                    }
                }
            }
            
            // 预设主题
            item {
                ModernSectionHeader(stringResource(R.string.preset_themes_title))
            }
            
            item {
                ModernPresetThemeNavigationCard(
                    onNavigateToPresetThemes = {
                        navController.navigate("preset_themes")
                    }
                )
            }
            
            // 底部间距
            item {
                Spacer(modifier = Modifier.height(32.dp))
                }
            }
            
            // 颜色选择器对话框
            if (showColorPicker) {
            ModernColorPickerDialog(
                    initialColor = selectedColor,
                    onColorSelected = { color ->
                        when (currentColorType) {
                            ColorType.PRIMARY -> viewModel.updateCustomPrimaryColor(color)
                            ColorType.BACKGROUND -> viewModel.updateCustomBackgroundColor(color)
                            ColorType.HOME -> viewModel.updatePageBackgroundColor(ColorType.HOME, color)
                            ColorType.CALENDAR -> viewModel.updatePageBackgroundColor(ColorType.CALENDAR, color)
                            ColorType.STATISTICS -> viewModel.updatePageBackgroundColor(ColorType.STATISTICS, color)
                            ColorType.PROFILE -> viewModel.updatePageBackgroundColor(ColorType.PROFILE, color)
                            ColorType.SETTINGS -> viewModel.updatePageBackgroundColor(ColorType.SETTINGS, color)
                        }
                        showColorPicker = false
                    },
                    onDismiss = { showColorPicker = false }
                )
            }
        }
    }

/**
 * 现代化欢迎卡片 - 莫兰迪配色
 */
@Composable
fun ModernWelcomeCard() {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 6.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color(0xFFF8F6F3)
        ),
        shape = RoundedCornerShape(16.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(20.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // 图标 - 缩小尺寸
            Box(
                modifier = Modifier
                    .size(48.dp)
                    .clip(CircleShape)
                    .background(
                        Brush.radialGradient(
                            colors = listOf(
                                Color(0xFFB8A9A0), // 莫兰迪灰棕
                                Color(0xFF9A8B7A)  // 莫兰迪深棕
                            )
                        )
                    ),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = Icons.Default.Palette,
                    contentDescription = null,
                    tint = Color.White,
                    modifier = Modifier.size(22.dp)
                )
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            Text(
                text = stringResource(R.string.personalize_experience),
                fontSize = 16.sp,
                fontWeight = FontWeight.SemiBold,
                color = MaterialTheme.colorScheme.primary,
                textAlign = TextAlign.Center
            )
            
            Spacer(modifier = Modifier.height(6.dp))
            
            Text(
                text = stringResource(R.string.choose_color_theme),
                fontSize = 12.sp,
                color = MaterialTheme.colorScheme.primary,
                textAlign = TextAlign.Center,
                lineHeight = 16.sp
            )
        }
    }
}

/**
 * 现代化Material You卡片 - 莫兰迪配色
 */
@Composable
fun ModernMaterialYouCard(
    enabled: Boolean,
    onEnabledChange: (Boolean) -> Unit,
    title: String,
    subtitle: String,
    icon: ImageVector
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 6.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color(0xFFF8F6F3)
        ),
        shape = RoundedCornerShape(16.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 图标 - 缩小尺寸
            Box(
                modifier = Modifier
                    .size(40.dp)
                    .clip(CircleShape)
                    .background(
                        if (enabled) Color(0xFFB8A9A0) else Color(0xFFE8E4E0)
                    ),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = icon,
                    contentDescription = null,
                    tint = if (enabled) Color.White else Color(0xFF8B7D6B),
                    modifier = Modifier.size(20.dp)
                )
            }
            
            Spacer(modifier = Modifier.width(12.dp))
            
            // 文本内容 - 缩小字体
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = title,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = MaterialTheme.colorScheme.primary
                )
                
                Text(
                    text = subtitle,
                    fontSize = 12.sp,
                    color = MaterialTheme.colorScheme.primary,
                    lineHeight = 16.sp
                )
            }
            
            // 开关
            Switch(
                checked = enabled,
                onCheckedChange = onEnabledChange,
                colors = SwitchDefaults.colors(
                    checkedThumbColor = Color.White,
                    checkedTrackColor = Color(0xFFB8A9A0),
                    uncheckedThumbColor = Color.White,
                    uncheckedTrackColor = Color(0xFFD4CEC8)
                )
            )
        }
    }
}

/**
 * 现代化颜色卡片 - 莫兰迪配色
 */
@Composable
fun ModernColorCard(
    title: String,
    subtitle: String,
    color: Color,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 6.dp)
            .clickable(onClick = onClick),
        colors = CardDefaults.cardColors(
            containerColor = Color(0xFFF8F6F3)
        ),
        shape = RoundedCornerShape(16.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 颜色预览 - 缩小尺寸
            Box(
                modifier = Modifier
                    .size(40.dp)
                    .clip(RoundedCornerShape(10.dp))
                    .background(color)
                    .border(
                        width = 1.5.dp,
                        color = Color(0xFFD4CEC8),
                        shape = RoundedCornerShape(10.dp)
                    )
            )
            
            Spacer(modifier = Modifier.width(12.dp))
            
            // 文本内容 - 缩小字体
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = title,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = MaterialTheme.colorScheme.primary
                )
                
                Text(
                    text = subtitle,
                    fontSize = 12.sp,
                    color = MaterialTheme.colorScheme.primary,
                    lineHeight = 16.sp
                )
            }
            
            // 箭头 - 缩小尺寸
            Icon(
                imageVector = Icons.Default.KeyboardArrowRight,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.primary,
                modifier = Modifier.size(18.dp)
            )
        }
    }
}

/**
 * 现代化分页面颜色设置 - 莫兰迪配色
 */
@Composable
fun ModernPageColorsSection(
    uiState: ThemeSettingsViewModel.UiState,
    onColorClick: (ColorType) -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 6.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color(0xFFF8F6F3)
        ),
        shape = RoundedCornerShape(16.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Text(
                text = stringResource(R.string.page_background_colors),
                fontSize = 16.sp,
                fontWeight = FontWeight.SemiBold,
                color = MaterialTheme.colorScheme.primary
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 页面颜色网格
            val pageColors = listOf(
                Triple(ColorType.HOME, stringResource(R.string.home_page), uiState.homePageColor),
                Triple(ColorType.CALENDAR, stringResource(R.string.calendar_page), uiState.calendarPageColor),
                Triple(ColorType.STATISTICS, stringResource(R.string.statistics_page), uiState.statisticsPageColor),
                Triple(ColorType.PROFILE, stringResource(R.string.profile_page), uiState.profilePageColor)
            )
            LazyVerticalGrid(
                columns = GridCells.Fixed(2),
                horizontalArrangement = Arrangement.spacedBy(10.dp),
                verticalArrangement = Arrangement.spacedBy(10.dp),
                modifier = Modifier.height(160.dp)
            ) {
                items(pageColors.size) { index ->
                    val (type, title, color) = pageColors[index]
                    ModernPageColorItem(
                        title = title,
                        color = color,
                        onClick = { onColorClick(type) }
                    )
                }
            }
        }
    }
}

/**
 * 现代化页面颜色项 - 莫兰迪配色
 */
@Composable
fun ModernPageColorItem(
    title: String,
    color: Color,
    onClick: () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .clickable(onClick = onClick),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Box(
            modifier = Modifier
                .size(50.dp)
                .clip(RoundedCornerShape(12.dp))
                .background(color)
                .border(
                    width = 1.5.dp,
                    color = Color(0xFFD4CEC8),
                    shape = RoundedCornerShape(12.dp)
                )
        )
        
        Spacer(modifier = Modifier.height(6.dp))
        
        Text(
            text = title,
            fontSize = 11.sp,
            fontWeight = FontWeight.Medium,
            color = MaterialTheme.colorScheme.primary,
            textAlign = TextAlign.Center
        )
    }
}

/**
 * 现代化预设主题导航卡片
 */
@Composable
fun ModernPresetThemeNavigationCard(
    onNavigateToPresetThemes: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 6.dp)
            .clickable { onNavigateToPresetThemes() },
        colors = CardDefaults.cardColors(
            containerColor = Color(0xFFF8F6F3)
        ),
        shape = RoundedCornerShape(16.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 图标
            Box(
                modifier = Modifier
                    .size(48.dp)
                    .clip(RoundedCornerShape(12.dp))
                    .background(
                        Brush.linearGradient(
                            colors = listOf(
                                Color(0xFFF2F1F3),
                                Color(0xFFB3A9B5)
                            )
                        )
                    ),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = Icons.Filled.Palette,
                    contentDescription = stringResource(R.string.preset_themes_title),
                    tint = Color(0xFF6B5A6B),
                    modifier = Modifier.size(24.dp)
                )
            }
            
            Spacer(modifier = Modifier.width(16.dp))
            
            // 文字内容
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = stringResource(R.string.preset_themes_title),
                    fontSize = 16.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = MaterialTheme.colorScheme.primary
                )
                
                Text(
                    text = stringResource(R.string.preset_themes_desc),
                    fontSize = 13.sp,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    lineHeight = 18.sp
                )
            }
            
            // 箭头图标
            Icon(
                imageVector = Icons.Filled.KeyboardArrowRight,
                contentDescription = stringResource(R.string.enter),
                tint = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.size(20.dp)
            )
        }
    }
    
    // 预览主题色块
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 20.dp)
            .padding(top = 8.dp),
        horizontalArrangement = Arrangement.spacedBy(6.dp)
    ) {
        val previewColors = listOf(
            Color(0xFF6200EE),  // 紫色
            Color(0xFF03DAC6),  // 青色  
            Color(0xFFFF9800),  // 橙色
            Color(0xFF4CAF50),  // 绿色
            Color(0xFF2196F3),  // 蓝色
            Color(0xFF9C27B0)   // 品红
        )
        
        previewColors.forEach { color ->
            Box(
                modifier = Modifier
                    .weight(1f)
                    .height(4.dp)
                    .clip(RoundedCornerShape(2.dp))
                    .background(color)
            )
        }
    }
}

/**
 * 现代化预设主题项 - 莫兰迪配色
 */
@Composable
fun ModernPresetThemeItem(
    name: String,
    primaryColor: Color,
    backgroundColor: Color,
    onClick: () -> Unit
) {
    Column(
        modifier = Modifier
            .width(70.dp)
            .clickable(onClick = onClick),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Box(
            modifier = Modifier
                .size(50.dp)
                .clip(RoundedCornerShape(12.dp))
                .background(backgroundColor)
                .border(
                    width = 1.5.dp,
                    color = primaryColor,
                    shape = RoundedCornerShape(12.dp)
                ),
            contentAlignment = Alignment.Center
        ) {
            Box(
                modifier = Modifier
                    .size(16.dp)
                    .clip(CircleShape)
                    .background(primaryColor)
            )
        }
        
        Spacer(modifier = Modifier.height(6.dp))
        
        Text(
            text = name,
            fontSize = 10.sp,
            fontWeight = FontWeight.Medium,
            color = MaterialTheme.colorScheme.primary,
            textAlign = TextAlign.Center
        )
    }
}

/**
 * 现代化分区标题 - 莫兰迪配色
 */
@Composable
fun ModernSectionHeader(title: String) {
    Text(
        text = title,
        fontSize = 18.sp,
        fontWeight = FontWeight.SemiBold,
        color = MaterialTheme.colorScheme.primary,
        modifier = Modifier.padding(
            horizontal = 4.dp,
            vertical = 12.dp
        )
    )
}

@Composable
fun ThemeColorSetting(
    title: String,
    subtitle: String,
    color: Color,
    onClick: () -> Unit
) {
    Card(
            modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp)
            .clickable(onClick = onClick),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 12.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
            Column(modifier = Modifier.weight(1f)) {
                            Text(
                    text = title,
                    style = MaterialTheme.typography.bodyLarge,
                    color = MaterialTheme.colorScheme.onSurface
                )
                
                            Text(
                    text = subtitle,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            
            // 颜色预览
            Box(
                modifier = Modifier
                    .size(40.dp)
                    .clip(RoundedCornerShape(8.dp))
                    .background(color)
            )
        }
    }
}

@Composable
fun SectionTitle(title: String) {
                        Text(
        text = title,
                                style = MaterialTheme.typography.titleMedium,
        color = MaterialTheme.colorScheme.onBackground,
                                modifier = Modifier.padding(vertical = 8.dp)
                            )
}

@Composable
fun EnhancedColorPickerDialog(
    initialColor: Color,
    onColorSelected: (Color) -> Unit,
    onDismiss: () -> Unit
) {
    var currentColor by remember { mutableStateOf(initialColor) }
    var red by remember { mutableStateOf((initialColor.red * 255).toInt()) }
    var green by remember { mutableStateOf((initialColor.green * 255).toInt()) }
    var blue by remember { mutableStateOf((initialColor.blue * 255).toInt()) }
    var alpha by remember { mutableStateOf((initialColor.alpha * 100).toInt()) } // 新增透明度状态，范围0-100
    var colorHex by remember { mutableStateOf(String.format("#%02X%02X%02X", red, green, blue)) }
    var isHexError by remember { mutableStateOf(false) }
    
    // 获取上下文
    val context = LocalContext.current
    
    LaunchedEffect(red, green, blue, alpha) { // 增加alpha依赖
        currentColor = Color(
            red = red / 255f,
            green = green / 255f,
            blue = blue / 255f,
            alpha = alpha / 100f // 将0-100的值转换为0-1的透明度
        )
        colorHex = String.format("#%02X%02X%02X", red, green, blue)
    }
    
    // 解析颜色代码的函数
    fun parseColorHex(hexCode: String) {
        try {
            // 移除可能存在的#前缀
            val cleanHex = if (hexCode.startsWith("#")) hexCode.substring(1) else hexCode
            
            // 检查格式是否正确
            if (cleanHex.length == 6 && cleanHex.all { it.isDigit() || it in 'A'..'F' || it in 'a'..'f' }) {
                // 解析RGB值
                val r = cleanHex.substring(0, 2).toInt(16)
                val g = cleanHex.substring(2, 4).toInt(16)
                val b = cleanHex.substring(4, 6).toInt(16)
                
                // 更新滑块值
                red = r
                green = g
                blue = b
                // 保持当前透明度不变
                
                // 重置错误状态
                isHexError = false
            } else {
                isHexError = true
            }
        } catch (e: Exception) {
            isHexError = true
        }
    }
    
    // 自定义对话框
    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = true
        )
    ) {
        Surface(
            shape = RoundedCornerShape(16.dp),
            color = Color.White,
            tonalElevation = 8.dp,
            shadowElevation = 8.dp,
            modifier = Modifier
                .width(340.dp)
                .padding(0.dp)
        ) {
            Column(
                modifier = Modifier
                    .padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // 标题
                Text(
                    text = stringResource(R.string.select_color),
                    style = MaterialTheme.typography.titleLarge,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 16.dp),
                    textAlign = TextAlign.Center
                )
                
                // 内容
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                ) {
                    // 颜色预览
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(60.dp)
                            .clip(RoundedCornerShape(8.dp))
                            .background(color = currentColor)
                    )
                    
                    // 十六进制值与透明度显示
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(top = 8.dp),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Text(
                            text = colorHex,
                            fontFamily = FontFamily.Monospace,
                            fontSize = 14.sp
                        )
                        Text(
                            text = stringResource(R.string.transparency, alpha),
                            fontFamily = FontFamily.Monospace,
                            fontSize = 14.sp
                        )
                    }
                    
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    // Red 滑块
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 8.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "R: $red",
                            style = MaterialTheme.typography.bodyMedium,
                            modifier = Modifier.width(60.dp),
                            fontFamily = FontFamily.Monospace,
                            textAlign = TextAlign.End // 文本右对齐
                        )
                        
                        Spacer(modifier = Modifier.width(8.dp))
                        
                        Box(
                            modifier = Modifier
                                .weight(1f)
                                .height(20.dp)
                                .clip(RoundedCornerShape(4.dp))
                                .background(
                                    brush = Brush.horizontalGradient(
                                        colors = listOf(
                                            Color.Black,
                                            Color.Red
                                        )
                                    )
                                )
                        ) {
                            Slider(
                                value = red.toFloat(),
                                onValueChange = { red = it.toInt() },
                                valueRange = 0f..255f,
                                modifier = Modifier.fillMaxSize(),
                                colors = SliderDefaults.colors(
                                    thumbColor = Color.Red,
                                    activeTrackColor = Color.Transparent,
                                    inactiveTrackColor = Color.Transparent
                                ),
                                thumb = {
                                    Box(
                                        modifier = Modifier
                                            .size(28.dp)
                                            .background(Color.White, CircleShape)
                                            .border(2.dp, Color.Red, CircleShape)
                                    )
                                }
                            )
                        }
                    }
                    
                    // Green 滑块
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 8.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "G: $green",
                            style = MaterialTheme.typography.bodyMedium,
                            modifier = Modifier.width(60.dp),
                            fontFamily = FontFamily.Monospace,
                            textAlign = TextAlign.End // 文本右对齐
                        )
                        
                        Spacer(modifier = Modifier.width(8.dp))
                        
                        Box(
                            modifier = Modifier
                                .weight(1f)
                                .height(20.dp)
                                .clip(RoundedCornerShape(4.dp))
                                .background(
                                    brush = Brush.horizontalGradient(
                                        colors = listOf(
                                            Color.Black,
                                            Color.Green
                                        )
                                    )
                                )
                        ) {
                            Slider(
                                value = green.toFloat(),
                                onValueChange = { green = it.toInt() },
                                valueRange = 0f..255f,
                                modifier = Modifier.fillMaxSize(),
                                colors = SliderDefaults.colors(
                                    thumbColor = Color.Green,
                                    activeTrackColor = Color.Transparent,
                                    inactiveTrackColor = Color.Transparent
                                ),
                                thumb = {
                                    Box(
                                        modifier = Modifier
                                            .size(28.dp)
                                            .background(Color.White, CircleShape)
                                            .border(2.dp, Color.Green, CircleShape)
                                    )
                                }
                            )
                        }
                    }
                    
                    // Blue 滑块
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 8.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "B: $blue",
                            style = MaterialTheme.typography.bodyMedium,
                            modifier = Modifier.width(60.dp),
                            fontFamily = FontFamily.Monospace,
                            textAlign = TextAlign.End // 文本右对齐
                        )
                        
                        Spacer(modifier = Modifier.width(8.dp))
                        
                        Box(
                            modifier = Modifier
                                .weight(1f)
                                .height(20.dp)
                                .clip(RoundedCornerShape(4.dp))
                                .background(
                                    brush = Brush.horizontalGradient(
                                        colors = listOf(
                                            Color.Black,
                                            Color.Blue
                                        )
                                    )
                                )
                        ) {
                            Slider(
                                value = blue.toFloat(),
                                onValueChange = { blue = it.toInt() },
                                valueRange = 0f..255f,
                                modifier = Modifier.fillMaxSize(),
                                colors = SliderDefaults.colors(
                                    thumbColor = Color.Blue,
                                    activeTrackColor = Color.Transparent,
                                    inactiveTrackColor = Color.Transparent
                                ),
                                thumb = {
                                    Box(
                                        modifier = Modifier
                                            .size(28.dp)
                                            .background(Color.White, CircleShape)
                                            .border(2.dp, Color.Blue, CircleShape)
                                    )
                                }
                            )
                        }
                    }
                    
                    // 新增的透明度滑块
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 8.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "A: $alpha%",
                            style = MaterialTheme.typography.bodyMedium,
                            modifier = Modifier.width(60.dp),
                            fontFamily = FontFamily.Monospace,
                            textAlign = TextAlign.End // 文本右对齐
                        )
                        
                        Spacer(modifier = Modifier.width(8.dp))
                        
                        Box(
                            modifier = Modifier
                                .weight(1f)
                                .height(20.dp)
                                .clip(RoundedCornerShape(4.dp))
                                .background(
                                    brush = Brush.horizontalGradient(
                                        colors = listOf(
                                            Color.White.copy(alpha = 0.0f),
                                            Color.White.copy(alpha = 1.0f)
                                        )
                                    )
                                )
                                .border(
                                    width = 1.dp,
                                    color = Color.LightGray,
                                    shape = RoundedCornerShape(4.dp)
                                )
                        ) {
                            Slider(
                                value = alpha.toFloat(),
                                onValueChange = { alpha = it.toInt() },
                                valueRange = 0f..100f,
                                modifier = Modifier.fillMaxSize(),
                                colors = SliderDefaults.colors(
                                    thumbColor = Color.DarkGray,
                                    activeTrackColor = Color.Transparent,
                                    inactiveTrackColor = Color.Transparent
                                ),
                                thumb = {
                                    Box(
                                        modifier = Modifier
                                            .size(28.dp)
                                            .background(Color.White, CircleShape)
                                            .border(2.dp, Color.DarkGray, CircleShape)
                                    )
                                }
                            )
                        }
                    }
                    
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    // 颜色代码输入框
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        OutlinedTextField(
                            value = colorHex,
                            onValueChange = { 
                                colorHex = it
                                if (it.length >= 6) {
                                    parseColorHex(it)
                                }
                            },
                            modifier = Modifier
                                .weight(1f)
                                .height(56.dp),
                            singleLine = true,
                            textStyle = TextStyle(
                                fontFamily = FontFamily.Monospace,
                                fontSize = 16.sp
                            ),
                            isError = isHexError,
                            label = { Text(stringResource(R.string.color_code)) },
                            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Ascii),
                            shape = RoundedCornerShape(8.dp),
                            colors = OutlinedTextFieldDefaults.colors(
                                focusedBorderColor = if (isHexError) Color.Red else Color.Transparent,
                                unfocusedBorderColor = if (isHexError) Color.Red.copy(alpha = 0.6f) else Color.Transparent,
                                focusedContainerColor = Color(0xFFF5F5F5),
                                unfocusedContainerColor = Color(0xFFF5F5F5)
                            )
                        )
                        
                        Spacer(modifier = Modifier.width(8.dp))
                        
                        IconButton(
                            onClick = {
                                // 复制颜色代码到剪贴板
                                val clipboard = context.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
                                val clip = ClipData.newPlainText("Color Hex", colorHex)
                                clipboard.setPrimaryClip(clip)
                                Toast.makeText(context, context.getString(R.string.color_code_copied), Toast.LENGTH_SHORT).show()
                            },
                            modifier = Modifier.size(40.dp)
                        ) {
                            Icon(
                                imageVector = Icons.Default.ContentCopy,
                                contentDescription = stringResource(R.string.copy_color_code),
                                modifier = Modifier.size(20.dp)
                            )
                        }
                    }
                    
                    // 错误提示
                    if (isHexError) {
                        Text(
                            text = stringResource(R.string.invalid_color_code),
                            color = Color.Red,
                            style = MaterialTheme.typography.bodySmall,
                            modifier = Modifier.padding(start = 4.dp, top = 4.dp)
                        )
                    }
                }
                
                // 底部按钮
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = 16.dp),
                    horizontalArrangement = Arrangement.End
                ) {
                    TextButton(onClick = onDismiss) {
                        Text(
                            stringResource(R.string.cancel),
                            color = Color(0xFF786D86) // 紫灰色按钮文字
                        )
                    }
                    
                    TextButton(onClick = { 
                        // 使用当前颜色包括透明度
                        val finalColor = Color(
                            red = red / 255f,
                            green = green / 255f,
                            blue = blue / 255f,
                            alpha = alpha / 100f // 应用透明度
                        )
                        onColorSelected(finalColor) 
                    }) {
                        Text(
                            stringResource(R.string.confirm),
                            color = Color(0xFF786D86) // 紫灰色按钮文字
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun SliderWithLabel(
    value: Float,
    onValueChange: (Float) -> Unit,
    valueRange: ClosedFloatingPointRange<Float>,
    label: String,
    colors: SliderColors
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = label,
            style = MaterialTheme.typography.bodyMedium,
            modifier = Modifier.width(60.dp),
            fontFamily = FontFamily.Monospace,
            textAlign = TextAlign.End // 文本右对齐
        )
        
        Spacer(modifier = Modifier.width(8.dp))
        
        Slider(
            value = value,
            onValueChange = { onValueChange(it) },
            valueRange = valueRange,
            modifier = Modifier
                .weight(1f)
                .height(48.dp), // 增加高度，使滑块更容易触摸
            colors = colors
        )
    }
}

@Composable
fun ThemeCard(
    title: String,
    description: String,
    icon: ImageVector,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    val haptic = LocalHapticFeedback.current
    
    val cardColor by animateColorAsStateCustom(
        targetValue = if (isSelected) 
            MaterialTheme.colorScheme.primaryContainer 
        else 
            MaterialTheme.colorScheme.surface,
        animationSpec = tween(durationMillis = 300),
        label = "cardColorAnimation"
    )
    
    val iconContainerColor by animateColorAsStateCustom(
        targetValue = if (isSelected)
            MaterialTheme.colorScheme.primary.copy(alpha = 0.2f)
        else
            MaterialTheme.colorScheme.surfaceVariant,
        animationSpec = tween(durationMillis = 300),
        label = "iconContainerColorAnimation"
    )
    
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 6.dp)
            .clickable { 
                onClick()
                haptic.performHapticFeedback(HapticFeedbackType.TextHandleMove)
            },
        colors = CardDefaults.cardColors(
            containerColor = cardColor
        ),
        shape = RoundedCornerShape(16.dp),
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Box(
                modifier = Modifier
                    .size(48.dp)
                    .clip(RoundedCornerShape(12.dp))
                    .background(
                        color = iconContainerColor
                    )
                    .padding(8.dp),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = icon,
                    contentDescription = null,
                    tint = if (isSelected) 
                        MaterialTheme.colorScheme.primary
                    else 
                        MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            
            Spacer(modifier = Modifier.width(16.dp))
            
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = title,
                    style = MaterialTheme.typography.titleMedium.copy(
                        fontWeight = FontWeight.Medium
                    ),
                    color = if (isSelected) 
                        MaterialTheme.colorScheme.onPrimaryContainer
                    else 
                        MaterialTheme.colorScheme.onSurface
                )
                
                Text(
                    text = description,
                    style = MaterialTheme.typography.bodySmall,
                    color = if (isSelected)
                        MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.7f)
                    else
                        MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                )
            }
            
            if (isSelected) {
                Icon(
                    imageVector = Icons.Default.Check,
                    contentDescription = stringResource(R.string.selected),
                    tint = MaterialTheme.colorScheme.primary
                )
            }
        }
    }
}

@Composable
fun ColorPickerRow(
    selectedColor: Color,
    onColorSelected: (Color) -> Unit,
    colors: List<Color>,
    title: String
) {
    val haptic = LocalHapticFeedback.current
    
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp)
    ) {
        Text(
            text = title,
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurface,
            modifier = Modifier.padding(vertical = 8.dp)
        )
        
        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(12.dp),
            contentPadding = PaddingValues(vertical = 8.dp, horizontal = 4.dp)
        ) {
            items(colors) { color ->
                val isSelected = color == selectedColor
                val borderWidth = animateDpAsStateCustom(
                    targetValue = if (isSelected) 3.dp else 0.dp
                )
                
                Box(
                    modifier = Modifier
                        .size(56.dp)
                        .clip(CircleShape)
                        .border(
                            width = borderWidth.value,
                            color = MaterialTheme.colorScheme.onBackground,
                            shape = CircleShape
                        )
                        .padding(borderWidth.value)
                        .background(color = color)
                        .clickable { 
                            onColorSelected(color)
                            haptic.performHapticFeedback(HapticFeedbackType.TextHandleMove)
                        },
                    contentAlignment = Alignment.Center
                ) {
                    if (isSelected) {
                        Icon(
                            imageVector = Icons.Default.Check,
                            contentDescription = null,
                            tint = getAppropriateTextColor(color)
                        )
                    }
                }
            }
        }
    }
}

/**
 * 页面背景选择卡片组件
 */
@Composable
fun PageThemeCard(
    title: String,
    subtitle: String,
    currentColor: Color,
    onColorSelected: (Color) -> Unit,
    tag: String? = null,
    presetColors: List<Color> = listOf(
        Color(0xFFF3E5F5), // 淡紫色
        Color(0xFFE8F5E9), // 薄荷绿
        Color(0xFFE3F2FD), // 天空蓝
        Color(0xFFFFF8E1), // 奶油黄
        Color(0xFFEFEBE9)  // 浅驼色
    )
) {
    var expanded by remember { mutableStateOf(false) }
    val haptic = LocalHapticFeedback.current
    val cardScale by animateFloatAsStateCustom(
        targetValue = if (expanded) 1.02f else 1f,
        animationSpec = spring(dampingRatio = 0.75f),
        label = "cardScale"
    )
    
    Surface(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp)
            .graphicsLayer {
                scaleX = cardScale
                scaleY = cardScale
            },
        color = MaterialTheme.colorScheme.surface.copy(alpha = 0.8f),
        shape = RoundedCornerShape(16.dp),
        shadowElevation = if (expanded) 4.dp else 0.dp,
        onClick = { 
            expanded = !expanded 
            haptic.performHapticFeedback(HapticFeedbackType.TextHandleMove)
        }
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 卡片头部
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 颜色预览
                Box(
                    modifier = Modifier
                        .size(48.dp)
                        .clip(RoundedCornerShape(12.dp))
                        .background(
                            brush = Brush.linearGradient(
                                colors = listOf(
                                    currentColor,
                                    currentColor.copy(alpha = 0.7f)
                                ),
                                start = Offset(0f, 0f),
                                end = Offset(100f, 100f)
                            )
                        )
                )
                
                Spacer(modifier = Modifier.width(16.dp))
                
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = title,
                        style = MaterialTheme.typography.titleMedium.copy(
                            fontWeight = FontWeight.Medium
                        )
                    )
                    
                    Text(
                        text = subtitle,
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                    )
                }
                
                if (tag != null) {
                    Surface(
                        color = MaterialTheme.colorScheme.primary.copy(alpha = 0.12f),
                        shape = RoundedCornerShape(8.dp)
                    ) {
                        Text(
                            text = tag,
                            style = MaterialTheme.typography.labelSmall,
                            color = MaterialTheme.colorScheme.primary,
                            modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
                        )
                    }
                }
                
                Spacer(modifier = Modifier.width(8.dp))
                
                Icon(
                    imageVector = if (expanded) Icons.Default.KeyboardArrowUp else Icons.Default.KeyboardArrowDown,
                    contentDescription = if (expanded) stringResource(R.string.collapse) else stringResource(R.string.expand),
                    tint = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            
            // 扩展内容区域
                    AnimatedVisibility(
                visible = expanded,
                enter = fadeIn() + expandVertically(),
                exit = fadeOut() + shrinkVertically()
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = 16.dp)
                ) {
                                Text(
                        text = stringResource(R.string.select_preset_color),
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
                        modifier = Modifier.padding(bottom = 8.dp)
                    )
                    
                    // 预设颜色选择器
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        presetColors.forEach { color ->
                            val isSelected = color.value.toInt() and 0x00FFFFFF == currentColor.value.toInt() and 0x00FFFFFF
                            val colorScale by animateFloatAsStateCustom(
                                targetValue = if (isSelected) 1.2f else 1.0f,
                                animationSpec = spring(dampingRatio = 0.6f),
                                label = "colorScale"
                            )
                            
                            Box(
                                modifier = Modifier
                                    .size(40.dp)
                                    .graphicsLayer {
                                        scaleX = colorScale
                                        scaleY = colorScale
                                    }
                                    .clip(CircleShape)
                                    .background(color)
                                    .border(
                                        width = if (isSelected) 2.dp else 0.dp,
                                        color = MaterialTheme.colorScheme.primary,
                                        shape = CircleShape
                                    )
                                    .clickable {
                                        onColorSelected(color)
                                        haptic.performHapticFeedback(HapticFeedbackType.LongPress)
                                    },
                                contentAlignment = Alignment.Center
                            ) {
                                if (isSelected) {
                                    Icon(
                                        imageVector = Icons.Default.Check,
                                        contentDescription = null,
                                        tint = if (color.luminance() > 0.5f) Color.Black else Color.White,
                                        modifier = Modifier.size(20.dp)
                                    )
                                }
                            }
                        }
                    }
                    
                    // 分隔线
                    Divider(
                        modifier = Modifier.padding(vertical = 16.dp),
                        color = MaterialTheme.colorScheme.outlineVariant.copy(alpha = 0.5f)
                    )
                    
                    // 自定义颜色选择器
                    Text(
                        text = stringResource(R.string.custom_color),
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
                        modifier = Modifier.padding(bottom = 8.dp)
                    )
                    
                    RGBColorPicker(
                        initialColor = currentColor,
                        onColorChanged = onColorSelected
                    )
                }
            }
        }
    }
}

/**
 * 优先级颜色选择组件
 */
@OptIn(ExperimentalLayoutApi::class)
@Composable
fun PriorityColorSelector(
    title: String,
    currentColor: Color,
    onColorSelected: (Color) -> Unit
) {
    val colorOptions = listOf(
        Color(0xFFFF4D4D), // 红色
        Color(0xFFFF7043), // 橙红色
        Color(0xFFFFA64D), // 橙色
        Color(0xFFFFD600), // 黄色
        Color(0xFF4DB8FF), // 蓝色
        Color(0xFF7986CB), // 紫色
        Color(0xFF4CAF50), // 绿色
        Color(0xFFD50000), // 深红色
        Color(0xFF9C27B0)  // 紫色
    )
    
    val haptic = LocalHapticFeedback.current
    
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .graphicsLayer(
                clip = false,
                shadowElevation = 0f
            )
    ) {
        Surface(
            shape = RoundedCornerShape(12.dp),
            color = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.5f),
            modifier = Modifier.fillMaxWidth()
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = title,
                        style = MaterialTheme.typography.bodyLarge.copy(
                            fontWeight = FontWeight.Medium
                        ),
                        modifier = Modifier.weight(1f)
                    )
                    
            // 显示当前选中的颜色
            Box(
                modifier = Modifier
                            .size(32.dp)
                    .clip(CircleShape)
                    .background(currentColor)
                            .border(1.dp, MaterialTheme.colorScheme.outline.copy(alpha = 0.5f), CircleShape)
            )
                }
                
                Spacer(modifier = Modifier.height(12.dp))
            
            // 颜色选项
                FlowRow(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp),
                    maxItemsInEachRow = 5
            ) {
                colorOptions.forEach { color ->
                    // 添加缩放和弹性动画
                    val isSelected = color.value.toInt() and 0x00FFFFFF == currentColor.value.toInt() and 0x00FFFFFF
                    val scale by animateFloatAsStateCustom(
                        targetValue = if (isSelected) 1.2f else 1.0f,
                        animationSpec = spring(dampingRatio = 0.6f),
                        label = "colorScale"
                    )
                    
                    Box(
                        modifier = Modifier
                            .size(36.dp)
                            .clip(CircleShape)
                            .background(color)
                            .border(
                                width = if (isSelected) 2.dp else 0.dp,
                                color = MaterialTheme.colorScheme.onSurface,
                                shape = CircleShape
                            )
                                .clickable { 
                                    onColorSelected(color) 
                                    haptic.performHapticFeedback(HapticFeedbackType.LongPress)
                                }
                            .graphicsLayer(
                                scaleX = scale,
                                scaleY = scale,
                                clip = false
                            ),
                        contentAlignment = Alignment.Center
                    ) {
                        if (isSelected) {
                            Icon(
                                imageVector = Icons.Default.Check,
                                contentDescription = null,
                                tint = if (color.luminance() > 0.5f) Color.Black else Color.White,
                                modifier = Modifier.size(20.dp)
                            )
                        }
                    }
                }
            }
        }
        }
    }
}

/**
 * 任务卡片预览组件
 */
@Composable
fun PreviewTaskCard(
    title: String,
    time: String,
    color: Color
) {
    val backgroundColor = color
    val borderColor = color.copy(alpha = 0.9f)
    val textColor = MaterialTheme.colorScheme.onSurface
    
    // 添加卡片动画
    val scale by animateFloatAsStateCustom(
        targetValue = 1f,
        animationSpec = spring(
            dampingRatio = 0.7f,
            stiffness = 100f
        ),
        label = "cardScale"
    )
    
    val alpha by animateFloatAsStateCustom(
        targetValue = 1f,
        animationSpec = tween(durationMillis = 300),
        label = "cardAlpha"
    )

    Box(
        modifier = Modifier
            .fillMaxWidth()
            .graphicsLayer(
                scaleX = scale,
                scaleY = scale,
                alpha = alpha,
                clip = false
            )
            .clip(RoundedCornerShape(8.dp))
            .background(backgroundColor)
            .border(
                width = 1.dp,
                color = borderColor,
                shape = RoundedCornerShape(8.dp)
            )
            .padding(horizontal = 16.dp, vertical = 12.dp)
    ) {
        Column {
            Text(
                text = title,
                fontWeight = FontWeight.Medium,
                fontSize = 15.sp,
                color = textColor,
                maxLines = 2,
                overflow = TextOverflow.Ellipsis
            )
            
            Spacer(modifier = Modifier.height(6.dp))
            
            Text(
                text = time,
                fontSize = 13.sp,
                color = textColor.copy(alpha = 0.8f),
                overflow = TextOverflow.Ellipsis,
                maxLines = 1
            )
        }
    }
}

/**
 * RGB颜色选择器组件
 * 允许用户通过RGB滑块精确选择颜色
 */
@Composable
fun RGBColorPicker(
    initialColor: Color,
    onColorChanged: (Color) -> Unit,
    modifier: Modifier = Modifier
) {
    var red by remember { mutableStateOf((initialColor.red * 255).toInt()) }
    var green by remember { mutableStateOf((initialColor.green * 255).toInt()) }
    var blue by remember { mutableStateOf((initialColor.blue * 255).toInt()) }
    var alpha by remember { mutableStateOf((initialColor.alpha * 100).toInt()) }
    
    val currentColor = remember(red, green, blue, alpha) {
        Color(
            red = red,
            green = green,
            blue = blue,
            alpha = alpha * 255 / 100
        )
    }
    
    LaunchedEffect(currentColor) {
        if (currentColor != initialColor) {
            onColorChanged(currentColor)
        }
    }
    
    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(16.dp)
    ) {
        // 颜色预览
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(50.dp)
                .padding(bottom = 16.dp)
                .clip(RoundedCornerShape(8.dp))
                .background(color = currentColor)
        )
        
        // Red 滑块
        ColorSlider(
            value = red,
            onValueChange = { red = it },
            valueRange = 0f..255f,
            label = "R: $red",
            thumbColor = Color.Red,
            trackColor = Color.Red.copy(alpha = 0.3f)
        )
        
        // Green 滑块
        ColorSlider(
            value = green,
            onValueChange = { green = it },
            valueRange = 0f..255f,
            label = "G: $green",
            thumbColor = Color.Green,
            trackColor = Color.Green.copy(alpha = 0.3f)
        )
        
        // Blue 滑块
        ColorSlider(
            value = blue,
            onValueChange = { blue = it },
            valueRange = 0f..255f,
            label = "B: $blue",
            thumbColor = Color.Blue,
            trackColor = Color.Blue.copy(alpha = 0.3f)
        )
        
        // Alpha 滑块
        ColorSlider(
            value = alpha,
            onValueChange = { alpha = it },
            valueRange = 0f..100f,
            label = "A: ${alpha}%",
            thumbColor = Color.Gray,
            trackColor = Color.Gray.copy(alpha = 0.3f)
        )
    }
}

/**
 * 颜色滑块组件
 */
@Composable
fun ColorSlider(
    value: Int,
    onValueChange: (Int) -> Unit,
    valueRange: ClosedFloatingPointRange<Float>,
    label: String,
    thumbColor: Color,
    trackColor: Color
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = label,
            style = MaterialTheme.typography.bodyMedium,
            modifier = Modifier.width(70.dp)
        )
        
        Slider(
            value = value.toFloat(),
            onValueChange = { onValueChange(it.toInt()) },
            valueRange = valueRange,
            modifier = Modifier.weight(1f),
            colors = SliderDefaults.colors(
                thumbColor = thumbColor,
                activeTrackColor = thumbColor,
                inactiveTrackColor = trackColor
            )
        )
    }
}

/**
 * Material You 设置组件
 */
@Composable
fun MaterialYouSettings(
    useDynamicColor: Boolean,
    onDynamicColorChanged: (Boolean) -> Unit,
    useMaterialYou: Boolean,
    onMaterialYouChanged: (Boolean) -> Unit
) {
    val isAndroid12Plus = Build.VERSION.SDK_INT >= Build.VERSION_CODES.S
    
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp)
    ) {
        Text(
            text = stringResource(R.string.material_you_settings),
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Bold,
            modifier = Modifier.padding(vertical = 8.dp)
        )
        
        // 动态颜色开关
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = Icons.Default.Palette,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.primary
            )
            
            Spacer(modifier = Modifier.width(16.dp))
            
            Column {
                Text(
                    text = stringResource(R.string.use_dynamic_colors),
                    style = MaterialTheme.typography.bodyLarge
                )
                
                if (!isAndroid12Plus) {
                    Text(
                        text = stringResource(R.string.android_12_required),
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.error
                    )
                }
            }
            
            Spacer(modifier = Modifier.weight(1f))
            
            Switch(
                checked = useDynamicColor && isAndroid12Plus,
                onCheckedChange = { onDynamicColorChanged(it) },
                enabled = isAndroid12Plus
            )
        }
        
        // Material You 主题开关
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = Icons.Default.ColorLens,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.primary
            )
            
            Spacer(modifier = Modifier.width(16.dp))
            
            Text(
                text = stringResource(R.string.use_material_you_theme),
                style = MaterialTheme.typography.bodyLarge
            )
            
            Spacer(modifier = Modifier.weight(1f))
            
            Switch(
                checked = useMaterialYou,
                onCheckedChange = { onMaterialYouChanged(it) }
            )
        }
        
        Divider()
    }
}

/**
 * 透明度选择器组件
 */
@Composable
fun TransparencySelector(
    options: List<Pair<String, Float>>,
    selectedOpacity: Float,
    onOpacitySelected: (Float) -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .graphicsLayer(
                clip = false,
                shadowElevation = 0f
            ),
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        options.forEach { (label, opacity) ->
            val chipScale by animateFloatAsStateCustom(
                targetValue = if (selectedOpacity == opacity) 1.05f else 1.0f,
                animationSpec = spring(dampingRatio = 0.6f),
                label = "chipScale"
            )
            
            Box(
                modifier = Modifier.graphicsLayer(
                    scaleX = chipScale,
                    scaleY = chipScale
                )
            ) {
                FilterChip(
                    selected = selectedOpacity == opacity,
                    onClick = { onOpacitySelected(opacity) },
                    label = { Text(label) }
                )
            }
        }
    }
}

/**
 * 设置卡片组件
 */
@Composable
fun SettingsCard(
    title: String,
    icon: ImageVector,
    content: @Composable () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface.copy(alpha = 0.9f)
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        shape = RoundedCornerShape(16.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 12.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = icon,
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.size(28.dp)
                )
                
                Spacer(modifier = Modifier.width(16.dp))
                
                Text(
                    text = title,
                    style = MaterialTheme.typography.titleMedium.copy(
                        fontWeight = FontWeight.SemiBold
                    )
                )
                
                Spacer(modifier = Modifier.weight(1f))
            }
            
            content()
        }
    }
}

/**
 * 设置卡片组件 - 无标题版本
 */
@Composable
fun SettingsCard(
    content: @Composable () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface.copy(alpha = 0.9f)
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        shape = RoundedCornerShape(16.dp)
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            content()
        }
    }
}

/**
 * 半透明玻璃效果卡片组件
 */
@Composable
fun GlassCard(
    modifier: Modifier = Modifier,
    content: @Composable () -> Unit
) {
    val cardAlpha = 0.72f
    
    Card(
        modifier = modifier
            .padding(horizontal = 16.dp, vertical = 8.dp)
            .fillMaxWidth()
            .graphicsLayer {
                clip = true
                shadowElevation = 4f
            },
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface.copy(alpha = cardAlpha)
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp),
        shape = RoundedCornerShape(24.dp),
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            content()
        }
    }
}

/**
 * 卡片标题组件
 */
@Composable
fun CardTitle(
    title: String,
    icon: ImageVector,
    action: (@Composable () -> Unit)? = null
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(bottom = 16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 带有圆形背景的图标
        Box(
            modifier = Modifier
                .size(36.dp)
                .background(
                    color = MaterialTheme.colorScheme.primary.copy(alpha = 0.12f),
                    shape = CircleShape
                )
                .padding(8.dp),
            contentAlignment = Alignment.Center
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.primary,
                modifier = Modifier.size(20.dp)
            )
        }
        
        Spacer(modifier = Modifier.width(12.dp))
        
        Text(
            text = title,
            style = MaterialTheme.typography.titleMedium.copy(
                fontWeight = FontWeight.SemiBold
            )
        )
        
        Spacer(modifier = Modifier.weight(1f))
        
        // 可选的操作按钮
        action?.invoke()
    }
}

/**
 * 优先级颜色设置卡片
 */
@Composable
fun PriorityColorCard(
    title: String,
    subtitle: String,
    priorities: List<Pair<String, Color>>,
    onColorSelected: (Int, Color) -> Unit
) {
    var expanded by remember { mutableStateOf(false) }
    val haptic = LocalHapticFeedback.current
    
    Surface(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp),
        color = MaterialTheme.colorScheme.surface.copy(alpha = 0.8f),
        shape = RoundedCornerShape(16.dp),
        shadowElevation = if (expanded) 4.dp else 0.dp,
        onClick = { 
            expanded = !expanded 
            haptic.performHapticFeedback(HapticFeedbackType.TextHandleMove)
        }
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 卡片头部
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 优先级图标
                Box(
                    modifier = Modifier
                        .size(48.dp)
                        .clip(RoundedCornerShape(12.dp))
                        .background(MaterialTheme.colorScheme.tertiaryContainer)
                        .padding(8.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = Icons.Default.PriorityHigh,
                        contentDescription = null,
                        tint = MaterialTheme.colorScheme.onTertiaryContainer
                    )
                }
                
                Spacer(modifier = Modifier.width(16.dp))
                
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = title,
                        style = MaterialTheme.typography.titleMedium.copy(
                            fontWeight = FontWeight.Medium
                        )
                    )
                    
                    Text(
                        text = subtitle,
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                    )
                }
                
                Icon(
                    imageVector = if (expanded) Icons.Default.KeyboardArrowUp else Icons.Default.KeyboardArrowDown,
                    contentDescription = if (expanded) "收起" else "展开",
                    tint = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            
            // 优先级颜色预览条
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 12.dp),
                horizontalArrangement = Arrangement.spacedBy((-4).dp)
            ) {
                priorities.forEachIndexed { index, (_, color) ->
                    Box(
                        modifier = Modifier
                            .height(8.dp)
                            .weight(1f)
                            .clip(
                                when (index) {
                                    0 -> RoundedCornerShape(topStart = 4.dp, bottomStart = 4.dp)
                                    priorities.size - 1 -> RoundedCornerShape(topEnd = 4.dp, bottomEnd = 4.dp)
                                    else -> RectangleShape
                                }
                            )
                            .background(color)
                    )
                }
            }
            
            // 扩展内容区域
            AnimatedVisibility(
                visible = expanded,
                enter = fadeIn() + expandVertically(),
                exit = fadeOut() + shrinkVertically()
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = 16.dp)
                ) {
                    priorities.forEachIndexed { index, (name, color) ->
                        var currentColor by remember { mutableStateOf(color) }
                        
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(vertical = 8.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = name,
                                style = MaterialTheme.typography.bodyMedium,
                                modifier = Modifier.weight(1f)
                            )
                            
                            Surface(
                                modifier = Modifier
                                    .size(36.dp)
                                    .clickable {
                                        haptic.performHapticFeedback(HapticFeedbackType.LongPress)
                                    },
                                color = currentColor,
                                shape = CircleShape,
                                border = BorderStroke(1.dp, MaterialTheme.colorScheme.outline.copy(alpha = 0.2f))
                            ) { }
                            
                            Spacer(modifier = Modifier.width(12.dp))
                            
                            Icon(
                                imageVector = Icons.Default.ColorLens,
                                contentDescription = "选择颜色",
                                tint = MaterialTheme.colorScheme.onSurfaceVariant,
                                modifier = Modifier.clickable {
                                    // 显示RGB颜色选择器对话框
                                    // 简化实现，实际应用中可能需要一个对话框
                                    // 这里直接修改颜色
                                    val newColor = Color(
                                        red = (0..255).random() / 255f,
                                        green = (0..255).random() / 255f,
                                        blue = (0..255).random() / 255f
                                    )
                                    currentColor = newColor
                                    onColorSelected(index, newColor)
                                    haptic.performHapticFeedback(HapticFeedbackType.LongPress)
                                }
                            )
                        }
                    }
                }
            }
        }
    }
}

/**
 * Material You动态主题设置卡片
 */
@Composable
fun MaterialYouCard(
    enabled: Boolean,
    onEnabledChange: (Boolean) -> Unit,
    title: String = "Material You",
    subtitle: String = "使用动态配色方案，根据壁纸自动生成主题色",
    icon: ImageVector = Icons.Default.Palette,
    titleResId: Int? = null, // Title string resource ID
    subtitleResId: Int? = null, // Subtitle string resource ID
    isUnifiedBackground: Boolean = false,
    customBackgroundColor: Color? = null
) {
    val haptic = LocalHapticFeedback.current
    
    // Process string resources if provided
    val titleText = if (titleResId != null) stringResource(titleResId) else title
    val subtitleText = if (subtitleResId != null) stringResource(subtitleResId) else subtitle
    
    // Card elevation animation
    val elevation by animateDpAsStateCustom(
        targetValue = if (enabled) 4.dp else 1.dp,
        animationSpec = tween(durationMillis = 300),
        label = "elevationAnimation"
    )
    
    // 背景色动画 - 考虑统一背景色设置
    val cardColor by animateColorAsStateCustom(
        targetValue = if (enabled) {
            if (isUnifiedBackground && customBackgroundColor != null) {
                // 使用更亮版本的自定义背景色作为卡片背景
                customBackgroundColor.copy(alpha = 0.9f)
            } else {
                MaterialTheme.colorScheme.secondaryContainer
            }
        } else {
            if (isUnifiedBackground && customBackgroundColor != null) {
                // 使用较暗版本的自定义背景色
                customBackgroundColor.copy(alpha = 0.7f)
            } else {
                MaterialTheme.colorScheme.surface
            }
        },
        animationSpec = tween(durationMillis = 300),
        label = "cardColorAnimation"
    )
    
    // 图标容器颜色动画
    val iconContainerColor by animateColorAsStateCustom(
        targetValue = if (enabled) {
            if (isUnifiedBackground && customBackgroundColor != null) {
                // 使用自定义背景色的较亮版本
                customBackgroundColor.copy(alpha = 0.3f)
            } else {
                MaterialTheme.colorScheme.secondary.copy(alpha = 0.2f)
            }
        } else {
            if (isUnifiedBackground && customBackgroundColor != null) {
                // 使用自定义背景色的较暗版本
                customBackgroundColor.copy(alpha = 0.1f)
            } else {
                MaterialTheme.colorScheme.surfaceVariant
            }
        },
        animationSpec = tween(durationMillis = 300),
        label = "iconContainerColorAnimation"
    )
    
    // 图标颜色动画
    val iconTint by animateColorAsStateCustom(
        targetValue = if (enabled) {
            if (isUnifiedBackground && customBackgroundColor != null) {
                // 如果背景色较暗则使用浅色图标，否则使用深色图标
                if (customBackgroundColor.luminance() < 0.5f) {
                    Color.White.copy(alpha = 0.9f)
                } else {
                    Color.Black.copy(alpha = 0.7f)
                }
            } else {
                MaterialTheme.colorScheme.secondary
            }
        } else {
            if (isUnifiedBackground && customBackgroundColor != null) {
                // 如果背景色较暗则使用浅色图标，否则使用深色图标
                if (customBackgroundColor.luminance() < 0.5f) {
                    Color.White.copy(alpha = 0.7f)
                } else {
                    Color.Black.copy(alpha = 0.5f)
                }
            } else {
                MaterialTheme.colorScheme.onSurfaceVariant
            }
        },
        animationSpec = tween(durationMillis = 300),
        label = "iconTintAnimation"
    )
    
    // 标题文本颜色动画
    val titleColor by animateColorAsStateCustom(
        targetValue = if (enabled) {
            if (isUnifiedBackground && customBackgroundColor != null) {
                // 根据背景色亮度选择合适的文本颜色
                if (customBackgroundColor.luminance() < 0.5f) {
                    Color.White
                } else {
                    Color.Black
                }
            } else {
                MaterialTheme.colorScheme.onSecondaryContainer
            }
        } else {
            if (isUnifiedBackground && customBackgroundColor != null) {
                // 根据背景色亮度选择合适的文本颜色，但透明度降低
                if (customBackgroundColor.luminance() < 0.5f) {
                    Color.White.copy(alpha = 0.8f)
                } else {
                    Color.Black.copy(alpha = 0.8f)
                }
            } else {
                MaterialTheme.colorScheme.onSurface
            }
        },
        animationSpec = tween(durationMillis = 300),
        label = "titleColorAnimation"
    )
    
    // 副标题文本颜色动画
    val subtitleColor by animateColorAsStateCustom(
        targetValue = if (enabled) {
            if (isUnifiedBackground && customBackgroundColor != null) {
                // 根据背景色亮度选择合适的文本颜色，但透明度降低
                if (customBackgroundColor.luminance() < 0.5f) {
                    Color.White.copy(alpha = 0.7f)
                } else {
                    Color.Black.copy(alpha = 0.7f)
                }
            } else {
                MaterialTheme.colorScheme.onSecondaryContainer.copy(alpha = 0.7f)
            }
        } else {
            if (isUnifiedBackground && customBackgroundColor != null) {
                // 根据背景色亮度选择合适的文本颜色，但透明度更低
                if (customBackgroundColor.luminance() < 0.5f) {
                    Color.White.copy(alpha = 0.5f)
                } else {
                    Color.Black.copy(alpha = 0.5f)
                }
            } else {
                MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
            }
        },
        animationSpec = tween(durationMillis = 300),
        label = "subtitleColorAnimation"
    )
    
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp)
            .clickable { 
                onEnabledChange(!enabled)
                haptic.performHapticFeedback(HapticFeedbackType.TextHandleMove)
            },
        colors = CardDefaults.cardColors(
            containerColor = cardColor
        ),
        shape = RoundedCornerShape(16.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = elevation)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 装饰图标
            Box(
                modifier = Modifier
                    .size(48.dp)
                    .clip(RoundedCornerShape(12.dp))
                    .background(
                        color = iconContainerColor
                    )
                    .padding(8.dp),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = icon,
                    contentDescription = null,
                    tint = iconTint
                )
            }
            
            Spacer(modifier = Modifier.width(16.dp))
            
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = titleText,
                    style = MaterialTheme.typography.titleMedium.copy(
                        fontWeight = FontWeight.Medium
                    ),
                    color = titleColor
                )
                
                Text(
                    text = subtitleText,
                    style = MaterialTheme.typography.bodySmall,
                    color = subtitleColor
                )
            }
            
            Switch(
                checked = enabled,
                onCheckedChange = { 
                    onEnabledChange(it)
                    haptic.performHapticFeedback(HapticFeedbackType.TextHandleMove)
                },
                colors = SwitchDefaults.colors(
                    checkedThumbColor = if (isUnifiedBackground && customBackgroundColor != null) {
                        // 根据背景色亮度选择合适的开关颜色
                        if (customBackgroundColor.luminance() < 0.5f) {
                            Color.White
                        } else {
                            MaterialTheme.colorScheme.primary
                        }
                    } else {
                        MaterialTheme.colorScheme.primary
                    },
                    checkedTrackColor = if (isUnifiedBackground && customBackgroundColor != null) {
                        // 根据背景色亮度选择合适的轨道颜色
                        if (customBackgroundColor.luminance() < 0.5f) {
                            Color.White.copy(alpha = 0.4f)
                        } else {
                            MaterialTheme.colorScheme.primaryContainer
                        }
                    } else {
                        MaterialTheme.colorScheme.primaryContainer
                    },
                    uncheckedThumbColor = MaterialTheme.colorScheme.outline,
                    uncheckedTrackColor = MaterialTheme.colorScheme.surfaceVariant
                )
            )
        }
    }
}

/**
 * 主题模式设置卡片，包含浅色、深色和跟随系统三种选项
 */
@Composable
fun ThemeModeCard(
    currentMode: ThemeMode,
    onModeSelected: (ThemeMode) -> Unit
) {
    val haptic = LocalHapticFeedback.current
    val expandedState = remember { mutableStateOf(false) }
    val rotationState = animateFloatAsStateCustom(
        targetValue = if (expandedState.value) 180f else 0f,
        animationSpec = tween(300), label = "rotationAnimation"
    )
    
    Surface(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp)
            ,
        color = MaterialTheme.colorScheme.surface.copy(alpha = 0.8f),
        shape = RoundedCornerShape(16.dp),
        onClick = { 
            expandedState.value = !expandedState.value
            haptic.performHapticFeedback(HapticFeedbackType.TextHandleMove)
        }
    ) {
        Column(modifier = Modifier.padding(16.dp)) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 装饰图标
                Box(
                    modifier = Modifier
                        .size(48.dp)
                        .clip(RoundedCornerShape(12.dp))
                        .background(MaterialTheme.colorScheme.surfaceVariant)
                        .padding(8.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = when(currentMode) {
                            ThemeMode.LIGHT -> Icons.Default.LightMode
                            ThemeMode.DARK -> Icons.Default.DarkMode
                            ThemeMode.FOLLOW_SYSTEM -> Icons.Default.SettingsSuggest
                        },
                        contentDescription = null,
                        tint = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
                
                Spacer(modifier = Modifier.width(16.dp))
                
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = stringResource(R.string.theme_mode),
                        style = MaterialTheme.typography.titleMedium.copy(
                            fontWeight = FontWeight.Medium
                        )
                    )
                    
                    Text(
                        text = when(currentMode) {
                            ThemeMode.LIGHT -> stringResource(R.string.light_mode)
                            ThemeMode.DARK -> stringResource(R.string.dark_mode)
                            ThemeMode.FOLLOW_SYSTEM -> stringResource(R.string.follow_system)
                        },
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                    )
                }
                
                Icon(
                    modifier = Modifier
                        .rotate(rotationState.value),
                    imageVector = Icons.Default.ExpandMore,
                    contentDescription = if (expandedState.value) stringResource(R.string.collapse) else stringResource(R.string.expand),
                    tint = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            
            AnimatedVisibility(
                visible = expandedState.value,
                enter = fadeIn() + expandVertically(),
                exit = fadeOut() + shrinkVertically()
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = 16.dp)
                ) {
                    ThemeOptionButton(
                        text = stringResource(R.string.light_mode),
                        icon = Icons.Default.LightMode,
                        isSelected = currentMode == ThemeMode.LIGHT,
                        onClick = { 
                            onModeSelected(ThemeMode.LIGHT)
                            haptic.performHapticFeedback(HapticFeedbackType.TextHandleMove)
                        }
                    )
                    
                    Spacer(modifier = Modifier.height(12.dp))
                    
                    ThemeOptionButton(
                        text = stringResource(R.string.dark_mode),
                        icon = Icons.Default.DarkMode,
                        isSelected = currentMode == ThemeMode.DARK,
                        onClick = { 
                            onModeSelected(ThemeMode.DARK)
                            haptic.performHapticFeedback(HapticFeedbackType.TextHandleMove)
                        }
                    )
                    
                    Spacer(modifier = Modifier.height(12.dp))
                    
                    ThemeOptionButton(
                        text = stringResource(R.string.follow_system),
                        icon = Icons.Default.SettingsSuggest,
                        isSelected = currentMode == ThemeMode.FOLLOW_SYSTEM,
                        onClick = { 
                            onModeSelected(ThemeMode.FOLLOW_SYSTEM)
                            haptic.performHapticFeedback(HapticFeedbackType.TextHandleMove)
                        }
                    )
                }
            }
        }
    }
}

@Composable
private fun ThemeOptionButton(
    text: String,
    icon: ImageVector,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    val backgroundColor = if (isSelected) 
        MaterialTheme.colorScheme.primaryContainer 
    else 
        MaterialTheme.colorScheme.surfaceVariant
        
    val contentColor = if (isSelected)
        MaterialTheme.colorScheme.onPrimaryContainer
    else
        MaterialTheme.colorScheme.onSurfaceVariant
        
    Surface(
        modifier = Modifier
            .fillMaxWidth()
            .height(48.dp),
        shape = RoundedCornerShape(12.dp),
        color = backgroundColor,
        onClick = onClick
    ) {
        Row(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                tint = contentColor
            )
            
            Spacer(modifier = Modifier.width(12.dp))
            
            Text(
                text = text,
                style = MaterialTheme.typography.bodyMedium,
                color = contentColor
            )
            
            Spacer(modifier = Modifier.weight(1f))
            
            if (isSelected) {
                Icon(
                    imageVector = Icons.Default.CheckCircle,
                    contentDescription = "已选择",
                    tint = MaterialTheme.colorScheme.primary
                )
            }
        }
    }
}

// 添加自定义的动画颜色函数替代animateColorAsState
@Composable
fun animateColorAsStateCustom(
    targetValue: Color,
    animationSpec: androidx.compose.animation.core.AnimationSpec<Color> = tween(300),
    label: String = "ColorAnimation"
): State<Color> {
    val animatedColor = remember { mutableStateOf(targetValue) }
    
    LaunchedEffect(targetValue) {
        animatedColor.value = targetValue
    }
    
    return animatedColor
}

// 添加自定义的animateFloatAsState函数
@Composable
fun animateFloatAsStateCustom(
    targetValue: Float,
    animationSpec: androidx.compose.animation.core.AnimationSpec<Float> = tween(300),
    label: String = "FloatAnimation"
): State<Float> {
    val animatedFloat = remember { mutableStateOf(targetValue) }
    
    LaunchedEffect(targetValue) {
        animatedFloat.value = targetValue
    }
    
    return animatedFloat
}

// 添加自定义的animateDpAsState函数
@Composable
fun animateDpAsStateCustom(
    targetValue: Dp,
    animationSpec: androidx.compose.animation.core.AnimationSpec<Dp> = tween(300),
    label: String = "DpAnimation"
): State<Dp> {
    val animatedDp = remember { mutableStateOf(targetValue) }
    
    LaunchedEffect(targetValue) {
        animatedDp.value = targetValue
    }
    
    return animatedDp
}

// 添加网格颜色选择项组件
@Composable
fun ColorGridItem(
    color: Color,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Box(
        modifier = Modifier
            .size(48.dp)
            .clip(CircleShape)
            .background(color = color)
            .border(
                width = if (isSelected) 2.dp else 1.dp,
                color = if (isSelected) MaterialTheme.colorScheme.primary 
                       else MaterialTheme.colorScheme.outline.copy(alpha = 0.3f),
                shape = CircleShape
            )
            .clickable(onClick = onClick),
        contentAlignment = Alignment.Center
    ) {
        if (isSelected) {
            Icon(
                imageVector = Icons.Default.Check,
                contentDescription = null,
                tint = getAppropriateTextColor(color)
            )
        }
    }
}

/**
 * 现代化颜色选择器对话框 - 莫兰迪配色
 * 支持手动输入十六进制颜色值
 */
@Composable
fun ModernColorPickerDialog(
    initialColor: Color,
    onColorSelected: (Color) -> Unit,
    onDismiss: () -> Unit
) {
    val context = LocalContext.current // 在 @Composable 函数中获取 context
    
    var currentColor by remember { mutableStateOf(initialColor) }
    var red by remember { mutableStateOf((initialColor.red * 255).toInt()) }
    var green by remember { mutableStateOf((initialColor.green * 255).toInt()) }
    var blue by remember { mutableStateOf((initialColor.blue * 255).toInt()) }
    var colorHex by remember { mutableStateOf(String.format("#%02X%02X%02X", red, green, blue)) }
    var hexInput by remember { mutableStateOf(String.format("#%02X%02X%02X", red, green, blue)) }
    var isHexValid by remember { mutableStateOf(true) }
    var isUpdatingFromSliders by remember { mutableStateOf(false) }
    
    // 十六进制颜色验证函数
    fun validateHexColor(hex: String): Boolean {
        val cleanHex = hex.trim()
        return cleanHex.matches(Regex("^#[0-9A-Fa-f]{6}$"))
    }
    
    // 从十六进制解析颜色
    fun parseHexColor(hex: String): Triple<Int, Int, Int>? {
        return try {
            val cleanHex = hex.removePrefix("#")
            val r = cleanHex.substring(0, 2).toInt(16)
            val g = cleanHex.substring(2, 4).toInt(16)
            val b = cleanHex.substring(4, 6).toInt(16)
            Triple(r, g, b)
        } catch (e: Exception) {
            null
        }
    }
    
    // 从RGB滑块更新时同步十六进制值
    LaunchedEffect(red, green, blue) {
        if (!isUpdatingFromSliders) {
            isUpdatingFromSliders = true
            currentColor = Color(
                red = red / 255f,
                green = green / 255f,
                blue = blue / 255f
            )
            val newHex = String.format("#%02X%02X%02X", red, green, blue)
            colorHex = newHex
            hexInput = newHex
            isUpdatingFromSliders = false
        }
    }
    
    // 从十六进制输入更新RGB值
    fun updateFromHexInput(hex: String) {
        hexInput = hex.uppercase()
        if (validateHexColor(hex)) {
            isHexValid = true
            parseHexColor(hex)?.let { (r, g, b) ->
                if (!isUpdatingFromSliders) {
                    isUpdatingFromSliders = true
                    red = r
                    green = g
                    blue = b
                    currentColor = Color(r / 255f, g / 255f, b / 255f)
                    colorHex = hex.uppercase()
                    isUpdatingFromSliders = false
                }
            }
        } else {
            isHexValid = false
        }
    }
    
    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = true
        )
    ) {
        Surface(
            shape = RoundedCornerShape(20.dp),
            color = Color(0xFFF8F6F3),
            modifier = Modifier
                .width(340.dp)
                .heightIn(max = 600.dp)
                .padding(0.dp)
        ) {
            Column(
                modifier = Modifier
                    .padding(20.dp)
                    .verticalScroll(rememberScrollState()),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // 标题
                Text(
                    text = "选择颜色",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.SemiBold,
                    color = MaterialTheme.colorScheme.primary,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 16.dp),
                    textAlign = TextAlign.Center
                )
                
                // 颜色预览
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(60.dp)
                        .clip(RoundedCornerShape(12.dp))
                        .background(color = currentColor)
                        .border(
                            width = 1.5.dp,
                            color = Color(0xFFD4CEC8),
                            shape = RoundedCornerShape(12.dp)
                        )
                )
                
                // 十六进制输入框
                Column(
                    modifier = Modifier.padding(top = 10.dp, bottom = 16.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        OutlinedTextField(
                            value = hexInput,
                            onValueChange = { input ->
                                // 自动添加#号
                                val cleanInput = input.trim()
                                val finalInput = if (cleanInput.isNotEmpty() && !cleanInput.startsWith("#")) {
                                    "#$cleanInput"
                                } else {
                                    cleanInput
                                }
                                updateFromHexInput(finalInput)
                            },
                            label = { 
                                Text(
                                    text = "十六进制颜色值",
                                    fontSize = 12.sp
                                ) 
                            },
                            placeholder = { 
                                Text(
                                    text = "#A8B8B8",
                                    fontSize = 14.sp,
                                    color = Color.Gray
                                ) 
                            },
                            isError = !isHexValid,
                            supportingText = {
                                if (!isHexValid) {
                                    Text(
                                        text = "请输入有效的十六进制颜色值 (如: #A8B8B8)",
                                        color = MaterialTheme.colorScheme.error,
                                        fontSize = 11.sp
                                    )
                                } else {
                                    Text(
                                        text = "支持格式: #RRGGBB 或 RRGGBB",
                                        color = Color.Gray,
                                        fontSize = 11.sp
                                    )
                                }
                            },
                            textStyle = TextStyle(
                                fontFamily = FontFamily.Monospace,
                                fontSize = 14.sp,
                                fontWeight = FontWeight.Medium,
                                textAlign = TextAlign.Center
                            ),
                            colors = OutlinedTextFieldDefaults.colors(
                                focusedBorderColor = if (isHexValid) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.error,
                                unfocusedBorderColor = if (isHexValid) Color(0xFFD4CEC8) else MaterialTheme.colorScheme.error,
                                focusedLabelColor = if (isHexValid) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.error,
                                unfocusedLabelColor = Color.Gray,
                                cursorColor = MaterialTheme.colorScheme.primary,
                                errorBorderColor = MaterialTheme.colorScheme.error,
                                errorLabelColor = MaterialTheme.colorScheme.error
                            ),
                            shape = RoundedCornerShape(12.dp),
                            keyboardOptions = KeyboardOptions(
                                keyboardType = KeyboardType.Text
                            ),
                            singleLine = true,
                            modifier = Modifier
                                .weight(1f)
                                .padding(horizontal = 8.dp),
                            trailingIcon = {
                                // 复制按钮
                                IconButton(
                                    onClick = {
                                        val clipboard = context.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
                                        val clip = ClipData.newPlainText("颜色代码", colorHex)
                                        clipboard.setPrimaryClip(clip)
                                        Toast.makeText(context, "已复制颜色代码：$colorHex", Toast.LENGTH_SHORT).show()
                                    }
                                ) {
                                    Icon(
                                        imageVector = Icons.Default.ContentCopy,
                                        contentDescription = "复制颜色代码",
                                        tint = MaterialTheme.colorScheme.primary,
                                        modifier = Modifier.size(16.dp)
                                    )
                                }
                            }
                        )
                    }
                    
                    // 预设颜色快选区
                    Text(
                        text = stringResource(R.string.common_colors),
                        fontSize = 12.sp,
                        fontWeight = FontWeight.Medium,
                        color = MaterialTheme.colorScheme.primary,
                        modifier = Modifier
                            .padding(top = 12.dp, bottom = 8.dp)
                            .align(Alignment.Start)
                    )
                    
                    val presetColors = listOf(
                        "#FF5722" to stringResource(R.string.vibrant_orange),
                        "#E91E63" to stringResource(R.string.romantic_pink),
                        "#9C27B0" to stringResource(R.string.elegant_purple),
                        "#673AB7" to stringResource(R.string.deep_purple),
                        "#3F51B5" to stringResource(R.string.classic_blue),
                        "#2196F3" to stringResource(R.string.sky_blue),
                        "#03DAC6" to stringResource(R.string.fresh_cyan),
                        "#4CAF50" to stringResource(R.string.natural_green),
                        "#8BC34A" to stringResource(R.string.vibrant_green),
                        "#FFC107" to stringResource(R.string.sunshine_yellow),
                        "#FF9800" to stringResource(R.string.warm_orange),
                        "#795548" to stringResource(R.string.coffee_brown),
                        "#607D8B" to stringResource(R.string.stylish_gray),
                        "#9E9E9E" to stringResource(R.string.neutral_gray),
                        "#A8B8B8" to stringResource(R.string.elegant_gray),
                        "#E0E0E0" to stringResource(R.string.light_gray)
                    )
                    
                    LazyVerticalGrid(
                        columns = GridCells.Fixed(8),
                        horizontalArrangement = Arrangement.spacedBy(6.dp),
                        verticalArrangement = Arrangement.spacedBy(6.dp),
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(60.dp)
                            .padding(horizontal = 8.dp)
                    ) {
                        items(presetColors) { (hexColor, name) ->
                            val presetColor = Color(android.graphics.Color.parseColor(hexColor))
                            Box(
                                modifier = Modifier
                                    .size(24.dp)
                                    .clip(CircleShape)
                                    .background(presetColor)
                                    .border(
                                        width = if (hexColor.uppercase() == colorHex) 2.dp else 1.dp,
                                        color = if (hexColor.uppercase() == colorHex) 
                                            MaterialTheme.colorScheme.primary 
                                        else Color(0xFFD4CEC8),
                                        shape = CircleShape
                                    )
                                    .clickable {
                                        updateFromHexInput(hexColor)
                                    }
                            ) {
                                if (hexColor.uppercase() == colorHex) {
                                    Icon(
                                        imageVector = Icons.Default.Check,
                                        contentDescription = null,
                                        tint = getAppropriateTextColor(presetColor),
                                        modifier = Modifier
                                            .size(12.dp)
                                            .align(Alignment.Center)
                                    )
                                }
                            }
                        }
                    }
                }
                
                // RGB滑块
                ModernColorSlider(
                    label = stringResource(R.string.red_color),
                    value = red.toFloat(),
                    onValueChange = { red = it.toInt() },
                    trackColor = Color(0xFFD4A5A5) // 莫兰迪红
                )
                
                ModernColorSlider(
                    label = stringResource(R.string.green_color),
                    value = green.toFloat(),
                    onValueChange = { green = it.toInt() },
                    trackColor = Color(0xFFA5C4A5) // 莫兰迪绿
                )
                
                ModernColorSlider(
                    label = stringResource(R.string.blue_color),
                    value = blue.toFloat(),
                    onValueChange = { blue = it.toInt() },
                    trackColor = Color(0xFFA5A5C4) // 莫兰迪蓝
                )
                
                Spacer(modifier = Modifier.height(20.dp))
                
                // 按钮
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(10.dp)
                ) {
                    // 取消按钮
                    OutlinedButton(
                        onClick = onDismiss,
                        modifier = Modifier.weight(1f),
                        shape = RoundedCornerShape(10.dp),
                        colors = ButtonDefaults.outlinedButtonColors(
                            contentColor = MaterialTheme.colorScheme.primary
                        )
                    ) {
                        Text(stringResource(R.string.cancel), fontSize = 12.sp, fontWeight = FontWeight.Medium)
                    }
                    
                    // 确认按钮
                    Button(
                        onClick = { onColorSelected(currentColor) },
                        modifier = Modifier.weight(1f),
                        shape = RoundedCornerShape(10.dp),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFFB8A9A0)
                        )
                    ) {
                        Text(stringResource(R.string.confirm), fontSize = 12.sp, fontWeight = FontWeight.Medium)
                    }
                }
            }
        }
    }
}

/**
 * 现代化颜色滑块 - 莫兰迪配色
 */
@Composable
fun ModernColorSlider(
    label: String,
    value: Float,
    onValueChange: (Float) -> Unit,
    trackColor: Color
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 6.dp)
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = label,
                fontSize = 12.sp,
                fontWeight = FontWeight.Medium,
                color = MaterialTheme.colorScheme.primary
            )
            
            Text(
                text = value.toInt().toString(),
                fontSize = 12.sp,
                fontWeight = FontWeight.Medium,
                color = MaterialTheme.colorScheme.primary,
                fontFamily = FontFamily.Monospace
            )
        }
        
        Spacer(modifier = Modifier.height(6.dp))
        
        Slider(
            value = value,
            onValueChange = onValueChange,
            valueRange = 0f..255f,
            modifier = Modifier.fillMaxWidth(),
            colors = SliderDefaults.colors(
                thumbColor = trackColor,
                activeTrackColor = trackColor,
                inactiveTrackColor = Color(0xFFD4CEC8)
            ),
            thumb = {
                Box(
                    modifier = Modifier
                        .size(16.dp)
                        .background(Color.White, CircleShape)
                        .border(1.5.dp, trackColor, CircleShape)
                )
            }
        )
    }
}

/**
 * 自定义字体设置组件
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CustomFontSection(
    uiState: ThemeSettingsViewModel.UiState,
    viewModel: ThemeSettingsViewModel
) {
    val context = LocalContext.current
    val hapticFeedback = LocalHapticFeedback.current
    
    // 文件选择器 - 使用OpenDocument获得更好的权限控制
    val fontFileLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.OpenDocument()
    ) { uri ->
        uri?.let { fileUri ->
            try {
                android.util.Log.d("FontImport", "开始处理字体文件: $fileUri")
                
                // 获取文件名，使用更可靠的方法
                var fileName = "custom_font.ttf"
                try {
                    context.contentResolver.query(fileUri, null, null, null, null)?.use { cursor ->
                        val nameIndex = cursor.getColumnIndex(android.provider.OpenableColumns.DISPLAY_NAME)
                        val sizeIndex = cursor.getColumnIndex(android.provider.OpenableColumns.SIZE)
                        
                        if (cursor.moveToFirst()) {
                            if (nameIndex >= 0) {
                                val queryFileName = cursor.getString(nameIndex)
                                if (!queryFileName.isNullOrEmpty()) {
                                    fileName = queryFileName
                                }
                            }
                            if (sizeIndex >= 0) {
                                val fileSize = cursor.getLong(sizeIndex)
                                android.util.Log.d("FontImport", "文件大小: $fileSize bytes")
                            }
                        }
                    }
                } catch (e: Exception) {
                    android.util.Log.w("FontImport", "无法查询文件信息: ${e.message}")
                    // 尝试从URI路径中提取文件名
                    fileUri.path?.let { path ->
                        val segments = path.split("/")
                        val lastSegment = segments.lastOrNull()
                        if (!lastSegment.isNullOrEmpty() && lastSegment.contains(".")) {
                            try {
                                fileName = java.net.URLDecoder.decode(lastSegment, "UTF-8")
                            } catch (decodeError: Exception) {
                                android.util.Log.w("FontImport", "文件名解码失败: ${decodeError.message}")
                            }
                        }
                    }
                }
                
                android.util.Log.d("FontImport", "文件名: $fileName")
                
                // 验证文件类型
                if (!fileName.lowercase().endsWith(".ttf")) {
                    Toast.makeText(context, "请选择TTF格式的字体文件", Toast.LENGTH_SHORT).show()
                    return@let
                }
                
                // 完全通过ContentResolver处理，避免任何文件路径访问
                try {
                    android.util.Log.d("FontImport", "开始通过ContentResolver处理文件: $fileUri")
                    
                    // 创建内部存储目录
                    val fontsDir = java.io.File(context.filesDir, "fonts")
                    if (!fontsDir.exists()) {
                        fontsDir.mkdirs()
                        android.util.Log.d("FontImport", "创建字体目录: ${fontsDir.absolutePath}")
                    }
                    
                    // 清理文件名
                    val cleanFileName = fileName.replace(Regex("[^a-zA-Z0-9._\\-\u4e00-\u9fa5]"), "_")
                    val targetFile = java.io.File(fontsDir, cleanFileName)
                    
                    android.util.Log.d("FontImport", "目标文件路径: ${targetFile.absolutePath}")
                    
                    // 先删除已存在的文件
                    if (targetFile.exists()) {
                        targetFile.delete()
                        android.util.Log.d("FontImport", "删除已存在的文件")
                    }
                    
                    // 首先读取所有字节到内存中，避免流访问权限问题
                    var fontBytes: ByteArray? = null
                    var success = false
                    
                    try {
                        // 方式1：尝试直接通过ContentResolver读取
                        context.contentResolver.openInputStream(fileUri)?.use { inputStream ->
                            android.util.Log.d("FontImport", "成功打开ContentResolver输入流")
                            fontBytes = inputStream.readBytes()
                            android.util.Log.d("FontImport", "成功读取字体数据: ${fontBytes?.size} bytes")
                            success = true
                        }
                    } catch (e: Exception) {
                        android.util.Log.w("FontImport", "ContentResolver读取失败，尝试其他方法: ${e.message}")
                        success = false
                    }
                    
                    // 如果第一种方式失败，尝试其他方式
                    if (!success && fontBytes == null) {
                        try {
                            // 方式2：通过DocumentsContract处理
                            android.util.Log.d("FontImport", "尝试DocumentsContract方式")
                            val inputStream = context.contentResolver.openInputStream(fileUri)
                            inputStream?.use { stream ->
                                val buffer = ByteArrayOutputStream()
                                val data = ByteArray(8192)
                                var bytesRead: Int
                                while (stream.read(data).also { bytesRead = it } != -1) {
                                    buffer.write(data, 0, bytesRead)
                                }
                                fontBytes = buffer.toByteArray()
                                android.util.Log.d("FontImport", "DocumentsContract读取成功: ${fontBytes?.size} bytes")
                                success = true
                            }
                        } catch (e: Exception) {
                            android.util.Log.e("FontImport", "DocumentsContract读取也失败: ${e.message}")
                        }
                    }
                    
                    // 如果成功读取到字体数据，写入内部存储
                    fontBytes?.let { bytes ->
                        if (bytes.isNotEmpty()) {
                            try {
                                targetFile.writeBytes(bytes)
                                android.util.Log.d("FontImport", "字体文件写入成功: ${targetFile.absolutePath}, 大小: ${bytes.size} bytes")
                                
                                // 验证文件写入成功
                                if (targetFile.exists() && targetFile.length() > 0) {
                                    val fontName = cleanFileName.removeSuffix(".ttf").removeSuffix(".TTF")
                                    android.util.Log.d("FontImport", "开始导入字体: $fontName")
                                    viewModel.importFontFile(targetFile.absolutePath, fontName)
                                } else {
                                    android.util.Log.e("FontImport", "文件写入验证失败")
                                    Toast.makeText(context, "字体文件保存失败", Toast.LENGTH_SHORT).show()
                                }
                            } catch (e: Exception) {
                                android.util.Log.e("FontImport", "写入文件失败: ${e.message}")
                                Toast.makeText(context, "字体文件保存失败: ${e.localizedMessage}", Toast.LENGTH_SHORT).show()
                            }
                        } else {
                            android.util.Log.e("FontImport", "读取到的字体数据为空")
                            Toast.makeText(context, "字体文件数据为空", Toast.LENGTH_SHORT).show()
                        }
                    } ?: run {
                        android.util.Log.e("FontImport", "无法读取字体文件数据")
                        Toast.makeText(context, 
                            "无法读取字体文件，请确保:\n1. 文件是有效的TTF格式\n2. 文件大小不超过10MB\n3. 重新选择文件", 
                            Toast.LENGTH_LONG).show()
                    }
                } catch (e: Exception) {
                    android.util.Log.e("FontImport", "文件操作失败", e)
                    Toast.makeText(context, "文件处理失败: ${e.localizedMessage}", Toast.LENGTH_SHORT).show()
                }
                
            } catch (e: SecurityException) {
                android.util.Log.e("FontImport", "权限错误", e)
                Toast.makeText(context, "文件访问权限不足，请重新选择文件", Toast.LENGTH_SHORT).show()
            } catch (e: Exception) {
                android.util.Log.e("FontImport", "导入字体失败", e)
                Toast.makeText(context, "导入字体失败: ${e.localizedMessage}", Toast.LENGTH_SHORT).show()
            }
        }
    }
    
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color(0xFFF8F6F3)
        ),
        shape = RoundedCornerShape(16.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(20.dp)
        ) {
            // 使用自定义字体开关
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = "使用自定义字体",
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Medium,
                        color = MaterialTheme.colorScheme.primary
                    )
                    Text(
                        text = if (uiState.useCustomFont && uiState.customFontName.isNotEmpty()) {
                            "当前字体: ${uiState.customFontName}"
                        } else {
                            "使用系统默认字体"
                        },
                        fontSize = 13.sp,
                        color = Color(0xFF8B7E7E),
                        modifier = Modifier.padding(top = 2.dp)
                    )
                }
                
                Switch(
                    checked = uiState.useCustomFont,
                    onCheckedChange = { enabled ->
                        hapticFeedback.performHapticFeedback(HapticFeedbackType.LongPress)
                        viewModel.toggleCustomFont(enabled)
                    },
                    colors = SwitchDefaults.colors(
                        checkedThumbColor = Color(0xFF8BAA80),
                        checkedTrackColor = Color(0xFFB8D4B0),
                        uncheckedThumbColor = Color(0xFFB8A9A0),
                        uncheckedTrackColor = Color(0xFFE0D5D0)
                    )
                )
            }
            
            // 当前字体信息显示
            if (uiState.customFontName.isNotEmpty()) {
                Spacer(modifier = Modifier.height(16.dp))
                
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = Color(0xFFF0EDE8)
                    ),
                    shape = RoundedCornerShape(12.dp)
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp)
                    ) {
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Column(modifier = Modifier.weight(1f)) {
                                Text(
                                    text = uiState.customFontName,
                                    fontSize = 14.sp,
                                    fontWeight = FontWeight.Medium,
                                    color = MaterialTheme.colorScheme.primary
                                )
                                                                 Text(
                                     text = "字体预览: 这是自定义字体的显示效果 Font Preview",
                                     fontSize = 12.sp,
                                     color = Color(0xFF8B7E7E),
                                     modifier = Modifier.padding(top = 4.dp),
                                     // 使用自定义字体显示预览效果
                                     fontFamily = if (uiState.useCustomFont) {
                                         com.timeflow.app.ui.theme.FontManager.currentFontFamily
                                     } else {
                                         androidx.compose.ui.text.font.FontFamily.Default
                                     }
                                 )
                            }
                            
                            // 删除按钮
                            IconButton(
                                onClick = {
                                    hapticFeedback.performHapticFeedback(HapticFeedbackType.LongPress)
                                    viewModel.deleteCustomFont()
                                },
                                modifier = Modifier.size(32.dp)
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Delete,
                                    contentDescription = "删除字体",
                                    tint = Color(0xFFB8695F),
                                    modifier = Modifier.size(18.dp)
                                )
                            }
                        }
                    }
                }
            }
            
            // 导入字体按钮
            Spacer(modifier = Modifier.height(16.dp))
            
            Button(
                onClick = {
                    hapticFeedback.performHapticFeedback(HapticFeedbackType.LongPress)
                    // 使用OpenDocument，支持多种字体文件类型
                    fontFileLauncher.launch(arrayOf("application/octet-stream", "font/ttf", "font/otf", "*/*"))
                },
                modifier = Modifier.fillMaxWidth(),
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color(0xFF8BAA80),
                    contentColor = Color.White
                ),
                shape = RoundedCornerShape(12.dp),
                enabled = !uiState.isProcessingFont
            ) {
                if (uiState.isProcessingFont) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(16.dp),
                        color = Color.White,
                        strokeWidth = 2.dp
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("处理中...")
                } else {
                    Icon(
                        imageVector = Icons.Default.FileUpload,
                        contentDescription = null,
                        modifier = Modifier.size(18.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = if (uiState.customFontName.isEmpty()) "导入TTF字体" else "更换字体",
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Medium
                    )
                }
            }
            
            // 处理状态消息
            if (uiState.fontProcessingMessage.isNotEmpty()) {
                Spacer(modifier = Modifier.height(12.dp))
                
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = if (uiState.fontProcessingMessage.contains("成功") || uiState.fontProcessingMessage.contains("已删除")) {
                            Color(0xFFE8F5E8)
                        } else if (uiState.fontProcessingMessage.contains("失败") || uiState.fontProcessingMessage.contains("错误")) {
                            Color(0xFFFFF2F2)
                        } else {
                            Color(0xFFF0F8FF)
                        }
                    ),
                    shape = RoundedCornerShape(8.dp)
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(12.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = uiState.fontProcessingMessage,
                            fontSize = 12.sp,
                            color = if (uiState.fontProcessingMessage.contains("成功") || uiState.fontProcessingMessage.contains("已删除")) {
                                Color(0xFF2E7D32)
                            } else if (uiState.fontProcessingMessage.contains("失败") || uiState.fontProcessingMessage.contains("错误")) {
                                Color(0xFFD32F2F)
                            } else {
                                Color(0xFF1976D2)
                            },
                            modifier = Modifier.weight(1f)
                        )
                        
                        if (!uiState.isProcessingFont && uiState.fontProcessingMessage.isNotEmpty()) {
                            IconButton(
                                onClick = { viewModel.clearFontProcessingMessage() },
                                modifier = Modifier.size(20.dp)
                            ) {
                                Text(
                                    text = "×",
                                    fontSize = 14.sp,
                                    color = Color(0xFF8B7E7E)
                                )
                            }
                        }
                    }
                }
            }
            
            // 帮助信息
            Spacer(modifier = Modifier.height(12.dp))
            
            Text(
                text = "• 支持TTF格式的字体文件\n• 导入后的字体将应用到整个应用的文本显示\n• 如果遇到权限问题，请从文件管理器中选择字体文件\n• 建议使用中文字体以获得更好的显示效果",
                fontSize = 11.sp,
                color = Color(0xFF9A8B7A),
                lineHeight = 16.sp
            )
        }
    }
}

// ... existing code ... 