package com.timeflow.app.navigation

import androidx.navigation.NavType
import androidx.navigation.navArgument

object AppDestinations {
    // 基础路由
    const val HOME_ROUTE = "home"
    const val UNIFIED_HOME_ROUTE = "unified_home"
    const val TASK_ROUTE = "task"
    const val TASK_LIST_ROUTE = "task_list"
    const val TASK_LIST_FULL_ROUTE = "task_list_full"
    const val TASK_DETAIL_ROUTE = "task_detail/{taskId}"
    const val TASK_EDIT_ROUTE = "task_edit"
    const val ADD_TASK_ROUTE = "add_task"
    const val CALENDAR_ROUTE = "calendar"
    const val SETTINGS_ROUTE = "settings"
    const val ABOUT_ROUTE = "about"
    
    // 愿望池路由
    const val WISH_LIST_ROUTE = "wish_list"
    const val WISH_DETAIL_ROUTE = "wish_detail/{wishId}"
    const val WISH_STATISTICS_ROUTE = "wish_statistics"
    
    // 时间追踪路由
    const val TIME_TRACKING_ROUTE = "time_tracking"
    const val TIME_STATISTICS_ROUTE = "time_statistics"
    
    // 分析统计路由
    const val ANALYTICS_ROUTE = "analytics"
    
    // 个人资料路由
    const val PROFILE_ROUTE = "profile"
    const val ACCOUNT_ROUTE = "account"
    const val EMOTION_STATS_ROUTE = "emotion_stats"
    const val DETAILED_EMOTION_RECORD_ROUTE = "detailed_emotion_record"
    
    // 主题设置路由
    const val THEME_SETTINGS_ROUTE = "theme_settings"

    // 语言设置路由
    const val LANGUAGE_SETTINGS_ROUTE = "language_settings"
    
    // AI助手路由
    const val AI_ASSISTANT_ROUTE = "ai_assistant"
    const val AI_ASSISTANT_WITH_TASK_ROUTE = "ai_assistant/{taskId}"
    const val AI_REVIEW_ROUTE = "ai_review"
    const val AI_SETTINGS_ROUTE = "ai_settings"
    const val AI_MODEL_SETTINGS_ROUTE = "ai_model_settings"
    const val AI_MODEL_SETTINGS_WITH_ID_ROUTE = "ai_model_settings/{configId}"
    
    // 反思路由
    const val REFLECTION_ROUTE = "reflection"
    const val REFLECTION_DETAIL_ROUTE = "reflection_detail/{reflectionId}"
    const val ADD_REFLECTION_ROUTE = "add_reflection"
    
    // 目标管理路由
    const val GOAL_MANAGEMENT = "goal_management"
    const val GOAL_DETAIL_ROUTE = "goal_detail/{goalId}"
    const val ADD_GOAL_ROUTE = "add_goal"
    const val EDIT_GOAL_ROUTE = "edit_goal/{goalId}"
    const val GOAL_BREAKDOWN_ROUTE = "goal_breakdown/{goalId}"
    const val GOAL_REVIEW_ROUTE = "goal_review/{goalId}"
    const val GOAL_COMPLETION_ANALYSIS_ROUTE = "goal_completion_analysis"
    
    // 健康追踪路由
    const val MENSTRUAL_CYCLE_ROUTE = "menstrual_cycle"
    const val MENSTRUAL_CYCLE_STATS_ROUTE = "menstrual_cycle_stats" // 🔧 融合：统一的统计页面
    const val PERIOD_HISTORY_ROUTE = "period_history"
    // 🔧 删除：PERIOD_ANALYTICS_ROUTE 已合并到 MENSTRUAL_CYCLE_STATS_ROUTE
    const val HABIT_TRACKER_ROUTE = "habit_tracker"
    const val HABIT_DETAIL_ROUTE = "habit_detail/{habitId}"
    const val ADD_HABIT_ROUTE = "add_habit"
    const val EDIT_HABIT_ROUTE = "edit_habit/{habitId}"
    const val SYMPTOMS_DETAIL_ROUTE = "symptoms_detail"
    const val MEDICATION_MANAGEMENT_ROUTE = "medication_management"
    
    // 里程碑路由
    const val MILESTONE_ROUTE = "milestone"
    
    // 目标模板路由
    const val GOAL_TEMPLATE_LIST = "goal_template_list"
    const val GOAL_TEMPLATE_IMPORT = "goal_template_import"
    const val GOAL_TEMPLATE_IMPORT_WITH_CATEGORY = "goal_template_import/{category}"
    const val GOAL_TEMPLATE_EDIT = "goal_template_edit"
    const val GOAL_TEMPLATE_EDIT_WITH_ID_ROUTE = "goal_template_edit/{templateId}"
    const val SAVE_GOAL_AS_TEMPLATE_ROUTE = "save_goal_as_template/{goalId}"

    // 参数定义
    val taskDetailArguments = listOf(
        navArgument("taskId") {
            type = NavType.StringType
            nullable = false
        }
    )
    
    val wishDetailArguments = listOf(
        navArgument("wishId") {
            type = NavType.StringType
            nullable = false
        }
    )
    
    val aiAssistantWithTaskArguments = listOf(
        navArgument("taskId") {
            type = NavType.StringType
            nullable = true
        }
    )
    
    val reflectionDetailArguments = listOf(
        navArgument("reflectionId") {
            type = NavType.StringType
            nullable = false
        }
    )
    
    val goalDetailArguments = listOf(
        navArgument("goalId") {
            type = NavType.StringType
            nullable = false
        }
    )
    
    val goalEditArguments = listOf(
        navArgument("goalId") {
            type = NavType.StringType
            nullable = false
        }
    )
    
    val goalBreakdownArguments = listOf(
        navArgument("goalId") {
            type = NavType.StringType
            nullable = false
        }
    )
    
    val goalReviewArguments = listOf(
        navArgument("goalId") {
            type = NavType.StringType
            nullable = false
        }
    )
    
    val habitDetailArguments = listOf(
        navArgument("habitId") {
            type = NavType.StringType
            nullable = false
        }
    )
    
    val habitEditArguments = listOf(
        navArgument("habitId") {
            type = NavType.StringType
            nullable = false
        }
    )
    
    val aiModelSettingsArguments = listOf(
        navArgument("configId") {
            type = NavType.StringType
            nullable = true
        }
    )
    
    val goalTemplateImportArguments = listOf(
        navArgument("category") {
            type = NavType.StringType
            nullable = false
        }
    )
    
    val goalTemplateEditArguments = listOf(
        navArgument("templateId") {
            type = NavType.StringType
            nullable = false
        }
    )
    
    val saveGoalAsTemplateArguments = listOf(
        navArgument("goalId") {
            type = NavType.StringType
            nullable = false
        }
    )

    fun taskDetailRoute(taskId: String): String {
        require(taskId.isNotBlank()) { "TaskId cannot be blank" }
        return "task_detail/$taskId"
    }
    
    fun taskEditRoute(taskId: String? = null): String {
        require(taskId == null || taskId.isNotBlank()) { "If taskId is provided, it cannot be blank" }
        return if (taskId != null) "task_edit/$taskId" else "task_edit"
    }
    
    fun wishDetailRoute(wishId: String): String {
        require(wishId.isNotBlank()) { "WishId cannot be blank" }
        return "wish_detail/$wishId"
    }
    
    fun goalDetailRoute(goalId: String): String {
        return "goal_detail/$goalId"
    }
    
    fun aiAssistantWithTaskRoute(taskId: String): String {
        return "ai_assistant/$taskId"
    }
    
    fun reflectionDetailRoute(reflectionId: String): String {
        return "reflection_detail/$reflectionId"
    }
    
    fun goalEditRoute(goalId: String): String {
        return "edit_goal/$goalId"
    }
    
    fun goalBreakdownRoute(goalId: String): String {
        return "goal_breakdown/$goalId"
    }
    
    fun goalReviewRoute(goalId: String): String {
        return "goal_review/$goalId"
    }
    
    fun habitDetailRoute(habitId: String): String {
        return "habit_detail/$habitId"
    }
    
    fun habitEditRoute(habitId: String): String {
        return "edit_habit/$habitId"
    }
    
    fun aiModelSettingsWithIdRoute(configId: String): String {
        return "ai_model_settings/$configId"
    }
    
    fun goalTemplateImportWithCategoryRoute(category: String): String {
        return "goal_template_import/$category"
    }
    
    fun goalTemplateEditWithIdRoute(templateId: String): String {
        return "goal_template_edit/$templateId"
    }
    
    fun saveGoalAsTemplateRoute(goalId: String): String {
        return "save_goal_as_template/$goalId"
    }
} 