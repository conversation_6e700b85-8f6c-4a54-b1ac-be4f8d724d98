package com.timeflow.app.di;

import com.timeflow.app.data.dao.GoalDao;
import com.timeflow.app.data.repository.GoalRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DatabaseModule_ProvideGoalRepositoryFactory implements Factory<GoalRepository> {
  private final Provider<GoalDao> goalDaoProvider;

  public DatabaseModule_ProvideGoalRepositoryFactory(Provider<GoalDao> goalDaoProvider) {
    this.goalDaoProvider = goalDaoProvider;
  }

  @Override
  public GoalRepository get() {
    return provideGoalRepository(goalDaoProvider.get());
  }

  public static DatabaseModule_ProvideGoalRepositoryFactory create(
      Provider<GoalDao> goalDaoProvider) {
    return new DatabaseModule_ProvideGoalRepositoryFactory(goalDaoProvider);
  }

  public static GoalRepository provideGoalRepository(GoalDao goalDao) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.provideGoalRepository(goalDao));
  }
}
