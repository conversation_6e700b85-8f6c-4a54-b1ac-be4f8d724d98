package com.timeflow.app.ui.screen.reflection.data;

import com.timeflow.app.data.dao.ReflectionDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ReflectionRepositoryImpl_Factory implements Factory<ReflectionRepositoryImpl> {
  private final Provider<ReflectionDao> reflectionDaoProvider;

  public ReflectionRepositoryImpl_Factory(Provider<ReflectionDao> reflectionDaoProvider) {
    this.reflectionDaoProvider = reflectionDaoProvider;
  }

  @Override
  public ReflectionRepositoryImpl get() {
    return newInstance(reflectionDaoProvider.get());
  }

  public static ReflectionRepositoryImpl_Factory create(
      Provider<ReflectionDao> reflectionDaoProvider) {
    return new ReflectionRepositoryImpl_Factory(reflectionDaoProvider);
  }

  public static ReflectionRepositoryImpl newInstance(ReflectionDao reflectionDao) {
    return new ReflectionRepositoryImpl(reflectionDao);
  }
}
