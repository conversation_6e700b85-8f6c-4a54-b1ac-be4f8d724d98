package com.timeflow.app.di;

import com.timeflow.app.data.dao.EmotionRecordDao;
import com.timeflow.app.data.db.AppDatabase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DatabaseModule_ProvideEmotionRecordDaoFactory implements Factory<EmotionRecordDao> {
  private final Provider<AppDatabase> databaseProvider;

  public DatabaseModule_ProvideEmotionRecordDaoFactory(Provider<AppDatabase> databaseProvider) {
    this.databaseProvider = databaseProvider;
  }

  @Override
  public EmotionRecordDao get() {
    return provideEmotionRecordDao(databaseProvider.get());
  }

  public static DatabaseModule_ProvideEmotionRecordDaoFactory create(
      Provider<AppDatabase> databaseProvider) {
    return new DatabaseModule_ProvideEmotionRecordDaoFactory(databaseProvider);
  }

  public static EmotionRecordDao provideEmotionRecordDao(AppDatabase database) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.provideEmotionRecordDao(database));
  }
}
