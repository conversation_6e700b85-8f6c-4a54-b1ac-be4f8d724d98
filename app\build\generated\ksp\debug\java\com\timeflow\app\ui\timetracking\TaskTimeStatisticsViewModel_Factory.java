package com.timeflow.app.ui.timetracking;

import com.timeflow.app.data.repository.TimeSessionRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class TaskTimeStatisticsViewModel_Factory implements Factory<TaskTimeStatisticsViewModel> {
  private final Provider<TimeSessionRepository> timeSessionRepositoryProvider;

  public TaskTimeStatisticsViewModel_Factory(
      Provider<TimeSessionRepository> timeSessionRepositoryProvider) {
    this.timeSessionRepositoryProvider = timeSessionRepositoryProvider;
  }

  @Override
  public TaskTimeStatisticsViewModel get() {
    return newInstance(timeSessionRepositoryProvider.get());
  }

  public static TaskTimeStatisticsViewModel_Factory create(
      Provider<TimeSessionRepository> timeSessionRepositoryProvider) {
    return new TaskTimeStatisticsViewModel_Factory(timeSessionRepositoryProvider);
  }

  public static TaskTimeStatisticsViewModel newInstance(
      TimeSessionRepository timeSessionRepository) {
    return new TaskTimeStatisticsViewModel(timeSessionRepository);
  }
}
