package com.timeflow.app.ui.viewmodel;

import com.timeflow.app.data.repository.CycleRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class MenstrualCycleViewModel_Factory implements Factory<MenstrualCycleViewModel> {
  private final Provider<CycleRepository> repositoryProvider;

  public MenstrualCycleViewModel_Factory(Provider<CycleRepository> repositoryProvider) {
    this.repositoryProvider = repositoryProvider;
  }

  @Override
  public MenstrualCycleViewModel get() {
    return newInstance(repositoryProvider.get());
  }

  public static MenstrualCycleViewModel_Factory create(
      Provider<CycleRepository> repositoryProvider) {
    return new MenstrualCycleViewModel_Factory(repositoryProvider);
  }

  public static MenstrualCycleViewModel newInstance(CycleRepository repository) {
    return new MenstrualCycleViewModel(repository);
  }
}
