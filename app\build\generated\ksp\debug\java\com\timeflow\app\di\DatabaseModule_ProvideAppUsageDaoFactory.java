package com.timeflow.app.di;

import com.timeflow.app.data.dao.AppUsageDao;
import com.timeflow.app.data.db.AppDatabase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DatabaseModule_ProvideAppUsageDaoFactory implements Factory<AppUsageDao> {
  private final Provider<AppDatabase> databaseProvider;

  public DatabaseModule_ProvideAppUsageDaoFactory(Provider<AppDatabase> databaseProvider) {
    this.databaseProvider = databaseProvider;
  }

  @Override
  public AppUsageDao get() {
    return provideAppUsageDao(databaseProvider.get());
  }

  public static DatabaseModule_ProvideAppUsageDaoFactory create(
      Provider<AppDatabase> databaseProvider) {
    return new DatabaseModule_ProvideAppUsageDaoFactory(databaseProvider);
  }

  public static AppUsageDao provideAppUsageDao(AppDatabase database) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.provideAppUsageDao(database));
  }
}
