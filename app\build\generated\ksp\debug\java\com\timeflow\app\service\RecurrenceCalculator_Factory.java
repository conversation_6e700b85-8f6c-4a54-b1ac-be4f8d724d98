package com.timeflow.app.service;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class RecurrenceCalculator_Factory implements Factory<RecurrenceCalculator> {
  @Override
  public RecurrenceCalculator get() {
    return newInstance();
  }

  public static RecurrenceCalculator_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static RecurrenceCalculator newInstance() {
    return new RecurrenceCalculator();
  }

  private static final class InstanceHolder {
    private static final RecurrenceCalculator_Factory INSTANCE = new RecurrenceCalculator_Factory();
  }
}
