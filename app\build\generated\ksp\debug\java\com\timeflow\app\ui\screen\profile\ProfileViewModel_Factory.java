package com.timeflow.app.ui.screen.profile;

import com.timeflow.app.data.repository.EmotionRecordRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ProfileViewModel_Factory implements Factory<ProfileViewModel> {
  private final Provider<EmotionRecordRepository> emotionRecordRepositoryProvider;

  public ProfileViewModel_Factory(
      Provider<EmotionRecordRepository> emotionRecordRepositoryProvider) {
    this.emotionRecordRepositoryProvider = emotionRecordRepositoryProvider;
  }

  @Override
  public ProfileViewModel get() {
    return newInstance(emotionRecordRepositoryProvider.get());
  }

  public static ProfileViewModel_Factory create(
      Provider<EmotionRecordRepository> emotionRecordRepositoryProvider) {
    return new ProfileViewModel_Factory(emotionRecordRepositoryProvider);
  }

  public static ProfileViewModel newInstance(EmotionRecordRepository emotionRecordRepository) {
    return new ProfileViewModel(emotionRecordRepository);
  }
}
