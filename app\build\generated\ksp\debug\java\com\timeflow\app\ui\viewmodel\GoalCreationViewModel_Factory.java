package com.timeflow.app.ui.viewmodel;

import com.timeflow.app.domain.usecase.goal.QuickGoalCreationUseCase;
import com.timeflow.app.domain.usecase.goal.SmartTemplateUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class GoalCreationViewModel_Factory implements Factory<GoalCreationViewModel> {
  private final Provider<SmartTemplateUseCase> smartTemplateUseCaseProvider;

  private final Provider<QuickGoalCreationUseCase> quickGoalCreationUseCaseProvider;

  public GoalCreationViewModel_Factory(Provider<SmartTemplateUseCase> smartTemplateUseCaseProvider,
      Provider<QuickGoalCreationUseCase> quickGoalCreationUseCaseProvider) {
    this.smartTemplateUseCaseProvider = smartTemplateUseCaseProvider;
    this.quickGoalCreationUseCaseProvider = quickGoalCreationUseCaseProvider;
  }

  @Override
  public GoalCreationViewModel get() {
    return newInstance(smartTemplateUseCaseProvider.get(), quickGoalCreationUseCaseProvider.get());
  }

  public static GoalCreationViewModel_Factory create(
      Provider<SmartTemplateUseCase> smartTemplateUseCaseProvider,
      Provider<QuickGoalCreationUseCase> quickGoalCreationUseCaseProvider) {
    return new GoalCreationViewModel_Factory(smartTemplateUseCaseProvider, quickGoalCreationUseCaseProvider);
  }

  public static GoalCreationViewModel newInstance(SmartTemplateUseCase smartTemplateUseCase,
      QuickGoalCreationUseCase quickGoalCreationUseCase) {
    return new GoalCreationViewModel(smartTemplateUseCase, quickGoalCreationUseCase);
  }
}
