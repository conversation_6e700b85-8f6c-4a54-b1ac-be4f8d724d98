package com.timeflow.app.data.dao;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.timeflow.app.data.converter.Converters;
import com.timeflow.app.data.entity.ReflectionEntity;
import java.lang.Class;
import java.lang.Exception;
import java.lang.IllegalStateException;
import java.lang.Integer;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class ReflectionDao_Impl implements ReflectionDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<ReflectionEntity> __insertionAdapterOfReflectionEntity;

  private final Converters __converters = new Converters();

  private final EntityDeletionOrUpdateAdapter<ReflectionEntity> __deletionAdapterOfReflectionEntity;

  private final EntityDeletionOrUpdateAdapter<ReflectionEntity> __updateAdapterOfReflectionEntity;

  private final SharedSQLiteStatement __preparedStmtOfDeleteReflectionById;

  private final SharedSQLiteStatement __preparedStmtOfClearAllReflections;

  public ReflectionDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfReflectionEntity = new EntityInsertionAdapter<ReflectionEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `reflections` (`id`,`title`,`content`,`rich_content_json`,`date`,`rating`,`tags_json`,`type`,`mood`,`plans_json`,`background_image`,`metrics_json`,`created_at`,`updated_at`,`task_id`,`task_title`,`is_from_task_completion`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final ReflectionEntity entity) {
        statement.bindString(1, entity.getId());
        statement.bindString(2, entity.getTitle());
        statement.bindString(3, entity.getContent());
        statement.bindString(4, entity.getRichContentJson());
        final Long _tmp = __converters.instantToTimestamp(entity.getDate());
        if (_tmp == null) {
          statement.bindNull(5);
        } else {
          statement.bindLong(5, _tmp);
        }
        statement.bindLong(6, entity.getRating());
        statement.bindString(7, entity.getTagsJson());
        statement.bindString(8, entity.getType());
        statement.bindString(9, entity.getMood());
        statement.bindString(10, entity.getPlansJson());
        if (entity.getBackgroundImage() == null) {
          statement.bindNull(11);
        } else {
          statement.bindString(11, entity.getBackgroundImage());
        }
        statement.bindString(12, entity.getMetricsJson());
        final Long _tmp_1 = __converters.instantToTimestamp(entity.getCreatedAt());
        if (_tmp_1 == null) {
          statement.bindNull(13);
        } else {
          statement.bindLong(13, _tmp_1);
        }
        final Long _tmp_2 = __converters.instantToTimestamp(entity.getUpdatedAt());
        if (_tmp_2 == null) {
          statement.bindNull(14);
        } else {
          statement.bindLong(14, _tmp_2);
        }
        if (entity.getTaskId() == null) {
          statement.bindNull(15);
        } else {
          statement.bindString(15, entity.getTaskId());
        }
        if (entity.getTaskTitle() == null) {
          statement.bindNull(16);
        } else {
          statement.bindString(16, entity.getTaskTitle());
        }
        final int _tmp_3 = entity.isFromTaskCompletion() ? 1 : 0;
        statement.bindLong(17, _tmp_3);
      }
    };
    this.__deletionAdapterOfReflectionEntity = new EntityDeletionOrUpdateAdapter<ReflectionEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `reflections` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final ReflectionEntity entity) {
        statement.bindString(1, entity.getId());
      }
    };
    this.__updateAdapterOfReflectionEntity = new EntityDeletionOrUpdateAdapter<ReflectionEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `reflections` SET `id` = ?,`title` = ?,`content` = ?,`rich_content_json` = ?,`date` = ?,`rating` = ?,`tags_json` = ?,`type` = ?,`mood` = ?,`plans_json` = ?,`background_image` = ?,`metrics_json` = ?,`created_at` = ?,`updated_at` = ?,`task_id` = ?,`task_title` = ?,`is_from_task_completion` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final ReflectionEntity entity) {
        statement.bindString(1, entity.getId());
        statement.bindString(2, entity.getTitle());
        statement.bindString(3, entity.getContent());
        statement.bindString(4, entity.getRichContentJson());
        final Long _tmp = __converters.instantToTimestamp(entity.getDate());
        if (_tmp == null) {
          statement.bindNull(5);
        } else {
          statement.bindLong(5, _tmp);
        }
        statement.bindLong(6, entity.getRating());
        statement.bindString(7, entity.getTagsJson());
        statement.bindString(8, entity.getType());
        statement.bindString(9, entity.getMood());
        statement.bindString(10, entity.getPlansJson());
        if (entity.getBackgroundImage() == null) {
          statement.bindNull(11);
        } else {
          statement.bindString(11, entity.getBackgroundImage());
        }
        statement.bindString(12, entity.getMetricsJson());
        final Long _tmp_1 = __converters.instantToTimestamp(entity.getCreatedAt());
        if (_tmp_1 == null) {
          statement.bindNull(13);
        } else {
          statement.bindLong(13, _tmp_1);
        }
        final Long _tmp_2 = __converters.instantToTimestamp(entity.getUpdatedAt());
        if (_tmp_2 == null) {
          statement.bindNull(14);
        } else {
          statement.bindLong(14, _tmp_2);
        }
        if (entity.getTaskId() == null) {
          statement.bindNull(15);
        } else {
          statement.bindString(15, entity.getTaskId());
        }
        if (entity.getTaskTitle() == null) {
          statement.bindNull(16);
        } else {
          statement.bindString(16, entity.getTaskTitle());
        }
        final int _tmp_3 = entity.isFromTaskCompletion() ? 1 : 0;
        statement.bindLong(17, _tmp_3);
        statement.bindString(18, entity.getId());
      }
    };
    this.__preparedStmtOfDeleteReflectionById = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM reflections WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfClearAllReflections = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM reflections";
        return _query;
      }
    };
  }

  @Override
  public Object insertReflection(final ReflectionEntity reflection,
      final Continuation<? super Long> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Long>() {
      @Override
      @NonNull
      public Long call() throws Exception {
        __db.beginTransaction();
        try {
          final Long _result = __insertionAdapterOfReflectionEntity.insertAndReturnId(reflection);
          __db.setTransactionSuccessful();
          return _result;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteReflection(final ReflectionEntity reflection,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfReflectionEntity.handle(reflection);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateReflection(final ReflectionEntity reflection,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfReflectionEntity.handle(reflection);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteReflectionById(final String reflectionId,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteReflectionById.acquire();
        int _argIndex = 1;
        _stmt.bindString(_argIndex, reflectionId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteReflectionById.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object clearAllReflections(final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfClearAllReflections.acquire();
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfClearAllReflections.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object getAllReflections(final Continuation<? super List<ReflectionEntity>> $completion) {
    final String _sql = "SELECT * FROM reflections ORDER BY date DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<ReflectionEntity>>() {
      @Override
      @NonNull
      public List<ReflectionEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfContent = CursorUtil.getColumnIndexOrThrow(_cursor, "content");
          final int _cursorIndexOfRichContentJson = CursorUtil.getColumnIndexOrThrow(_cursor, "rich_content_json");
          final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
          final int _cursorIndexOfRating = CursorUtil.getColumnIndexOrThrow(_cursor, "rating");
          final int _cursorIndexOfTagsJson = CursorUtil.getColumnIndexOrThrow(_cursor, "tags_json");
          final int _cursorIndexOfType = CursorUtil.getColumnIndexOrThrow(_cursor, "type");
          final int _cursorIndexOfMood = CursorUtil.getColumnIndexOrThrow(_cursor, "mood");
          final int _cursorIndexOfPlansJson = CursorUtil.getColumnIndexOrThrow(_cursor, "plans_json");
          final int _cursorIndexOfBackgroundImage = CursorUtil.getColumnIndexOrThrow(_cursor, "background_image");
          final int _cursorIndexOfMetricsJson = CursorUtil.getColumnIndexOrThrow(_cursor, "metrics_json");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
          final int _cursorIndexOfTaskId = CursorUtil.getColumnIndexOrThrow(_cursor, "task_id");
          final int _cursorIndexOfTaskTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "task_title");
          final int _cursorIndexOfIsFromTaskCompletion = CursorUtil.getColumnIndexOrThrow(_cursor, "is_from_task_completion");
          final List<ReflectionEntity> _result = new ArrayList<ReflectionEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final ReflectionEntity _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpTitle;
            _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            final String _tmpContent;
            _tmpContent = _cursor.getString(_cursorIndexOfContent);
            final String _tmpRichContentJson;
            _tmpRichContentJson = _cursor.getString(_cursorIndexOfRichContentJson);
            final Instant _tmpDate;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfDate);
            }
            final Instant _tmp_1 = __converters.fromTimestampToInstant(_tmp);
            if (_tmp_1 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.Instant', but it was NULL.");
            } else {
              _tmpDate = _tmp_1;
            }
            final int _tmpRating;
            _tmpRating = _cursor.getInt(_cursorIndexOfRating);
            final String _tmpTagsJson;
            _tmpTagsJson = _cursor.getString(_cursorIndexOfTagsJson);
            final String _tmpType;
            _tmpType = _cursor.getString(_cursorIndexOfType);
            final String _tmpMood;
            _tmpMood = _cursor.getString(_cursorIndexOfMood);
            final String _tmpPlansJson;
            _tmpPlansJson = _cursor.getString(_cursorIndexOfPlansJson);
            final String _tmpBackgroundImage;
            if (_cursor.isNull(_cursorIndexOfBackgroundImage)) {
              _tmpBackgroundImage = null;
            } else {
              _tmpBackgroundImage = _cursor.getString(_cursorIndexOfBackgroundImage);
            }
            final String _tmpMetricsJson;
            _tmpMetricsJson = _cursor.getString(_cursorIndexOfMetricsJson);
            final Instant _tmpCreatedAt;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            final Instant _tmp_3 = __converters.fromTimestampToInstant(_tmp_2);
            if (_tmp_3 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.Instant', but it was NULL.");
            } else {
              _tmpCreatedAt = _tmp_3;
            }
            final Instant _tmpUpdatedAt;
            final Long _tmp_4;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            final Instant _tmp_5 = __converters.fromTimestampToInstant(_tmp_4);
            if (_tmp_5 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.Instant', but it was NULL.");
            } else {
              _tmpUpdatedAt = _tmp_5;
            }
            final String _tmpTaskId;
            if (_cursor.isNull(_cursorIndexOfTaskId)) {
              _tmpTaskId = null;
            } else {
              _tmpTaskId = _cursor.getString(_cursorIndexOfTaskId);
            }
            final String _tmpTaskTitle;
            if (_cursor.isNull(_cursorIndexOfTaskTitle)) {
              _tmpTaskTitle = null;
            } else {
              _tmpTaskTitle = _cursor.getString(_cursorIndexOfTaskTitle);
            }
            final boolean _tmpIsFromTaskCompletion;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfIsFromTaskCompletion);
            _tmpIsFromTaskCompletion = _tmp_6 != 0;
            _item = new ReflectionEntity(_tmpId,_tmpTitle,_tmpContent,_tmpRichContentJson,_tmpDate,_tmpRating,_tmpTagsJson,_tmpType,_tmpMood,_tmpPlansJson,_tmpBackgroundImage,_tmpMetricsJson,_tmpCreatedAt,_tmpUpdatedAt,_tmpTaskId,_tmpTaskTitle,_tmpIsFromTaskCompletion);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<ReflectionEntity>> getAllReflectionsFlow() {
    final String _sql = "SELECT * FROM reflections ORDER BY date DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"reflections"}, new Callable<List<ReflectionEntity>>() {
      @Override
      @NonNull
      public List<ReflectionEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfContent = CursorUtil.getColumnIndexOrThrow(_cursor, "content");
          final int _cursorIndexOfRichContentJson = CursorUtil.getColumnIndexOrThrow(_cursor, "rich_content_json");
          final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
          final int _cursorIndexOfRating = CursorUtil.getColumnIndexOrThrow(_cursor, "rating");
          final int _cursorIndexOfTagsJson = CursorUtil.getColumnIndexOrThrow(_cursor, "tags_json");
          final int _cursorIndexOfType = CursorUtil.getColumnIndexOrThrow(_cursor, "type");
          final int _cursorIndexOfMood = CursorUtil.getColumnIndexOrThrow(_cursor, "mood");
          final int _cursorIndexOfPlansJson = CursorUtil.getColumnIndexOrThrow(_cursor, "plans_json");
          final int _cursorIndexOfBackgroundImage = CursorUtil.getColumnIndexOrThrow(_cursor, "background_image");
          final int _cursorIndexOfMetricsJson = CursorUtil.getColumnIndexOrThrow(_cursor, "metrics_json");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
          final int _cursorIndexOfTaskId = CursorUtil.getColumnIndexOrThrow(_cursor, "task_id");
          final int _cursorIndexOfTaskTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "task_title");
          final int _cursorIndexOfIsFromTaskCompletion = CursorUtil.getColumnIndexOrThrow(_cursor, "is_from_task_completion");
          final List<ReflectionEntity> _result = new ArrayList<ReflectionEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final ReflectionEntity _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpTitle;
            _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            final String _tmpContent;
            _tmpContent = _cursor.getString(_cursorIndexOfContent);
            final String _tmpRichContentJson;
            _tmpRichContentJson = _cursor.getString(_cursorIndexOfRichContentJson);
            final Instant _tmpDate;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfDate);
            }
            final Instant _tmp_1 = __converters.fromTimestampToInstant(_tmp);
            if (_tmp_1 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.Instant', but it was NULL.");
            } else {
              _tmpDate = _tmp_1;
            }
            final int _tmpRating;
            _tmpRating = _cursor.getInt(_cursorIndexOfRating);
            final String _tmpTagsJson;
            _tmpTagsJson = _cursor.getString(_cursorIndexOfTagsJson);
            final String _tmpType;
            _tmpType = _cursor.getString(_cursorIndexOfType);
            final String _tmpMood;
            _tmpMood = _cursor.getString(_cursorIndexOfMood);
            final String _tmpPlansJson;
            _tmpPlansJson = _cursor.getString(_cursorIndexOfPlansJson);
            final String _tmpBackgroundImage;
            if (_cursor.isNull(_cursorIndexOfBackgroundImage)) {
              _tmpBackgroundImage = null;
            } else {
              _tmpBackgroundImage = _cursor.getString(_cursorIndexOfBackgroundImage);
            }
            final String _tmpMetricsJson;
            _tmpMetricsJson = _cursor.getString(_cursorIndexOfMetricsJson);
            final Instant _tmpCreatedAt;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            final Instant _tmp_3 = __converters.fromTimestampToInstant(_tmp_2);
            if (_tmp_3 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.Instant', but it was NULL.");
            } else {
              _tmpCreatedAt = _tmp_3;
            }
            final Instant _tmpUpdatedAt;
            final Long _tmp_4;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            final Instant _tmp_5 = __converters.fromTimestampToInstant(_tmp_4);
            if (_tmp_5 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.Instant', but it was NULL.");
            } else {
              _tmpUpdatedAt = _tmp_5;
            }
            final String _tmpTaskId;
            if (_cursor.isNull(_cursorIndexOfTaskId)) {
              _tmpTaskId = null;
            } else {
              _tmpTaskId = _cursor.getString(_cursorIndexOfTaskId);
            }
            final String _tmpTaskTitle;
            if (_cursor.isNull(_cursorIndexOfTaskTitle)) {
              _tmpTaskTitle = null;
            } else {
              _tmpTaskTitle = _cursor.getString(_cursorIndexOfTaskTitle);
            }
            final boolean _tmpIsFromTaskCompletion;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfIsFromTaskCompletion);
            _tmpIsFromTaskCompletion = _tmp_6 != 0;
            _item = new ReflectionEntity(_tmpId,_tmpTitle,_tmpContent,_tmpRichContentJson,_tmpDate,_tmpRating,_tmpTagsJson,_tmpType,_tmpMood,_tmpPlansJson,_tmpBackgroundImage,_tmpMetricsJson,_tmpCreatedAt,_tmpUpdatedAt,_tmpTaskId,_tmpTaskTitle,_tmpIsFromTaskCompletion);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getReflectionById(final String reflectionId,
      final Continuation<? super ReflectionEntity> $completion) {
    final String _sql = "SELECT * FROM reflections WHERE id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, reflectionId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<ReflectionEntity>() {
      @Override
      @Nullable
      public ReflectionEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfContent = CursorUtil.getColumnIndexOrThrow(_cursor, "content");
          final int _cursorIndexOfRichContentJson = CursorUtil.getColumnIndexOrThrow(_cursor, "rich_content_json");
          final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
          final int _cursorIndexOfRating = CursorUtil.getColumnIndexOrThrow(_cursor, "rating");
          final int _cursorIndexOfTagsJson = CursorUtil.getColumnIndexOrThrow(_cursor, "tags_json");
          final int _cursorIndexOfType = CursorUtil.getColumnIndexOrThrow(_cursor, "type");
          final int _cursorIndexOfMood = CursorUtil.getColumnIndexOrThrow(_cursor, "mood");
          final int _cursorIndexOfPlansJson = CursorUtil.getColumnIndexOrThrow(_cursor, "plans_json");
          final int _cursorIndexOfBackgroundImage = CursorUtil.getColumnIndexOrThrow(_cursor, "background_image");
          final int _cursorIndexOfMetricsJson = CursorUtil.getColumnIndexOrThrow(_cursor, "metrics_json");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
          final int _cursorIndexOfTaskId = CursorUtil.getColumnIndexOrThrow(_cursor, "task_id");
          final int _cursorIndexOfTaskTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "task_title");
          final int _cursorIndexOfIsFromTaskCompletion = CursorUtil.getColumnIndexOrThrow(_cursor, "is_from_task_completion");
          final ReflectionEntity _result;
          if (_cursor.moveToFirst()) {
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpTitle;
            _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            final String _tmpContent;
            _tmpContent = _cursor.getString(_cursorIndexOfContent);
            final String _tmpRichContentJson;
            _tmpRichContentJson = _cursor.getString(_cursorIndexOfRichContentJson);
            final Instant _tmpDate;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfDate);
            }
            final Instant _tmp_1 = __converters.fromTimestampToInstant(_tmp);
            if (_tmp_1 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.Instant', but it was NULL.");
            } else {
              _tmpDate = _tmp_1;
            }
            final int _tmpRating;
            _tmpRating = _cursor.getInt(_cursorIndexOfRating);
            final String _tmpTagsJson;
            _tmpTagsJson = _cursor.getString(_cursorIndexOfTagsJson);
            final String _tmpType;
            _tmpType = _cursor.getString(_cursorIndexOfType);
            final String _tmpMood;
            _tmpMood = _cursor.getString(_cursorIndexOfMood);
            final String _tmpPlansJson;
            _tmpPlansJson = _cursor.getString(_cursorIndexOfPlansJson);
            final String _tmpBackgroundImage;
            if (_cursor.isNull(_cursorIndexOfBackgroundImage)) {
              _tmpBackgroundImage = null;
            } else {
              _tmpBackgroundImage = _cursor.getString(_cursorIndexOfBackgroundImage);
            }
            final String _tmpMetricsJson;
            _tmpMetricsJson = _cursor.getString(_cursorIndexOfMetricsJson);
            final Instant _tmpCreatedAt;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            final Instant _tmp_3 = __converters.fromTimestampToInstant(_tmp_2);
            if (_tmp_3 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.Instant', but it was NULL.");
            } else {
              _tmpCreatedAt = _tmp_3;
            }
            final Instant _tmpUpdatedAt;
            final Long _tmp_4;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            final Instant _tmp_5 = __converters.fromTimestampToInstant(_tmp_4);
            if (_tmp_5 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.Instant', but it was NULL.");
            } else {
              _tmpUpdatedAt = _tmp_5;
            }
            final String _tmpTaskId;
            if (_cursor.isNull(_cursorIndexOfTaskId)) {
              _tmpTaskId = null;
            } else {
              _tmpTaskId = _cursor.getString(_cursorIndexOfTaskId);
            }
            final String _tmpTaskTitle;
            if (_cursor.isNull(_cursorIndexOfTaskTitle)) {
              _tmpTaskTitle = null;
            } else {
              _tmpTaskTitle = _cursor.getString(_cursorIndexOfTaskTitle);
            }
            final boolean _tmpIsFromTaskCompletion;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfIsFromTaskCompletion);
            _tmpIsFromTaskCompletion = _tmp_6 != 0;
            _result = new ReflectionEntity(_tmpId,_tmpTitle,_tmpContent,_tmpRichContentJson,_tmpDate,_tmpRating,_tmpTagsJson,_tmpType,_tmpMood,_tmpPlansJson,_tmpBackgroundImage,_tmpMetricsJson,_tmpCreatedAt,_tmpUpdatedAt,_tmpTaskId,_tmpTaskTitle,_tmpIsFromTaskCompletion);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getReflectionsByDateRange(final long startDate, final long endDate,
      final Continuation<? super List<ReflectionEntity>> $completion) {
    final String _sql = "SELECT * FROM reflections WHERE date BETWEEN ? AND ? ORDER BY date DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, startDate);
    _argIndex = 2;
    _statement.bindLong(_argIndex, endDate);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<ReflectionEntity>>() {
      @Override
      @NonNull
      public List<ReflectionEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfContent = CursorUtil.getColumnIndexOrThrow(_cursor, "content");
          final int _cursorIndexOfRichContentJson = CursorUtil.getColumnIndexOrThrow(_cursor, "rich_content_json");
          final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
          final int _cursorIndexOfRating = CursorUtil.getColumnIndexOrThrow(_cursor, "rating");
          final int _cursorIndexOfTagsJson = CursorUtil.getColumnIndexOrThrow(_cursor, "tags_json");
          final int _cursorIndexOfType = CursorUtil.getColumnIndexOrThrow(_cursor, "type");
          final int _cursorIndexOfMood = CursorUtil.getColumnIndexOrThrow(_cursor, "mood");
          final int _cursorIndexOfPlansJson = CursorUtil.getColumnIndexOrThrow(_cursor, "plans_json");
          final int _cursorIndexOfBackgroundImage = CursorUtil.getColumnIndexOrThrow(_cursor, "background_image");
          final int _cursorIndexOfMetricsJson = CursorUtil.getColumnIndexOrThrow(_cursor, "metrics_json");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
          final int _cursorIndexOfTaskId = CursorUtil.getColumnIndexOrThrow(_cursor, "task_id");
          final int _cursorIndexOfTaskTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "task_title");
          final int _cursorIndexOfIsFromTaskCompletion = CursorUtil.getColumnIndexOrThrow(_cursor, "is_from_task_completion");
          final List<ReflectionEntity> _result = new ArrayList<ReflectionEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final ReflectionEntity _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpTitle;
            _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            final String _tmpContent;
            _tmpContent = _cursor.getString(_cursorIndexOfContent);
            final String _tmpRichContentJson;
            _tmpRichContentJson = _cursor.getString(_cursorIndexOfRichContentJson);
            final Instant _tmpDate;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfDate);
            }
            final Instant _tmp_1 = __converters.fromTimestampToInstant(_tmp);
            if (_tmp_1 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.Instant', but it was NULL.");
            } else {
              _tmpDate = _tmp_1;
            }
            final int _tmpRating;
            _tmpRating = _cursor.getInt(_cursorIndexOfRating);
            final String _tmpTagsJson;
            _tmpTagsJson = _cursor.getString(_cursorIndexOfTagsJson);
            final String _tmpType;
            _tmpType = _cursor.getString(_cursorIndexOfType);
            final String _tmpMood;
            _tmpMood = _cursor.getString(_cursorIndexOfMood);
            final String _tmpPlansJson;
            _tmpPlansJson = _cursor.getString(_cursorIndexOfPlansJson);
            final String _tmpBackgroundImage;
            if (_cursor.isNull(_cursorIndexOfBackgroundImage)) {
              _tmpBackgroundImage = null;
            } else {
              _tmpBackgroundImage = _cursor.getString(_cursorIndexOfBackgroundImage);
            }
            final String _tmpMetricsJson;
            _tmpMetricsJson = _cursor.getString(_cursorIndexOfMetricsJson);
            final Instant _tmpCreatedAt;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            final Instant _tmp_3 = __converters.fromTimestampToInstant(_tmp_2);
            if (_tmp_3 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.Instant', but it was NULL.");
            } else {
              _tmpCreatedAt = _tmp_3;
            }
            final Instant _tmpUpdatedAt;
            final Long _tmp_4;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            final Instant _tmp_5 = __converters.fromTimestampToInstant(_tmp_4);
            if (_tmp_5 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.Instant', but it was NULL.");
            } else {
              _tmpUpdatedAt = _tmp_5;
            }
            final String _tmpTaskId;
            if (_cursor.isNull(_cursorIndexOfTaskId)) {
              _tmpTaskId = null;
            } else {
              _tmpTaskId = _cursor.getString(_cursorIndexOfTaskId);
            }
            final String _tmpTaskTitle;
            if (_cursor.isNull(_cursorIndexOfTaskTitle)) {
              _tmpTaskTitle = null;
            } else {
              _tmpTaskTitle = _cursor.getString(_cursorIndexOfTaskTitle);
            }
            final boolean _tmpIsFromTaskCompletion;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfIsFromTaskCompletion);
            _tmpIsFromTaskCompletion = _tmp_6 != 0;
            _item = new ReflectionEntity(_tmpId,_tmpTitle,_tmpContent,_tmpRichContentJson,_tmpDate,_tmpRating,_tmpTagsJson,_tmpType,_tmpMood,_tmpPlansJson,_tmpBackgroundImage,_tmpMetricsJson,_tmpCreatedAt,_tmpUpdatedAt,_tmpTaskId,_tmpTaskTitle,_tmpIsFromTaskCompletion);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getReflectionsByType(final String type,
      final Continuation<? super List<ReflectionEntity>> $completion) {
    final String _sql = "SELECT * FROM reflections WHERE type = ? ORDER BY date DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, type);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<ReflectionEntity>>() {
      @Override
      @NonNull
      public List<ReflectionEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfContent = CursorUtil.getColumnIndexOrThrow(_cursor, "content");
          final int _cursorIndexOfRichContentJson = CursorUtil.getColumnIndexOrThrow(_cursor, "rich_content_json");
          final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
          final int _cursorIndexOfRating = CursorUtil.getColumnIndexOrThrow(_cursor, "rating");
          final int _cursorIndexOfTagsJson = CursorUtil.getColumnIndexOrThrow(_cursor, "tags_json");
          final int _cursorIndexOfType = CursorUtil.getColumnIndexOrThrow(_cursor, "type");
          final int _cursorIndexOfMood = CursorUtil.getColumnIndexOrThrow(_cursor, "mood");
          final int _cursorIndexOfPlansJson = CursorUtil.getColumnIndexOrThrow(_cursor, "plans_json");
          final int _cursorIndexOfBackgroundImage = CursorUtil.getColumnIndexOrThrow(_cursor, "background_image");
          final int _cursorIndexOfMetricsJson = CursorUtil.getColumnIndexOrThrow(_cursor, "metrics_json");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
          final int _cursorIndexOfTaskId = CursorUtil.getColumnIndexOrThrow(_cursor, "task_id");
          final int _cursorIndexOfTaskTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "task_title");
          final int _cursorIndexOfIsFromTaskCompletion = CursorUtil.getColumnIndexOrThrow(_cursor, "is_from_task_completion");
          final List<ReflectionEntity> _result = new ArrayList<ReflectionEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final ReflectionEntity _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpTitle;
            _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            final String _tmpContent;
            _tmpContent = _cursor.getString(_cursorIndexOfContent);
            final String _tmpRichContentJson;
            _tmpRichContentJson = _cursor.getString(_cursorIndexOfRichContentJson);
            final Instant _tmpDate;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfDate);
            }
            final Instant _tmp_1 = __converters.fromTimestampToInstant(_tmp);
            if (_tmp_1 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.Instant', but it was NULL.");
            } else {
              _tmpDate = _tmp_1;
            }
            final int _tmpRating;
            _tmpRating = _cursor.getInt(_cursorIndexOfRating);
            final String _tmpTagsJson;
            _tmpTagsJson = _cursor.getString(_cursorIndexOfTagsJson);
            final String _tmpType;
            _tmpType = _cursor.getString(_cursorIndexOfType);
            final String _tmpMood;
            _tmpMood = _cursor.getString(_cursorIndexOfMood);
            final String _tmpPlansJson;
            _tmpPlansJson = _cursor.getString(_cursorIndexOfPlansJson);
            final String _tmpBackgroundImage;
            if (_cursor.isNull(_cursorIndexOfBackgroundImage)) {
              _tmpBackgroundImage = null;
            } else {
              _tmpBackgroundImage = _cursor.getString(_cursorIndexOfBackgroundImage);
            }
            final String _tmpMetricsJson;
            _tmpMetricsJson = _cursor.getString(_cursorIndexOfMetricsJson);
            final Instant _tmpCreatedAt;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            final Instant _tmp_3 = __converters.fromTimestampToInstant(_tmp_2);
            if (_tmp_3 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.Instant', but it was NULL.");
            } else {
              _tmpCreatedAt = _tmp_3;
            }
            final Instant _tmpUpdatedAt;
            final Long _tmp_4;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            final Instant _tmp_5 = __converters.fromTimestampToInstant(_tmp_4);
            if (_tmp_5 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.Instant', but it was NULL.");
            } else {
              _tmpUpdatedAt = _tmp_5;
            }
            final String _tmpTaskId;
            if (_cursor.isNull(_cursorIndexOfTaskId)) {
              _tmpTaskId = null;
            } else {
              _tmpTaskId = _cursor.getString(_cursorIndexOfTaskId);
            }
            final String _tmpTaskTitle;
            if (_cursor.isNull(_cursorIndexOfTaskTitle)) {
              _tmpTaskTitle = null;
            } else {
              _tmpTaskTitle = _cursor.getString(_cursorIndexOfTaskTitle);
            }
            final boolean _tmpIsFromTaskCompletion;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfIsFromTaskCompletion);
            _tmpIsFromTaskCompletion = _tmp_6 != 0;
            _item = new ReflectionEntity(_tmpId,_tmpTitle,_tmpContent,_tmpRichContentJson,_tmpDate,_tmpRating,_tmpTagsJson,_tmpType,_tmpMood,_tmpPlansJson,_tmpBackgroundImage,_tmpMetricsJson,_tmpCreatedAt,_tmpUpdatedAt,_tmpTaskId,_tmpTaskTitle,_tmpIsFromTaskCompletion);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getReflectionsByMood(final String mood,
      final Continuation<? super List<ReflectionEntity>> $completion) {
    final String _sql = "SELECT * FROM reflections WHERE mood = ? ORDER BY date DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, mood);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<ReflectionEntity>>() {
      @Override
      @NonNull
      public List<ReflectionEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfContent = CursorUtil.getColumnIndexOrThrow(_cursor, "content");
          final int _cursorIndexOfRichContentJson = CursorUtil.getColumnIndexOrThrow(_cursor, "rich_content_json");
          final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
          final int _cursorIndexOfRating = CursorUtil.getColumnIndexOrThrow(_cursor, "rating");
          final int _cursorIndexOfTagsJson = CursorUtil.getColumnIndexOrThrow(_cursor, "tags_json");
          final int _cursorIndexOfType = CursorUtil.getColumnIndexOrThrow(_cursor, "type");
          final int _cursorIndexOfMood = CursorUtil.getColumnIndexOrThrow(_cursor, "mood");
          final int _cursorIndexOfPlansJson = CursorUtil.getColumnIndexOrThrow(_cursor, "plans_json");
          final int _cursorIndexOfBackgroundImage = CursorUtil.getColumnIndexOrThrow(_cursor, "background_image");
          final int _cursorIndexOfMetricsJson = CursorUtil.getColumnIndexOrThrow(_cursor, "metrics_json");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
          final int _cursorIndexOfTaskId = CursorUtil.getColumnIndexOrThrow(_cursor, "task_id");
          final int _cursorIndexOfTaskTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "task_title");
          final int _cursorIndexOfIsFromTaskCompletion = CursorUtil.getColumnIndexOrThrow(_cursor, "is_from_task_completion");
          final List<ReflectionEntity> _result = new ArrayList<ReflectionEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final ReflectionEntity _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpTitle;
            _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            final String _tmpContent;
            _tmpContent = _cursor.getString(_cursorIndexOfContent);
            final String _tmpRichContentJson;
            _tmpRichContentJson = _cursor.getString(_cursorIndexOfRichContentJson);
            final Instant _tmpDate;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfDate);
            }
            final Instant _tmp_1 = __converters.fromTimestampToInstant(_tmp);
            if (_tmp_1 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.Instant', but it was NULL.");
            } else {
              _tmpDate = _tmp_1;
            }
            final int _tmpRating;
            _tmpRating = _cursor.getInt(_cursorIndexOfRating);
            final String _tmpTagsJson;
            _tmpTagsJson = _cursor.getString(_cursorIndexOfTagsJson);
            final String _tmpType;
            _tmpType = _cursor.getString(_cursorIndexOfType);
            final String _tmpMood;
            _tmpMood = _cursor.getString(_cursorIndexOfMood);
            final String _tmpPlansJson;
            _tmpPlansJson = _cursor.getString(_cursorIndexOfPlansJson);
            final String _tmpBackgroundImage;
            if (_cursor.isNull(_cursorIndexOfBackgroundImage)) {
              _tmpBackgroundImage = null;
            } else {
              _tmpBackgroundImage = _cursor.getString(_cursorIndexOfBackgroundImage);
            }
            final String _tmpMetricsJson;
            _tmpMetricsJson = _cursor.getString(_cursorIndexOfMetricsJson);
            final Instant _tmpCreatedAt;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            final Instant _tmp_3 = __converters.fromTimestampToInstant(_tmp_2);
            if (_tmp_3 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.Instant', but it was NULL.");
            } else {
              _tmpCreatedAt = _tmp_3;
            }
            final Instant _tmpUpdatedAt;
            final Long _tmp_4;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            final Instant _tmp_5 = __converters.fromTimestampToInstant(_tmp_4);
            if (_tmp_5 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.Instant', but it was NULL.");
            } else {
              _tmpUpdatedAt = _tmp_5;
            }
            final String _tmpTaskId;
            if (_cursor.isNull(_cursorIndexOfTaskId)) {
              _tmpTaskId = null;
            } else {
              _tmpTaskId = _cursor.getString(_cursorIndexOfTaskId);
            }
            final String _tmpTaskTitle;
            if (_cursor.isNull(_cursorIndexOfTaskTitle)) {
              _tmpTaskTitle = null;
            } else {
              _tmpTaskTitle = _cursor.getString(_cursorIndexOfTaskTitle);
            }
            final boolean _tmpIsFromTaskCompletion;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfIsFromTaskCompletion);
            _tmpIsFromTaskCompletion = _tmp_6 != 0;
            _item = new ReflectionEntity(_tmpId,_tmpTitle,_tmpContent,_tmpRichContentJson,_tmpDate,_tmpRating,_tmpTagsJson,_tmpType,_tmpMood,_tmpPlansJson,_tmpBackgroundImage,_tmpMetricsJson,_tmpCreatedAt,_tmpUpdatedAt,_tmpTaskId,_tmpTaskTitle,_tmpIsFromTaskCompletion);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object searchReflections(final String query,
      final Continuation<? super List<ReflectionEntity>> $completion) {
    final String _sql = "SELECT * FROM reflections WHERE title LIKE '%' || ? || '%' OR content LIKE '%' || ? || '%' ORDER BY date DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindString(_argIndex, query);
    _argIndex = 2;
    _statement.bindString(_argIndex, query);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<ReflectionEntity>>() {
      @Override
      @NonNull
      public List<ReflectionEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfContent = CursorUtil.getColumnIndexOrThrow(_cursor, "content");
          final int _cursorIndexOfRichContentJson = CursorUtil.getColumnIndexOrThrow(_cursor, "rich_content_json");
          final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
          final int _cursorIndexOfRating = CursorUtil.getColumnIndexOrThrow(_cursor, "rating");
          final int _cursorIndexOfTagsJson = CursorUtil.getColumnIndexOrThrow(_cursor, "tags_json");
          final int _cursorIndexOfType = CursorUtil.getColumnIndexOrThrow(_cursor, "type");
          final int _cursorIndexOfMood = CursorUtil.getColumnIndexOrThrow(_cursor, "mood");
          final int _cursorIndexOfPlansJson = CursorUtil.getColumnIndexOrThrow(_cursor, "plans_json");
          final int _cursorIndexOfBackgroundImage = CursorUtil.getColumnIndexOrThrow(_cursor, "background_image");
          final int _cursorIndexOfMetricsJson = CursorUtil.getColumnIndexOrThrow(_cursor, "metrics_json");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
          final int _cursorIndexOfTaskId = CursorUtil.getColumnIndexOrThrow(_cursor, "task_id");
          final int _cursorIndexOfTaskTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "task_title");
          final int _cursorIndexOfIsFromTaskCompletion = CursorUtil.getColumnIndexOrThrow(_cursor, "is_from_task_completion");
          final List<ReflectionEntity> _result = new ArrayList<ReflectionEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final ReflectionEntity _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpTitle;
            _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            final String _tmpContent;
            _tmpContent = _cursor.getString(_cursorIndexOfContent);
            final String _tmpRichContentJson;
            _tmpRichContentJson = _cursor.getString(_cursorIndexOfRichContentJson);
            final Instant _tmpDate;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfDate);
            }
            final Instant _tmp_1 = __converters.fromTimestampToInstant(_tmp);
            if (_tmp_1 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.Instant', but it was NULL.");
            } else {
              _tmpDate = _tmp_1;
            }
            final int _tmpRating;
            _tmpRating = _cursor.getInt(_cursorIndexOfRating);
            final String _tmpTagsJson;
            _tmpTagsJson = _cursor.getString(_cursorIndexOfTagsJson);
            final String _tmpType;
            _tmpType = _cursor.getString(_cursorIndexOfType);
            final String _tmpMood;
            _tmpMood = _cursor.getString(_cursorIndexOfMood);
            final String _tmpPlansJson;
            _tmpPlansJson = _cursor.getString(_cursorIndexOfPlansJson);
            final String _tmpBackgroundImage;
            if (_cursor.isNull(_cursorIndexOfBackgroundImage)) {
              _tmpBackgroundImage = null;
            } else {
              _tmpBackgroundImage = _cursor.getString(_cursorIndexOfBackgroundImage);
            }
            final String _tmpMetricsJson;
            _tmpMetricsJson = _cursor.getString(_cursorIndexOfMetricsJson);
            final Instant _tmpCreatedAt;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            final Instant _tmp_3 = __converters.fromTimestampToInstant(_tmp_2);
            if (_tmp_3 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.Instant', but it was NULL.");
            } else {
              _tmpCreatedAt = _tmp_3;
            }
            final Instant _tmpUpdatedAt;
            final Long _tmp_4;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            final Instant _tmp_5 = __converters.fromTimestampToInstant(_tmp_4);
            if (_tmp_5 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.Instant', but it was NULL.");
            } else {
              _tmpUpdatedAt = _tmp_5;
            }
            final String _tmpTaskId;
            if (_cursor.isNull(_cursorIndexOfTaskId)) {
              _tmpTaskId = null;
            } else {
              _tmpTaskId = _cursor.getString(_cursorIndexOfTaskId);
            }
            final String _tmpTaskTitle;
            if (_cursor.isNull(_cursorIndexOfTaskTitle)) {
              _tmpTaskTitle = null;
            } else {
              _tmpTaskTitle = _cursor.getString(_cursorIndexOfTaskTitle);
            }
            final boolean _tmpIsFromTaskCompletion;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfIsFromTaskCompletion);
            _tmpIsFromTaskCompletion = _tmp_6 != 0;
            _item = new ReflectionEntity(_tmpId,_tmpTitle,_tmpContent,_tmpRichContentJson,_tmpDate,_tmpRating,_tmpTagsJson,_tmpType,_tmpMood,_tmpPlansJson,_tmpBackgroundImage,_tmpMetricsJson,_tmpCreatedAt,_tmpUpdatedAt,_tmpTaskId,_tmpTaskTitle,_tmpIsFromTaskCompletion);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getRecentReflections(final int limit,
      final Continuation<? super List<ReflectionEntity>> $completion) {
    final String _sql = "SELECT * FROM reflections ORDER BY date DESC LIMIT ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, limit);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<ReflectionEntity>>() {
      @Override
      @NonNull
      public List<ReflectionEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfContent = CursorUtil.getColumnIndexOrThrow(_cursor, "content");
          final int _cursorIndexOfRichContentJson = CursorUtil.getColumnIndexOrThrow(_cursor, "rich_content_json");
          final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
          final int _cursorIndexOfRating = CursorUtil.getColumnIndexOrThrow(_cursor, "rating");
          final int _cursorIndexOfTagsJson = CursorUtil.getColumnIndexOrThrow(_cursor, "tags_json");
          final int _cursorIndexOfType = CursorUtil.getColumnIndexOrThrow(_cursor, "type");
          final int _cursorIndexOfMood = CursorUtil.getColumnIndexOrThrow(_cursor, "mood");
          final int _cursorIndexOfPlansJson = CursorUtil.getColumnIndexOrThrow(_cursor, "plans_json");
          final int _cursorIndexOfBackgroundImage = CursorUtil.getColumnIndexOrThrow(_cursor, "background_image");
          final int _cursorIndexOfMetricsJson = CursorUtil.getColumnIndexOrThrow(_cursor, "metrics_json");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
          final int _cursorIndexOfTaskId = CursorUtil.getColumnIndexOrThrow(_cursor, "task_id");
          final int _cursorIndexOfTaskTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "task_title");
          final int _cursorIndexOfIsFromTaskCompletion = CursorUtil.getColumnIndexOrThrow(_cursor, "is_from_task_completion");
          final List<ReflectionEntity> _result = new ArrayList<ReflectionEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final ReflectionEntity _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpTitle;
            _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            final String _tmpContent;
            _tmpContent = _cursor.getString(_cursorIndexOfContent);
            final String _tmpRichContentJson;
            _tmpRichContentJson = _cursor.getString(_cursorIndexOfRichContentJson);
            final Instant _tmpDate;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfDate);
            }
            final Instant _tmp_1 = __converters.fromTimestampToInstant(_tmp);
            if (_tmp_1 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.Instant', but it was NULL.");
            } else {
              _tmpDate = _tmp_1;
            }
            final int _tmpRating;
            _tmpRating = _cursor.getInt(_cursorIndexOfRating);
            final String _tmpTagsJson;
            _tmpTagsJson = _cursor.getString(_cursorIndexOfTagsJson);
            final String _tmpType;
            _tmpType = _cursor.getString(_cursorIndexOfType);
            final String _tmpMood;
            _tmpMood = _cursor.getString(_cursorIndexOfMood);
            final String _tmpPlansJson;
            _tmpPlansJson = _cursor.getString(_cursorIndexOfPlansJson);
            final String _tmpBackgroundImage;
            if (_cursor.isNull(_cursorIndexOfBackgroundImage)) {
              _tmpBackgroundImage = null;
            } else {
              _tmpBackgroundImage = _cursor.getString(_cursorIndexOfBackgroundImage);
            }
            final String _tmpMetricsJson;
            _tmpMetricsJson = _cursor.getString(_cursorIndexOfMetricsJson);
            final Instant _tmpCreatedAt;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            final Instant _tmp_3 = __converters.fromTimestampToInstant(_tmp_2);
            if (_tmp_3 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.Instant', but it was NULL.");
            } else {
              _tmpCreatedAt = _tmp_3;
            }
            final Instant _tmpUpdatedAt;
            final Long _tmp_4;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            final Instant _tmp_5 = __converters.fromTimestampToInstant(_tmp_4);
            if (_tmp_5 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.Instant', but it was NULL.");
            } else {
              _tmpUpdatedAt = _tmp_5;
            }
            final String _tmpTaskId;
            if (_cursor.isNull(_cursorIndexOfTaskId)) {
              _tmpTaskId = null;
            } else {
              _tmpTaskId = _cursor.getString(_cursorIndexOfTaskId);
            }
            final String _tmpTaskTitle;
            if (_cursor.isNull(_cursorIndexOfTaskTitle)) {
              _tmpTaskTitle = null;
            } else {
              _tmpTaskTitle = _cursor.getString(_cursorIndexOfTaskTitle);
            }
            final boolean _tmpIsFromTaskCompletion;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfIsFromTaskCompletion);
            _tmpIsFromTaskCompletion = _tmp_6 != 0;
            _item = new ReflectionEntity(_tmpId,_tmpTitle,_tmpContent,_tmpRichContentJson,_tmpDate,_tmpRating,_tmpTagsJson,_tmpType,_tmpMood,_tmpPlansJson,_tmpBackgroundImage,_tmpMetricsJson,_tmpCreatedAt,_tmpUpdatedAt,_tmpTaskId,_tmpTaskTitle,_tmpIsFromTaskCompletion);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getTaskCompletionReflections(
      final Continuation<? super List<ReflectionEntity>> $completion) {
    final String _sql = "SELECT * FROM reflections WHERE is_from_task_completion = 1 ORDER BY date DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<ReflectionEntity>>() {
      @Override
      @NonNull
      public List<ReflectionEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfContent = CursorUtil.getColumnIndexOrThrow(_cursor, "content");
          final int _cursorIndexOfRichContentJson = CursorUtil.getColumnIndexOrThrow(_cursor, "rich_content_json");
          final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
          final int _cursorIndexOfRating = CursorUtil.getColumnIndexOrThrow(_cursor, "rating");
          final int _cursorIndexOfTagsJson = CursorUtil.getColumnIndexOrThrow(_cursor, "tags_json");
          final int _cursorIndexOfType = CursorUtil.getColumnIndexOrThrow(_cursor, "type");
          final int _cursorIndexOfMood = CursorUtil.getColumnIndexOrThrow(_cursor, "mood");
          final int _cursorIndexOfPlansJson = CursorUtil.getColumnIndexOrThrow(_cursor, "plans_json");
          final int _cursorIndexOfBackgroundImage = CursorUtil.getColumnIndexOrThrow(_cursor, "background_image");
          final int _cursorIndexOfMetricsJson = CursorUtil.getColumnIndexOrThrow(_cursor, "metrics_json");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
          final int _cursorIndexOfTaskId = CursorUtil.getColumnIndexOrThrow(_cursor, "task_id");
          final int _cursorIndexOfTaskTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "task_title");
          final int _cursorIndexOfIsFromTaskCompletion = CursorUtil.getColumnIndexOrThrow(_cursor, "is_from_task_completion");
          final List<ReflectionEntity> _result = new ArrayList<ReflectionEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final ReflectionEntity _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpTitle;
            _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            final String _tmpContent;
            _tmpContent = _cursor.getString(_cursorIndexOfContent);
            final String _tmpRichContentJson;
            _tmpRichContentJson = _cursor.getString(_cursorIndexOfRichContentJson);
            final Instant _tmpDate;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfDate);
            }
            final Instant _tmp_1 = __converters.fromTimestampToInstant(_tmp);
            if (_tmp_1 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.Instant', but it was NULL.");
            } else {
              _tmpDate = _tmp_1;
            }
            final int _tmpRating;
            _tmpRating = _cursor.getInt(_cursorIndexOfRating);
            final String _tmpTagsJson;
            _tmpTagsJson = _cursor.getString(_cursorIndexOfTagsJson);
            final String _tmpType;
            _tmpType = _cursor.getString(_cursorIndexOfType);
            final String _tmpMood;
            _tmpMood = _cursor.getString(_cursorIndexOfMood);
            final String _tmpPlansJson;
            _tmpPlansJson = _cursor.getString(_cursorIndexOfPlansJson);
            final String _tmpBackgroundImage;
            if (_cursor.isNull(_cursorIndexOfBackgroundImage)) {
              _tmpBackgroundImage = null;
            } else {
              _tmpBackgroundImage = _cursor.getString(_cursorIndexOfBackgroundImage);
            }
            final String _tmpMetricsJson;
            _tmpMetricsJson = _cursor.getString(_cursorIndexOfMetricsJson);
            final Instant _tmpCreatedAt;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            final Instant _tmp_3 = __converters.fromTimestampToInstant(_tmp_2);
            if (_tmp_3 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.Instant', but it was NULL.");
            } else {
              _tmpCreatedAt = _tmp_3;
            }
            final Instant _tmpUpdatedAt;
            final Long _tmp_4;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            final Instant _tmp_5 = __converters.fromTimestampToInstant(_tmp_4);
            if (_tmp_5 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.Instant', but it was NULL.");
            } else {
              _tmpUpdatedAt = _tmp_5;
            }
            final String _tmpTaskId;
            if (_cursor.isNull(_cursorIndexOfTaskId)) {
              _tmpTaskId = null;
            } else {
              _tmpTaskId = _cursor.getString(_cursorIndexOfTaskId);
            }
            final String _tmpTaskTitle;
            if (_cursor.isNull(_cursorIndexOfTaskTitle)) {
              _tmpTaskTitle = null;
            } else {
              _tmpTaskTitle = _cursor.getString(_cursorIndexOfTaskTitle);
            }
            final boolean _tmpIsFromTaskCompletion;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfIsFromTaskCompletion);
            _tmpIsFromTaskCompletion = _tmp_6 != 0;
            _item = new ReflectionEntity(_tmpId,_tmpTitle,_tmpContent,_tmpRichContentJson,_tmpDate,_tmpRating,_tmpTagsJson,_tmpType,_tmpMood,_tmpPlansJson,_tmpBackgroundImage,_tmpMetricsJson,_tmpCreatedAt,_tmpUpdatedAt,_tmpTaskId,_tmpTaskTitle,_tmpIsFromTaskCompletion);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getReflectionsByTaskId(final String taskId,
      final Continuation<? super List<ReflectionEntity>> $completion) {
    final String _sql = "SELECT * FROM reflections WHERE task_id = ? ORDER BY date DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, taskId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<ReflectionEntity>>() {
      @Override
      @NonNull
      public List<ReflectionEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfContent = CursorUtil.getColumnIndexOrThrow(_cursor, "content");
          final int _cursorIndexOfRichContentJson = CursorUtil.getColumnIndexOrThrow(_cursor, "rich_content_json");
          final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
          final int _cursorIndexOfRating = CursorUtil.getColumnIndexOrThrow(_cursor, "rating");
          final int _cursorIndexOfTagsJson = CursorUtil.getColumnIndexOrThrow(_cursor, "tags_json");
          final int _cursorIndexOfType = CursorUtil.getColumnIndexOrThrow(_cursor, "type");
          final int _cursorIndexOfMood = CursorUtil.getColumnIndexOrThrow(_cursor, "mood");
          final int _cursorIndexOfPlansJson = CursorUtil.getColumnIndexOrThrow(_cursor, "plans_json");
          final int _cursorIndexOfBackgroundImage = CursorUtil.getColumnIndexOrThrow(_cursor, "background_image");
          final int _cursorIndexOfMetricsJson = CursorUtil.getColumnIndexOrThrow(_cursor, "metrics_json");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
          final int _cursorIndexOfTaskId = CursorUtil.getColumnIndexOrThrow(_cursor, "task_id");
          final int _cursorIndexOfTaskTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "task_title");
          final int _cursorIndexOfIsFromTaskCompletion = CursorUtil.getColumnIndexOrThrow(_cursor, "is_from_task_completion");
          final List<ReflectionEntity> _result = new ArrayList<ReflectionEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final ReflectionEntity _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpTitle;
            _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            final String _tmpContent;
            _tmpContent = _cursor.getString(_cursorIndexOfContent);
            final String _tmpRichContentJson;
            _tmpRichContentJson = _cursor.getString(_cursorIndexOfRichContentJson);
            final Instant _tmpDate;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfDate);
            }
            final Instant _tmp_1 = __converters.fromTimestampToInstant(_tmp);
            if (_tmp_1 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.Instant', but it was NULL.");
            } else {
              _tmpDate = _tmp_1;
            }
            final int _tmpRating;
            _tmpRating = _cursor.getInt(_cursorIndexOfRating);
            final String _tmpTagsJson;
            _tmpTagsJson = _cursor.getString(_cursorIndexOfTagsJson);
            final String _tmpType;
            _tmpType = _cursor.getString(_cursorIndexOfType);
            final String _tmpMood;
            _tmpMood = _cursor.getString(_cursorIndexOfMood);
            final String _tmpPlansJson;
            _tmpPlansJson = _cursor.getString(_cursorIndexOfPlansJson);
            final String _tmpBackgroundImage;
            if (_cursor.isNull(_cursorIndexOfBackgroundImage)) {
              _tmpBackgroundImage = null;
            } else {
              _tmpBackgroundImage = _cursor.getString(_cursorIndexOfBackgroundImage);
            }
            final String _tmpMetricsJson;
            _tmpMetricsJson = _cursor.getString(_cursorIndexOfMetricsJson);
            final Instant _tmpCreatedAt;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            final Instant _tmp_3 = __converters.fromTimestampToInstant(_tmp_2);
            if (_tmp_3 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.Instant', but it was NULL.");
            } else {
              _tmpCreatedAt = _tmp_3;
            }
            final Instant _tmpUpdatedAt;
            final Long _tmp_4;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            final Instant _tmp_5 = __converters.fromTimestampToInstant(_tmp_4);
            if (_tmp_5 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.Instant', but it was NULL.");
            } else {
              _tmpUpdatedAt = _tmp_5;
            }
            final String _tmpTaskId;
            if (_cursor.isNull(_cursorIndexOfTaskId)) {
              _tmpTaskId = null;
            } else {
              _tmpTaskId = _cursor.getString(_cursorIndexOfTaskId);
            }
            final String _tmpTaskTitle;
            if (_cursor.isNull(_cursorIndexOfTaskTitle)) {
              _tmpTaskTitle = null;
            } else {
              _tmpTaskTitle = _cursor.getString(_cursorIndexOfTaskTitle);
            }
            final boolean _tmpIsFromTaskCompletion;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfIsFromTaskCompletion);
            _tmpIsFromTaskCompletion = _tmp_6 != 0;
            _item = new ReflectionEntity(_tmpId,_tmpTitle,_tmpContent,_tmpRichContentJson,_tmpDate,_tmpRating,_tmpTagsJson,_tmpType,_tmpMood,_tmpPlansJson,_tmpBackgroundImage,_tmpMetricsJson,_tmpCreatedAt,_tmpUpdatedAt,_tmpTaskId,_tmpTaskTitle,_tmpIsFromTaskCompletion);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getReflectionCount(final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT COUNT(*) FROM reflections";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final int _tmp;
            _tmp = _cursor.getInt(0);
            _result = _tmp;
          } else {
            _result = 0;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getReflectionsByTag(final String tag,
      final Continuation<? super List<ReflectionEntity>> $completion) {
    final String _sql = "SELECT * FROM reflections WHERE tags_json LIKE '%' || ? || '%' ORDER BY date DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, tag);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<ReflectionEntity>>() {
      @Override
      @NonNull
      public List<ReflectionEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfContent = CursorUtil.getColumnIndexOrThrow(_cursor, "content");
          final int _cursorIndexOfRichContentJson = CursorUtil.getColumnIndexOrThrow(_cursor, "rich_content_json");
          final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
          final int _cursorIndexOfRating = CursorUtil.getColumnIndexOrThrow(_cursor, "rating");
          final int _cursorIndexOfTagsJson = CursorUtil.getColumnIndexOrThrow(_cursor, "tags_json");
          final int _cursorIndexOfType = CursorUtil.getColumnIndexOrThrow(_cursor, "type");
          final int _cursorIndexOfMood = CursorUtil.getColumnIndexOrThrow(_cursor, "mood");
          final int _cursorIndexOfPlansJson = CursorUtil.getColumnIndexOrThrow(_cursor, "plans_json");
          final int _cursorIndexOfBackgroundImage = CursorUtil.getColumnIndexOrThrow(_cursor, "background_image");
          final int _cursorIndexOfMetricsJson = CursorUtil.getColumnIndexOrThrow(_cursor, "metrics_json");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
          final int _cursorIndexOfTaskId = CursorUtil.getColumnIndexOrThrow(_cursor, "task_id");
          final int _cursorIndexOfTaskTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "task_title");
          final int _cursorIndexOfIsFromTaskCompletion = CursorUtil.getColumnIndexOrThrow(_cursor, "is_from_task_completion");
          final List<ReflectionEntity> _result = new ArrayList<ReflectionEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final ReflectionEntity _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpTitle;
            _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            final String _tmpContent;
            _tmpContent = _cursor.getString(_cursorIndexOfContent);
            final String _tmpRichContentJson;
            _tmpRichContentJson = _cursor.getString(_cursorIndexOfRichContentJson);
            final Instant _tmpDate;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfDate);
            }
            final Instant _tmp_1 = __converters.fromTimestampToInstant(_tmp);
            if (_tmp_1 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.Instant', but it was NULL.");
            } else {
              _tmpDate = _tmp_1;
            }
            final int _tmpRating;
            _tmpRating = _cursor.getInt(_cursorIndexOfRating);
            final String _tmpTagsJson;
            _tmpTagsJson = _cursor.getString(_cursorIndexOfTagsJson);
            final String _tmpType;
            _tmpType = _cursor.getString(_cursorIndexOfType);
            final String _tmpMood;
            _tmpMood = _cursor.getString(_cursorIndexOfMood);
            final String _tmpPlansJson;
            _tmpPlansJson = _cursor.getString(_cursorIndexOfPlansJson);
            final String _tmpBackgroundImage;
            if (_cursor.isNull(_cursorIndexOfBackgroundImage)) {
              _tmpBackgroundImage = null;
            } else {
              _tmpBackgroundImage = _cursor.getString(_cursorIndexOfBackgroundImage);
            }
            final String _tmpMetricsJson;
            _tmpMetricsJson = _cursor.getString(_cursorIndexOfMetricsJson);
            final Instant _tmpCreatedAt;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            final Instant _tmp_3 = __converters.fromTimestampToInstant(_tmp_2);
            if (_tmp_3 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.Instant', but it was NULL.");
            } else {
              _tmpCreatedAt = _tmp_3;
            }
            final Instant _tmpUpdatedAt;
            final Long _tmp_4;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            final Instant _tmp_5 = __converters.fromTimestampToInstant(_tmp_4);
            if (_tmp_5 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.Instant', but it was NULL.");
            } else {
              _tmpUpdatedAt = _tmp_5;
            }
            final String _tmpTaskId;
            if (_cursor.isNull(_cursorIndexOfTaskId)) {
              _tmpTaskId = null;
            } else {
              _tmpTaskId = _cursor.getString(_cursorIndexOfTaskId);
            }
            final String _tmpTaskTitle;
            if (_cursor.isNull(_cursorIndexOfTaskTitle)) {
              _tmpTaskTitle = null;
            } else {
              _tmpTaskTitle = _cursor.getString(_cursorIndexOfTaskTitle);
            }
            final boolean _tmpIsFromTaskCompletion;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfIsFromTaskCompletion);
            _tmpIsFromTaskCompletion = _tmp_6 != 0;
            _item = new ReflectionEntity(_tmpId,_tmpTitle,_tmpContent,_tmpRichContentJson,_tmpDate,_tmpRating,_tmpTagsJson,_tmpType,_tmpMood,_tmpPlansJson,_tmpBackgroundImage,_tmpMetricsJson,_tmpCreatedAt,_tmpUpdatedAt,_tmpTaskId,_tmpTaskTitle,_tmpIsFromTaskCompletion);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getAllUsedTags(final Continuation<? super List<String>> $completion) {
    final String _sql = "SELECT DISTINCT tags_json FROM reflections WHERE tags_json != '[]'";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<String>>() {
      @Override
      @NonNull
      public List<String> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final List<String> _result = new ArrayList<String>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final String _item;
            _item = _cursor.getString(0);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
