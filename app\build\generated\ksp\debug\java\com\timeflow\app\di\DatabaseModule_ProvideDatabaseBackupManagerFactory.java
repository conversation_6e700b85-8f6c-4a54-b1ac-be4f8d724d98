package com.timeflow.app.di;

import android.content.Context;
import com.timeflow.app.utils.DatabaseBackupManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DatabaseModule_ProvideDatabaseBackupManagerFactory implements Factory<DatabaseBackupManager> {
  private final Provider<Context> contextProvider;

  public DatabaseModule_ProvideDatabaseBackupManagerFactory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public DatabaseBackupManager get() {
    return provideDatabaseBackupManager(contextProvider.get());
  }

  public static DatabaseModule_ProvideDatabaseBackupManagerFactory create(
      Provider<Context> contextProvider) {
    return new DatabaseModule_ProvideDatabaseBackupManagerFactory(contextProvider);
  }

  public static DatabaseBackupManager provideDatabaseBackupManager(Context context) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.provideDatabaseBackupManager(context));
  }
}
