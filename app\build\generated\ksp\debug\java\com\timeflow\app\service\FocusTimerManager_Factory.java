package com.timeflow.app.service;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class FocusTimerManager_Factory implements Factory<FocusTimerManager> {
  private final Provider<Context> contextProvider;

  public FocusTimerManager_Factory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public FocusTimerManager get() {
    return newInstance(contextProvider.get());
  }

  public static FocusTimerManager_Factory create(Provider<Context> contextProvider) {
    return new FocusTimerManager_Factory(contextProvider);
  }

  public static FocusTimerManager newInstance(Context context) {
    return new FocusTimerManager(context);
  }
}
