package com.timeflow.app.data.repository;

import com.timeflow.app.data.dao.GoalTemplateDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DefaultTemplateInitializer_Factory implements Factory<DefaultTemplateInitializer> {
  private final Provider<GoalTemplateDao> templateDaoProvider;

  public DefaultTemplateInitializer_Factory(Provider<GoalTemplateDao> templateDaoProvider) {
    this.templateDaoProvider = templateDaoProvider;
  }

  @Override
  public DefaultTemplateInitializer get() {
    return newInstance(templateDaoProvider.get());
  }

  public static DefaultTemplateInitializer_Factory create(
      Provider<GoalTemplateDao> templateDaoProvider) {
    return new DefaultTemplateInitializer_Factory(templateDaoProvider);
  }

  public static DefaultTemplateInitializer newInstance(GoalTemplateDao templateDao) {
    return new DefaultTemplateInitializer(templateDao);
  }
}
