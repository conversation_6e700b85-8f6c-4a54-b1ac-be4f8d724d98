package com.timeflow.app.ui.viewmodel;

import android.content.SharedPreferences;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class GlobalTimerViewModel_Factory implements Factory<GlobalTimerViewModel> {
  private final Provider<SharedPreferences> sharedPreferencesProvider;

  public GlobalTimerViewModel_Factory(Provider<SharedPreferences> sharedPreferencesProvider) {
    this.sharedPreferencesProvider = sharedPreferencesProvider;
  }

  @Override
  public GlobalTimerViewModel get() {
    return newInstance(sharedPreferencesProvider.get());
  }

  public static GlobalTimerViewModel_Factory create(
      Provider<SharedPreferences> sharedPreferencesProvider) {
    return new GlobalTimerViewModel_Factory(sharedPreferencesProvider);
  }

  public static GlobalTimerViewModel newInstance(SharedPreferences sharedPreferences) {
    return new GlobalTimerViewModel(sharedPreferences);
  }
}
