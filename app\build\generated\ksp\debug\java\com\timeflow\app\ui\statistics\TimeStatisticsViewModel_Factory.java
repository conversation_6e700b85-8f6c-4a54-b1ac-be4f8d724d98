package com.timeflow.app.ui.statistics;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class TimeStatisticsViewModel_Factory implements Factory<TimeStatisticsViewModel> {
  @Override
  public TimeStatisticsViewModel get() {
    return newInstance();
  }

  public static TimeStatisticsViewModel_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static TimeStatisticsViewModel newInstance() {
    return new TimeStatisticsViewModel();
  }

  private static final class InstanceHolder {
    private static final TimeStatisticsViewModel_Factory INSTANCE = new TimeStatisticsViewModel_Factory();
  }
}
