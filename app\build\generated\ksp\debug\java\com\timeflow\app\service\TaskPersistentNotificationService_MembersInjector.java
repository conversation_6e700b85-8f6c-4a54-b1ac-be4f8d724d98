package com.timeflow.app.service;

import com.timeflow.app.data.repository.TaskRepository;
import com.timeflow.app.utils.NotificationHelper;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class TaskPersistentNotificationService_MembersInjector implements MembersInjector<TaskPersistentNotificationService> {
  private final Provider<TaskRepository> taskRepositoryProvider;

  private final Provider<NotificationHelper> notificationHelperProvider;

  public TaskPersistentNotificationService_MembersInjector(
      Provider<TaskRepository> taskRepositoryProvider,
      Provider<NotificationHelper> notificationHelperProvider) {
    this.taskRepositoryProvider = taskRepositoryProvider;
    this.notificationHelperProvider = notificationHelperProvider;
  }

  public static MembersInjector<TaskPersistentNotificationService> create(
      Provider<TaskRepository> taskRepositoryProvider,
      Provider<NotificationHelper> notificationHelperProvider) {
    return new TaskPersistentNotificationService_MembersInjector(taskRepositoryProvider, notificationHelperProvider);
  }

  @Override
  public void injectMembers(TaskPersistentNotificationService instance) {
    injectTaskRepository(instance, taskRepositoryProvider.get());
    injectNotificationHelper(instance, notificationHelperProvider.get());
  }

  @InjectedFieldSignature("com.timeflow.app.service.TaskPersistentNotificationService.taskRepository")
  public static void injectTaskRepository(TaskPersistentNotificationService instance,
      TaskRepository taskRepository) {
    instance.taskRepository = taskRepository;
  }

  @InjectedFieldSignature("com.timeflow.app.service.TaskPersistentNotificationService.notificationHelper")
  public static void injectNotificationHelper(TaskPersistentNotificationService instance,
      NotificationHelper notificationHelper) {
    instance.notificationHelper = notificationHelper;
  }
}
