package com.timeflow.app.di;

import com.timeflow.app.data.repository.TaskTimeRepository;
import com.timeflow.app.domain.usecase.TaskTimeUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class TaskTimeModule_ProvideTaskTimeUseCaseFactory implements Factory<TaskTimeUseCase> {
  private final Provider<TaskTimeRepository> taskTimeRepositoryProvider;

  public TaskTimeModule_ProvideTaskTimeUseCaseFactory(
      Provider<TaskTimeRepository> taskTimeRepositoryProvider) {
    this.taskTimeRepositoryProvider = taskTimeRepositoryProvider;
  }

  @Override
  public TaskTimeUseCase get() {
    return provideTaskTimeUseCase(taskTimeRepositoryProvider.get());
  }

  public static TaskTimeModule_ProvideTaskTimeUseCaseFactory create(
      Provider<TaskTimeRepository> taskTimeRepositoryProvider) {
    return new TaskTimeModule_ProvideTaskTimeUseCaseFactory(taskTimeRepositoryProvider);
  }

  public static TaskTimeUseCase provideTaskTimeUseCase(TaskTimeRepository taskTimeRepository) {
    return Preconditions.checkNotNullFromProvides(TaskTimeModule.INSTANCE.provideTaskTimeUseCase(taskTimeRepository));
  }
}
