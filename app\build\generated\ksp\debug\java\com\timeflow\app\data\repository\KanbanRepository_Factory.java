package com.timeflow.app.data.repository;

import com.timeflow.app.data.dao.KanbanBoardDao;
import com.timeflow.app.data.dao.KanbanColumnDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class KanbanRepository_Factory implements Factory<KanbanRepository> {
  private final Provider<KanbanBoardDao> kanbanBoardDaoProvider;

  private final Provider<KanbanColumnDao> kanbanColumnDaoProvider;

  public KanbanRepository_Factory(Provider<KanbanBoardDao> kanbanBoardDaoProvider,
      Provider<KanbanColumnDao> kanbanColumnDaoProvider) {
    this.kanbanBoardDaoProvider = kanbanBoardDaoProvider;
    this.kanbanColumnDaoProvider = kanbanColumnDaoProvider;
  }

  @Override
  public KanbanRepository get() {
    return newInstance(kanbanBoardDaoProvider.get(), kanbanColumnDaoProvider.get());
  }

  public static KanbanRepository_Factory create(Provider<KanbanBoardDao> kanbanBoardDaoProvider,
      Provider<KanbanColumnDao> kanbanColumnDaoProvider) {
    return new KanbanRepository_Factory(kanbanBoardDaoProvider, kanbanColumnDaoProvider);
  }

  public static KanbanRepository newInstance(KanbanBoardDao kanbanBoardDao,
      KanbanColumnDao kanbanColumnDao) {
    return new KanbanRepository(kanbanBoardDao, kanbanColumnDao);
  }
}
