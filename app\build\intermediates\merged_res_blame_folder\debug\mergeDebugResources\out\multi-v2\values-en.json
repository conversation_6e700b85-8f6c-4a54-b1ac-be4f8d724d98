{"logs": [{"outputFile": "D:\\development\\Android\\gradle\\daemon\\8.11.1\\com.timeflow.app-mergeDebugResources-85:\\values-en\\values-en.xml", "map": [{"source": "D:\\development\\Codes\\MyApplication\\app\\src\\main\\res\\values-en\\strings.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,242,243,241,244,236,235,238,237,234,233,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,174,175,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,205,226,230,214,212,216,178,179,181,180,203,227,222,229,219,196,198,197,199,208,202,215,220,225,213,209,224,192,193,195,194,228,217,206,223,221,187,188,189,191,190,204,210,211,218,182,183,184,185,186,207,-1,-1,-1,-1,-1,-1,-1,-1,172,-1,-1,-1,-1,-1,-1,-1,-1,-1,173,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,4,4,4,4,4,4,4,4,4,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,14320,14401,14225,14512,13521,13435,14002,13924,13055,12958,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,9310,9372,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,11311,12611,12867,11865,11733,11983,9465,9529,9693,9607,11185,12677,12351,12793,12167,10751,10909,10819,10991,11497,11115,11925,12227,12543,11801,11559,12475,10463,10525,10679,10599,12735,12039,11379,12413,12285,10119,10177,10249,10395,10319,11247,11621,11677,12099,9767,9823,9897,9971,10049,11441,-1,-1,-1,-1,-1,-1,-1,-1,9198,-1,-1,-1,-1,-1,-1,-1,-1,-1,9258,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "endColumns": "-1,-1,-1,-1,-1,-1,80,110,94,92,402,85,185,77,379,96,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,61,59,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,67,65,61,59,67,55,63,77,73,85,61,57,61,73,59,67,81,89,81,61,69,57,57,67,63,61,67,61,73,71,79,57,59,61,61,65,57,71,69,67,75,63,55,55,67,55,73,73,77,69,55,-1,-1,-1,-1,-1,-1,-1,-1,59,-1,-1,-1,-1,-1,-1,-1,-1,-1,51,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "endOffsets": "-1,-1,-1,-1,-1,-1,14396,14507,14315,14600,13919,13516,14183,13997,13430,13050,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,9367,9427,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,11374,12672,12924,11920,11796,12034,9524,9602,9762,9688,11242,12730,12408,12862,12222,10814,10986,10904,11068,11554,11180,11978,12280,12606,11860,11616,12538,10520,10594,10746,10674,12788,12094,11436,12470,12346,10172,10244,10314,10458,10390,11306,11672,11728,12162,9818,9892,9966,10044,10114,11492,-1,-1,-1,-1,-1,-1,-1,-1,9253,-1,-1,-1,-1,-1,-1,-1,-1,-1,9305,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,95,131,192,256,326,374,455,566,661,754,1157,1243,1429,1507,1887,1984,2032,2086,2163,2209,2277,2317,2355,2417,2503,2575,2621,2677,2719,2759,2799,2853,2923,2967,3030,3089,3157,3212,3262,3305,3347,3410,3491,3572,3649,3695,3733,3771,3910,4050,4212,4351,4498,4663,4707,4783,4823,4887,4945,5005,5106,5158,5228,5284,5346,5411,5483,5541,5583,5645,5705,5761,5840,5911,5977,6032,6101,6140,6202,6240,6285,6381,6443,6507,6554,6608,6735,6810,6870,6922,6961,7016,7054,7088,7170,7284,7368,7476,7563,7669,7745,7849,7921,7955,8021,8085,8147,8193,8249,8325,8387,8431,8500,8550,8592,8632,8688,8740,8781,8819,8887,8953,9015,9075,9143,9199,9263,9341,9415,9501,9563,9621,9683,9757,9817,9885,9967,10057,10139,10201,10271,10329,10387,10455,10519,10581,10649,10711,10785,10857,10937,10995,11055,11117,11179,11245,11303,11375,11445,11513,11589,11653,11709,11765,11833,11889,11963,12037,12115,12185,12241,12301,12386,12458,12504,12564,12630,12674,12713,12773,12813,12859,12917,13000,13058,13099,13137,13202,13271,13323,13400,13467,13535,13575,13641,13737,13781,13825,13867,13929,13977", "endColumns": "39,35,60,63,69,47,80,110,94,92,402,85,185,77,379,96,47,53,76,45,67,39,37,61,85,71,45,55,41,39,39,53,69,43,62,58,67,54,49,42,41,62,80,80,76,45,37,37,138,139,161,138,146,164,43,75,39,63,57,59,100,51,69,55,61,64,71,57,41,61,59,55,78,70,65,54,68,38,61,37,44,95,61,63,46,53,126,74,59,51,38,54,37,33,81,113,83,107,86,105,75,103,71,33,65,63,61,45,55,75,61,43,68,49,41,39,55,51,40,37,67,65,61,59,67,55,63,77,73,85,61,57,61,73,59,67,81,89,81,61,69,57,57,67,63,61,67,61,73,71,79,57,59,61,61,65,57,71,69,67,75,63,55,55,67,55,73,73,77,69,55,59,84,71,45,59,65,43,38,59,39,45,57,82,57,40,37,64,68,51,76,66,67,39,65,95,43,43,41,61,47,35", "endOffsets": "90,126,187,251,321,369,450,561,656,749,1152,1238,1424,1502,1882,1979,2027,2081,2158,2204,2272,2312,2350,2412,2498,2570,2616,2672,2714,2754,2794,2848,2918,2962,3025,3084,3152,3207,3257,3300,3342,3405,3486,3567,3644,3690,3728,3766,3905,4045,4207,4346,4493,4658,4702,4778,4818,4882,4940,5000,5101,5153,5223,5279,5341,5406,5478,5536,5578,5640,5700,5756,5835,5906,5972,6027,6096,6135,6197,6235,6280,6376,6438,6502,6549,6603,6730,6805,6865,6917,6956,7011,7049,7083,7165,7279,7363,7471,7558,7664,7740,7844,7916,7950,8016,8080,8142,8188,8244,8320,8382,8426,8495,8545,8587,8627,8683,8735,8776,8814,8882,8948,9010,9070,9138,9194,9258,9336,9410,9496,9558,9616,9678,9752,9812,9880,9962,10052,10134,10196,10266,10324,10382,10450,10514,10576,10644,10706,10780,10852,10932,10990,11050,11112,11174,11240,11298,11370,11440,11508,11584,11648,11704,11760,11828,11884,11958,12032,12110,12180,12236,12296,12381,12453,12499,12559,12625,12669,12708,12768,12808,12854,12912,12995,13053,13094,13132,13197,13266,13318,13395,13462,13530,13570,13636,13732,13776,13820,13862,13924,13972,14008"}}]}, {"outputFile": "com.timeflow.app-mergeDebugResources-85:/values-en/values-en.xml", "map": [{"source": "D:\\development\\Codes\\MyApplication\\app\\src\\main\\res\\values-en\\strings.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,247,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,248", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,14634,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,14700", "endColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,65,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,77", "endOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,14695,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,14773"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,95,131,192,256,326,374,455,566,661,754,1157,1243,1429,1507,1887,1984,2032,2086,2163,2209,2277,2317,2355,2417,2503,2575,2621,2677,2719,2759,2799,2853,2923,2967,3030,3089,3157,3212,3262,3305,3347,3410,3491,3572,3649,3695,3733,3771,3910,4050,4212,4351,4498,4663,4707,4783,4823,4887,4945,5005,5106,5158,5228,5284,5346,5411,5483,5541,5583,5645,5705,5761,5840,5911,5977,6032,6101,6140,6202,6240,6285,6381,6443,6507,6554,6608,6735,6810,6870,6922,6961,7016,7054,7088,7170,7284,7368,7476,7563,7669,7745,7849,7921,7955,8021,8085,8147,8193,8249,8325,8387,8431,8500,8550,8592,8632,8688,8740,8781,8819,8887,8953,9015,9075,9143,9199,9263,9341,9415,9501,9563,9621,9683,9757,9817,9885,9967,10057,10139,10201,10271,10329,10387,10455,10519,10581,10649,10711,10785,10857,10937,10995,11055,11117,11179,11245,11303,11375,11445,11513,11589,11653,11709,11765,11833,11889,11963,12037,12115,12185,12241,12307,12367,12452,12524,12570,12630,12696,12740,12779,12839,12879,12925,12983,13066,13124,13165,13203,13268,13337,13389,13466,13533,13601,13641,13707,13803,13847,13891,13933,13995,14043,14079", "endColumns": "39,35,60,63,69,47,80,110,94,92,402,85,185,77,379,96,47,53,76,45,67,39,37,61,85,71,45,55,41,39,39,53,69,43,62,58,67,54,49,42,41,62,80,80,76,45,37,37,138,139,161,138,146,164,43,75,39,63,57,59,100,51,69,55,61,64,71,57,41,61,59,55,78,70,65,54,68,38,61,37,44,95,61,63,46,53,126,74,59,51,38,54,37,33,81,113,83,107,86,105,75,103,71,33,65,63,61,45,55,75,61,43,68,49,41,39,55,51,40,37,67,65,61,59,67,55,63,77,73,85,61,57,61,73,59,67,81,89,81,61,69,57,57,67,63,61,67,61,73,71,79,57,59,61,61,65,57,71,69,67,75,63,55,55,67,55,73,73,77,69,55,65,59,84,71,45,59,65,43,38,59,39,45,57,82,57,40,37,64,68,51,76,66,67,39,65,95,43,43,41,61,47,35,77", "endOffsets": "90,126,187,251,321,369,450,561,656,749,1152,1238,1424,1502,1882,1979,2027,2081,2158,2204,2272,2312,2350,2412,2498,2570,2616,2672,2714,2754,2794,2848,2918,2962,3025,3084,3152,3207,3257,3300,3342,3405,3486,3567,3644,3690,3728,3766,3905,4045,4207,4346,4493,4658,4702,4778,4818,4882,4940,5000,5101,5153,5223,5279,5341,5406,5478,5536,5578,5640,5700,5756,5835,5906,5972,6027,6096,6135,6197,6235,6280,6376,6438,6502,6549,6603,6730,6805,6865,6917,6956,7011,7049,7083,7165,7279,7363,7471,7558,7664,7740,7844,7916,7950,8016,8080,8142,8188,8244,8320,8382,8426,8495,8545,8587,8627,8683,8735,8776,8814,8882,8948,9010,9070,9138,9194,9258,9336,9410,9496,9558,9616,9678,9752,9812,9880,9962,10052,10134,10196,10266,10324,10382,10450,10514,10576,10644,10706,10780,10852,10932,10990,11050,11112,11174,11240,11298,11370,11440,11508,11584,11648,11704,11760,11828,11884,11958,12032,12110,12180,12236,12302,12362,12447,12519,12565,12625,12691,12735,12774,12834,12874,12920,12978,13061,13119,13160,13198,13263,13332,13384,13461,13528,13596,13636,13702,13798,13842,13886,13928,13990,14038,14074,14152"}}]}]}