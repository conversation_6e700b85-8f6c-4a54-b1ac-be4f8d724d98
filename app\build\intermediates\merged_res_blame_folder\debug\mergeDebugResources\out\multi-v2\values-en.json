{"logs": [{"outputFile": "com.timeflow.app-mergeDebugResources-85:/values-en/values-en.xml", "map": [{"source": "D:\\development\\Codes\\MyApplication\\app\\src\\main\\res\\values-en\\strings.xml", "from": {"startLines": "70,81,14,15,7,53,54,2,67,85,87,45,46,47,10,26,73,83,86,36,37,74,38,28,25,27,80,8,78,79,58,92,21,90,55,66,3,95,60,59,52,91,33,35,34,18,19,88,77,68,75,22,24,20,89,39,40,69,9,29,82,84,62,61,72,41,42,65,23,56,93,4,5,43,44,32,6,57,48,49,71,94,11,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3771,4201,526,587,263,2803,2857,55,3569,4359,4439,2321,2383,2469,401,1141,3893,4279,4399,1711,1765,3935,1835,1240,1091,1197,4159,311,4083,4121,3124,4650,843,4561,2934,3507,101,4778,3230,3168,2739,4603,1455,1584,1509,669,729,4477,4049,3637,3979,901,1027,781,4515,1903,1959,3709,357,1295,4237,4319,3378,3326,3855,2035,2095,3461,967,2990,4690,139,179,2180,2238,1397,225,3056,2541,2607,3811,4734,447,4013", "endColumns": "39,35,60,47,47,53,76,45,67,39,37,61,85,71,45,55,41,39,39,53,69,43,67,54,49,42,41,45,37,37,43,39,57,41,55,61,37,44,95,61,63,46,53,126,74,59,51,37,33,71,33,65,63,61,45,55,75,61,43,68,41,39,55,51,37,59,84,45,59,65,43,39,45,57,82,57,37,67,65,95,43,43,47,35", "endOffsets": "3806,4232,582,630,306,2852,2929,96,3632,4394,4472,2378,2464,2536,442,1192,3930,4314,4434,1760,1830,3974,1898,1290,1136,1235,4196,352,4116,4154,3163,4685,896,4598,2985,3564,134,4818,3321,3225,2798,4645,1504,1706,1579,724,776,4510,4078,3704,4008,962,1086,838,4556,1954,2030,3766,396,1359,4274,4354,3429,3373,3888,2090,2175,3502,1022,3051,4729,174,220,2233,2316,1450,258,3119,2602,2698,3850,4773,490,4044"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,95,131,192,240,288,342,419,465,533,573,611,673,759,831,877,933,975,1015,1055,1109,1179,1223,1291,1346,1396,1439,1481,1527,1565,1603,1647,1687,1745,1787,1843,1905,1943,1988,2084,2146,2210,2257,2311,2438,2513,2573,2625,2663,2697,2769,2803,2869,2933,2995,3041,3097,3173,3235,3279,3348,3390,3430,3486,3538,3576,3636,3721,3767,3827,3893,3937,3977,4023,4081,4164,4222,4260,4328,4394,4490,4534,4578,4626", "endColumns": "39,35,60,47,47,53,76,45,67,39,37,61,85,71,45,55,41,39,39,53,69,43,67,54,49,42,41,45,37,37,43,39,57,41,55,61,37,44,95,61,63,46,53,126,74,59,51,37,33,71,33,65,63,61,45,55,75,61,43,68,41,39,55,51,37,59,84,45,59,65,43,39,45,57,82,57,37,67,65,95,43,43,47,35", "endOffsets": "90,126,187,235,283,337,414,460,528,568,606,668,754,826,872,928,970,1010,1050,1104,1174,1218,1286,1341,1391,1434,1476,1522,1560,1598,1642,1682,1740,1782,1838,1900,1938,1983,2079,2141,2205,2252,2306,2433,2508,2568,2620,2658,2692,2764,2798,2864,2928,2990,3036,3092,3168,3230,3274,3343,3385,3425,3481,3533,3571,3631,3716,3762,3822,3888,3932,3972,4018,4076,4159,4217,4255,4323,4389,4485,4529,4573,4621,4657"}}]}]}