package com.timeflow.app.data.repository;

import com.timeflow.app.data.dao.GoalTemplateDao;
import com.timeflow.app.utils.TemplateCache;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class GoalTemplateRepositoryImpl_Factory implements Factory<GoalTemplateRepositoryImpl> {
  private final Provider<GoalTemplateDao> templateDaoProvider;

  private final Provider<GoalRepository> goalRepositoryProvider;

  private final Provider<TemplateCache> templateCacheProvider;

  public GoalTemplateRepositoryImpl_Factory(Provider<GoalTemplateDao> templateDaoProvider,
      Provider<GoalRepository> goalRepositoryProvider,
      Provider<TemplateCache> templateCacheProvider) {
    this.templateDaoProvider = templateDaoProvider;
    this.goalRepositoryProvider = goalRepositoryProvider;
    this.templateCacheProvider = templateCacheProvider;
  }

  @Override
  public GoalTemplateRepositoryImpl get() {
    return newInstance(templateDaoProvider.get(), goalRepositoryProvider.get(), templateCacheProvider.get());
  }

  public static GoalTemplateRepositoryImpl_Factory create(
      Provider<GoalTemplateDao> templateDaoProvider,
      Provider<GoalRepository> goalRepositoryProvider,
      Provider<TemplateCache> templateCacheProvider) {
    return new GoalTemplateRepositoryImpl_Factory(templateDaoProvider, goalRepositoryProvider, templateCacheProvider);
  }

  public static GoalTemplateRepositoryImpl newInstance(GoalTemplateDao templateDao,
      GoalRepository goalRepository, TemplateCache templateCache) {
    return new GoalTemplateRepositoryImpl(templateDao, goalRepository, templateCache);
  }
}
