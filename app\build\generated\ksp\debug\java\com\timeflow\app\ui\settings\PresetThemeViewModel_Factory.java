package com.timeflow.app.ui.settings;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class PresetThemeViewModel_Factory implements Factory<PresetThemeViewModel> {
  @Override
  public PresetThemeViewModel get() {
    return newInstance();
  }

  public static PresetThemeViewModel_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static PresetThemeViewModel newInstance() {
    return new PresetThemeViewModel();
  }

  private static final class InstanceHolder {
    private static final PresetThemeViewModel_Factory INSTANCE = new PresetThemeViewModel_Factory();
  }
}
