package com.timeflow.app.ui;

import androidx.datastore.core.DataStore;
import androidx.datastore.preferences.core.Preferences;
import com.timeflow.app.data.db.AppDatabase;
import com.timeflow.app.di.DataStoreModule;
import com.timeflow.app.utils.NotificationPermissionHelper;
import com.timeflow.app.utils.RenderOptimizer;
import com.timeflow.app.utils.SampleDataGenerator;
import com.timeflow.app.utils.SystemBarManager;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@QualifierMetadata("com.timeflow.app.di.DataStoreModule.ThemeDataStore")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class MainActivity_MembersInjector implements MembersInjector<MainActivity> {
  private final Provider<AppDatabase> appDatabaseProvider;

  private final Provider<SampleDataGenerator> sampleDataGeneratorProvider;

  private final Provider<RenderOptimizer> renderOptimizerProvider;

  private final Provider<SystemBarManager> systemBarManagerProvider;

  private final Provider<DataStore<Preferences>> themeDataStoreProvider;

  private final Provider<NotificationPermissionHelper> notificationPermissionHelperProvider;

  public MainActivity_MembersInjector(Provider<AppDatabase> appDatabaseProvider,
      Provider<SampleDataGenerator> sampleDataGeneratorProvider,
      Provider<RenderOptimizer> renderOptimizerProvider,
      Provider<SystemBarManager> systemBarManagerProvider,
      Provider<DataStore<Preferences>> themeDataStoreProvider,
      Provider<NotificationPermissionHelper> notificationPermissionHelperProvider) {
    this.appDatabaseProvider = appDatabaseProvider;
    this.sampleDataGeneratorProvider = sampleDataGeneratorProvider;
    this.renderOptimizerProvider = renderOptimizerProvider;
    this.systemBarManagerProvider = systemBarManagerProvider;
    this.themeDataStoreProvider = themeDataStoreProvider;
    this.notificationPermissionHelperProvider = notificationPermissionHelperProvider;
  }

  public static MembersInjector<MainActivity> create(Provider<AppDatabase> appDatabaseProvider,
      Provider<SampleDataGenerator> sampleDataGeneratorProvider,
      Provider<RenderOptimizer> renderOptimizerProvider,
      Provider<SystemBarManager> systemBarManagerProvider,
      Provider<DataStore<Preferences>> themeDataStoreProvider,
      Provider<NotificationPermissionHelper> notificationPermissionHelperProvider) {
    return new MainActivity_MembersInjector(appDatabaseProvider, sampleDataGeneratorProvider, renderOptimizerProvider, systemBarManagerProvider, themeDataStoreProvider, notificationPermissionHelperProvider);
  }

  @Override
  public void injectMembers(MainActivity instance) {
    injectAppDatabase(instance, appDatabaseProvider.get());
    injectSampleDataGenerator(instance, sampleDataGeneratorProvider.get());
    injectRenderOptimizer(instance, renderOptimizerProvider.get());
    injectSystemBarManager(instance, systemBarManagerProvider.get());
    injectThemeDataStore(instance, themeDataStoreProvider.get());
    injectNotificationPermissionHelper(instance, notificationPermissionHelperProvider.get());
  }

  @InjectedFieldSignature("com.timeflow.app.ui.MainActivity.appDatabase")
  public static void injectAppDatabase(MainActivity instance, AppDatabase appDatabase) {
    instance.appDatabase = appDatabase;
  }

  @InjectedFieldSignature("com.timeflow.app.ui.MainActivity.sampleDataGenerator")
  public static void injectSampleDataGenerator(MainActivity instance,
      SampleDataGenerator sampleDataGenerator) {
    instance.sampleDataGenerator = sampleDataGenerator;
  }

  @InjectedFieldSignature("com.timeflow.app.ui.MainActivity.renderOptimizer")
  public static void injectRenderOptimizer(MainActivity instance, RenderOptimizer renderOptimizer) {
    instance.renderOptimizer = renderOptimizer;
  }

  @InjectedFieldSignature("com.timeflow.app.ui.MainActivity.systemBarManager")
  public static void injectSystemBarManager(MainActivity instance,
      SystemBarManager systemBarManager) {
    instance.systemBarManager = systemBarManager;
  }

  @InjectedFieldSignature("com.timeflow.app.ui.MainActivity.themeDataStore")
  @DataStoreModule.ThemeDataStore
  public static void injectThemeDataStore(MainActivity instance,
      DataStore<Preferences> themeDataStore) {
    instance.themeDataStore = themeDataStore;
  }

  @InjectedFieldSignature("com.timeflow.app.ui.MainActivity.notificationPermissionHelper")
  public static void injectNotificationPermissionHelper(MainActivity instance,
      NotificationPermissionHelper notificationPermissionHelper) {
    instance.notificationPermissionHelper = notificationPermissionHelper;
  }
}
