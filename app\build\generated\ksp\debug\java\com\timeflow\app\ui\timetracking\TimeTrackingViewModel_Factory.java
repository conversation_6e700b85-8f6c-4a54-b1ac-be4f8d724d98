package com.timeflow.app.ui.timetracking;

import android.content.Context;
import android.content.SharedPreferences;
import com.timeflow.app.data.repository.TaskRepository;
import com.timeflow.app.data.repository.TimeAnalyticsRepository;
import com.timeflow.app.data.repository.TimeSessionRepository;
import com.timeflow.app.service.FocusTimerManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class TimeTrackingViewModel_Factory implements Factory<TimeTrackingViewModel> {
  private final Provider<TimeAnalyticsRepository> timeAnalyticsRepositoryProvider;

  private final Provider<Context> contextProvider;

  private final Provider<TaskRepository> taskRepositoryProvider;

  private final Provider<TimeSessionRepository> timeSessionRepositoryProvider;

  private final Provider<SharedPreferences> sharedPreferencesProvider;

  private final Provider<FocusTimerManager> focusTimerManagerProvider;

  public TimeTrackingViewModel_Factory(
      Provider<TimeAnalyticsRepository> timeAnalyticsRepositoryProvider,
      Provider<Context> contextProvider, Provider<TaskRepository> taskRepositoryProvider,
      Provider<TimeSessionRepository> timeSessionRepositoryProvider,
      Provider<SharedPreferences> sharedPreferencesProvider,
      Provider<FocusTimerManager> focusTimerManagerProvider) {
    this.timeAnalyticsRepositoryProvider = timeAnalyticsRepositoryProvider;
    this.contextProvider = contextProvider;
    this.taskRepositoryProvider = taskRepositoryProvider;
    this.timeSessionRepositoryProvider = timeSessionRepositoryProvider;
    this.sharedPreferencesProvider = sharedPreferencesProvider;
    this.focusTimerManagerProvider = focusTimerManagerProvider;
  }

  @Override
  public TimeTrackingViewModel get() {
    return newInstance(timeAnalyticsRepositoryProvider.get(), contextProvider.get(), taskRepositoryProvider.get(), timeSessionRepositoryProvider.get(), sharedPreferencesProvider.get(), focusTimerManagerProvider.get());
  }

  public static TimeTrackingViewModel_Factory create(
      Provider<TimeAnalyticsRepository> timeAnalyticsRepositoryProvider,
      Provider<Context> contextProvider, Provider<TaskRepository> taskRepositoryProvider,
      Provider<TimeSessionRepository> timeSessionRepositoryProvider,
      Provider<SharedPreferences> sharedPreferencesProvider,
      Provider<FocusTimerManager> focusTimerManagerProvider) {
    return new TimeTrackingViewModel_Factory(timeAnalyticsRepositoryProvider, contextProvider, taskRepositoryProvider, timeSessionRepositoryProvider, sharedPreferencesProvider, focusTimerManagerProvider);
  }

  public static TimeTrackingViewModel newInstance(TimeAnalyticsRepository timeAnalyticsRepository,
      Context context, TaskRepository taskRepository, TimeSessionRepository timeSessionRepository,
      SharedPreferences sharedPreferences, FocusTimerManager focusTimerManager) {
    return new TimeTrackingViewModel(timeAnalyticsRepository, context, taskRepository, timeSessionRepository, sharedPreferences, focusTimerManager);
  }
}
