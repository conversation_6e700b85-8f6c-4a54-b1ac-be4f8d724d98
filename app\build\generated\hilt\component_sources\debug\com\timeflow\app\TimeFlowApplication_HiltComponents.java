package com.timeflow.app;

import androidx.hilt.work.HiltWrapper_WorkerFactoryModule;
import com.timeflow.app.di.AnalyticsModule;
import com.timeflow.app.di.AppModule;
import com.timeflow.app.di.DataStoreModule;
import com.timeflow.app.di.DatabaseModule;
import com.timeflow.app.di.ImageModule;
import com.timeflow.app.di.MedicationModule;
import com.timeflow.app.di.ReflectionModule;
import com.timeflow.app.di.RepositoryBindingsModule;
import com.timeflow.app.di.RepositoryModule;
import com.timeflow.app.di.SyncModule;
import com.timeflow.app.di.TaskTimeModule;
import com.timeflow.app.di.UtilsModule;
import com.timeflow.app.di.ViewModelModule;
import com.timeflow.app.di.WishModule;
import com.timeflow.app.di.WorkManagerInitializer;
import com.timeflow.app.di.WorkManagerModule;
import com.timeflow.app.initializer.RecurringTaskInitializer;
import com.timeflow.app.receiver.BootCompletedReceiver_GeneratedInjector;
import com.timeflow.app.receiver.DailyReviewAlarmReceiver_GeneratedInjector;
import com.timeflow.app.receiver.FocusTimerActionReceiver_GeneratedInjector;
import com.timeflow.app.receiver.HabitAlarmReceiver_GeneratedInjector;
import com.timeflow.app.receiver.TaskAlarmReceiver_GeneratedInjector;
import com.timeflow.app.receiver.TaskPersistentNotificationActionReceiver_GeneratedInjector;
import com.timeflow.app.service.AutoBackupService_GeneratedInjector;
import com.timeflow.app.service.FocusTimerService_GeneratedInjector;
import com.timeflow.app.service.NotificationTestService_GeneratedInjector;
import com.timeflow.app.service.TaskPersistentNotificationService_GeneratedInjector;
import com.timeflow.app.service.TimeTrackingService_GeneratedInjector;
import com.timeflow.app.ui.language.LanguageSettingsViewModel_HiltModules;
import com.timeflow.app.ui.screen.ai.AIReviewViewModel_HiltModules;
import com.timeflow.app.ui.screen.analytics.AnalyticsViewModel_HiltModules;
import com.timeflow.app.ui.screen.calendar.CalendarViewModel_HiltModules;
import com.timeflow.app.ui.screen.discover.DiscoverViewModel_HiltModules;
import com.timeflow.app.ui.screen.goal.GoalTemplateViewModel_HiltModules;
import com.timeflow.app.ui.screen.goal.GoalViewModel_HiltModules;
import com.timeflow.app.ui.screen.health.AddHabitViewModel_HiltModules;
import com.timeflow.app.ui.screen.health.ProfessionalMedicationViewModel_HiltModules;
import com.timeflow.app.ui.screen.health.UserPreferencesManagerEntryPoint;
import com.timeflow.app.ui.screen.milestone.MilestoneViewModel_HiltModules;
import com.timeflow.app.ui.screen.profile.EmotionStatisticsViewModel_HiltModules;
import com.timeflow.app.ui.screen.profile.ProfileViewModel_HiltModules;
import com.timeflow.app.ui.screen.reflection.ReflectionDetailViewModel_HiltModules;
import com.timeflow.app.ui.screen.reflection.ReflectionViewModel_HiltModules;
import com.timeflow.app.ui.screen.settings.NotificationSettingsViewModel_HiltModules;
import com.timeflow.app.ui.screen.settings.SettingsViewModel_HiltModules;
import com.timeflow.app.ui.screen.settings.SyncSettingsViewModel_HiltModules;
import com.timeflow.app.ui.screen.task.TaskDetailViewModel_HiltModules;
import com.timeflow.app.ui.screen.task.TaskViewModel_HiltModules;
import com.timeflow.app.ui.settings.PresetThemeViewModel_HiltModules;
import com.timeflow.app.ui.settings.ThemeSettingsViewModel_HiltModules;
import com.timeflow.app.ui.statistics.TimeStatisticsViewModel_HiltModules;
import com.timeflow.app.ui.task.KanbanViewModel_HiltModules;
import com.timeflow.app.ui.task.TaskListViewModel_HiltModules;
import com.timeflow.app.ui.timetracking.TaskTimeStatisticsViewModel_HiltModules;
import com.timeflow.app.ui.timetracking.TimeTrackingViewModel_HiltModules;
import com.timeflow.app.ui.viewmodel.AiAssistantViewModel_HiltModules;
import com.timeflow.app.ui.viewmodel.AiConfigViewModel_HiltModules;
import com.timeflow.app.ui.viewmodel.AiSettingsViewModel_HiltModules;
import com.timeflow.app.ui.viewmodel.GlobalTimerViewModel_HiltModules;
import com.timeflow.app.ui.viewmodel.GoalCreationViewModel_HiltModules;
import com.timeflow.app.ui.viewmodel.HabitViewModel_HiltModules;
import com.timeflow.app.ui.viewmodel.MenstrualCycleViewModel_HiltModules;
import com.timeflow.app.ui.viewmodel.TaskTimeViewModel_HiltModules;
import com.timeflow.app.ui.viewmodel.TimeFlowViewModel_HiltModules;
import com.timeflow.app.ui.viewmodel.WishListViewModel_HiltModules;
import com.timeflow.app.ui.viewmodel.WishStatisticsViewModel_HiltModules;
import com.timeflow.app.viewmodel.BackupRestoreViewModel_HiltModules;
import com.timeflow.app.viewmodel.BackupSettingsViewModel_HiltModules;
import com.timeflow.app.viewmodel.DataManagementViewModel_HiltModules;
import com.timeflow.app.worker.AutoBackupWorker_HiltModule;
import com.timeflow.app.worker.OverdueTaskCheckWorkerEntryPoint;
import com.timeflow.app.worker.RecurringTaskWorker_HiltModule;
import dagger.Binds;
import dagger.Component;
import dagger.Module;
import dagger.Subcomponent;
import dagger.hilt.android.components.ActivityComponent;
import dagger.hilt.android.components.ActivityRetainedComponent;
import dagger.hilt.android.components.FragmentComponent;
import dagger.hilt.android.components.ServiceComponent;
import dagger.hilt.android.components.ViewComponent;
import dagger.hilt.android.components.ViewModelComponent;
import dagger.hilt.android.components.ViewWithFragmentComponent;
import dagger.hilt.android.flags.FragmentGetContextFix;
import dagger.hilt.android.flags.HiltWrapper_FragmentGetContextFix_FragmentGetContextFixModule;
import dagger.hilt.android.internal.builders.ActivityComponentBuilder;
import dagger.hilt.android.internal.builders.ActivityRetainedComponentBuilder;
import dagger.hilt.android.internal.builders.FragmentComponentBuilder;
import dagger.hilt.android.internal.builders.ServiceComponentBuilder;
import dagger.hilt.android.internal.builders.ViewComponentBuilder;
import dagger.hilt.android.internal.builders.ViewModelComponentBuilder;
import dagger.hilt.android.internal.builders.ViewWithFragmentComponentBuilder;
import dagger.hilt.android.internal.lifecycle.DefaultViewModelFactories;
import dagger.hilt.android.internal.lifecycle.HiltViewModelFactory;
import dagger.hilt.android.internal.lifecycle.HiltWrapper_DefaultViewModelFactories_ActivityModule;
import dagger.hilt.android.internal.lifecycle.HiltWrapper_HiltViewModelFactory_ActivityCreatorEntryPoint;
import dagger.hilt.android.internal.lifecycle.HiltWrapper_HiltViewModelFactory_ViewModelModule;
import dagger.hilt.android.internal.managers.ActivityComponentManager;
import dagger.hilt.android.internal.managers.FragmentComponentManager;
import dagger.hilt.android.internal.managers.HiltWrapper_ActivityRetainedComponentManager_ActivityRetainedComponentBuilderEntryPoint;
import dagger.hilt.android.internal.managers.HiltWrapper_ActivityRetainedComponentManager_ActivityRetainedLifecycleEntryPoint;
import dagger.hilt.android.internal.managers.HiltWrapper_ActivityRetainedComponentManager_LifecycleModule;
import dagger.hilt.android.internal.managers.HiltWrapper_SavedStateHandleModule;
import dagger.hilt.android.internal.managers.ServiceComponentManager;
import dagger.hilt.android.internal.managers.ViewComponentManager;
import dagger.hilt.android.internal.modules.ApplicationContextModule;
import dagger.hilt.android.internal.modules.HiltWrapper_ActivityModule;
import dagger.hilt.android.scopes.ActivityRetainedScoped;
import dagger.hilt.android.scopes.ActivityScoped;
import dagger.hilt.android.scopes.FragmentScoped;
import dagger.hilt.android.scopes.ServiceScoped;
import dagger.hilt.android.scopes.ViewModelScoped;
import dagger.hilt.android.scopes.ViewScoped;
import dagger.hilt.components.SingletonComponent;
import dagger.hilt.internal.GeneratedComponent;
import dagger.hilt.migration.DisableInstallInCheck;
import javax.annotation.processing.Generated;
import javax.inject.Singleton;

@Generated("dagger.hilt.processor.internal.root.RootProcessor")
public final class TimeFlowApplication_HiltComponents {
  private TimeFlowApplication_HiltComponents() {
  }

  @Module(
      subcomponents = ServiceC.class
  )
  @DisableInstallInCheck
  @Generated("dagger.hilt.processor.internal.root.RootProcessor")
  abstract interface ServiceCBuilderModule {
    @Binds
    ServiceComponentBuilder bind(ServiceC.Builder builder);
  }

  @Module(
      subcomponents = ActivityRetainedC.class
  )
  @DisableInstallInCheck
  @Generated("dagger.hilt.processor.internal.root.RootProcessor")
  abstract interface ActivityRetainedCBuilderModule {
    @Binds
    ActivityRetainedComponentBuilder bind(ActivityRetainedC.Builder builder);
  }

  @Module(
      subcomponents = ActivityC.class
  )
  @DisableInstallInCheck
  @Generated("dagger.hilt.processor.internal.root.RootProcessor")
  abstract interface ActivityCBuilderModule {
    @Binds
    ActivityComponentBuilder bind(ActivityC.Builder builder);
  }

  @Module(
      subcomponents = ViewModelC.class
  )
  @DisableInstallInCheck
  @Generated("dagger.hilt.processor.internal.root.RootProcessor")
  abstract interface ViewModelCBuilderModule {
    @Binds
    ViewModelComponentBuilder bind(ViewModelC.Builder builder);
  }

  @Module(
      subcomponents = ViewC.class
  )
  @DisableInstallInCheck
  @Generated("dagger.hilt.processor.internal.root.RootProcessor")
  abstract interface ViewCBuilderModule {
    @Binds
    ViewComponentBuilder bind(ViewC.Builder builder);
  }

  @Module(
      subcomponents = FragmentC.class
  )
  @DisableInstallInCheck
  @Generated("dagger.hilt.processor.internal.root.RootProcessor")
  abstract interface FragmentCBuilderModule {
    @Binds
    FragmentComponentBuilder bind(FragmentC.Builder builder);
  }

  @Module(
      subcomponents = ViewWithFragmentC.class
  )
  @DisableInstallInCheck
  @Generated("dagger.hilt.processor.internal.root.RootProcessor")
  abstract interface ViewWithFragmentCBuilderModule {
    @Binds
    ViewWithFragmentComponentBuilder bind(ViewWithFragmentC.Builder builder);
  }

  @Component(
      modules = {
          AnalyticsModule.class,
          AppModule.class,
          ApplicationContextModule.class,
          AutoBackupWorker_HiltModule.class,
          DataStoreModule.class,
          DatabaseModule.class,
          HiltWrapper_FragmentGetContextFix_FragmentGetContextFixModule.class,
          HiltWrapper_WorkerFactoryModule.class,
          ImageModule.class,
          MedicationModule.class,
          RecurringTaskWorker_HiltModule.class,
          ReflectionModule.class,
          RepositoryBindingsModule.class,
          RepositoryModule.class,
          SyncModule.class,
          TaskTimeModule.class,
          ActivityRetainedCBuilderModule.class,
          ServiceCBuilderModule.class,
          UtilsModule.class,
          WishModule.class,
          WorkManagerModule.class
      }
  )
  @Singleton
  public abstract static class SingletonC implements TimeFlowApplication_GeneratedInjector,
      WorkManagerInitializer.WorkManagerInitializerEntryPoint,
      RecurringTaskInitializer.RecurringTaskInitializerEntryPoint,
      BootCompletedReceiver_GeneratedInjector,
      DailyReviewAlarmReceiver_GeneratedInjector,
      FocusTimerActionReceiver_GeneratedInjector,
      HabitAlarmReceiver_GeneratedInjector,
      TaskAlarmReceiver_GeneratedInjector,
      TaskPersistentNotificationActionReceiver_GeneratedInjector,
      UserPreferencesManagerEntryPoint,
      OverdueTaskCheckWorkerEntryPoint,
      FragmentGetContextFix.FragmentGetContextFixEntryPoint,
      HiltWrapper_ActivityRetainedComponentManager_ActivityRetainedComponentBuilderEntryPoint,
      ServiceComponentManager.ServiceComponentBuilderEntryPoint,
      SingletonComponent,
      GeneratedComponent {
  }

  @Subcomponent
  @ServiceScoped
  public abstract static class ServiceC implements AutoBackupService_GeneratedInjector,
      FocusTimerService_GeneratedInjector,
      NotificationTestService_GeneratedInjector,
      TaskPersistentNotificationService_GeneratedInjector,
      TimeTrackingService_GeneratedInjector,
      ServiceComponent,
      GeneratedComponent {
    @Subcomponent.Builder
    abstract interface Builder extends ServiceComponentBuilder {
    }
  }

  @Subcomponent(
      modules = {
          AIReviewViewModel_HiltModules.KeyModule.class,
          AddHabitViewModel_HiltModules.KeyModule.class,
          AiAssistantViewModel_HiltModules.KeyModule.class,
          AiConfigViewModel_HiltModules.KeyModule.class,
          AiSettingsViewModel_HiltModules.KeyModule.class,
          AnalyticsViewModel_HiltModules.KeyModule.class,
          BackupRestoreViewModel_HiltModules.KeyModule.class,
          BackupSettingsViewModel_HiltModules.KeyModule.class,
          CalendarViewModel_HiltModules.KeyModule.class,
          DataManagementViewModel_HiltModules.KeyModule.class,
          DiscoverViewModel_HiltModules.KeyModule.class,
          EmotionStatisticsViewModel_HiltModules.KeyModule.class,
          GlobalTimerViewModel_HiltModules.KeyModule.class,
          GoalCreationViewModel_HiltModules.KeyModule.class,
          GoalTemplateViewModel_HiltModules.KeyModule.class,
          GoalViewModel_HiltModules.KeyModule.class,
          com.timeflow.app.ui.viewmodel.GoalViewModel_HiltModules.KeyModule.class,
          HabitViewModel_HiltModules.KeyModule.class,
          HiltWrapper_ActivityRetainedComponentManager_LifecycleModule.class,
          HiltWrapper_SavedStateHandleModule.class,
          KanbanViewModel_HiltModules.KeyModule.class,
          LanguageSettingsViewModel_HiltModules.KeyModule.class,
          MenstrualCycleViewModel_HiltModules.KeyModule.class,
          MilestoneViewModel_HiltModules.KeyModule.class,
          NotificationSettingsViewModel_HiltModules.KeyModule.class,
          PresetThemeViewModel_HiltModules.KeyModule.class,
          ProfessionalMedicationViewModel_HiltModules.KeyModule.class,
          ProfileViewModel_HiltModules.KeyModule.class,
          ReflectionDetailViewModel_HiltModules.KeyModule.class,
          ReflectionViewModel_HiltModules.KeyModule.class,
          SettingsViewModel_HiltModules.KeyModule.class,
          SyncSettingsViewModel_HiltModules.KeyModule.class,
          TaskDetailViewModel_HiltModules.KeyModule.class,
          TaskListViewModel_HiltModules.KeyModule.class,
          TaskTimeStatisticsViewModel_HiltModules.KeyModule.class,
          TaskTimeViewModel_HiltModules.KeyModule.class,
          TaskViewModel_HiltModules.KeyModule.class,
          ThemeSettingsViewModel_HiltModules.KeyModule.class,
          ActivityCBuilderModule.class,
          ViewModelCBuilderModule.class,
          TimeFlowViewModel_HiltModules.KeyModule.class,
          TimeStatisticsViewModel_HiltModules.KeyModule.class,
          TimeTrackingViewModel_HiltModules.KeyModule.class,
          WishListViewModel_HiltModules.KeyModule.class,
          WishStatisticsViewModel_HiltModules.KeyModule.class
      }
  )
  @ActivityRetainedScoped
  public abstract static class ActivityRetainedC implements ActivityRetainedComponent,
      ActivityComponentManager.ActivityComponentBuilderEntryPoint,
      HiltWrapper_ActivityRetainedComponentManager_ActivityRetainedLifecycleEntryPoint,
      GeneratedComponent {
    @Subcomponent.Builder
    abstract interface Builder extends ActivityRetainedComponentBuilder {
    }
  }

  @Subcomponent(
      modules = {
          HiltWrapper_ActivityModule.class,
          HiltWrapper_DefaultViewModelFactories_ActivityModule.class,
          FragmentCBuilderModule.class,
          ViewCBuilderModule.class
      }
  )
  @ActivityScoped
  public abstract static class ActivityC implements MainActivity_GeneratedInjector,
      com.timeflow.app.ui.MainActivity_GeneratedInjector,
      ActivityComponent,
      DefaultViewModelFactories.ActivityEntryPoint,
      HiltWrapper_HiltViewModelFactory_ActivityCreatorEntryPoint,
      FragmentComponentManager.FragmentComponentBuilderEntryPoint,
      ViewComponentManager.ViewComponentBuilderEntryPoint,
      GeneratedComponent {
    @Subcomponent.Builder
    abstract interface Builder extends ActivityComponentBuilder {
    }
  }

  @Subcomponent(
      modules = {
          AIReviewViewModel_HiltModules.BindsModule.class,
          AddHabitViewModel_HiltModules.BindsModule.class,
          AiAssistantViewModel_HiltModules.BindsModule.class,
          AiConfigViewModel_HiltModules.BindsModule.class,
          AiSettingsViewModel_HiltModules.BindsModule.class,
          AnalyticsViewModel_HiltModules.BindsModule.class,
          BackupRestoreViewModel_HiltModules.BindsModule.class,
          BackupSettingsViewModel_HiltModules.BindsModule.class,
          CalendarViewModel_HiltModules.BindsModule.class,
          DataManagementViewModel_HiltModules.BindsModule.class,
          DiscoverViewModel_HiltModules.BindsModule.class,
          EmotionStatisticsViewModel_HiltModules.BindsModule.class,
          GlobalTimerViewModel_HiltModules.BindsModule.class,
          GoalCreationViewModel_HiltModules.BindsModule.class,
          GoalTemplateViewModel_HiltModules.BindsModule.class,
          GoalViewModel_HiltModules.BindsModule.class,
          com.timeflow.app.ui.viewmodel.GoalViewModel_HiltModules.BindsModule.class,
          HabitViewModel_HiltModules.BindsModule.class,
          HiltWrapper_HiltViewModelFactory_ViewModelModule.class,
          KanbanViewModel_HiltModules.BindsModule.class,
          LanguageSettingsViewModel_HiltModules.BindsModule.class,
          MenstrualCycleViewModel_HiltModules.BindsModule.class,
          MilestoneViewModel_HiltModules.BindsModule.class,
          NotificationSettingsViewModel_HiltModules.BindsModule.class,
          PresetThemeViewModel_HiltModules.BindsModule.class,
          ProfessionalMedicationViewModel_HiltModules.BindsModule.class,
          ProfileViewModel_HiltModules.BindsModule.class,
          ReflectionDetailViewModel_HiltModules.BindsModule.class,
          ReflectionViewModel_HiltModules.BindsModule.class,
          SettingsViewModel_HiltModules.BindsModule.class,
          SyncSettingsViewModel_HiltModules.BindsModule.class,
          TaskDetailViewModel_HiltModules.BindsModule.class,
          TaskListViewModel_HiltModules.BindsModule.class,
          TaskTimeStatisticsViewModel_HiltModules.BindsModule.class,
          TaskTimeViewModel_HiltModules.BindsModule.class,
          TaskViewModel_HiltModules.BindsModule.class,
          ThemeSettingsViewModel_HiltModules.BindsModule.class,
          TimeFlowViewModel_HiltModules.BindsModule.class,
          TimeStatisticsViewModel_HiltModules.BindsModule.class,
          TimeTrackingViewModel_HiltModules.BindsModule.class,
          ViewModelModule.class,
          WishListViewModel_HiltModules.BindsModule.class,
          WishStatisticsViewModel_HiltModules.BindsModule.class
      }
  )
  @ViewModelScoped
  public abstract static class ViewModelC implements ViewModelComponent,
      HiltViewModelFactory.ViewModelFactoriesEntryPoint,
      GeneratedComponent {
    @Subcomponent.Builder
    abstract interface Builder extends ViewModelComponentBuilder {
    }
  }

  @Subcomponent
  @ViewScoped
  public abstract static class ViewC implements ViewComponent,
      GeneratedComponent {
    @Subcomponent.Builder
    abstract interface Builder extends ViewComponentBuilder {
    }
  }

  @Subcomponent(
      modules = ViewWithFragmentCBuilderModule.class
  )
  @FragmentScoped
  public abstract static class FragmentC implements FragmentComponent,
      DefaultViewModelFactories.FragmentEntryPoint,
      ViewComponentManager.ViewWithFragmentComponentBuilderEntryPoint,
      GeneratedComponent {
    @Subcomponent.Builder
    abstract interface Builder extends FragmentComponentBuilder {
    }
  }

  @Subcomponent
  @ViewScoped
  public abstract static class ViewWithFragmentC implements ViewWithFragmentComponent,
      GeneratedComponent {
    @Subcomponent.Builder
    abstract interface Builder extends ViewWithFragmentComponentBuilder {
    }
  }
}
