package com.timeflow.app.utils;

import android.content.Context;
import com.timeflow.app.service.TaskPersistentNotificationManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class TaskPersistentNotificationTestHelper_Factory implements Factory<TaskPersistentNotificationTestHelper> {
  private final Provider<Context> contextProvider;

  private final Provider<TaskPersistentNotificationManager> taskPersistentNotificationManagerProvider;

  public TaskPersistentNotificationTestHelper_Factory(Provider<Context> contextProvider,
      Provider<TaskPersistentNotificationManager> taskPersistentNotificationManagerProvider) {
    this.contextProvider = contextProvider;
    this.taskPersistentNotificationManagerProvider = taskPersistentNotificationManagerProvider;
  }

  @Override
  public TaskPersistentNotificationTestHelper get() {
    return newInstance(contextProvider.get(), taskPersistentNotificationManagerProvider.get());
  }

  public static TaskPersistentNotificationTestHelper_Factory create(
      Provider<Context> contextProvider,
      Provider<TaskPersistentNotificationManager> taskPersistentNotificationManagerProvider) {
    return new TaskPersistentNotificationTestHelper_Factory(contextProvider, taskPersistentNotificationManagerProvider);
  }

  public static TaskPersistentNotificationTestHelper newInstance(Context context,
      TaskPersistentNotificationManager taskPersistentNotificationManager) {
    return new TaskPersistentNotificationTestHelper(context, taskPersistentNotificationManager);
  }
}
