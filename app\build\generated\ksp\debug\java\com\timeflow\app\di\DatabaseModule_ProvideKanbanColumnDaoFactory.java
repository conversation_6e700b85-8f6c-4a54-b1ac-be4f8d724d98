package com.timeflow.app.di;

import com.timeflow.app.data.dao.KanbanColumnDao;
import com.timeflow.app.data.db.AppDatabase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DatabaseModule_ProvideKanbanColumnDaoFactory implements Factory<KanbanColumnDao> {
  private final Provider<AppDatabase> databaseProvider;

  public DatabaseModule_ProvideKanbanColumnDaoFactory(Provider<AppDatabase> databaseProvider) {
    this.databaseProvider = databaseProvider;
  }

  @Override
  public KanbanColumnDao get() {
    return provideKanbanColumnDao(databaseProvider.get());
  }

  public static DatabaseModule_ProvideKanbanColumnDaoFactory create(
      Provider<AppDatabase> databaseProvider) {
    return new DatabaseModule_ProvideKanbanColumnDaoFactory(databaseProvider);
  }

  public static KanbanColumnDao provideKanbanColumnDao(AppDatabase database) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.provideKanbanColumnDao(database));
  }
}
