package com.timeflow.app.ui.screen.reflection;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ReflectionViewModel_Factory implements Factory<ReflectionViewModel> {
  private final Provider<ReflectionRepository> reflectionRepositoryProvider;

  private final Provider<SearchSuggestionService> searchSuggestionServiceProvider;

  public ReflectionViewModel_Factory(Provider<ReflectionRepository> reflectionRepositoryProvider,
      Provider<SearchSuggestionService> searchSuggestionServiceProvider) {
    this.reflectionRepositoryProvider = reflectionRepositoryProvider;
    this.searchSuggestionServiceProvider = searchSuggestionServiceProvider;
  }

  @Override
  public ReflectionViewModel get() {
    return newInstance(reflectionRepositoryProvider.get(), searchSuggestionServiceProvider.get());
  }

  public static ReflectionViewModel_Factory create(
      Provider<ReflectionRepository> reflectionRepositoryProvider,
      Provider<SearchSuggestionService> searchSuggestionServiceProvider) {
    return new ReflectionViewModel_Factory(reflectionRepositoryProvider, searchSuggestionServiceProvider);
  }

  public static ReflectionViewModel newInstance(ReflectionRepository reflectionRepository,
      SearchSuggestionService searchSuggestionService) {
    return new ReflectionViewModel(reflectionRepository, searchSuggestionService);
  }
}
