package com.timeflow.app.data.repository;

import com.timeflow.app.data.dao.GoalDao;
import com.timeflow.app.data.dao.HabitDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class HabitRepositoryImpl_Factory implements Factory<HabitRepositoryImpl> {
  private final Provider<HabitDao> habitDaoProvider;

  private final Provider<GoalDao> goalDaoProvider;

  public HabitRepositoryImpl_Factory(Provider<HabitDao> habitDaoProvider,
      Provider<GoalDao> goalDaoProvider) {
    this.habitDaoProvider = habitDaoProvider;
    this.goalDaoProvider = goalDaoProvider;
  }

  @Override
  public HabitRepositoryImpl get() {
    return newInstance(habitDaoProvider.get(), goalDaoProvider.get());
  }

  public static HabitRepositoryImpl_Factory create(Provider<HabitDao> habitDaoProvider,
      Provider<GoalDao> goalDaoProvider) {
    return new HabitRepositoryImpl_Factory(habitDaoProvider, goalDaoProvider);
  }

  public static HabitRepositoryImpl newInstance(HabitDao habitDao, GoalDao goalDao) {
    return new HabitRepositoryImpl(habitDao, goalDao);
  }
}
