#Thu Jul 17 23:36:38 CST 2025
com.timeflow.app-main-89\:/drawable/flowers.png=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_flowers.png.flat
com.timeflow.app-main-89\:/layout/widget_today_tasks_small.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_widget_today_tasks_small.xml.flat
com.timeflow.app-main-89\:/drawable/ic_finance.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_finance.xml.flat
com.timeflow.app-main-89\:/drawable/widget_stats_background.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_widget_stats_background.xml.flat
com.timeflow.app-main-89\:/mipmap-hdpi/ic_launcher.webp=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher.webp.flat
com.timeflow.app-main-89\:/xml/data_extraction_rules.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_data_extraction_rules.xml.flat
com.timeflow.app-main-89\:/anim/slide_out_right_fast.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\anim_slide_out_right_fast.xml.flat
com.timeflow.app-main-89\:/drawable/yinhua.png=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_yinhua.png.flat
com.timeflow.app-main-89\:/drawable/ic_impact.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_impact.xml.flat
com.timeflow.app-main-89\:/drawable/widget_pill_background.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_widget_pill_background.xml.flat
com.timeflow.app-main-89\:/mipmap-xhdpi/ic_launcher.webp=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher.webp.flat
com.timeflow.app-main-89\:/drawable/ic_family.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_family.xml.flat
com.timeflow.app-main-89\:/drawable/widget_status_paused.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_widget_status_paused.xml.flat
com.timeflow.app-main-89\:/mipmap-xxxhdpi/ic_launcher_round.webp=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher_round.webp.flat
com.timeflow.app-main-89\:/drawable/widget_task_background.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_widget_task_background.xml.flat
com.timeflow.app-main-89\:/drawable/ic_timer_pause.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_timer_pause.xml.flat
com.timeflow.app-main-89\:/layout/widget_time_insight.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_widget_time_insight.xml.flat
com.timeflow.app-main-89\:/mipmap-xxhdpi/ic_launcher_round.webp=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher_round.webp.flat
com.timeflow.app-main-89\:/xml/backup_rules.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_backup_rules.xml.flat
com.timeflow.app-main-89\:/drawable/widget_circle_button_secondary.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_widget_circle_button_secondary.xml.flat
com.timeflow.app-main-89\:/drawable/widget_status_running.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_widget_status_running.xml.flat
com.timeflow.app-main-89\:/drawable/ic_timer_play.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_timer_play.xml.flat
com.timeflow.app-main-89\:/xml/weekly_stats_widget_info.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_weekly_stats_widget_info.xml.flat
com.timeflow.app-main-89\:/drawable/ic_parallel_universe.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_parallel_universe.xml.flat
com.timeflow.app-main-89\:/drawable/ic_timeflow_logo.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_timeflow_logo.xml.flat
com.timeflow.app-main-89\:/drawable/ic_check_circle.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_check_circle.xml.flat
com.timeflow.app-main-89\:/drawable/widget_background.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_widget_background.xml.flat
com.timeflow.app-main-89\:/anim/slide_in_right_fast.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\anim_slide_in_right_fast.xml.flat
com.timeflow.app-main-89\:/mipmap-hdpi/ic_launcher_round.webp=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher_round.webp.flat
com.timeflow.app-main-89\:/layout/widget_quick_timer.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_widget_quick_timer.xml.flat
com.timeflow.app-main-89\:/layout/widget_today_tasks_large.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_widget_today_tasks_large.xml.flat
com.timeflow.app-main-89\:/drawable/widget_chart_background.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_widget_chart_background.xml.flat
com.timeflow.app-main-89\:/mipmap-xxhdpi/ic_launcher.webp=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher.webp.flat
com.timeflow.app-main-89\:/drawable/pig.png=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_pig.png.flat
com.timeflow.app-main-89\:/drawable/ic_travel.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_travel.xml.flat
com.timeflow.app-main-89\:/drawable/ic_ai.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_ai.xml.flat
com.timeflow.app-main-89\:/drawable/nomessage.png=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_nomessage.png.flat
com.timeflow.app-main-89\:/anim/fast_fade_in.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\anim_fast_fade_in.xml.flat
com.timeflow.app-main-89\:/drawable/ic_love.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_love.xml.flat
com.timeflow.app-main-89\:/drawable/widget_today_tasks_preview.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_widget_today_tasks_preview.xml.flat
com.timeflow.app-main-89\:/drawable/widget_checkbox_selector.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_widget_checkbox_selector.xml.flat
com.timeflow.app-main-89\:/drawable/ic_other.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_other.xml.flat
com.timeflow.app-main-89\:/drawable/ic_launcher_background.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_launcher_background.xml.flat
com.timeflow.app-main-89\:/drawable/widget_card_background.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_widget_card_background.xml.flat
com.timeflow.app-main-89\:/drawable/ic_play_arrow.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_play_arrow.xml.flat
com.timeflow.app-main-89\:/drawable/widget_focus_timer_preview.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_widget_focus_timer_preview.xml.flat
com.timeflow.app-main-89\:/drawable/ic_history.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_history.xml.flat
com.timeflow.app-main-89\:/mipmap-mdpi/ic_launcher_round.webp=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher_round.webp.flat
com.timeflow.app-main-89\:/drawable/widget_background_blue.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_widget_background_blue.xml.flat
com.timeflow.app-main-89\:/drawable/flower2.png=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_flower2.png.flat
com.timeflow.app-main-89\:/drawable/placeholder_image.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_placeholder_image.xml.flat
com.timeflow.app-main-89\:/drawable/widget_button_background.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_widget_button_background.xml.flat
com.timeflow.app-main-89\:/xml/time_insight_widget_info.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_time_insight_widget_info.xml.flat
com.timeflow.app-main-89\:/drawable/ic_image_placeholder.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_image_placeholder.xml.flat
com.timeflow.app-main-89\:/xml/goal_progress_widget_info.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_goal_progress_widget_info.xml.flat
com.timeflow.app-main-89\:/drawable/xx.png=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_xx.png.flat
com.timeflow.app-main-89\:/xml/file_paths.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_file_paths.xml.flat
com.timeflow.app-main-89\:/drawable/ic_play.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_play.xml.flat
com.timeflow.app-main-89\:/drawable/ic_star_outline.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_star_outline.xml.flat
com.timeflow.app-main-89\:/layout/widget_today_tasks.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_widget_today_tasks.xml.flat
com.timeflow.app-main-89\:/anim/slide_out_left_fast.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\anim_slide_out_left_fast.xml.flat
com.timeflow.app-main-89\:/mipmap-anydpi-v26/ic_launcher.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-anydpi-v26_ic_launcher.xml.flat
com.timeflow.app-main-89\:/drawable/widget_checkbox_checked.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_widget_checkbox_checked.xml.flat
com.timeflow.app-main-89\:/anim/slide_in_left_fast.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\anim_slide_in_left_fast.xml.flat
com.timeflow.app-main-89\:/drawable/widget_circle_button_accent.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_widget_circle_button_accent.xml.flat
com.timeflow.app-main-89\:/layout/widget_today_tasks_medium.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_widget_today_tasks_medium.xml.flat
com.timeflow.app-main-89\:/drawable/widget_circle_button_white.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_widget_circle_button_white.xml.flat
com.timeflow.app-main-89\:/anim/fade_in_fast.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\anim_fade_in_fast.xml.flat
com.timeflow.app-main-89\:/xml/today_tasks_widget_info.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_today_tasks_widget_info.xml.flat
com.timeflow.app-main-89\:/drawable/ic_launcher_monochrome.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_launcher_monochrome.xml.flat
com.timeflow.app-main-89\:/drawable/ic_health_check.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_health_check.xml.flat
com.timeflow.app-main-89\:/drawable/ic_stop.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_stop.xml.flat
com.timeflow.app-main-89\:/drawable/widget_status_idle.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_widget_status_idle.xml.flat
com.timeflow.app-main-89\:/drawable/ic_life.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_life.xml.flat
com.timeflow.app-main-89\:/drawable/ic_star_half.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_star_half.xml.flat
com.timeflow.app-main-89\:/layout/activity_main.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_main.xml.flat
com.timeflow.app-main-89\:/drawable/ic_health.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_health.xml.flat
com.timeflow.app-main-89\:/drawable/widget_checkbox_unchecked.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_widget_checkbox_unchecked.xml.flat
com.timeflow.app-main-89\:/drawable/ic_launcher_foreground.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_launcher_foreground.xml.flat
com.timeflow.app-main-89\:/drawable/widget_focus_background.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_widget_focus_background.xml.flat
com.timeflow.app-main-89\:/drawable/ic_check.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_check.xml.flat
com.timeflow.app-main-89\:/drawable/widget_background_green.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_widget_background_green.xml.flat
com.timeflow.app-main-89\:/xml/focus_timer_widget_info.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_focus_timer_widget_info.xml.flat
com.timeflow.app-main-89\:/mipmap-xxxhdpi/ic_launcher.webp=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher.webp.flat
com.timeflow.app-main-89\:/drawable/ic_star_filled.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_star_filled.xml.flat
com.timeflow.app-main-89\:/drawable/ic_timeflow_app_icon.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_timeflow_app_icon.xml.flat
com.timeflow.app-main-89\:/drawable/widget_button_secondary.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_widget_button_secondary.xml.flat
com.timeflow.app-main-89\:/drawable/flower.png=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_flower.png.flat
com.timeflow.app-main-89\:/drawable/ic_turning_point.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_turning_point.xml.flat
com.timeflow.app-main-89\:/drawable/widget_button_primary.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_widget_button_primary.xml.flat
com.timeflow.app-main-89\:/layout/widget_quick_timer_small.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_widget_quick_timer_small.xml.flat
com.timeflow.app-main-89\:/drawable/widget_emoji_background.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_widget_emoji_background.xml.flat
com.timeflow.app-main-89\:/drawable/widget_pill_background_white.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_widget_pill_background_white.xml.flat
com.timeflow.app-main-89\:/drawable/ic_career.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_career.xml.flat
com.timeflow.app-main-89\:/drawable/ic_timer_stop.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_timer_stop.xml.flat
com.timeflow.app-main-89\:/drawable/widget_circle_red.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_widget_circle_red.xml.flat
com.timeflow.app-main-89\:/drawable/ic_time_machine.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_time_machine.xml.flat
com.timeflow.app-main-89\:/drawable/widget_timer_custom_background.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_widget_timer_custom_background.xml.flat
com.timeflow.app-main-89\:/anim/fast_fade_out.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\anim_fast_fade_out.xml.flat
com.timeflow.app-main-89\:/drawable/widget_gradient_timetracking.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_widget_gradient_timetracking.xml.flat
com.timeflow.app-main-89\:/drawable/ic_time_capsule.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_time_capsule.xml.flat
com.timeflow.app-main-89\:/drawable/compat_ripple.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_compat_ripple.xml.flat
com.timeflow.app-main-89\:/drawable/widget_circle_gray.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_widget_circle_gray.xml.flat
com.timeflow.app-main-89\:/xml/quick_timer_widget_info.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_quick_timer_widget_info.xml.flat
com.timeflow.app-main-89\:/mipmap-anydpi-v26/ic_launcher_round.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-anydpi-v26_ic_launcher_round.xml.flat
com.timeflow.app-main-89\:/drawable/ic_skill.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_skill.xml.flat
com.timeflow.app-main-89\:/layout/widget_weekly_stats.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_widget_weekly_stats.xml.flat
com.timeflow.app-main-89\:/drawable/widget_task_item_background.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_widget_task_item_background.xml.flat
com.timeflow.app-main-89\:/drawable/widget_gradient_blue.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_widget_gradient_blue.xml.flat
com.timeflow.app-main-89\:/layout/widget_focus_timer.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_widget_focus_timer.xml.flat
com.timeflow.app-main-89\:/drawable/widget_circle_green.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_widget_circle_green.xml.flat
com.timeflow.app-main-89\:/drawable/ic_milestone.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_milestone.xml.flat
com.timeflow.app-main-89\:/anim/fade_out_fast.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\anim_fade_out_fast.xml.flat
com.timeflow.app-main-89\:/mipmap-xhdpi/ic_launcher_round.webp=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher_round.webp.flat
com.timeflow.app-main-89\:/drawable/ic_nebula.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_nebula.xml.flat
com.timeflow.app-main-89\:/drawable/jh.png=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_jh.png.flat
com.timeflow.app-main-89\:/layout/widget_focus_timer_small.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_widget_focus_timer_small.xml.flat
com.timeflow.app-main-89\:/mipmap-mdpi/ic_launcher.webp=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher.webp.flat
com.timeflow.app-main-89\:/drawable/cat.png=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_cat.png.flat
com.timeflow.app-main-89\:/layout/widget_focus_timer_large.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_widget_focus_timer_large.xml.flat
com.timeflow.app-main-89\:/drawable/ic_list.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_list.xml.flat
com.timeflow.app-main-89\:/drawable/ic_education.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_education.xml.flat
com.timeflow.app-main-89\:/layout/widget_goal_progress.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_widget_goal_progress.xml.flat
com.timeflow.app-main-89\:/drawable/widget_circle_blue.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_widget_circle_blue.xml.flat
com.timeflow.app-main-89\:/drawable/ic_pause.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_pause.xml.flat
com.timeflow.app-main-89\:/drawable/widget_circle_orange.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_widget_circle_orange.xml.flat
com.timeflow.app-main-89\:/drawable/widget_timer_button_background.xml=D\:\\development\\Codes\\MyApplication\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_widget_timer_button_background.xml.flat
