package com.timeflow.app.di;

import com.timeflow.app.data.dao.KanbanBoardDao;
import com.timeflow.app.data.dao.KanbanColumnDao;
import com.timeflow.app.data.repository.KanbanRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class RepositoryModule_ProvideKanbanRepositoryFactory implements Factory<KanbanRepository> {
  private final Provider<KanbanBoardDao> kanbanBoardDaoProvider;

  private final Provider<KanbanColumnDao> kanbanColumnDaoProvider;

  public RepositoryModule_ProvideKanbanRepositoryFactory(
      Provider<KanbanBoardDao> kanbanBoardDaoProvider,
      Provider<KanbanColumnDao> kanbanColumnDaoProvider) {
    this.kanbanBoardDaoProvider = kanbanBoardDaoProvider;
    this.kanbanColumnDaoProvider = kanbanColumnDaoProvider;
  }

  @Override
  public KanbanRepository get() {
    return provideKanbanRepository(kanbanBoardDaoProvider.get(), kanbanColumnDaoProvider.get());
  }

  public static RepositoryModule_ProvideKanbanRepositoryFactory create(
      Provider<KanbanBoardDao> kanbanBoardDaoProvider,
      Provider<KanbanColumnDao> kanbanColumnDaoProvider) {
    return new RepositoryModule_ProvideKanbanRepositoryFactory(kanbanBoardDaoProvider, kanbanColumnDaoProvider);
  }

  public static KanbanRepository provideKanbanRepository(KanbanBoardDao kanbanBoardDao,
      KanbanColumnDao kanbanColumnDao) {
    return Preconditions.checkNotNullFromProvides(RepositoryModule.INSTANCE.provideKanbanRepository(kanbanBoardDao, kanbanColumnDao));
  }
}
