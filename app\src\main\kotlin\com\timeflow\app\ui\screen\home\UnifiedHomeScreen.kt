package com.timeflow.app.ui.screen.home

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.*
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material.icons.outlined.*
import androidx.compose.material.ripple.rememberRipple
import androidx.compose.material3.*
import androidx.compose.material3.TabRowDefaults.tabIndicatorOffset
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalView
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import androidx.navigation.compose.currentBackStackEntryAsState
import com.timeflow.app.R
import com.timeflow.app.navigation.AppDestinations
import com.timeflow.app.ui.task.TaskListViewModel
import com.timeflow.app.ui.task.TaskListState
import com.timeflow.app.ui.theme.*
import com.timeflow.app.ui.viewmodel.TimeFlowViewModel
import com.timeflow.app.utils.SystemBarManager
import com.timeflow.app.utils.formatLocalizedDate
import kotlinx.coroutines.launch
import com.timeflow.app.R
import kotlinx.coroutines.delay
import java.time.DayOfWeek
import java.time.LocalDateTime
import android.app.Activity
import android.util.Log
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.rememberScrollState
import com.timeflow.app.ui.screen.task.model.TaskModel as ModelTaskData
import com.timeflow.app.ui.screen.task.model.TaskUrgency
import com.timeflow.app.ui.screen.task.model.formatTimeLeft
import com.timeflow.app.ui.screen.task.model.getUrgencyColor
import java.time.LocalDate
import android.os.Build
import androidx.core.view.WindowCompat
import android.view.WindowManager
import com.timeflow.app.ui.task.components.common.event.EventBus
import com.timeflow.app.ui.task.components.common.event.AppEvent
import androidx.compose.ui.platform.LocalDensity
import com.timeflow.app.utils.RenderOptimizer
import androidx.compose.foundation.gestures.animateScrollBy
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.foundation.gestures.detectTapGestures
import com.timeflow.app.ui.screen.calendar.CalendarViewModel
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.timeflow.app.data.model.Goal
import com.timeflow.app.data.model.GoalPriority
import com.timeflow.app.ui.screen.goal.GoalViewModel
import com.timeflow.app.ui.screen.goal.GoalUiState
import java.time.temporal.ChronoUnit
import android.content.res.Configuration
import com.timeflow.app.data.repository.GoalStats
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.compositeOver
import com.timeflow.app.ui.navigation.LocalNavController
import java.time.format.DateTimeFormatter
import com.timeflow.app.utils.NavigationOptimizer
import com.timeflow.app.ui.optimization.ComposeRecompositionOptimizer
import com.timeflow.app.ui.optimization.ComposeRecompositionOptimizer.collectAsOptimizedState
import com.timeflow.app.ui.optimization.ComposeRecompositionOptimizer.rememberCallback
import com.timeflow.app.ui.optimization.ComposeRecompositionOptimizer.rememberCallback1
import com.timeflow.app.ui.optimization.ComposeRecompositionOptimizer.rememberCallback2
import com.timeflow.app.ui.optimization.ComposeRecompositionOptimizer.rememberVoidCallback
import com.timeflow.app.ui.optimization.ComposeRecompositionOptimizer.rememberDerivedStateOf
import com.timeflow.app.ui.screen.settings.SettingsViewModel

/**
 * 任务列表状态数据类
 */
data class TaskListState(
    val tasks: List<ModelTaskData> = emptyList(),
    val isLoading: Boolean = false,
    val error: String? = null
)

/**
 * 统一的主屏幕 - 整合了HomeScreen和TaskListScreen的功能
 */
@OptIn(ExperimentalMaterial3Api::class, ExperimentalFoundationApi::class)
@Composable
fun UnifiedHomeScreen(
    navController: NavController,
    timeFlowViewModel: TimeFlowViewModel = hiltViewModel(),
    taskViewModel: TaskListViewModel = hiltViewModel(),
    calendarViewModel: CalendarViewModel = hiltViewModel(),
    settingsViewModel: SettingsViewModel = hiltViewModel() // 🔧 添加设置ViewModel
) {
    // 🔧 临时禁用重组监控以减少性能开销
    // ComposeRecompositionOptimizer.RecompositionLogger("UnifiedHomeScreen")
    
    val scope = rememberCoroutineScope()
    
    // 🔧 获取用户信息
    val userInfo by settingsViewModel.userInfo.collectAsOptimizedState()
    
    // 🔧 修复背景色管理 - 直接使用ThemeManager的主题设置
    var currentBackgroundColor by remember { mutableStateOf(Color(0xFFF9F9F9)) } // 默认背景色
    
    // 🔧 在组件初始化时立即从ThemeManager获取当前主题设置
    LaunchedEffect(Unit) {
        // 从ThemeManager获取当前主题偏好
        val themePreference = com.timeflow.app.ui.theme.ThemeManager.userThemePreference.value
        val initialBackgroundColor = Color(themePreference.homePageColor.toArgb())
        currentBackgroundColor = initialBackgroundColor
        
        Log.d("UnifiedHomeScreen", "🎨 初始化首页背景色: ${initialBackgroundColor.toArgb().toString(16)}")
        
        // 请求主题设置更新，确保所有页面同步
        delay(50) // 短暂延迟确保ThemeManager已初始化
        EventBus.tryEmit(AppEvent.ThemeSettingsRequested)
        Log.d("UnifiedHomeScreen", "📡 已请求主题设置更新")
    }
    
    // 🔧 监听主题设置变化 - 优化事件处理
    LaunchedEffect(Unit) {
        EventBus.events.collect { event ->
            when (event) {
                is AppEvent.PageBackgroundChanged -> {
                    if (event.pageName == "home") {
                        try {
                            val colorInt = event.colorArgb.toInt()
                            val newColor = Color(colorInt)
                            
                            Log.d("UnifiedHomeScreen", "🔄 收到首页背景色变更事件: ${event.colorArgb.toString(16)} -> ${newColor.toArgb().toString(16)}")
                            
                            // 立即更新背景色
                            currentBackgroundColor = newColor
                            
                            Log.d("UnifiedHomeScreen", "✅ 首页背景色已更新")
                        } catch (e: Exception) {
                            Log.e("UnifiedHomeScreen", "❌ 背景色转换失败: ${e.message}", e)
                        }
                    }
                }
                is AppEvent.ThemeSettingsChanged -> {
                    if (event.useUnifiedBackground) {
                        try {
                            val colorInt = event.colorArgb.toInt()
                            val newColor = Color(colorInt)
                            
                            Log.d("UnifiedHomeScreen", "🔄 收到统一背景色事件: ${event.colorArgb.toString(16)} -> ${newColor.toArgb().toString(16)}")
                            
                            // 统一背景色时也更新首页背景
                            currentBackgroundColor = newColor
                            
                            Log.d("UnifiedHomeScreen", "✅ 统一背景色已应用到首页")
                        } catch (e: Exception) {
                            Log.e("UnifiedHomeScreen", "❌ 统一背景色转换失败: ${e.message}", e)
                        }
                    }
                }
            }
        }
    }
    
    // 🔧 页面恢复时重新加载背景色设置
    LaunchedEffect(Unit) {
        // 监听组合生命周期，当页面重新变为活跃时刷新设置
        delay(200) // 等待页面完全加载
        
        // 重新从ThemeManager获取最新设置
        val currentThemePreference = com.timeflow.app.ui.theme.ThemeManager.userThemePreference.value
        val refreshedBackgroundColor = Color(currentThemePreference.homePageColor.toArgb())
        
        if (currentBackgroundColor != refreshedBackgroundColor) {
            Log.d("UnifiedHomeScreen", "🔄 页面恢复时发现背景色变化: ${currentBackgroundColor.toArgb().toString(16)} -> ${refreshedBackgroundColor.toArgb().toString(16)}")
            currentBackgroundColor = refreshedBackgroundColor
        } else {
            Log.d("UnifiedHomeScreen", "✅ 页面恢复时背景色一致: ${currentBackgroundColor.toArgb().toString(16)}")
        }
    }
    
    // 🔧 优化2: 使用优化的状态收集器
    val taskListState by taskViewModel.taskListState.collectAsOptimizedState()
    
    // 🔧 优化4: 缓存主要的回调函数
    val handleTaskClick = rememberCallback1(navController) { taskId: String ->
        NavigationOptimizer.safeNavigate(
            navController = navController,
            route = AppDestinations.taskDetailRoute(taskId)
        )
    }
    
    val handleTaskStatusChanged = rememberCallback2(taskViewModel) { taskId: String, isCompleted: Boolean ->
        scope.launch {
            try {
                taskViewModel.updateTaskStatus(taskId, isCompleted)
                Log.d("UnifiedHomeScreen", "任务状态更新: $taskId -> $isCompleted")
            } catch (e: Exception) {
                Log.e("UnifiedHomeScreen", "任务状态更新失败", e)
            }
        }
    }
    
    val handleAddTaskClick = rememberCallback(navController) {
        navController.navigate(AppDestinations.ADD_TASK_ROUTE)
    }
    
    val handleViewAllClick = rememberCallback(navController) {
        NavigationOptimizer.safeNavigate(
            navController = navController,
            route = AppDestinations.TASK_LIST_ROUTE
        )
    }
    
    val handleSettingsClick = rememberCallback(navController) {
        navController.navigate(AppDestinations.SETTINGS_ROUTE)
    }
    
    val handleReflectionClick = rememberCallback(navController) {
        navController.navigate(AppDestinations.REFLECTION_ROUTE)
    }
    
    // 简化懒加载控制 - 使用单一状态减少状态更新和重组
    // 不再需要分阶段显示控制
    
    // 获取Activity引用
    val context = LocalContext.current
    val activity = context as? Activity
    
    // 优化UI配置
    val view = LocalView.current
    LaunchedEffect(Unit) {
        try {
            // 🔧 修复：立即显示所有内容，不使用分阶段延迟
            Log.d("UnifiedHomeScreen", "===== 首页初始化开始 =====")
            
            // 主动触发任务加载
            Log.d("UnifiedHomeScreen", "触发任务数据刷新...")
            taskViewModel.refreshTasks()
            
            // 同步过滤状态为"今天"
            Log.d("UnifiedHomeScreen", "设置过滤状态为今天...")
            taskViewModel.updateSharedFilterState("今天")
            
            Log.d("UnifiedHomeScreen", "✓ 首页初始化完成")
        } catch (e: Exception) {
            // 安全处理异常
            Log.e("UnifiedHomeScreen", "初始化失败", e)
        }
    }
    
    // 🔧 临时禁用任务数据监听以减少重组
    // LaunchedEffect(Unit) {
    //     taskViewModel.taskListState.collect { state ->
    //         // 🔧 优化：只在关键状态变化时输出日志
    //         if (state.isLoading) {
    //             Log.d("UnifiedHomeScreen", "任务数据加载中...")
    //         } else if (state.error != null) {
    //             Log.e("UnifiedHomeScreen", "任务数据加载失败: ${state.error}")
    //         } else {
    //             Log.d("UnifiedHomeScreen", "✓ 任务数据已就绪，共${state.tasks.size}个任务")
    //
    //             // 🔧 统计父任务数量
    //             val parentTaskCount = state.tasks.count { task ->
    //                 task.parentTaskId == null || task.parentTaskId.isBlank()
    //             }
    //             Log.d("UnifiedHomeScreen", "其中父任务${parentTaskCount}个，子任务${state.tasks.size - parentTaskCount}个")
    //         }
    //     }
    // }
    
    // 过滤状态 - 默认选中"今天"
    var selectedFilterIndex by remember { mutableIntStateOf(1) } // 🎯 默认选中索引1（"今天"）
    val filterTabs = remember { listOf("全部", "今天", "未完成", "已完成", "未定期") }
    
    // 🔧 优化5: 使用优化的状态收集器收集过滤任务
    val filteredTasks by taskViewModel.filteredTasksState.collectAsOptimizedState()
    

    
    // 点击反馈状态 - 用于平滑过渡
    var isItemClicked by remember { mutableStateOf(false) }
    var clickedTaskId by remember { mutableStateOf<String?>(null) }
    
    // 提供NavController给CompositionLocal
    CompositionLocalProvider(LocalNavController provides navController) {
        // 🔧 临时禁用性能分析器以减少重组
        // ComposeRecompositionOptimizer.PerformanceAnalyzer("UnifiedHomeScreen_MainContent") {
        Scaffold(
            containerColor = currentBackgroundColor,
            floatingActionButton = {
                // AI助手悬浮按钮已移除
            }
        ) { paddingValues ->
            // 使用Column布局，将标题固定在顶部
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(bottom = paddingValues.calculateBottomPadding())
            ) {
                // 固定的状态栏间距
                Spacer(modifier = Modifier.height(SystemBarManager.getFixedStatusBarHeight()))
                
                // 固定的顶部欢迎区域
                GreetingHeader(
                    userName = userInfo.nickname, // 🔧 使用用户设置的昵称
                    dateTime = LocalDateTime.now(),
                    modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp),
                    onSettingsClick = handleSettingsClick,
                    onReflectionClick = handleReflectionClick
                )
                
                // 记住滚动状态，确保页面启动时在顶部
                val listState = rememberLazyListState()
                
                // 🔧 临时禁用自动滚动以减少重组
                // LaunchedEffect(Unit) {
                //     try {
                //         listState.scrollToItem(0)
                //         Log.d("UnifiedHomeScreen", "页面已滚动到顶部")
                //     } catch (e: Exception) {
                //         Log.e("UnifiedHomeScreen", "滚动到顶部失败", e)
                //     }
                // }
                
                // 可滚动的内容区域
                LazyColumn(
                    state = listState,
                    modifier = Modifier
                        .fillMaxSize()
                        // 使用graphicsLayer优化渲染性能，禁用不必要的图形功能
                        .graphicsLayer(
                            renderEffect = null, 
                            shadowElevation = 0f,
                            clip = false,
                            // 不应用scale可以避免UI元素模糊并减少绘制负担
                            scaleX = 1f,
                            scaleY = 1f,
                            // 禁用alpha动画可以提高渲染性能
                            alpha = 1f
                        ),
                    // 优化滑动性能
                    contentPadding = PaddingValues(0.dp),
                    verticalArrangement = Arrangement.Top,
                    // 启用向后滚动支持以提升性能
                    reverseLayout = false,
                    // 禁用过度滚动效果以提升性能
                    userScrollEnabled = true
                ) {
                
                // AI建议区域 - 直接显示
                item(key = "ai_suggestions") {
                    AITaskSuggestionArea(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 16.dp, vertical = 8.dp),
                        navController = navController
                    )
                }
                
                // 添加间隔
                item(key = "spacer1") { 
                    Spacer(modifier = Modifier.height(8.dp)) 
                }
                
                // 任务管理区域 - 直接显示
                item(key = "task_management") {
                    TaskManagementCard(
                        taskListState = taskListState,
                        filteredTasks = filteredTasks,
                        selectedFilterIndex = selectedFilterIndex,
                        filterTabs = filterTabs,
                        onFilterSelected = rememberCallback1(taskViewModel, filterTabs) { index: Int ->
                            selectedFilterIndex = index
                            taskViewModel.updateSharedFilterState(filterTabs[index])
                        },
                        onTaskClick = handleTaskClick,
                        onTaskStatusChanged = handleTaskStatusChanged,
                        isItemClicked = isItemClicked,
                        clickedTaskId = clickedTaskId,
                        navController = navController,
                        onAddTaskClick = handleAddTaskClick,
                        onViewAllClick = handleViewAllClick,
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 16.dp, vertical = 8.dp)
                    )
                }
                
                // 添加间隔
                item(key = "spacer2") { 
                    Spacer(modifier = Modifier.height(8.dp)) 
                }
                
                // 目标追踪区域 - 直接显示
                item(key = "goal_tracking") {
                    GoalTrackingArea(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 16.dp, vertical = 8.dp),
                        navController = navController
                    )
                }
                
                // 添加间隔
                item(key = "spacer3") { 
                    Spacer(modifier = Modifier.height(8.dp)) 
                }
                
                // 愿望星云区域 - 直接显示
                item(key = "wish_cloud_section") {
                    WishCloudSection(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 16.dp, vertical = 8.dp),
                        navController = navController
                    )
                }
                
                // 添加间隔
                item(key = "spacer4") { 
                    Spacer(modifier = Modifier.height(8.dp)) 
                }
                
                // 感想展示区域 - 直接显示
                item(key = "reflection_section") {
                    HomeReflectionSection(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 16.dp, vertical = 8.dp),
                        navController = navController
                    )
                }
                
                    // 底部填充 - 确保内容可以完全滚动到视图底部
                    item(key = "bottom_space") { 
                        Spacer(modifier = Modifier.height(80.dp)) 
                    }
                }
            }
        }
        // } // ComposeRecompositionOptimizer.PerformanceAnalyzer
    }
}

/**
 * 为标签文本生成自适应颜色
 */
@Composable
fun AdaptiveTabColor(isSelected: Boolean): Color {
    val isDarkTheme = isSystemInDarkTheme()
    
    return if (isSelected) {
        MaterialTheme.colorScheme.primary // 🎨 使用主题色替代固定颜色
    } else {
        if (isDarkTheme) TabTextColor.copy(alpha = 0.7f) else TabTextColor
    }
}

/**
 * 分段过滤器组件
 */
@Composable
fun FilterSegmentGroup(
    options: List<String>,
    selectedIndex: Int,
    onIndexSelected: (Int) -> Unit,
    modifier: Modifier = Modifier
) {
    val backgroundColor = Color(0xFFEAE8F2)
    val selectedColor = MaterialTheme.colorScheme.primary // 🎨 使用主题色替代固定颜色
    
    Row(
        modifier = modifier
            .background(backgroundColor, RoundedCornerShape(30.dp))
            .padding(4.dp),
        horizontalArrangement = Arrangement.SpaceEvenly
    ) {
        options.forEachIndexed { index, option ->
            val isSelected = index == selectedIndex
            
            Box(
                modifier = Modifier
                    .weight(1f)
                    .height(32.dp)
                    .clip(RoundedCornerShape(16.dp))
                    .background(
                        if (isSelected) selectedColor else Color.Transparent
                    )
                    .clickable { onIndexSelected(index) }
                    .padding(horizontal = 4.dp),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = option,
                    style = MaterialTheme.typography.bodyMedium,
                    fontSize = 14.sp,
                    fontWeight = if (isSelected) FontWeight.Medium else FontWeight.Normal,
                    color = if (isSelected) Color.White else Color.DarkGray.copy(alpha = 0.7f)
                )
            }
        }
    }
}

/**
 * 统一的今日内容 - 整合了两个页面的内容
 */
@Composable
fun TodayContentUnified(
    modifier: Modifier = Modifier,
    tasksViewModel: TaskListViewModel
) {
    val context = LocalContext.current
    val navController = LocalNavController.current
    val scrollState = rememberScrollState()
    val scope = rememberCoroutineScope()
    
    // 任务状态收集 - 使用produceState优化状态更新
    val taskListState by tasksViewModel.taskListState.collectAsState()
    
    // 使用remember和derivedStateOf优化计算
    val tasks by remember(taskListState) {
        derivedStateOf { taskListState.tasks }
    }
    
    // 点击状态变量
    var isItemClicked by remember { mutableStateOf(false) }
    var clickedTaskId by remember { mutableStateOf<String?>(null) }
    
    // 过滤状态
    var selectedFilterIndex by remember { mutableStateOf(0) }
    val filterTabs = remember { listOf("全部", "今天", "未完成", "已完成", "未定期") }
    val selectedFilter = remember(selectedFilterIndex) { filterTabs[selectedFilterIndex] }
    
    // 按过滤条件筛选任务 - 使用derivedStateOf优化计算
    val filteredTasks by remember(tasks, selectedFilter) {
        derivedStateOf {
            tasks.asSequence().filter { task ->
                when (selectedFilter) {
                    "今天" -> task.daysLeft == 0
                    "未完成" -> !task.isCompleted
                    "已完成" -> task.isCompleted
                    "未定期" -> task.daysLeft == Int.MAX_VALUE
                    else -> true
                }
            }.toList()
        }
    }
    
    // 处理任务状态更新函数
    val handleTaskStatusChanged = remember {
        { taskId: String, isCompleted: Boolean ->
            tasksViewModel.updateTaskStatus(taskId, isCompleted)
        }
    }
    
    // 懒加载状态控制 - 使用单一状态减少重组
    // 不再需要可见性状态控制，直接显示所有内容
    
    // 内容区域 - 使用Box替代Column减少嵌套层级
    Box(
        modifier = modifier
            .fillMaxSize()
            .padding(horizontal = 16.dp, vertical = 8.dp)
    ) {
        Column(
            modifier = Modifier.fillMaxSize()
        ) {
            // AI建议区域 - 直接显示
            AITaskSuggestionArea(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 8.dp),
                navController = navController
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 任务管理区域 - 直接显示
            TaskManagementCard(
                taskListState = taskListState,
                filteredTasks = filteredTasks,
                selectedFilterIndex = selectedFilterIndex,
                filterTabs = filterTabs,
                onFilterSelected = { index ->
                    selectedFilterIndex = index
                    tasksViewModel.updateSharedFilterState(filterTabs[index])
                },
                onTaskClick = { taskId ->
                    clickedTaskId = taskId
                    isItemClicked = true
                    NavigationOptimizer.safeNavigate(
                        navController = navController,
                        route = AppDestinations.taskDetailRoute(taskId)
                    )
                    isItemClicked = false
                },
                onTaskStatusChanged = handleTaskStatusChanged,
                isItemClicked = isItemClicked,
                clickedTaskId = clickedTaskId,
                navController = navController,
                onAddTaskClick = {
                    navController.navigate(AppDestinations.ADD_TASK_ROUTE)
                },
                onViewAllClick = {
                    // 使用NavigationOptimizer进行淡入导航效果
                    com.timeflow.app.utils.NavigationOptimizer.safeNavigate(
                        navController = navController,
                        route = AppDestinations.TASK_LIST_ROUTE
                    )
                }
            )
            
            Spacer(modifier = Modifier.height(20.dp))
            
            // 目标追踪区域 - 直接显示
            GoalTrackingArea(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 100.dp),
                navController = navController
            )
        }
    }
}

/**
 * 任务管理卡片组件 - 提取为独立组件减少重组
 */
@Composable
private fun TaskManagementCard(
    taskListState: TaskListState,
    filteredTasks: List<ModelTaskData>,
    selectedFilterIndex: Int,
    filterTabs: List<String>,
    onFilterSelected: (Int) -> Unit,
    onTaskClick: (String) -> Unit,
    onTaskStatusChanged: (String, Boolean) -> Unit,
    isItemClicked: Boolean,
    clickedTaskId: String?,
    navController: NavController,
    onAddTaskClick: () -> Unit,
    onViewAllClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    // 提前计算任务分组以减少重组时的计算量
    val topTasks = remember(filteredTasks) {
        Log.d("UnifiedHomeScreen", "===== 开始计算首页显示任务 =====")
        Log.d("UnifiedHomeScreen", "输入任务总数: ${filteredTasks.size}")
        
        // 🔧 关键修复：确保只显示父任务，不显示子任务
        val parentTasksOnly = filteredTasks
            .asSequence()
            .filter { task ->
                // 🎯 核心过滤条件：只显示父任务
                val isParentTask = task.parentTaskId == null || task.parentTaskId.isBlank()
                val shouldDisplayInList = task.displayInTaskList
                
                val shouldShow = isParentTask && shouldDisplayInList
                
                if (!shouldShow) {
                    val reason = when {
                        !isParentTask -> "是子任务(parentTaskId=${task.parentTaskId})"
                        !shouldDisplayInList -> "displayInTaskList=false"
                        else -> "其他原因"
                    }
                    Log.d("UnifiedHomeScreen", "任务 ${task.id}(${task.title}) 被过滤: $reason")
                }
                
                shouldShow
            }
            .sortedWith(
                compareByDescending<ModelTaskData> { task -> 
                    when(task.urgency) {
                        TaskUrgency.CRITICAL -> 4
                        TaskUrgency.HIGH -> 3
                        TaskUrgency.MEDIUM -> 2
                        TaskUrgency.LOW -> 1
                        else -> 0
                    }
                }.thenBy { it.daysLeft }
            )
            .take(3)
            .toList()
            
        Log.d("UnifiedHomeScreen", "过滤后父任务数: ${parentTasksOnly.size}")
        parentTasksOnly.forEach { task ->
            Log.d("UnifiedHomeScreen", "显示任务: ${task.title} (ID: ${task.id}, 父任务ID: ${task.parentTaskId})")
        }
        Log.d("UnifiedHomeScreen", "===== 任务计算完成 =====")
        
        parentTasksOnly
    }
    
    // 注意：不要对每个任务都使用AnimatedVisibility，这会导致性能问题
    // 对整个卡片使用graphicsLayer优化渲染
    ElevatedCard(
        modifier = modifier.graphicsLayer(
            // 禁用不必要的渲染效果以提高性能
            clip = false,
            shadowElevation = 0f
        ),
        shape = RoundedCornerShape(16.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            // 标题和导航按钮
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "任务管理",
                    fontSize = 16.sp, // 减小到16.sp
                    fontWeight = FontWeight.SemiBold,
                    color = MaterialTheme.colorScheme.primary
                )
                
                Icon(
                    imageVector = Icons.Default.ArrowForward,
                    contentDescription = "查看更多",
                    tint = MaterialTheme.colorScheme.primary, // 🎨 使用主题色替代固定颜色
                    modifier = Modifier
                        .size(20.dp)
                        .clickable { onViewAllClick() }
                )
            }
            
            Spacer(modifier = Modifier.height(12.dp)) // 从16.dp减小到12.dp
            
            // 过滤标签栏 - 使用可重复使用的组件
            FilterTabs(
                selectedIndex = selectedFilterIndex,
                tabs = filterTabs,
                onTabSelected = onFilterSelected
            )
            
            Spacer(modifier = Modifier.height(12.dp)) // 从16.dp减小到12.dp
            
            // 任务列表内容 - 高度固定避免布局变化
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(240.dp)
            ) {
                when {
                    taskListState.isLoading -> {
                        // 使用轻量级加载指示器
                        Box(
                            modifier = Modifier.fillMaxSize(),
                            contentAlignment = Alignment.Center
                        ) {
                            CircularProgressIndicator(
                                color = MaterialTheme.colorScheme.primary, // 🎨 使用主题色替代固定颜色
                                modifier = Modifier.size(24.dp),
                                strokeWidth = 2.dp
                            )
                        }
                    }
                    topTasks.isEmpty() -> {
                        // 使用轻量级空状态
                        Box(
                            modifier = Modifier.fillMaxSize(),
                            contentAlignment = Alignment.Center
                        ) {
                            Column(
                                horizontalAlignment = Alignment.CenterHorizontally
                            ) {
                                Icon(
                                    imageVector = Icons.Outlined.TaskAlt,
                                    contentDescription = null,
                                    modifier = Modifier.size(48.dp),
                                    tint = Color.LightGray
                                )
                                
                                Spacer(modifier = Modifier.height(12.dp))
                                
                                Text(
                                    text = "暂无任务",
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = Color.Gray
                                )
                            }
                        }
                    }
                    else -> {
                        // 使用直接绘制任务列表，避免LazyColumn嵌套导致的性能问题
                        Column {
                            topTasks.forEachIndexed { index, task ->
                                OptimizedTaskItemRow(
                                    task = task,
                                    onTaskClick = onTaskClick,
                                    onTaskCompletionToggle = onTaskStatusChanged,
                                    isClicked = clickedTaskId == task.id && isItemClicked
                                )
                                
                                if (index < topTasks.size - 1) {
                                    Divider(
                                        color = Color.LightGray,
                                        thickness = 0.5.dp,
                                        modifier = Modifier.padding(vertical = 8.dp)
                                    )
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

/**
 * 过滤标签栏组件 - 提取为独立组件并优化重组
 */
@Composable
private fun FilterTabs(
    selectedIndex: Int,
    tabs: List<String>,
    onTabSelected: (Int) -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .background(
                color = Color(0xFFF5F5F5),
                shape = RoundedCornerShape(30.dp)
            )
            .padding(4.dp),
        horizontalArrangement = Arrangement.SpaceEvenly
    ) {
        tabs.forEachIndexed { index, tabText ->
            val isSelected = index == selectedIndex
            Box(
                modifier = Modifier
                    .weight(1f)
                    .clip(RoundedCornerShape(30.dp))
                    .background(
                        color = if (isSelected) MaterialTheme.colorScheme.primary else Color(0xFFF5F5F5) // 🎨 使用主题色替代固定颜色
                    )
                    .clickable { onTabSelected(index) }
                    .padding(vertical = 8.dp),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = tabText,
                    style = MaterialTheme.typography.bodySmall, // 从bodyMedium改为bodySmall
                    color = if (isSelected) Color.White else Color.Gray,
                    fontWeight = if (isSelected) FontWeight.Medium else FontWeight.Normal
                )
            }
        }
    }
}

/**
 * 优化的任务项组件 - 减少重组，直接使用基本参数
 */
@Composable
private fun OptimizedTaskItemRow(
    task: ModelTaskData,
    onTaskClick: (String) -> Unit,
    onTaskCompletionToggle: (String, Boolean) -> Unit,
    isClicked: Boolean
) {
    // 取消动画，直接使用固定alpha值
    val currentAlpha = if (isClicked) 0.85f else 1f
    
    // 预计算一些经常使用的值
    val bgColor = remember(task.urgency) {
        when (task.urgency) {
            TaskUrgency.CRITICAL -> Color(0xFFFFF8E1).copy(alpha = 0.3f)
            TaskUrgency.HIGH -> Color(0xFFFFF8E1).copy(alpha = 0.15f)
            else -> Color.Transparent
        }
    }
    
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .alpha(currentAlpha)
            .background(bgColor)
            .clickable(
                onClick = { onTaskClick(task.id) },
                // 使用轻量级的ripple效果提供视觉反馈但不造成性能负担
                indication = rememberRipple(bounded = true, color = MaterialTheme.colorScheme.primary.copy(alpha = 0.2f)), // 🎨 使用主题色替代固定颜色
                interactionSource = remember { MutableInteractionSource() }
            )
            .padding(vertical = 8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 复选框 - 优化绘制而不是使用Checkbox组件
        Box(
            modifier = Modifier
                .size(22.dp)
                .clip(RoundedCornerShape(4.dp))
                .background(if (task.isCompleted) MaterialTheme.colorScheme.primary else Color.White) // 🎨 使用主题色替代固定颜色
                .border(
                    width = 1.dp,
                    color = if (task.isCompleted) MaterialTheme.colorScheme.primary else Color.LightGray, // 🎨 使用主题色替代固定颜色
                    shape = RoundedCornerShape(4.dp)
                )
                .clickable { onTaskCompletionToggle(task.id, !task.isCompleted) },
            contentAlignment = Alignment.Center
        ) {
            if (task.isCompleted) {
                Icon(
                    imageVector = Icons.Default.Check,
                    contentDescription = null,
                    tint = Color.White,
                    modifier = Modifier.size(16.dp)
                )
            }
        }
        
        Spacer(modifier = Modifier.width(12.dp))
        
        // 任务信息 - 避免使用remember重新计算已有的值
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = task.title,
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Medium,
                color = if (task.isCompleted) Color.Gray else Color.DarkGray,
                textDecoration = if (task.isCompleted) TextDecoration.LineThrough else TextDecoration.None,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
            
            Spacer(modifier = Modifier.height(4.dp))
            
            // 任务时间信息 - 使用任务的实际dueDate(开始时间)
            val timeText = if (task.isCompleted) {
                "已完成"
            } else if (task.dueDate != null) {
                // 从任务的实际dueDate获取时间
                val now = LocalDateTime.now().toLocalDate()
                val dueDate = task.dueDate
                val time = dueDate.format(DateTimeFormatter.ofPattern("HH:mm"))
                
                // 根据日期显示"今天"、"明天"或具体日期
                when {
                    dueDate.toLocalDate().isEqual(now) -> "今天 $time"
                    dueDate.toLocalDate().isEqual(now.plusDays(1)) -> "明天 $time"
                    dueDate.toLocalDate().isEqual(now.plusDays(2)) -> "后天 $time"
                    else -> "${dueDate.monthValue}月${dueDate.dayOfMonth}日 $time"
                }
            } else {
                "未设置时间"
            }
            
            Text(
                text = timeText,
                style = MaterialTheme.typography.bodySmall,
                color = Color.Gray,
                fontSize = 12.sp
            )
        }
        
        // 星标标记 - 直接判断而不用remember
        if (task.urgency == TaskUrgency.CRITICAL || task.urgency == TaskUrgency.HIGH) {
            Icon(
                imageVector = Icons.Default.Star,
                contentDescription = "重要任务",
                tint = if (task.urgency == TaskUrgency.CRITICAL) 
                    Color(0xFFFFC107) else Color(0xFFFFD54F),
                modifier = Modifier.size(20.dp)
            )
        }
        
        Spacer(modifier = Modifier.width(8.dp))
        
        // 更多操作按钮
        Icon(
            imageVector = Icons.Default.MoreVert,
            contentDescription = "更多操作",
            tint = Color.Gray,
            modifier = Modifier.size(20.dp)
        )
    }
}

/**
 * 顶部问候区域
 */
@Composable
fun GreetingHeader(
    userName: String,
    dateTime: LocalDateTime,
    modifier: Modifier = Modifier,
    onSettingsClick: () -> Unit = {},
    onReflectionClick: () -> Unit = {}
) {
    val context = LocalContext.current
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Column(
            verticalArrangement = Arrangement.spacedBy(2.dp)
        ) {
            // 显示问候语
            Text(
                text = "$userName，今天怎么样呢",
                style = MaterialTheme.typography.headlineSmall,
                fontSize = 19.sp,
                fontWeight = FontWeight.W500,
                letterSpacing = 0.15.sp,
                color = MaterialTheme.colorScheme.primary
            )
            
            // 显示日期
            Text(
                text = dateTime.formatLocalizedDate(context),
                style = MaterialTheme.typography.bodyMedium,
                fontSize = 12.sp,
                letterSpacing = 0.2.sp,
                color = TextSecondary.copy(alpha = 0.8f)
            )
        }
        
        // 操作按钮
        Row(
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 感想按钮
            Box(
                modifier = Modifier
                    .size(32.dp)
                    .background(MaterialTheme.colorScheme.primary.copy(alpha = 0.7f), CircleShape) // 🎨 使用主题色替代固定颜色
                    .clickable(onClick = onReflectionClick),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = Icons.Default.Create,
                    contentDescription = stringResource(R.string.content_desc_reflection),
                    tint = Color.White,
                    modifier = Modifier.size(20.dp)
                )
            }
            
            // 设置按钮
            Box(
                modifier = Modifier
                    .size(32.dp)
                    .background(MaterialTheme.colorScheme.primary.copy(alpha = 0.7f), CircleShape) // 🎨 使用主题色替代固定颜色
                    .clickable(onClick = onSettingsClick),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = Icons.Filled.Settings,
                    contentDescription = "设置",
                    tint = Color.White,
                    modifier = Modifier.size(20.dp)
                )
            }
        }
    }
}



/**
 * 健康内容
 */
@Composable
fun HealthContent() {
    // 取消所有动画，直接显示内容
    Box(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = "健康内容开发中...",
            style = MaterialTheme.typography.titleMedium,
            color = TextPrimary
        )
    }
}

@Composable
fun AITaskSuggestionArea(
    modifier: Modifier = Modifier,
    navController: NavController = LocalNavController.current
) {
    ElevatedCard(
        shape = RoundedCornerShape(16.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        ),
        modifier = modifier.clickable { 
            // 点击整个卡片跳转到AI助手页面
            navController.navigate(AppDestinations.AI_ASSISTANT_ROUTE)
        }
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Row(
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 4.dp) // 从8.dp减小到4.dp
            ) {
                Text(
                    text = stringResource(id = R.string.ai_assistant_header),
                    fontSize = 16.sp, // 从18.sp减小到16.sp
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.primary // 🎨 使用主题色替代固定颜色
                )
                Box(
                    modifier = Modifier
                        .size(20.dp) // 从24.dp减小到20.dp
                        .clip(CircleShape)
                        .background(color = MaterialTheme.colorScheme.primary) // 🎨 使用主题色替代固定颜色
                        .clickable { 
                            // 点击跳转到AI复盘页面
                            navController.navigate(AppDestinations.AI_REVIEW_ROUTE) 
                        },
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        painter = painterResource(id = R.drawable.cat),
                        contentDescription = "AI复盘",
                        tint = Color.White,
                        modifier = Modifier.size(12.dp) // 从16.dp减小到12.dp
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(8.dp)) // 从12.dp减小到8.dp
            
            Text(
                text = "任务拆分建议",
                style = MaterialTheme.typography.bodySmall, // 从bodyMedium改为bodySmall
                fontWeight = FontWeight.Medium,
                color = Color.DarkGray
            )
            
            Spacer(modifier = Modifier.height(6.dp)) // 从8.dp减小到6.dp
            
            Text(
                text = "\"设计产品原型\"任务可拆分为:",
                style = MaterialTheme.typography.bodySmall, // 从bodyMedium改为bodySmall
                color = Color.Gray
            )
            
            Spacer(modifier = Modifier.height(8.dp)) // 从12.dp减小到8.dp
            
            // 只显示两个建议项，减少高度
            TaskSuggestionItem(text = "用户需求分析 (2小时)")
            TaskSuggestionItem(text = "界面草图设计 (1.5小时)")
            
            Spacer(modifier = Modifier.height(8.dp)) // 从12.dp减小到8.dp
            
            Text(
                text = "点击查看详情 →",
                style = MaterialTheme.typography.labelSmall, // 从bodySmall改为labelSmall
                color = MaterialTheme.colorScheme.primary, // 🎨 使用主题色替代固定颜色
                modifier = Modifier.align(Alignment.End)
            )
        }
    }
}

@Composable
private fun TaskSuggestionItem(text: String) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier.padding(vertical = 2.dp) // 从4.dp减小到2.dp
    ) {
        Box(
            modifier = Modifier
                .size(6.dp) // 从8.dp减小到6.dp
                .background(MaterialTheme.colorScheme.primary.copy(alpha = 0.7f), CircleShape) // 🎨 使用主题色替代固定颜色
        )
        
        Spacer(modifier = Modifier.width(8.dp)) // 从12.dp减小到8.dp
        
        Text(
            text = text,
            style = MaterialTheme.typography.bodySmall, // 从bodyMedium改为bodySmall
            color = Color.Gray
        )
    }
}

/**
 * 目标追踪区域 - 高性能优化版本
 */
@Composable
fun GoalTrackingArea(
    modifier: Modifier = Modifier,
    navController: NavController = LocalNavController.current
) {
    val goalViewModel: GoalViewModel = hiltViewModel()
    
    // 使用ViewModel中预计算的Flow，避免UI线程计算
    val uiState = goalViewModel.uiState.value
    val stats = goalViewModel.stats.value
    
    // 使用预计算的目标列表代替在UI中计算
    val allGoals = goalViewModel.goals.value
    
    // 按开始时间排序的前五个目标
    val topFiveGoals = remember(allGoals) {
        allGoals.sortedBy { 
            it.startDate ?: LocalDateTime.MAX 
        }.take(5)
    }
    
    // 🔧 临时禁用目标数据加载以减少重组
    // LaunchedEffect(Unit) {
    //     // 检查数据是否已存在，避免不必要的数据加载
    //     if (goalViewModel.goals.value.isEmpty()) {
    //         goalViewModel.loadGoals()
    //     }
    //     if (goalViewModel.stats.value == null) {
    //         goalViewModel.loadStats()
    //     }
    // }
    
    // 使用RenderOptimizer优化渲染性能
    val cardModifier = modifier
        .fillMaxWidth()
        // 使用graphicsLayer高级属性显著提升渲染性能
        .graphicsLayer(
            // 禁用剪切提高性能
            clip = false,
            // 禁用阴影渲染提高性能
            shadowElevation = 0f,
            // 轻量级缓存层
            renderEffect = null,
            // 在绘制之前应用透明度
            alpha = 0.99f
        )
    
    // 使用key策略优化重组，仅当关键数据变化时重组
    key(allGoals.size) {
        ElevatedCard(
            shape = RoundedCornerShape(16.dp),
            elevation = CardDefaults.cardElevation(defaultElevation = 0.dp),
            colors = CardDefaults.cardColors(
                containerColor = Color.White
            ),
            // 不使用animateContentSize，而是采用固定高度策略减少布局计算
            modifier = cardModifier
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    // 固定最小高度保证UI稳定性，减少因内容变化引起的布局重新计算
                    .heightIn(min = 200.dp)
                    .padding(16.dp)
            ) {
                // 标题和按钮行 - 独立组件，有独立的重组边界
                GoalTrackingHeader(
                    onAddGoalClick = {
                        navController.navigate(AppDestinations.ADD_GOAL_ROUTE)
                    },
                    onViewAllClick = {
                        navController.navigate(AppDestinations.GOAL_MANAGEMENT)
                    }
                )
                
                // 统计数据行 - 独立组件，有独立的重组边界
                OptimizedStatsArea(stats = stats)
                
                Spacer(modifier = Modifier.height(12.dp)) // 从16.dp减小到12.dp
                
                // 目标列表内容 - 显示前五个按开始时间排序的目标
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .heightIn(min = 120.dp)
                ) {
                    when {
                        // 加载状态
                        uiState is GoalUiState.Loading -> {
                            Box(
                                modifier = Modifier.fillMaxSize(),
                                contentAlignment = Alignment.Center
                            ) {
                                CircularProgressIndicator(
                                    color = MaterialTheme.colorScheme.primary, // 🎨 使用主题色替代固定颜色
                                    modifier = Modifier.size(24.dp),
                                    strokeWidth = 2.dp
                                )
                            }
                        }
                        // 空状态
                        allGoals.isEmpty() -> {
                            Box(
                                modifier = Modifier.fillMaxSize(),
                                contentAlignment = Alignment.Center
                            ) {
                                EmptyGoalsPlaceholder()
                            }
                        }
                        // 目标列表 - 简洁列表显示
                        else -> {
                            SimplifiedGoalList(
                                goals = topFiveGoals,
                                onGoalClick = { goalId ->
                                    navController.navigate(AppDestinations.goalDetailRoute(goalId))
                                }
                            )
                        }
                    }
                }
            }
        }
    }
}

/**
 * 简化的目标列表显示 - 只显示标题，按照"-目标1"格式
 */
@Composable
private fun SimplifiedGoalList(
    goals: List<Goal>,
    onGoalClick: (String) -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp)
    ) {
        if (goals.isEmpty()) {
            Text(
                text = "暂无目标，点击右上角添加",
                style = MaterialTheme.typography.bodyMedium,
                color = Color.Gray,
                modifier = Modifier.padding(vertical = 8.dp)
            )
        } else {
            Text(
                text = "我的目标：",
                style = MaterialTheme.typography.bodySmall, // 从bodyMedium改为bodySmall
                fontWeight = FontWeight.Medium,
                color = Color.DarkGray,
                modifier = Modifier.padding(bottom = 8.dp) // 从12.dp减小到8.dp
            )
            
            goals.forEach { goal ->
                SimplifiedGoalItem(
                    goal = goal,
                    onGoalClick = { onGoalClick(goal.id) }
                )
            }
        }
    }
}

/**
 * 简化的目标项组件 - 简洁风格
 */
@Composable
private fun SimplifiedGoalItem(
    goal: Goal,
    onGoalClick: () -> Unit
) {
    val isCompleted = remember(goal.progress) { goal.progress >= 1.0f }
    
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier
            .fillMaxWidth()
            .clickable(
                interactionSource = remember { MutableInteractionSource() },
                indication = rememberRipple(bounded = false, color = MaterialTheme.colorScheme.primary), // 🎨 使用主题色替代固定颜色
                onClick = onGoalClick
            )
            .padding(vertical = 6.dp)
    ) {
        // 目标前缀符号
        Text(
            text = "- ",
            style = MaterialTheme.typography.bodySmall, // 从bodyMedium改为bodySmall
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.primary // 🎨 使用主题色替代固定颜色
        )
        
        // 目标标题
        Text(
            text = goal.title,
            style = MaterialTheme.typography.bodySmall, // 从bodyMedium改为bodySmall
            fontWeight = FontWeight.Normal,
            color = if (isCompleted) Color.Gray else Color.DarkGray,
            textDecoration = if (isCompleted) TextDecoration.LineThrough else TextDecoration.None,
            maxLines = 1,
            overflow = TextOverflow.Ellipsis,
            modifier = Modifier.weight(1f)
        )
        
        // 显示截止日期或状态
        val daysLeft = goal.dueDate?.let {
            ChronoUnit.DAYS.between(LocalDateTime.now(), it).toInt().coerceAtLeast(0)
        } ?: -1
        
        val statusText = when {
            isCompleted -> "已完成"
            daysLeft == 0 -> "今日"
            daysLeft > 0 -> "${daysLeft}天"
            else -> ""
        }
        
        if (statusText.isNotEmpty()) {
            Text(
                text = statusText,
                style = MaterialTheme.typography.bodySmall,
                color = when {
                    isCompleted -> MaterialTheme.colorScheme.primary // 🎨 使用主题色替代固定颜色
                    daysLeft == 0 -> Color(0xFFE57373) // 红色
                    daysLeft in 1..3 -> Color(0xFFFFB74D) // 橙色
                    else -> Color.Gray
                },
                fontSize = 12.sp,
                modifier = Modifier.padding(start = 8.dp)
            )
        }
    }
}

/**
 * 优化的统计区域 - 使用记忆化技术隔离重组
 */
@Composable
private fun OptimizedStatsArea(stats: GoalStats?) {
    if (stats != null) {
        Spacer(modifier = Modifier.height(12.dp))
        GoalStatsRow(stats = stats)
    }
}

/**
 * 优化的目标内容容器 - 根据不同状态展示不同内容
 */
@Composable
private fun OptimizedGoalListContainer(
    uiState: GoalUiState,
    goals: List<Goal>,
    sortedGoals: List<Goal>,
    onGoalClick: (String) -> Unit,
    onProgressChange: (String, Float) -> Unit
) {
    // 固定高度避免布局闪烁
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .heightIn(min = 120.dp)
    ) {
        when {
            // 加载状态
            uiState is GoalUiState.Loading -> {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator(
                        color = MaterialTheme.colorScheme.primary, // 🎨 使用主题色替代固定颜色
                        modifier = Modifier.size(24.dp),
                        strokeWidth = 2.dp
                    )
                }
            }
            // 空状态
            goals.isEmpty() -> {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    EmptyGoalsPlaceholder()
                }
            }
            // 无到期目标状态
            sortedGoals.isEmpty() -> {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    NoUpcomingGoalsMessage()
                }
            }
            // 目标列表 - 使用虚拟化列表
            else -> {
                VirtualizedGoalList(
                    goals = sortedGoals,
                    onGoalClick = onGoalClick,
                    onProgressChange = onProgressChange
                )
            }
        }
    }
}

/**
 * 虚拟化目标列表 - 使用LazyColumn高效渲染
 */
@Composable
private fun VirtualizedGoalList(
    goals: List<Goal>,
    onGoalClick: (String) -> Unit,
    onProgressChange: (String, Float) -> Unit
) {
    // 使用简单的Column替代LazyColumn避免嵌套滚动问题
    // 由于目标数量有限，这样做不会影响性能
    Column(
        modifier = Modifier
            .fillMaxWidth()
            // 添加固定高度避免无限约束问题
            .heightIn(max = 400.dp)
    ) {
        goals.forEachIndexed { index, goal ->
            // 为每个Item增加记忆化处理，隔离重组
            HighPerformanceGoalItem(
                goal = goal,
                onGoalClick = { onGoalClick(goal.id) },
                onProgressChange = { progress -> onProgressChange(goal.id, progress) }
            )
            
            // 分隔线仅在非最后一项显示
            if (index < goals.size - 1) {
                Divider(
                    color = Color.LightGray,
                    thickness = 0.5.dp,
                    modifier = Modifier.padding(vertical = 8.dp)
                )
            }
        }
    }
}

/**
 * 高性能目标项 - 使用内部记忆化减少重组和计算
 */
@Composable
private fun HighPerformanceGoalItem(
    goal: Goal,
    onGoalClick: () -> Unit,
    onProgressChange: (Float) -> Unit
) {
    // 预计算所有状态值，避免重复计算
    // 使用remember缓存所有计算结果，避免重组时重新计算
    val isCompleted = remember(goal.progress) { goal.progress >= 1.0f }
    
    // 将多个计算合并在一起，减少remember调用和lambda创建
    val (daysLeft, statusText, statusColor) = remember(goal.dueDate, isCompleted) {
        val days = goal.dueDate?.let {
            ChronoUnit.DAYS.between(LocalDateTime.now(), it).toInt().coerceAtLeast(0)
        } ?: -1
        
        val text = when {
            isCompleted -> "已完成"
            days < 0 -> "无截止日期"
            days == 0 -> "今日截止"
            else -> "剩余${days}天"
        }
        
        val color = Color.Gray // 临时使用，后面会在Text组件中直接使用MaterialTheme.colorScheme.primary
        
        Triple(days, text, color)
    }
    
    // 取消动画，直接使用progress值
    val currentProgress = goal.progress
    
    // 使用固定高度布局减少测量开销
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .height(78.dp) // 固定高度避免每次重组都重新测量
            // 使用无涟漪点击提高性能
            .clickable(
                interactionSource = remember { MutableInteractionSource() },
                indication = null,
                onClick = onGoalClick
            )
            .padding(vertical = 6.dp)
            // 使用graphicsLayer优化每个Item的渲染性能
            .graphicsLayer(
                clip = false,
                shadowElevation = 0f
            ),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 使用记忆化的复选框组件
        val checkboxState = remember(isCompleted) {
            isCompleted
        }
        GoalCheckbox(
            isCompleted = checkboxState,
            onToggle = { onProgressChange(if (isCompleted) 0.0f else 1.0f) }
        )
        
        Spacer(modifier = Modifier.width(12.dp))
        
        // 标题和进度区域
        Column(
            modifier = Modifier
                .weight(1f)
                // 固定高度避免内容变化导致的高度变化
                .heightIn(min = 60.dp)
        ) {
            // 使用固定参数的文本，减少参数计算
            Text(
                text = goal.title,
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Medium,
                color = if (isCompleted) Color.Gray else Color.DarkGray,
                textDecoration = if (isCompleted) TextDecoration.LineThrough else TextDecoration.None,
                maxLines = 2,
                overflow = TextOverflow.Ellipsis
            )
            
            Spacer(modifier = Modifier.height(6.dp))
            
            // 进度指示器使用简单实现，减少渲染复杂度
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(4.dp) // 减小高度,减少渲染面积
                    .clip(RoundedCornerShape(2.dp))
                    .background(Color.LightGray.copy(alpha = 0.25f)) // 降低透明度,减少混合计算
            ) {
                Box(
                    modifier = Modifier
                        .fillMaxHeight()
                        .fillMaxWidth(currentProgress)
                        .clip(RoundedCornerShape(2.dp))
                        .background(MaterialTheme.colorScheme.primary) // 🎨 使用主题色替代固定颜色
                )
            }
            
            Spacer(modifier = Modifier.height(4.dp))
            
            // 状态行
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 使用预计算的文本和颜色
                Text(
                    text = statusText,
                    style = MaterialTheme.typography.bodySmall,
                    color = when {
                        isCompleted -> MaterialTheme.colorScheme.primary // 🎨 使用主题色替代固定颜色
                        daysLeft == 0 -> Color(0xFFE57373) // 红色
                        daysLeft in 1..3 -> Color(0xFFFFB74D) // 橙色
                        else -> Color.Gray
                    },
                    fontSize = 11.sp // 减小字体,减少文字渲染开销
                )
                
                // 优先级标记
                if (goal.priority.value > GoalPriority.LOW.value) {
                    // 预计算优先级颜色,减少计算
                    val priorityColor = remember(goal.priority) {
                        when (goal.priority) {
                            GoalPriority.URGENT -> Color(0xFFE57373)
                            GoalPriority.HIGH -> Color(0xFFFF9800)
                            GoalPriority.MEDIUM -> Color(0xFFFBC02D)
                            else -> Color.Gray
                        }
                    }
                    
                    Box(
                        modifier = Modifier
                            .size(6.dp) // 减小大小,减少渲染面积
                            .background(priorityColor, CircleShape)
                    )
                }
            }
        }
        
        // AI图标,仅在需要时显示
        if (goal.hasAiBreakdown) {
            Spacer(modifier = Modifier.width(8.dp))
            Icon(
                imageVector = Icons.Outlined.Psychology,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.primary.copy(alpha = 0.6f), // 🎨 使用主题色替代固定颜色
                modifier = Modifier.size(16.dp) // 减小图标大小
            )
        }
    }
}

/**
 * 抽取的重用组件 - 目标复选框
 */
@Composable
private fun GoalCheckbox(
    isCompleted: Boolean,
    onToggle: () -> Unit
) {
    Box(
        modifier = Modifier
            .size(22.dp)
            .clip(RoundedCornerShape(4.dp))
            .background(if (isCompleted) MaterialTheme.colorScheme.primary else Color.White) // 🎨 使用主题色替代固定颜色
            .border(
                width = 1.dp,
                color = if (isCompleted) MaterialTheme.colorScheme.primary else Color.LightGray, // 🎨 使用主题色替代固定颜色
                shape = RoundedCornerShape(4.dp)
            )
            .clickable(
                interactionSource = remember { MutableInteractionSource() },
                indication = null,
                onClick = onToggle
            ),
        contentAlignment = Alignment.Center
    ) {
        if (isCompleted) {
            Icon(
                imageVector = Icons.Default.Check,
                contentDescription = null,
                tint = Color.White,
                modifier = Modifier.size(16.dp)
            )
        }
    }
}

/**
 * 抽取的重用组件 - 优先级指示点
 */
@Composable
private fun GoalPriorityDot(priority: GoalPriority) {
    // 使用静态颜色映射避免计算
    val priorityColor = when (priority) {
        GoalPriority.URGENT -> Color(0xFFE57373)
        GoalPriority.HIGH -> Color(0xFFFF9800)
        GoalPriority.MEDIUM -> Color(0xFFFBC02D)
        else -> Color.Gray
    }
    
    Box(
        modifier = Modifier
            .size(8.dp)
            .background(priorityColor, CircleShape)
    )
}

@Composable
fun EmptyGoalsPlaceholder() {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            Icon(
                imageVector = Icons.Outlined.Flag,
                contentDescription = null,
                modifier = Modifier.size(48.dp),
                tint = Color.LightGray
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            Text(
                text = "暂无目标",
                style = MaterialTheme.typography.bodyMedium,
                color = Color.Gray,
                fontWeight = FontWeight.Medium
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = "点击右上角添加新目标",
                style = MaterialTheme.typography.bodySmall,
                color = Color.Gray.copy(alpha = 0.7f)
            )
        }
    }
}

@Composable
fun CategoryHeader(category: String) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        val iconRes = when (category) {
            "收件箱" -> Icons.Outlined.Inbox
            "工作" -> Icons.Outlined.Work 
            "个人" -> Icons.Outlined.Person
            "购物" -> Icons.Outlined.ShoppingCart
            "健康" -> Icons.Outlined.Favorite
            else -> Icons.Outlined.Label
        }
        
        Icon(
            imageVector = iconRes,
            contentDescription = null,
            tint = MaterialTheme.colorScheme.primary,
            modifier = Modifier.size(18.dp)
        )
        
        Spacer(modifier = Modifier.width(8.dp))
        
        Text(
            text = category,
            style = MaterialTheme.typography.labelMedium,
            color = MaterialTheme.colorScheme.onSurface
        )
    }
}

@Composable
fun EnhancedTaskItem(
    task: ModelTaskData,
    onTaskClick: (String) -> Unit,
    onTaskCompletionToggle: (String, Boolean) -> Unit
) {
    val backgroundColor = if (task.isCompleted) {
        MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.5f)
    } else {
        MaterialTheme.colorScheme.surface
    }
    
    val textColor = if (task.isCompleted) {
        MaterialTheme.colorScheme.onSurface.copy(alpha = 0.5f)
    } else {
        MaterialTheme.colorScheme.onSurface
    }
    
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(8.dp))
            .background(backgroundColor)
            .clickable { onTaskClick(task.id) }
            .padding(horizontal = 12.dp, vertical = 8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Checkbox(
            checked = task.isCompleted,
            onCheckedChange = { isChecked ->
                onTaskCompletionToggle(task.id, isChecked)
            },
            colors = CheckboxDefaults.colors(
                checkedColor = MaterialTheme.colorScheme.primary,
                uncheckedColor = MaterialTheme.colorScheme.outline
            )
        )
        
        Spacer(modifier = Modifier.width(8.dp))
        
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = task.title,
                style = MaterialTheme.typography.bodyMedium,
                color = textColor,
                textDecoration = if (task.isCompleted) TextDecoration.LineThrough else TextDecoration.None
            )
            
            if (task.dueDate != null) {
                Spacer(modifier = Modifier.height(4.dp))
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Outlined.CalendarToday,
                        contentDescription = null,
                        modifier = Modifier.size(14.dp),
                        tint = MaterialTheme.colorScheme.primary.copy(alpha = 0.7f)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    
                    // 显示具体的开始时间
                    val now = LocalDateTime.now().toLocalDate()
                    val dueDate = task.dueDate
                    val time = dueDate.format(DateTimeFormatter.ofPattern("HH:mm"))
                    
                    val timeText = when {
                        dueDate.toLocalDate().isEqual(now) -> "今天 $time"
                        dueDate.toLocalDate().isEqual(now.plusDays(1)) -> "明天 $time"
                        dueDate.toLocalDate().isEqual(now.plusDays(2)) -> "后天 $time"
                        else -> "${dueDate.monthValue}月${dueDate.dayOfMonth}日 $time"
                    }
                    
                    Text(
                        text = timeText,
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.7f)
                    )
                }
            } else if (task.daysLeft >= 0) {
                // 兼容旧的显示方式
                Spacer(modifier = Modifier.height(4.dp))
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Outlined.CalendarToday,
                        contentDescription = null,
                        modifier = Modifier.size(14.dp),
                        tint = MaterialTheme.colorScheme.primary.copy(alpha = 0.7f)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(
                        text = formatTimeLeft(task.daysLeft),
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.7f)
                    )
                }
            }
        }
        
        // 紧急程度指示器
        Box(
            modifier = Modifier
                .size(8.dp)
                .background(getUrgencyColor(task.urgency), CircleShape)
        )
    }
}

/**
 * 目标统计项组件
 */
@Composable
fun GoalStatItem(
    count: Int,
    label: String,
    modifier: Modifier = Modifier
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = modifier
    ) {
        Text(
            text = count.toString(),
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.primary // 🎨 使用主题色替代固定颜色
        )
        
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = Color.Gray
        )
    }
}

/**
 * 目标追踪头部组件
 */
@Composable
private fun GoalTrackingHeader(
    onAddGoalClick: () -> Unit,
    onViewAllClick: () -> Unit
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = "目标追踪",
            fontSize = 16.sp, // 减小到16.sp
            fontWeight = FontWeight.SemiBold,
            color = MaterialTheme.colorScheme.primary
        )
        
        Row(
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "+ 添加目标",
                style = MaterialTheme.typography.bodySmall, // 从bodyMedium改为bodySmall
                color = MaterialTheme.colorScheme.primary, // 🎨 使用主题色替代固定颜色
                modifier = Modifier.clickable(
                    interactionSource = remember { MutableInteractionSource() },
                    indication = null,
                    onClick = onAddGoalClick
                )
            )
            
            Spacer(modifier = Modifier.width(8.dp))
            
            Icon(
                imageVector = Icons.Default.ArrowForward,
                contentDescription = "查看更多",
                tint = MaterialTheme.colorScheme.primary, // 🎨 使用主题色替代固定颜色
                modifier = Modifier
                    .size(20.dp)
                    .clickable(
                        interactionSource = remember { MutableInteractionSource() },
                        indication = null, 
                        onClick = onViewAllClick
                    )
            )
        }
    }
}

/**
 * 无到期目标消息组件
 */
@Composable
private fun NoUpcomingGoalsMessage() {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Icon(
            imageVector = Icons.Outlined.EventAvailable,
            contentDescription = null,
            modifier = Modifier.size(48.dp),
            tint = Color.LightGray
        )
        
        Spacer(modifier = Modifier.height(12.dp))
        
        Text(
            text = "暂无即将到期的目标",
            style = MaterialTheme.typography.bodyMedium,
            color = Color.Gray,
            fontWeight = FontWeight.Medium
        )
    }
}

/**
 * 目标统计行组件 - 优化版本，减少不必要的重组
 */
@Composable
private fun GoalStatsRow(stats: GoalStats?) {
    stats?.let { s ->
        Row(
            modifier = Modifier
                .fillMaxWidth()
                // 固定高度减少布局计算
                .height(50.dp),
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            // 使用稳定的key避免重组
            key(s.totalGoals) {
                OptimizedGoalStatItem(
                    count = s.totalGoals,
                    label = "总目标",
                    modifier = Modifier.weight(1f)
                )
            }
            
            key(s.aiBreakdownGoals) {
                OptimizedGoalStatItem(
                    count = s.aiBreakdownGoals,
                    label = "AI拆解",
                    modifier = Modifier.weight(1f)
                )
            }
            
            key(s.totalSubTasks) {
                OptimizedGoalStatItem(
                    count = s.totalSubTasks,
                    label = "子任务",
                    modifier = Modifier.weight(1f)
                )
            }
        }
    }
}

/**
 * 优化的目标统计项组件 - 使用固定大小并避免不必要计算
 */
@Composable
private fun OptimizedGoalStatItem(
    count: Int,
    label: String,
    modifier: Modifier = Modifier
) {
    // 使用固定大小的布局
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = modifier
    ) {
        // 避免字体计算
        Text(
            text = count.toString(),
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.primary // 🎨 使用主题色替代固定颜色
        )
        
        // 使用固定大小的文本显示
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = Color.Gray
        )
    }
}

/**
 * 愿望星云区域组件
 * 第2级入口：在概览页面的日常查看区域
 */
@Composable
fun WishCloudSection(
    modifier: Modifier = Modifier,
    navController: NavController
) {
    val wishListViewModel: com.timeflow.app.ui.viewmodel.WishListViewModel = hiltViewModel()
    val wishUiState by wishListViewModel.uiState.collectAsState()
    
    // 🔧 临时禁用导航状态监听以减少重组
    // val navBackStackEntry by navController.currentBackStackEntryAsState()
    // LaunchedEffect(navBackStackEntry) {
    //     // 每次回到首页时刷新愿望数据
    //     android.util.Log.d("WishCloudSection", "检测到导航状态变化，刷新愿望数据")
    //     try {
    //         wishListViewModel.loadWishes()
    //         android.util.Log.d("WishCloudSection", "愿望数据刷新完成")
    //     } catch (e: Exception) {
    //         android.util.Log.e("WishCloudSection", "愿望数据刷新失败: ${e.message}")
    //     }
    // }
    
    // 🔧 修复：不仅过滤ACTIVE状态，也显示其他状态的愿望
    val topWishes = remember(wishUiState.wishes) {
        val activeWishes = wishUiState.wishes.filter { it.status == com.timeflow.app.data.model.WishStatus.ACTIVE }
        android.util.Log.d("WishCloudSection", "活跃愿望筛选结果: ${activeWishes.size} 个活跃愿望")
        
        // 如果没有活跃愿望，显示最近的愿望（任何状态）
        if (activeWishes.isEmpty()) {
            android.util.Log.d("WishCloudSection", "没有活跃愿望，显示最近愿望")
            wishUiState.wishes
                .sortedByDescending { it.updatedAt }
                .take(3)
        } else {
            activeWishes
                .sortedByDescending { it.priority }
                .take(3)
        }
    }
    
    // 🔧 临时禁用愿望数据变化监听以减少重组
    // LaunchedEffect(wishUiState.wishes) {
    //     android.util.Log.d("WishCloudSection", "检测到愿望数据变化，当前愿望数量: ${wishUiState.wishes.size}")
    //     if (wishUiState.wishes.isNotEmpty()) {
    //         wishUiState.wishes.forEach { wish ->
    //             android.util.Log.d("WishCloudSection", "愿望: ${wish.title}, 状态: ${wish.status}")
    //         }
    //     }
    // }
    
    ElevatedCard(
        modifier = modifier,
        shape = RoundedCornerShape(16.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            // 标题和导航按钮
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.Star,
                        contentDescription = null,
                        tint = Color(0xFFFFD700), // 金色星星
                        modifier = Modifier.size(20.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = stringResource(R.string.wish_cloud),
                        fontSize = 16.sp, // 减小到16.sp
                        fontWeight = FontWeight.SemiBold,
                        color = MaterialTheme.colorScheme.primary
                    )
                }
                
                Icon(
                    imageVector = Icons.Default.ArrowForward,
                    contentDescription = stringResource(R.string.view_wish_pool),
                    tint = MaterialTheme.colorScheme.primary, // 🎨 使用主题色替代固定颜色
                    modifier = Modifier
                        .size(20.dp)
                        .clickable { 
                            navController.navigate(AppDestinations.WISH_LIST_ROUTE)
                        }
                )
            }
            
            Spacer(modifier = Modifier.height(12.dp)) // 从16.dp减小到12.dp
            
            // 愿望内容
            if (topWishes.isNotEmpty()) {
                // 显示前3个愿望
                Column(
                    verticalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    topWishes.forEach { wish ->
                        WishCloudItem(
                            wish = wish,
                            onClick = {
                                navController.navigate(AppDestinations.WISH_LIST_ROUTE)
                            }
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(12.dp))
                
                // 底部统计
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    WishStatItem(
                        count = wishUiState.wishes.count { it.status == com.timeflow.app.data.model.WishStatus.ACTIVE },
                        label = "活跃愿望",
                        modifier = Modifier.weight(1f)
                    )
                    WishStatItem(
                        count = wishUiState.wishes.count { it.status == com.timeflow.app.data.model.WishStatus.ACHIEVED },
                        label = "已实现",
                        modifier = Modifier.weight(1f)
                    )
                    WishStatItem(
                        count = wishUiState.wishes.count { it.status == com.timeflow.app.data.model.WishStatus.CONVERTED_TO_GOAL },
                        label = "转为目标",
                        modifier = Modifier.weight(1f)
                    )
                }
            } else {
                // 空状态
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Icon(
                        imageVector = Icons.Default.Star,
                        contentDescription = null,
                        tint = Color.LightGray,
                        modifier = Modifier.size(48.dp)
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = stringResource(R.string.no_wishes_yet),
                        style = MaterialTheme.typography.bodyMedium,
                        color = Color.Gray,
                        textAlign = TextAlign.Center
                    )
                    Spacer(modifier = Modifier.height(12.dp))
                    Button(
                        onClick = { navController.navigate(AppDestinations.WISH_LIST_ROUTE) },
                        colors = ButtonDefaults.buttonColors(
                            containerColor = MaterialTheme.colorScheme.primary // 🎨 使用主题色替代固定颜色
                        )
                    ) {
                        Text(stringResource(R.string.add_wish), color = Color.White)
                    }
                }
            }
        }
    }
}

/**
 * 愿望星云项组件
 */
@Composable
private fun WishCloudItem(
    wish: com.timeflow.app.data.model.WishModel,
    onClick: () -> Unit
) {
    Surface(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() },
        color = Color(0xFFF8F8F8),
        shape = RoundedCornerShape(12.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 类别emoji
            Text(
                text = wish.category.emoji,
                fontSize = 20.sp,  // 使用固定字体大小而不是typography样式
                modifier = Modifier.wrapContentSize()  // 改为自适应内容大小
            )
            
            Spacer(modifier = Modifier.width(12.dp))
            
            // 愿望内容
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = wish.title,
                    style = MaterialTheme.typography.bodySmall, // 从bodyMedium改为bodySmall
                    fontWeight = FontWeight.Medium,
                    color = Color.DarkGray,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
                
                if (wish.description.isNotBlank()) {
                    Spacer(modifier = Modifier.height(2.dp))
                    Text(
                        text = wish.description,
                        style = MaterialTheme.typography.bodySmall,
                        color = Color.Gray,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                }
                
                // 添加状态显示
                Spacer(modifier = Modifier.height(4.dp))
                Surface(
                    color = when (wish.status) {
                        com.timeflow.app.data.model.WishStatus.ACTIVE -> Color(0xFF4CAF50).copy(alpha = 0.1f)
                        com.timeflow.app.data.model.WishStatus.ACHIEVED -> Color(0xFFFFD700).copy(alpha = 0.1f)
                        com.timeflow.app.data.model.WishStatus.CONVERTED_TO_GOAL -> Color(0xFF2196F3).copy(alpha = 0.1f)
                        else -> Color.LightGray.copy(alpha = 0.1f)
                    },
                    shape = RoundedCornerShape(6.dp)
                ) {
                    Text(
                        text = when (wish.status) {
                            com.timeflow.app.data.model.WishStatus.ACTIVE -> "活跃"
                            com.timeflow.app.data.model.WishStatus.ACHIEVED -> "已实现"
                            com.timeflow.app.data.model.WishStatus.CONVERTED_TO_GOAL -> "转为目标"
                            else -> "其他"
                        },
                        modifier = Modifier.padding(horizontal = 6.dp, vertical = 2.dp),
                        style = MaterialTheme.typography.labelSmall,
                        color = when (wish.status) {
                            com.timeflow.app.data.model.WishStatus.ACTIVE -> Color(0xFF4CAF50)
                            com.timeflow.app.data.model.WishStatus.ACHIEVED -> Color(0xFFFFD700)
                            com.timeflow.app.data.model.WishStatus.CONVERTED_TO_GOAL -> Color(0xFF2196F3)
                            else -> Color.Gray
                        }
                    )
                }
            }
            
            // 右侧优先级星星 - 显示5颗星
            Row(
                horizontalArrangement = Arrangement.End,
                verticalAlignment = Alignment.CenterVertically
            ) {
                repeat(5) { index ->
                    Icon(
                        imageVector = if (index < wish.priority) Icons.Default.Star else Icons.Outlined.StarBorder,
                        contentDescription = null,
                        tint = if (index < wish.priority) Color(0xFFFFD700) else Color(0xFFFFD700).copy(alpha = 0.3f),
                        modifier = Modifier.size(12.dp)
                    )
                }
            }
        }
    }
}

/**
 * 愿望统计项组件
 */
@Composable
private fun WishStatItem(
    count: Int,
    label: String,
    modifier: Modifier = Modifier
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = modifier
    ) {
        Text(
            text = count.toString(),
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Bold,
            color = Color(0xFFFFD700) // 金色
        )
        
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = Color.Gray
        )
    }
} 