package com.timeflow.app.ui.task.components

import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.outlined.*
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.timeflow.app.R
import com.timeflow.app.ui.task.model.ViewMode
import com.timeflow.app.utils.SystemBarManager

/**
 * 任务列表顶部导航栏
 * 参考TickTick和Notion的设计风格
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TaskListTopBar(
    onNavigationClick: () -> Unit,
    onViewModeChange: (ViewMode) -> Unit,
    currentViewMode: ViewMode,
    modifier: Modifier = Modifier
) {
    TopAppBar(
        title = {
            Text(
                stringResource(R.string.task_management),
                fontSize = SystemBarManager.getStandardTitleTextSize(),
                fontWeight = FontWeight.SemiBold
            )
        },
        navigationIcon = {
            IconButton(onClick = onNavigationClick) {
                Icon(
                    imageVector = Icons.Default.ArrowBack,
                    contentDescription = stringResource(R.string.back)
                )
            }
        },
        actions = {
            // 视图模式切换 - 只保留列表和看板两种视图
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.padding(end = 8.dp)
            ) {
                // 列表视图 (类似Notion树形结构)
                IconButton(
                    onClick = { onViewModeChange(ViewMode.LIST) },
                    colors = IconButtonDefaults.iconButtonColors(
                        contentColor = if (currentViewMode == ViewMode.LIST) 
                            MaterialTheme.colorScheme.primary 
                        else 
                            MaterialTheme.colorScheme.onSurfaceVariant
                    )
                ) {
                    Icon(
                        imageVector = Icons.Outlined.ViewList,
                        contentDescription = "列表视图"
                    )
                }
                
                // 看板视图 (类似TickTick)
                IconButton(
                    onClick = { onViewModeChange(ViewMode.KANBAN) },
                    colors = IconButtonDefaults.iconButtonColors(
                        contentColor = if (currentViewMode == ViewMode.KANBAN) 
                            MaterialTheme.colorScheme.primary 
                        else 
                            MaterialTheme.colorScheme.onSurfaceVariant
                    )
                ) {
                    Icon(
                        imageVector = Icons.Outlined.Dashboard,
                        contentDescription = "看板视图"
                    )
                }
            }
        },
        colors = TopAppBarDefaults.topAppBarColors(
            containerColor = MaterialTheme.colorScheme.surface,
            titleContentColor = MaterialTheme.colorScheme.primary
        ),
        modifier = modifier
    )
} 