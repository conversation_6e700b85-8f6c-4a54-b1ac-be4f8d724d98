package com.timeflow.app.di;

import com.timeflow.app.utils.SystemBarManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class UtilsModule_ProvideSystemBarManagerFactory implements Factory<SystemBarManager> {
  @Override
  public SystemBarManager get() {
    return provideSystemBarManager();
  }

  public static UtilsModule_ProvideSystemBarManagerFactory create() {
    return InstanceHolder.INSTANCE;
  }

  public static SystemBarManager provideSystemBarManager() {
    return Preconditions.checkNotNullFromProvides(UtilsModule.INSTANCE.provideSystemBarManager());
  }

  private static final class InstanceHolder {
    private static final UtilsModule_ProvideSystemBarManagerFactory INSTANCE = new UtilsModule_ProvideSystemBarManagerFactory();
  }
}
