package com.timeflow.app.utils;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class TimeFlowNotificationManager_Factory implements Factory<TimeFlowNotificationManager> {
  private final Provider<Context> contextProvider;

  private final Provider<NotificationHelper> notificationHelperProvider;

  public TimeFlowNotificationManager_Factory(Provider<Context> contextProvider,
      Provider<NotificationHelper> notificationHelperProvider) {
    this.contextProvider = contextProvider;
    this.notificationHelperProvider = notificationHelperProvider;
  }

  @Override
  public TimeFlowNotificationManager get() {
    return newInstance(contextProvider.get(), notificationHelperProvider.get());
  }

  public static TimeFlowNotificationManager_Factory create(Provider<Context> contextProvider,
      Provider<NotificationHelper> notificationHelperProvider) {
    return new TimeFlowNotificationManager_Factory(contextProvider, notificationHelperProvider);
  }

  public static TimeFlowNotificationManager newInstance(Context context,
      NotificationHelper notificationHelper) {
    return new TimeFlowNotificationManager(context, notificationHelper);
  }
}
