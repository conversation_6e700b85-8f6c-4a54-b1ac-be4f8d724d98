package com.timeflow.app.ui.screen.task;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class TaskTimeSync_Factory implements Factory<TaskTimeSync> {
  private final Provider<TaskTimeManager> taskTimeManagerProvider;

  public TaskTimeSync_Factory(Provider<TaskTimeManager> taskTimeManagerProvider) {
    this.taskTimeManagerProvider = taskTimeManagerProvider;
  }

  @Override
  public TaskTimeSync get() {
    return newInstance(taskTimeManagerProvider.get());
  }

  public static TaskTimeSync_Factory create(Provider<TaskTimeManager> taskTimeManagerProvider) {
    return new TaskTimeSync_Factory(taskTimeManagerProvider);
  }

  public static TaskTimeSync newInstance(TaskTimeManager taskTimeManager) {
    return new TaskTimeSync(taskTimeManager);
  }
}
