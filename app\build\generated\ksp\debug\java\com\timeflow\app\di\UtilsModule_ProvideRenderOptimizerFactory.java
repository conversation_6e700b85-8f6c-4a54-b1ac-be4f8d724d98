package com.timeflow.app.di;

import com.timeflow.app.utils.RenderOptimizer;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class UtilsModule_ProvideRenderOptimizerFactory implements Factory<RenderOptimizer> {
  @Override
  public RenderOptimizer get() {
    return provideRenderOptimizer();
  }

  public static UtilsModule_ProvideRenderOptimizerFactory create() {
    return InstanceHolder.INSTANCE;
  }

  public static RenderOptimizer provideRenderOptimizer() {
    return Preconditions.checkNotNullFromProvides(UtilsModule.INSTANCE.provideRenderOptimizer());
  }

  private static final class InstanceHolder {
    private static final UtilsModule_ProvideRenderOptimizerFactory INSTANCE = new UtilsModule_ProvideRenderOptimizerFactory();
  }
}
