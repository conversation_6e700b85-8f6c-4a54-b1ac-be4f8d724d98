package com.timeflow.app.ui.screen.calendar;

import com.timeflow.app.data.repository.SharedPendingDeletionState;
import com.timeflow.app.data.repository.TaskRepository;
import com.timeflow.app.data.repository.TimeSessionRepository;
import com.timeflow.app.domain.usecase.TaskTimeUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class CalendarViewModel_Factory implements Factory<CalendarViewModel> {
  private final Provider<TaskRepository> taskRepositoryProvider;

  private final Provider<SharedPendingDeletionState> sharedPendingDeletionStateProvider;

  private final Provider<TaskTimeUseCase> taskTimeUseCaseProvider;

  private final Provider<TimeSessionRepository> timeSessionRepositoryProvider;

  public CalendarViewModel_Factory(Provider<TaskRepository> taskRepositoryProvider,
      Provider<SharedPendingDeletionState> sharedPendingDeletionStateProvider,
      Provider<TaskTimeUseCase> taskTimeUseCaseProvider,
      Provider<TimeSessionRepository> timeSessionRepositoryProvider) {
    this.taskRepositoryProvider = taskRepositoryProvider;
    this.sharedPendingDeletionStateProvider = sharedPendingDeletionStateProvider;
    this.taskTimeUseCaseProvider = taskTimeUseCaseProvider;
    this.timeSessionRepositoryProvider = timeSessionRepositoryProvider;
  }

  @Override
  public CalendarViewModel get() {
    return newInstance(taskRepositoryProvider.get(), sharedPendingDeletionStateProvider.get(), taskTimeUseCaseProvider.get(), timeSessionRepositoryProvider.get());
  }

  public static CalendarViewModel_Factory create(Provider<TaskRepository> taskRepositoryProvider,
      Provider<SharedPendingDeletionState> sharedPendingDeletionStateProvider,
      Provider<TaskTimeUseCase> taskTimeUseCaseProvider,
      Provider<TimeSessionRepository> timeSessionRepositoryProvider) {
    return new CalendarViewModel_Factory(taskRepositoryProvider, sharedPendingDeletionStateProvider, taskTimeUseCaseProvider, timeSessionRepositoryProvider);
  }

  public static CalendarViewModel newInstance(TaskRepository taskRepository,
      SharedPendingDeletionState sharedPendingDeletionState, TaskTimeUseCase taskTimeUseCase,
      TimeSessionRepository timeSessionRepository) {
    return new CalendarViewModel(taskRepository, sharedPendingDeletionState, taskTimeUseCase, timeSessionRepository);
  }
}
