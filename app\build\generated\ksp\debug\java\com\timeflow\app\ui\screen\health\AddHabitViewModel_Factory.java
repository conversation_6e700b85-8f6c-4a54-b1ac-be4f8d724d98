package com.timeflow.app.ui.screen.health;

import com.timeflow.app.data.repository.GoalRepository;
import com.timeflow.app.data.repository.HabitRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AddHabitViewModel_Factory implements Factory<AddHabitViewModel> {
  private final Provider<HabitRepository> habitRepositoryProvider;

  private final Provider<GoalRepository> goalRepositoryProvider;

  public AddHabitViewModel_Factory(Provider<HabitRepository> habitRepositoryProvider,
      Provider<GoalRepository> goalRepositoryProvider) {
    this.habitRepositoryProvider = habitRepositoryProvider;
    this.goalRepositoryProvider = goalRepositoryProvider;
  }

  @Override
  public AddHabitViewModel get() {
    return newInstance(habitRepositoryProvider.get(), goalRepositoryProvider.get());
  }

  public static AddHabitViewModel_Factory create(Provider<HabitRepository> habitRepositoryProvider,
      Provider<GoalRepository> goalRepositoryProvider) {
    return new AddHabitViewModel_Factory(habitRepositoryProvider, goalRepositoryProvider);
  }

  public static AddHabitViewModel newInstance(HabitRepository habitRepository,
      GoalRepository goalRepository) {
    return new AddHabitViewModel(habitRepository, goalRepository);
  }
}
