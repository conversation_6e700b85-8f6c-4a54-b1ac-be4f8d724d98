package com.timeflow.app.di;

import com.timeflow.app.data.repository.TaskTimeRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class TaskTimeModule_ProvideTaskTimeRepositoryFactory implements Factory<TaskTimeRepository> {
  @Override
  public TaskTimeRepository get() {
    return provideTaskTimeRepository();
  }

  public static TaskTimeModule_ProvideTaskTimeRepositoryFactory create() {
    return InstanceHolder.INSTANCE;
  }

  public static TaskTimeRepository provideTaskTimeRepository() {
    return Preconditions.checkNotNullFromProvides(TaskTimeModule.INSTANCE.provideTaskTimeRepository());
  }

  private static final class InstanceHolder {
    private static final TaskTimeModule_ProvideTaskTimeRepositoryFactory INSTANCE = new TaskTimeModule_ProvideTaskTimeRepositoryFactory();
  }
}
