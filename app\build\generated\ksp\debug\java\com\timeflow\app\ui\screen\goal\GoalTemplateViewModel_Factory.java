package com.timeflow.app.ui.screen.goal;

import com.timeflow.app.data.repository.GoalRepository;
import com.timeflow.app.data.repository.GoalTemplateRepository;
import com.timeflow.app.domain.usecase.goal.SmartTemplateUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class GoalTemplateViewModel_Factory implements Factory<GoalTemplateViewModel> {
  private final Provider<GoalTemplateRepository> templateRepositoryProvider;

  private final Provider<GoalRepository> goalRepositoryProvider;

  private final Provider<SmartTemplateUseCase> smartTemplateUseCaseProvider;

  public GoalTemplateViewModel_Factory(Provider<GoalTemplateRepository> templateRepositoryProvider,
      Provider<GoalRepository> goalRepositoryProvider,
      Provider<SmartTemplateUseCase> smartTemplateUseCaseProvider) {
    this.templateRepositoryProvider = templateRepositoryProvider;
    this.goalRepositoryProvider = goalRepositoryProvider;
    this.smartTemplateUseCaseProvider = smartTemplateUseCaseProvider;
  }

  @Override
  public GoalTemplateViewModel get() {
    return newInstance(templateRepositoryProvider.get(), goalRepositoryProvider.get(), smartTemplateUseCaseProvider.get());
  }

  public static GoalTemplateViewModel_Factory create(
      Provider<GoalTemplateRepository> templateRepositoryProvider,
      Provider<GoalRepository> goalRepositoryProvider,
      Provider<SmartTemplateUseCase> smartTemplateUseCaseProvider) {
    return new GoalTemplateViewModel_Factory(templateRepositoryProvider, goalRepositoryProvider, smartTemplateUseCaseProvider);
  }

  public static GoalTemplateViewModel newInstance(GoalTemplateRepository templateRepository,
      GoalRepository goalRepository, SmartTemplateUseCase smartTemplateUseCase) {
    return new GoalTemplateViewModel(templateRepository, goalRepository, smartTemplateUseCase);
  }
}
