4app/src/main/kotlin/com/timeflow/app/MainActivity.kt;app/src/main/kotlin/com/timeflow/app/TimeFlowApplication.ktAapp/src/main/kotlin/com/timeflow/app/ai/GoalCategoryClassifier.kt>app/src/main/kotlin/com/timeflow/app/data/ai/model/AiConfig.ktDapp/src/main/kotlin/com/timeflow/app/data/ai/model/AiConversation.kt@app/src/main/kotlin/com/timeflow/app/data/ai/model/AiSettings.ktIapp/src/main/kotlin/com/timeflow/app/data/ai/model/AiTaskDecomposition.ktCapp/src/main/kotlin/com/timeflow/app/data/ai/model/AiTaskInsight.ktFapp/src/main/kotlin/com/timeflow/app/data/ai/model/AiTimeEstimation.kt@app/src/main/kotlin/com/timeflow/app/data/ai/model/SentStatus.ktPapp/src/main/kotlin/com/timeflow/app/data/algorithm/PeriodPredictionAlgorithm.ktFapp/src/main/kotlin/com/timeflow/app/data/analytics/PeriodAnalytics.ktAapp/src/main/kotlin/com/timeflow/app/data/converter/Converters.ktHapp/src/main/kotlin/com/timeflow/app/data/converter/DateTimeConverter.ktJapp/src/main/kotlin/com/timeflow/app/data/converter/ListStringConverter.ktIapp/src/main/kotlin/com/timeflow/app/data/converter/LocalDateConverter.ktJapp/src/main/kotlin/com/timeflow/app/data/converter/StringListConverter.ktDapp/src/main/kotlin/com/timeflow/app/data/converter/TaskConverter.kt<app/src/main/kotlin/com/timeflow/app/data/dao/AppUsageDao.kt9app/src/main/kotlin/com/timeflow/app/data/dao/CycleDao.ktAapp/src/main/kotlin/com/timeflow/app/data/dao/EmotionRecordDao.kt8app/src/main/kotlin/com/timeflow/app/data/dao/GoalDao.kt@app/src/main/kotlin/com/timeflow/app/data/dao/GoalTemplateDao.kt9app/src/main/kotlin/com/timeflow/app/data/dao/HabitDao.kt?app/src/main/kotlin/com/timeflow/app/data/dao/KanbanBoardDao.kt@app/src/main/kotlin/com/timeflow/app/data/dao/KanbanColumnDao.ktDapp/src/main/kotlin/com/timeflow/app/data/dao/MedicationRecordDao.kt>app/src/main/kotlin/com/timeflow/app/data/dao/ReflectionDao.kt8app/src/main/kotlin/com/timeflow/app/data/dao/TaskDao.kt?app/src/main/kotlin/com/timeflow/app/data/dao/TimeSessionDao.kt8app/src/main/kotlin/com/timeflow/app/data/dao/WishDao.kt;app/src/main/kotlin/com/timeflow/app/data/db/AppDatabase.ktBapp/src/main/kotlin/com/timeflow/app/data/entity/AppUsageEntity.kt?app/src/main/kotlin/com/timeflow/app/data/entity/CycleRecord.ktGapp/src/main/kotlin/com/timeflow/app/data/entity/EmotionRecordEntity.kt8app/src/main/kotlin/com/timeflow/app/data/entity/Goal.kt@app/src/main/kotlin/com/timeflow/app/data/entity/GoalTemplate.kt9app/src/main/kotlin/com/timeflow/app/data/entity/Habit.kt?app/src/main/kotlin/com/timeflow/app/data/entity/KanbanBoard.kt@app/src/main/kotlin/com/timeflow/app/data/entity/KanbanColumn.ktDapp/src/main/kotlin/com/timeflow/app/data/entity/MedicationRecord.kt<app/src/main/kotlin/com/timeflow/app/data/entity/Priority.ktDapp/src/main/kotlin/com/timeflow/app/data/entity/ReflectionEntity.ktAapp/src/main/kotlin/com/timeflow/app/data/entity/SymptomRecord.kt8app/src/main/kotlin/com/timeflow/app/data/entity/Task.kt?app/src/main/kotlin/com/timeflow/app/data/entity/TaskClosure.kt;app/src/main/kotlin/com/timeflow/app/data/entity/TaskTag.kt8app/src/main/kotlin/com/timeflow/app/data/entity/Wish.ktDapp/src/main/kotlin/com/timeflow/app/data/mapper/ReflectionMapper.kt>app/src/main/kotlin/com/timeflow/app/data/model/AiApiResult.kt;app/src/main/kotlin/com/timeflow/app/data/model/AiConfig.kt@app/src/main/kotlin/com/timeflow/app/data/model/AiConfigModel.kt?app/src/main/kotlin/com/timeflow/app/data/model/AppUsageData.ktCapp/src/main/kotlin/com/timeflow/app/data/model/DataLoadingState.kt8app/src/main/kotlin/com/timeflow/app/data/model/Event.kt7app/src/main/kotlin/com/timeflow/app/data/model/Goal.kt?app/src/main/kotlin/com/timeflow/app/data/model/GoalCategory.kt>app/src/main/kotlin/com/timeflow/app/data/model/GoalSubTask.kt?app/src/main/kotlin/com/timeflow/app/data/model/GoalTemplate.kt;app/src/main/kotlin/com/timeflow/app/data/model/GoalType.ktCapp/src/main/kotlin/com/timeflow/app/data/model/GoalWizardModels.kt=app/src/main/kotlin/com/timeflow/app/data/model/HabitModel.ktEapp/src/main/kotlin/com/timeflow/app/data/model/HistoryPeriodModel.kt?app/src/main/kotlin/com/timeflow/app/data/model/KanbanColumn.kt=app/src/main/kotlin/com/timeflow/app/data/model/Medication.kt;app/src/main/kotlin/com/timeflow/app/data/model/MoodType.ktEapp/src/main/kotlin/com/timeflow/app/data/model/NotificationConfig.kt>app/src/main/kotlin/com/timeflow/app/data/model/PaymentInfo.kt;app/src/main/kotlin/com/timeflow/app/data/model/Priority.ktBapp/src/main/kotlin/com/timeflow/app/data/model/RecurrenceModel.ktBapp/src/main/kotlin/com/timeflow/app/data/model/RecurringPeriod.ktDapp/src/main/kotlin/com/timeflow/app/data/model/RecurringSettings.kt=app/src/main/kotlin/com/timeflow/app/data/model/Reflection.kt?app/src/main/kotlin/com/timeflow/app/data/model/ReminderType.kt7app/src/main/kotlin/com/timeflow/app/data/model/Task.kt<app/src/main/kotlin/com/timeflow/app/data/model/TaskGroup.kt:app/src/main/kotlin/com/timeflow/app/data/model/TaskTag.kt;app/src/main/kotlin/com/timeflow/app/data/model/TaskTime.kt>app/src/main/kotlin/com/timeflow/app/data/model/TimeSession.kt?app/src/main/kotlin/com/timeflow/app/data/model/TimeSlotInfo.kt?app/src/main/kotlin/com/timeflow/app/data/model/ViewTimeSlot.kt<app/src/main/kotlin/com/timeflow/app/data/model/WishModel.kt>app/src/main/kotlin/com/timeflow/app/data/model/WorkManager.ktQapp/src/main/kotlin/com/timeflow/app/data/notification/GoalNotificationManager.ktSapp/src/main/kotlin/com/timeflow/app/data/notification/GoalNotificationScheduler.ktOapp/src/main/kotlin/com/timeflow/app/data/preferences/UserPreferencesManager.ktHapp/src/main/kotlin/com/timeflow/app/data/repository/AiTaskRepository.ktLapp/src/main/kotlin/com/timeflow/app/data/repository/AiTaskRepositoryImpl.ktFapp/src/main/kotlin/com/timeflow/app/data/repository/BaseRepository.ktGapp/src/main/kotlin/com/timeflow/app/data/repository/CycleRepository.ktRapp/src/main/kotlin/com/timeflow/app/data/repository/DefaultTemplateInitializer.ktOapp/src/main/kotlin/com/timeflow/app/data/repository/EmotionRecordRepository.ktGapp/src/main/kotlin/com/timeflow/app/data/repository/EventRepository.ktFapp/src/main/kotlin/com/timeflow/app/data/repository/GoalRepository.ktNapp/src/main/kotlin/com/timeflow/app/data/repository/GoalTemplateRepository.ktGapp/src/main/kotlin/com/timeflow/app/data/repository/HabitRepository.ktKapp/src/main/kotlin/com/timeflow/app/data/repository/HabitRepositoryImpl.ktOapp/src/main/kotlin/com/timeflow/app/data/repository/HistoryPeriodRepository.ktMapp/src/main/kotlin/com/timeflow/app/data/repository/KanbanBoardRepository.ktQapp/src/main/kotlin/com/timeflow/app/data/repository/KanbanBoardRepositoryImpl.ktNapp/src/main/kotlin/com/timeflow/app/data/repository/KanbanColumnRepository.ktRapp/src/main/kotlin/com/timeflow/app/data/repository/KanbanColumnRepositoryImpl.ktHapp/src/main/kotlin/com/timeflow/app/data/repository/KanbanRepository.ktLapp/src/main/kotlin/com/timeflow/app/data/repository/MedicationRepository.ktIapp/src/main/kotlin/com/timeflow/app/data/repository/SharedFilterState.ktRapp/src/main/kotlin/com/timeflow/app/data/repository/SharedPendingDeletionState.ktFapp/src/main/kotlin/com/timeflow/app/data/repository/TaskRepository.ktJapp/src/main/kotlin/com/timeflow/app/data/repository/TaskRepositoryImpl.ktJapp/src/main/kotlin/com/timeflow/app/data/repository/TaskTimeRepository.ktOapp/src/main/kotlin/com/timeflow/app/data/repository/TimeAnalyticsRepository.ktSapp/src/main/kotlin/com/timeflow/app/data/repository/TimeAnalyticsRepositoryImpl.ktMapp/src/main/kotlin/com/timeflow/app/data/repository/TimeSessionRepository.ktIapp/src/main/kotlin/com/timeflow/app/data/repository/TransactionHelper.ktPapp/src/main/kotlin/com/timeflow/app/data/repository/UserPreferenceRepository.ktFapp/src/main/kotlin/com/timeflow/app/data/repository/WishRepository.ktJapp/src/main/kotlin/com/timeflow/app/data/repository/WishRepositoryImpl.kt@app/src/main/kotlin/com/timeflow/app/debug/PriorityUpdateTest.kt:app/src/main/kotlin/com/timeflow/app/di/AnalyticsModule.kt9app/src/main/kotlin/com/timeflow/app/di/AppInitializer.kt4app/src/main/kotlin/com/timeflow/app/di/AppModule.kt:app/src/main/kotlin/com/timeflow/app/di/DataStoreModule.kt9app/src/main/kotlin/com/timeflow/app/di/DatabaseModule.kt6app/src/main/kotlin/com/timeflow/app/di/ImageModule.kt;app/src/main/kotlin/com/timeflow/app/di/MedicationModule.kt9app/src/main/kotlin/com/timeflow/app/di/PreferenceKeys.kt;app/src/main/kotlin/com/timeflow/app/di/ReflectionModule.kt;app/src/main/kotlin/com/timeflow/app/di/RepositoryModule.kt5app/src/main/kotlin/com/timeflow/app/di/SyncModule.kt9app/src/main/kotlin/com/timeflow/app/di/TaskTimeModule.kt6app/src/main/kotlin/com/timeflow/app/di/UtilsModule.kt7app/src/main/kotlin/com/timeflow/app/di/ViewModelKey.kt:app/src/main/kotlin/com/timeflow/app/di/ViewModelModule.kt5app/src/main/kotlin/com/timeflow/app/di/WishModule.ktAapp/src/main/kotlin/com/timeflow/app/di/WorkManagerInitializer.ktFapp/src/main/kotlin/com/timeflow/app/domain/usecase/TaskTimeUseCase.ktTapp/src/main/kotlin/com/timeflow/app/domain/usecase/goal/QuickGoalCreationUseCase.ktPapp/src/main/kotlin/com/timeflow/app/domain/usecase/goal/SmartTemplateUseCase.ktLapp/src/main/kotlin/com/timeflow/app/initializer/RecurringTaskInitializer.ktBapp/src/main/kotlin/com/timeflow/app/navigation/AppDestinations.kt;app/src/main/kotlin/com/timeflow/app/navigation/NavGraph.ktBapp/src/main/kotlin/com/timeflow/app/navigation/TimeFlowNavHost.ktFapp/src/main/kotlin/com/timeflow/app/receiver/BootCompletedReceiver.ktIapp/src/main/kotlin/com/timeflow/app/receiver/DailyReviewAlarmReceiver.ktIapp/src/main/kotlin/com/timeflow/app/receiver/FocusTimerActionReceiver.ktCapp/src/main/kotlin/com/timeflow/app/receiver/HabitAlarmReceiver.ktBapp/src/main/kotlin/com/timeflow/app/receiver/TaskAlarmReceiver.ktYapp/src/main/kotlin/com/timeflow/app/receiver/TaskPersistentNotificationActionReceiver.ktEapp/src/main/kotlin/com/timeflow/app/service/AiSuggestionScheduler.ktAapp/src/main/kotlin/com/timeflow/app/service/AutoBackupService.ktFapp/src/main/kotlin/com/timeflow/app/service/DailyReviewDataService.ktPapp/src/main/kotlin/com/timeflow/app/service/DailyReviewNotificationGenerator.ktDapp/src/main/kotlin/com/timeflow/app/service/DailyReviewScheduler.ktAapp/src/main/kotlin/com/timeflow/app/service/FocusTimerManager.ktAapp/src/main/kotlin/com/timeflow/app/service/FocusTimerService.ktIapp/src/main/kotlin/com/timeflow/app/service/MedicationReminderManager.ktIapp/src/main/kotlin/com/timeflow/app/service/NotificationConfigManager.ktGapp/src/main/kotlin/com/timeflow/app/service/NotificationTestService.kt>app/src/main/kotlin/com/timeflow/app/service/PaymentManager.ktDapp/src/main/kotlin/com/timeflow/app/service/RecurrenceCalculator.ktDapp/src/main/kotlin/com/timeflow/app/service/RecurringTaskManager.ktQapp/src/main/kotlin/com/timeflow/app/service/TaskPersistentNotificationManager.ktQapp/src/main/kotlin/com/timeflow/app/service/TaskPersistentNotificationService.ktEapp/src/main/kotlin/com/timeflow/app/service/TaskReminderScheduler.ktCapp/src/main/kotlin/com/timeflow/app/service/TimeTrackingService.ktEapp/src/main/kotlin/com/timeflow/app/test/NotificationSettingsTest.kt7app/src/main/kotlin/com/timeflow/app/ui/MainActivity.kt6app/src/main/kotlin/com/timeflow/app/ui/TimeFlowApp.kt:app/src/main/kotlin/com/timeflow/app/ui/TimeFlowNavHost.kt=app/src/main/kotlin/com/timeflow/app/ui/base/SafeViewModel.ktRapp/src/main/kotlin/com/timeflow/app/ui/component/goal/ModernProgressComponents.ktBapp/src/main/kotlin/com/timeflow/app/ui/components/BottomNavBar.ktBapp/src/main/kotlin/com/timeflow/app/ui/components/CalendarGrid.kt?app/src/main/kotlin/com/timeflow/app/ui/components/ChartCard.kt@app/src/main/kotlin/com/timeflow/app/ui/components/EmptyState.kt@app/src/main/kotlin/com/timeflow/app/ui/components/ErrorState.ktFapp/src/main/kotlin/com/timeflow/app/ui/components/GlassmorphicCard.ktDapp/src/main/kotlin/com/timeflow/app/ui/components/LoadingContent.ktFapp/src/main/kotlin/com/timeflow/app/ui/components/LoadingIndicator.ktBapp/src/main/kotlin/com/timeflow/app/ui/components/LoadingState.ktBapp/src/main/kotlin/com/timeflow/app/ui/components/MiniTimerBar.kt?app/src/main/kotlin/com/timeflow/app/ui/components/StatsCard.ktFapp/src/main/kotlin/com/timeflow/app/ui/components/WishCloudSection.ktHapp/src/main/kotlin/com/timeflow/app/ui/components/WishLinkedGoalCard.ktIapp/src/main/kotlin/com/timeflow/app/ui/components/WishToGoalAnimation.ktFapp/src/main/kotlin/com/timeflow/app/ui/components/ai/AiPromptInput.ktOapp/src/main/kotlin/com/timeflow/app/ui/components/goal/GoalAssistantButtons.ktOapp/src/main/kotlin/com/timeflow/app/ui/components/goal/GoalCategorySelector.ktVapp/src/main/kotlin/com/timeflow/app/ui/components/goal/SmartCategoryRecommendation.ktLapp/src/main/kotlin/com/timeflow/app/ui/components/goal/SmartTemplateList.ktKapp/src/main/kotlin/com/timeflow/app/ui/components/payment/PaymentDialog.ktKapp/src/main/kotlin/com/timeflow/app/ui/navigation/AnimationConfigurator.ktEapp/src/main/kotlin/com/timeflow/app/ui/navigation/AppDestinations.ktOapp/src/main/kotlin/com/timeflow/app/ui/navigation/BottomNavAnimationManager.ktCapp/src/main/kotlin/com/timeflow/app/ui/navigation/BottomNavItem.ktHapp/src/main/kotlin/com/timeflow/app/ui/navigation/LocalNavController.ktPapp/src/main/kotlin/com/timeflow/app/ui/navigation/LocalNavigationDestination.ktCapp/src/main/kotlin/com/timeflow/app/ui/navigation/NavAnimations.ktEapp/src/main/kotlin/com/timeflow/app/ui/navigation/NavigationGraph.ktEapp/src/main/kotlin/com/timeflow/app/ui/navigation/NavigationRoute.kt<app/src/main/kotlin/com/timeflow/app/ui/navigation/Screen.ktDapp/src/main/kotlin/com/timeflow/app/ui/navigation/ScreenWrappers.ktGapp/src/main/kotlin/com/timeflow/app/ui/navigation/ScreenWrappersFix.ktEapp/src/main/kotlin/com/timeflow/app/ui/navigation/TimeFlowNavHost.ktGapp/src/main/kotlin/com/timeflow/app/ui/navigation/TimeFlowNavigator.ktUapp/src/main/kotlin/com/timeflow/app/ui/optimization/ComposeRecompositionOptimizer.ktSapp/src/main/kotlin/com/timeflow/app/ui/optimization/OptimizedTaskListComponents.ktCapp/src/main/kotlin/com/timeflow/app/ui/screen/SharedFilterState.ktGapp/src/main/kotlin/com/timeflow/app/ui/screen/account/AccountScreen.ktCapp/src/main/kotlin/com/timeflow/app/ui/screen/ai/AIReviewScreen.ktFapp/src/main/kotlin/com/timeflow/app/ui/screen/ai/AIReviewViewModel.ktFapp/src/main/kotlin/com/timeflow/app/ui/screen/ai/AiAssistantScreen.ktOapp/src/main/kotlin/com/timeflow/app/ui/screen/analytics/AnalyticsComponents.ktLapp/src/main/kotlin/com/timeflow/app/ui/screen/analytics/AnalyticsContent.ktPapp/src/main/kotlin/com/timeflow/app/ui/screen/analytics/AnalyticsDataService.ktSapp/src/main/kotlin/com/timeflow/app/ui/screen/analytics/AnalyticsInsightService.ktKapp/src/main/kotlin/com/timeflow/app/ui/screen/analytics/AnalyticsScreen.ktNapp/src/main/kotlin/com/timeflow/app/ui/screen/analytics/AnalyticsViewModel.ktIapp/src/main/kotlin/com/timeflow/app/ui/screen/calendar/CalendarScreen.ktLapp/src/main/kotlin/com/timeflow/app/ui/screen/calendar/CalendarViewModel.ktIapp/src/main/kotlin/com/timeflow/app/ui/screen/calendar/DimensionUtils.kt^app/src/main/kotlin/com/timeflow/app/ui/screen/calendar/components/CreateFloatingTaskDialog.ktZapp/src/main/kotlin/com/timeflow/app/ui/screen/calendar/components/FloatingTasksSection.ktSapp/src/main/kotlin/com/timeflow/app/ui/screen/calendar/components/ModernDayView.ktIapp/src/main/kotlin/com/timeflow/app/ui/screen/discover/DiscoverScreen.ktLapp/src/main/kotlin/com/timeflow/app/ui/screen/discover/DiscoverViewModel.ktDapp/src/main/kotlin/com/timeflow/app/ui/screen/goal/AddGoalScreen.ktEapp/src/main/kotlin/com/timeflow/app/ui/screen/goal/EditGoalScreen.ktGapp/src/main/kotlin/com/timeflow/app/ui/screen/goal/GoalAnalysisTest.ktJapp/src/main/kotlin/com/timeflow/app/ui/screen/goal/GoalBreakdownScreen.ktSapp/src/main/kotlin/com/timeflow/app/ui/screen/goal/GoalCategoryManagementScreen.ktSapp/src/main/kotlin/com/timeflow/app/ui/screen/goal/GoalCompletionAnalysisScreen.ktJapp/src/main/kotlin/com/timeflow/app/ui/screen/goal/GoalDashboardScreen.ktGapp/src/main/kotlin/com/timeflow/app/ui/screen/goal/GoalDetailScreen.ktKapp/src/main/kotlin/com/timeflow/app/ui/screen/goal/GoalManagementScreen.ktGapp/src/main/kotlin/com/timeflow/app/ui/screen/goal/GoalReviewScreen.ktLapp/src/main/kotlin/com/timeflow/app/ui/screen/goal/GoalSetupWizardScreen.ktMapp/src/main/kotlin/com/timeflow/app/ui/screen/goal/GoalTemplateEditScreen.ktOapp/src/main/kotlin/com/timeflow/app/ui/screen/goal/GoalTemplateImportScreen.ktMapp/src/main/kotlin/com/timeflow/app/ui/screen/goal/GoalTemplateListScreen.ktIapp/src/main/kotlin/com/timeflow/app/ui/screen/goal/GoalTemplateScreen.ktLapp/src/main/kotlin/com/timeflow/app/ui/screen/goal/GoalTemplateViewModel.ktDapp/src/main/kotlin/com/timeflow/app/ui/screen/goal/GoalViewModel.ktOapp/src/main/kotlin/com/timeflow/app/ui/screen/goal/SaveGoalAsTemplateScreen.ktNapp/src/main/kotlin/com/timeflow/app/ui/screen/goal/SmartCategoryTestScreen.ktZapp/src/main/kotlin/com/timeflow/app/ui/screen/goal/components/AiBreakdownProcessScreen.ktYapp/src/main/kotlin/com/timeflow/app/ui/screen/goal/components/AiBreakdownResultScreen.ktWapp/src/main/kotlin/com/timeflow/app/ui/screen/goal/components/AiTaskBreakdownScreen.ktXapp/src/main/kotlin/com/timeflow/app/ui/screen/goal/components/RecurringSettingsPanel.ktWapp/src/main/kotlin/com/timeflow/app/ui/screen/goal/components/ReminderSettingsPanel.ktGapp/src/main/kotlin/com/timeflow/app/ui/screen/health/AddHabitScreen.ktJapp/src/main/kotlin/com/timeflow/app/ui/screen/health/AddHabitViewModel.ktQapp/src/main/kotlin/com/timeflow/app/ui/screen/health/EnhancedHealthComponents.ktFapp/src/main/kotlin/com/timeflow/app/ui/screen/health/FrequencyType.ktJapp/src/main/kotlin/com/timeflow/app/ui/screen/health/HabitDetailScreen.ktSapp/src/main/kotlin/com/timeflow/app/ui/screen/health/HabitDetailScreenOptimized.ktKapp/src/main/kotlin/com/timeflow/app/ui/screen/health/HabitTrackerScreen.ktMapp/src/main/kotlin/com/timeflow/app/ui/screen/health/MenstrualCycleScreen.ktNapp/src/main/kotlin/com/timeflow/app/ui/screen/health/PeriodAnalyticsScreen.ktJapp/src/main/kotlin/com/timeflow/app/ui/screen/health/PeriodEditDialogs.ktLapp/src/main/kotlin/com/timeflow/app/ui/screen/health/PeriodHistoryScreen.ktUapp/src/main/kotlin/com/timeflow/app/ui/screen/health/ProfessionalMedicationScreen.ktXapp/src/main/kotlin/com/timeflow/app/ui/screen/health/ProfessionalMedicationViewModel.ktFapp/src/main/kotlin/com/timeflow/app/ui/screen/health/SymptomWidget.ktMapp/src/main/kotlin/com/timeflow/app/ui/screen/health/SymptomsDetailScreen.ktLapp/src/main/kotlin/com/timeflow/app/ui/screen/home/<USER>/src/main/kotlin/com/timeflow/app/ui/screen/home/<USER>/src/main/kotlin/com/timeflow/app/ui/screen/home/<USER>/src/main/kotlin/com/timeflow/app/ui/screen/milestone/EditMilestoneScreen.ktDapp/src/main/kotlin/com/timeflow/app/ui/screen/milestone/GridView.ktEapp/src/main/kotlin/com/timeflow/app/ui/screen/milestone/Milestone.ktMapp/src/main/kotlin/com/timeflow/app/ui/screen/milestone/MilestoneCategory.ktQapp/src/main/kotlin/com/timeflow/app/ui/screen/milestone/MilestoneDetailDialog.ktMapp/src/main/kotlin/com/timeflow/app/ui/screen/milestone/MilestoneInsights.ktKapp/src/main/kotlin/com/timeflow/app/ui/screen/milestone/MilestoneRoutes.ktKapp/src/main/kotlin/com/timeflow/app/ui/screen/milestone/MilestoneScreen.ktKapp/src/main/kotlin/com/timeflow/app/ui/screen/milestone/MilestoneTopBar.ktIapp/src/main/kotlin/com/timeflow/app/ui/screen/milestone/MilestoneType.ktNapp/src/main/kotlin/com/timeflow/app/ui/screen/milestone/MilestoneViewModel.ktJapp/src/main/kotlin/com/timeflow/app/ui/screen/milestone/RichTextEditor.ktHapp/src/main/kotlin/com/timeflow/app/ui/screen/milestone/TimelineView.ktDapp/src/main/kotlin/com/timeflow/app/ui/screen/milestone/ViewMode.ktLapp/src/main/kotlin/com/timeflow/app/ui/screen/milestone/YearTimelineView.ktFapp/src/main/kotlin/com/timeflow/app/ui/screen/milestone/YearlyView.ktUapp/src/main/kotlin/com/timeflow/app/ui/screen/profile/DetailedEmotionRecordScreen.ktSapp/src/main/kotlin/com/timeflow/app/ui/screen/profile/EmotionRecordDetailScreen.ktSapp/src/main/kotlin/com/timeflow/app/ui/screen/profile/EmotionRecordReviewScreen.ktQapp/src/main/kotlin/com/timeflow/app/ui/screen/profile/EmotionStatisticsScreen.ktGapp/src/main/kotlin/com/timeflow/app/ui/screen/profile/ProfileScreen.ktJapp/src/main/kotlin/com/timeflow/app/ui/screen/profile/ProfileViewModel.ktQapp/src/main/kotlin/com/timeflow/app/ui/screen/reflection/ReflectionComponents.ktSapp/src/main/kotlin/com/timeflow/app/ui/screen/reflection/ReflectionDetailScreen.ktMapp/src/main/kotlin/com/timeflow/app/ui/screen/reflection/ReflectionModels.ktMapp/src/main/kotlin/com/timeflow/app/ui/screen/reflection/ReflectionScreen.ktPapp/src/main/kotlin/com/timeflow/app/ui/screen/reflection/ReflectionViewModel.ktZapp/src/main/kotlin/com/timeflow/app/ui/screen/reflection/components/ShareOptionsDialog.kt_app/src/main/kotlin/com/timeflow/app/ui/screen/reflection/components/ShareableReflectionCard.ktZapp/src/main/kotlin/com/timeflow/app/ui/screen/reflection/data/ReflectionRepositoryImpl.kt]app/src/main/kotlin/com/timeflow/app/ui/screen/reflection/data/SearchSuggestionServiceImpl.ktVapp/src/main/kotlin/com/timeflow/app/ui/screen/reflection/model/ReflectionListState.ktQapp/src/main/kotlin/com/timeflow/app/ui/screen/reflection/model/TimeViewModels.ktFapp/src/main/kotlin/com/timeflow/app/ui/screen/settings/AboutScreen.ktPapp/src/main/kotlin/com/timeflow/app/ui/screen/settings/AiModelSettingsScreen.ktKapp/src/main/kotlin/com/timeflow/app/ui/screen/settings/AiSettingsScreen.ktOapp/src/main/kotlin/com/timeflow/app/ui/screen/settings/BackupSettingsScreen.ktSapp/src/main/kotlin/com/timeflow/app/ui/screen/settings/CloudStorageConfigScreen.ktOapp/src/main/kotlin/com/timeflow/app/ui/screen/settings/DataManagementScreen.ktMapp/src/main/kotlin/com/timeflow/app/ui/screen/settings/DataRecoveryScreen.ktUapp/src/main/kotlin/com/timeflow/app/ui/screen/settings/NotificationSettingsScreen.ktXapp/src/main/kotlin/com/timeflow/app/ui/screen/settings/NotificationSettingsViewModel.ktIapp/src/main/kotlin/com/timeflow/app/ui/screen/settings/SettingsScreen.ktLapp/src/main/kotlin/com/timeflow/app/ui/screen/settings/SettingsViewModel.ktMapp/src/main/kotlin/com/timeflow/app/ui/screen/settings/SyncRepositoryImpl.ktMapp/src/main/kotlin/com/timeflow/app/ui/screen/settings/SyncSettingsScreen.ktPapp/src/main/kotlin/com/timeflow/app/ui/screen/settings/SyncSettingsViewModel.ktDapp/src/main/kotlin/com/timeflow/app/ui/screen/task/AddTaskScreen.kt>app/src/main/kotlin/com/timeflow/app/ui/screen/task/SubTask.ktCapp/src/main/kotlin/com/timeflow/app/ui/screen/task/TaskAdapters.ktBapp/src/main/kotlin/com/timeflow/app/ui/screen/task/TaskContent.ktLapp/src/main/kotlin/com/timeflow/app/ui/screen/task/TaskDetailBottomSheet.ktGapp/src/main/kotlin/com/timeflow/app/ui/screen/task/TaskDetailScreen.ktJapp/src/main/kotlin/com/timeflow/app/ui/screen/task/TaskDetailViewModel.ktEapp/src/main/kotlin/com/timeflow/app/ui/screen/task/TaskEditScreen.ktIapp/src/main/kotlin/com/timeflow/app/ui/screen/task/TaskFeedbackDialog.ktIapp/src/main/kotlin/com/timeflow/app/ui/screen/task/TaskListFullScreen.kt@app/src/main/kotlin/com/timeflow/app/ui/screen/task/TaskModel.ktKapp/src/main/kotlin/com/timeflow/app/ui/screen/task/TaskQuickDetailSheet.ktFapp/src/main/kotlin/com/timeflow/app/ui/screen/task/TaskTimeManager.ktCapp/src/main/kotlin/com/timeflow/app/ui/screen/task/TaskTimeSync.ktGapp/src/main/kotlin/com/timeflow/app/ui/screen/task/TaskTimeSyncTest.ktDapp/src/main/kotlin/com/timeflow/app/ui/screen/task/TaskViewModel.ktXapp/src/main/kotlin/com/timeflow/app/ui/screen/task/components/GoalSelectionComponent.ktIapp/src/main/kotlin/com/timeflow/app/ui/screen/task/model/FeedbackData.ktEapp/src/main/kotlin/com/timeflow/app/ui/screen/task/model/TaskData.ktOapp/src/main/kotlin/com/timeflow/app/ui/screen/task/model/TaskDataExtensions.ktGapp/src/main/kotlin/com/timeflow/app/ui/screen/task/model/TaskModels.ktPapp/src/main/kotlin/com/timeflow/app/ui/screen/wishlist/EnhancedAddWishDialog.ktIapp/src/main/kotlin/com/timeflow/app/ui/screen/wishlist/WishListScreen.ktYapp/src/main/kotlin/com/timeflow/app/ui/screen/wishlist/WishRealizationAnimationEffect.ktOapp/src/main/kotlin/com/timeflow/app/ui/screen/wishlist/WishStatisticsScreen.kt=app/src/main/kotlin/com/timeflow/app/ui/settings/ColorType.ktEapp/src/main/kotlin/com/timeflow/app/ui/settings/PresetThemeScreen.ktGapp/src/main/kotlin/com/timeflow/app/ui/settings/ThemeSettingsScreen.ktJapp/src/main/kotlin/com/timeflow/app/ui/settings/ThemeSettingsViewModel.kt>app/src/main/kotlin/com/timeflow/app/ui/splash/SplashScreen.ktJapp/src/main/kotlin/com/timeflow/app/ui/statistics/TimeStatisticsScreen.ktMapp/src/main/kotlin/com/timeflow/app/ui/statistics/TimeStatisticsViewModel.kt?app/src/main/kotlin/com/timeflow/app/ui/task/KanbanViewModel.ktAapp/src/main/kotlin/com/timeflow/app/ui/task/SharedFilterState.ktAapp/src/main/kotlin/com/timeflow/app/ui/task/TaskListOptimizer.kt=app/src/main/kotlin/com/timeflow/app/ui/task/TaskViewModel.ktKapp/src/main/kotlin/com/timeflow/app/ui/task/components/AiSuggestionArea.ktKapp/src/main/kotlin/com/timeflow/app/ui/task/components/AiSuggestionCard.ktOapp/src/main/kotlin/com/timeflow/app/ui/task/components/DraggableKanbanBoard.ktOapp/src/main/kotlin/com/timeflow/app/ui/task/components/HierarchicalTaskList.ktPapp/src/main/kotlin/com/timeflow/app/ui/task/components/OptimizedTaskListView.ktIapp/src/main/kotlin/com/timeflow/app/ui/task/components/PriorityPicker.ktKapp/src/main/kotlin/com/timeflow/app/ui/task/components/SegmentedControl.ktIapp/src/main/kotlin/com/timeflow/app/ui/task/components/SmartTaskInput.ktLapp/src/main/kotlin/com/timeflow/app/ui/task/components/SwipeableTaskCard.ktJapp/src/main/kotlin/com/timeflow/app/ui/task/components/TaskFilterChips.ktFapp/src/main/kotlin/com/timeflow/app/ui/task/components/TaskFilters.ktHapp/src/main/kotlin/com/timeflow/app/ui/task/components/TaskGroupList.ktIapp/src/main/kotlin/com/timeflow/app/ui/task/components/TaskListTopBar.ktKapp/src/main/kotlin/com/timeflow/app/ui/task/components/VoiceInputDialog.ktNapp/src/main/kotlin/com/timeflow/app/ui/task/components/common/BaseTaskCard.ktRapp/src/main/kotlin/com/timeflow/app/ui/task/components/common/EnhancedTaskCard.ktSapp/src/main/kotlin/com/timeflow/app/ui/task/components/common/PriorityIndicator.ktOapp/src/main/kotlin/com/timeflow/app/ui/task/components/common/TaskCardTheme.ktUapp/src/main/kotlin/com/timeflow/app/ui/task/components/common/cache/ChangeTracker.ktYapp/src/main/kotlin/com/timeflow/app/ui/task/components/common/cache/SmartCacheManager.kt[app/src/main/kotlin/com/timeflow/app/ui/task/components/common/cache/TaskRepositoryCache.ktPapp/src/main/kotlin/com/timeflow/app/ui/task/components/common/event/EventBus.ktTapp/src/main/kotlin/com/timeflow/app/ui/task/components/common/state/ManagedState.ktPapp/src/main/kotlin/com/timeflow/app/ui/task/components/common/util/RetryUtil.kt@app/src/main/kotlin/com/timeflow/app/ui/task/model/SortOption.kt@app/src/main/kotlin/com/timeflow/app/ui/task/model/TaskStatus.ktFapp/src/main/kotlin/com/timeflow/app/ui/task/model/TaskStatusChange.ktEapp/src/main/kotlin/com/timeflow/app/ui/task/model/TasksStatistics.kt>app/src/main/kotlin/com/timeflow/app/ui/task/model/ViewMode.kt?app/src/main/kotlin/com/timeflow/app/ui/theme/AnalyticsTheme.kt6app/src/main/kotlin/com/timeflow/app/ui/theme/Color.ktCapp/src/main/kotlin/com/timeflow/app/ui/theme/DynamicThemeHelper.kt;app/src/main/kotlin/com/timeflow/app/ui/theme/GlassPanel.kt6app/src/main/kotlin/com/timeflow/app/ui/theme/Shape.kt6app/src/main/kotlin/com/timeflow/app/ui/theme/Theme.kt=app/src/main/kotlin/com/timeflow/app/ui/theme/ThemeManager.kt<app/src/main/kotlin/com/timeflow/app/ui/theme/ThemeModels.kt5app/src/main/kotlin/com/timeflow/app/ui/theme/Type.ktPapp/src/main/kotlin/com/timeflow/app/ui/timetracking/TaskTimeStatisticsScreen.ktSapp/src/main/kotlin/com/timeflow/app/ui/timetracking/TaskTimeStatisticsViewModel.ktMapp/src/main/kotlin/com/timeflow/app/ui/timetracking/TimeTrackingViewModel.ktIapp/src/main/kotlin/com/timeflow/app/ui/timetracking/TimerUIComponents.ktOapp/src/main/kotlin/com/timeflow/app/ui/timetracking/components/CalendarInfo.kt\app/src/main/kotlin/com/timeflow/app/ui/timetracking/components/TaskTimeStatisticsSection.ktYapp/src/main/kotlin/com/timeflow/app/ui/timetracking/components/TimeTrackingComponents.ktYapp/src/main/kotlin/com/timeflow/app/ui/timetracking/screens/TimeAnalyticsDetailScreen.ktRapp/src/main/kotlin/com/timeflow/app/ui/timetracking/screens/TimeTrackingScreen.kt@app/src/main/kotlin/com/timeflow/app/ui/utils/DateFormatUtils.ktIapp/src/main/kotlin/com/timeflow/app/ui/viewmodel/AiAssistantViewModel.ktFapp/src/main/kotlin/com/timeflow/app/ui/viewmodel/AiConfigViewModel.ktHapp/src/main/kotlin/com/timeflow/app/ui/viewmodel/AiSettingsViewModel.ktIapp/src/main/kotlin/com/timeflow/app/ui/viewmodel/GlobalTimerViewModel.ktJapp/src/main/kotlin/com/timeflow/app/ui/viewmodel/GoalCreationViewModel.ktBapp/src/main/kotlin/com/timeflow/app/ui/viewmodel/GoalViewModel.ktCapp/src/main/kotlin/com/timeflow/app/ui/viewmodel/HabitViewModel.ktLapp/src/main/kotlin/com/timeflow/app/ui/viewmodel/MenstrualCycleViewModel.ktFapp/src/main/kotlin/com/timeflow/app/ui/viewmodel/TaskTimeViewModel.ktFapp/src/main/kotlin/com/timeflow/app/ui/viewmodel/TimeFlowViewModel.ktFapp/src/main/kotlin/com/timeflow/app/ui/viewmodel/WishListViewModel.ktLapp/src/main/kotlin/com/timeflow/app/ui/viewmodel/WishStatisticsViewModel.ktAapp/src/main/kotlin/com/timeflow/app/util/DataConsistencyFixer.ktBapp/src/main/kotlin/com/timeflow/app/util/DataConsistencyHelper.kt:app/src/main/kotlin/com/timeflow/app/util/EventListener.kt=app/src/main/kotlin/com/timeflow/app/util/HabitGoalManager.kt>app/src/main/kotlin/com/timeflow/app/util/MarkdownFormatter.kt?app/src/main/kotlin/com/timeflow/app/util/NotificationCenter.kt6app/src/main/kotlin/com/timeflow/app/util/TaskUtils.ktEapp/src/main/kotlin/com/timeflow/app/utils/ActivityContextProvider.kt@app/src/main/kotlin/com/timeflow/app/utils/ActivityExtensions.ktAapp/src/main/kotlin/com/timeflow/app/utils/AppExceptionHandler.ktEapp/src/main/kotlin/com/timeflow/app/utils/BinderTransactionHelper.kt8app/src/main/kotlin/com/timeflow/app/utils/ColorUtils.ktDapp/src/main/kotlin/com/timeflow/app/utils/ComposeScreenshotUtils.kt8app/src/main/kotlin/com/timeflow/app/utils/CycleUtils.ktCapp/src/main/kotlin/com/timeflow/app/utils/DatabaseBackupManager.kt;app/src/main/kotlin/com/timeflow/app/utils/DateTimeUtils.kt6app/src/main/kotlin/com/timeflow/app/utils/EventBus.kt?app/src/main/kotlin/com/timeflow/app/utils/HwcLutsErrorFixer.kt9app/src/main/kotlin/com/timeflow/app/utils/ImageLoader.kt8app/src/main/kotlin/com/timeflow/app/utils/ImageUtils.kt7app/src/main/kotlin/com/timeflow/app/utils/LogConfig.ktAapp/src/main/kotlin/com/timeflow/app/utils/NavigationOptimizer.kt@app/src/main/kotlin/com/timeflow/app/utils/NotificationHelper.ktJapp/src/main/kotlin/com/timeflow/app/utils/NotificationPermissionHelper.kt@app/src/main/kotlin/com/timeflow/app/utils/PerformanceMonitor.ktBapp/src/main/kotlin/com/timeflow/app/utils/PerformanceOptimizer.kt<app/src/main/kotlin/com/timeflow/app/utils/PreferenceKeys.ktDapp/src/main/kotlin/com/timeflow/app/utils/ReflectionShareManager.kt=app/src/main/kotlin/com/timeflow/app/utils/RenderOptimizer.kt9app/src/main/kotlin/com/timeflow/app/utils/RenderUtils.ktBapp/src/main/kotlin/com/timeflow/app/utils/RestrictedReflection.kt=app/src/main/kotlin/com/timeflow/app/utils/RippleOptimizer.kt=app/src/main/kotlin/com/timeflow/app/utils/SafeImageLoader.kt<app/src/main/kotlin/com/timeflow/app/utils/SafeNavigation.kt>app/src/main/kotlin/com/timeflow/app/utils/SafeParcelHelper.kt9app/src/main/kotlin/com/timeflow/app/utils/SafetyGuard.ktAapp/src/main/kotlin/com/timeflow/app/utils/SampleDataGenerator.kt=app/src/main/kotlin/com/timeflow/app/utils/SettingsManager.kt<app/src/main/kotlin/com/timeflow/app/utils/StatusBarUtils.kt>app/src/main/kotlin/com/timeflow/app/utils/SystemBarManager.ktRapp/src/main/kotlin/com/timeflow/app/utils/TaskPersistentNotificationTestHelper.kt?app/src/main/kotlin/com/timeflow/app/utils/TaskReminderUtils.kt;app/src/main/kotlin/com/timeflow/app/utils/TemplateCache.ktIapp/src/main/kotlin/com/timeflow/app/utils/TimeFlowNotificationManager.kt;app/src/main/kotlin/com/timeflow/app/utils/TimeZoneUtils.kt<app/src/main/kotlin/com/timeflow/app/utils/ViewExtensions.ktHapp/src/main/kotlin/com/timeflow/app/viewmodel/BackupRestoreViewModel.ktIapp/src/main/kotlin/com/timeflow/app/viewmodel/BackupSettingsViewModel.ktIapp/src/main/kotlin/com/timeflow/app/viewmodel/DataManagementViewModel.kt?app/src/main/kotlin/com/timeflow/app/widget/FocusTimerWidget.ktAapp/src/main/kotlin/com/timeflow/app/widget/GoalProgressWidget.kt?app/src/main/kotlin/com/timeflow/app/widget/QuickTimerWidget.kt@app/src/main/kotlin/com/timeflow/app/widget/TimeInsightWidget.ktAapp/src/main/kotlin/com/timeflow/app/widget/TimerWidgetUpdater.ktEapp/src/main/kotlin/com/timeflow/app/widget/TodayTasksDataProvider.kt?app/src/main/kotlin/com/timeflow/app/widget/TodayTasksWidget.kt@app/src/main/kotlin/com/timeflow/app/widget/WeeklyStatsWidget.ktBapp/src/main/kotlin/com/timeflow/app/widget/WidgetUpdateManager.ktAapp/src/main/kotlin/com/timeflow/app/worker/AiSuggestionWorker.kt?app/src/main/kotlin/com/timeflow/app/worker/AutoBackupWorker.kt@app/src/main/kotlin/com/timeflow/app/worker/DailyReviewWorker.ktBapp/src/main/kotlin/com/timeflow/app/worker/HabitReminderWorker.ktEapp/src/main/kotlin/com/timeflow/app/worker/OverdueTaskCheckWorker.ktOapp/src/main/kotlin/com/timeflow/app/worker/OverdueTaskCheckWorkerEntryPoint.ktBapp/src/main/kotlin/com/timeflow/app/worker/RecurringTaskWorker.ktAapp/src/main/kotlin/com/timeflow/app/worker/TaskReminderWorker.kt>app/src/main/kotlin/com/timeflow/app/data/dao/MedicationDao.kt=app/src/main/kotlin/com/timeflow/app/data/model/Converters.ktUapp/src/main/kotlin/com/timeflow/app/data/repository/impl/MedicationRepositoryImpl.kt;app/src/main/kotlin/com/timeflow/app/di/SingletonModules.kt;app/src/main/kotlin/com/timeflow/app/ui/screen/task/temp.ktJapp/src/main/kotlin/com/timeflow/app/ui/task/components/TaskGroupHeader.ktNapp/build/generated/source/buildConfig/debug/com/timeflow/app/BuildConfig.java                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              