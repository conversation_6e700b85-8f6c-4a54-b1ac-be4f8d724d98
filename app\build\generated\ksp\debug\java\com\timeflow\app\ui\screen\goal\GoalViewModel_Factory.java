package com.timeflow.app.ui.screen.goal;

import android.app.Application;
import com.timeflow.app.data.repository.AiTaskRepository;
import com.timeflow.app.data.repository.GoalRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class GoalViewModel_Factory implements Factory<GoalViewModel> {
  private final Provider<Application> applicationProvider;

  private final Provider<GoalRepository> goalRepositoryProvider;

  private final Provider<AiTaskRepository> aiTaskRepositoryProvider;

  public GoalViewModel_Factory(Provider<Application> applicationProvider,
      Provider<GoalRepository> goalRepositoryProvider,
      Provider<AiTaskRepository> aiTaskRepositoryProvider) {
    this.applicationProvider = applicationProvider;
    this.goalRepositoryProvider = goalRepositoryProvider;
    this.aiTaskRepositoryProvider = aiTaskRepositoryProvider;
  }

  @Override
  public GoalViewModel get() {
    return newInstance(applicationProvider.get(), goalRepositoryProvider.get(), aiTaskRepositoryProvider.get());
  }

  public static GoalViewModel_Factory create(Provider<Application> applicationProvider,
      Provider<GoalRepository> goalRepositoryProvider,
      Provider<AiTaskRepository> aiTaskRepositoryProvider) {
    return new GoalViewModel_Factory(applicationProvider, goalRepositoryProvider, aiTaskRepositoryProvider);
  }

  public static GoalViewModel newInstance(Application application, GoalRepository goalRepository,
      AiTaskRepository aiTaskRepository) {
    return new GoalViewModel(application, goalRepository, aiTaskRepository);
  }
}
