package com.timeflow.app.data.dao;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.timeflow.app.data.converter.Converters;
import com.timeflow.app.data.converter.DateTimeConverter;
import com.timeflow.app.data.entity.Wish;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Float;
import java.lang.IllegalStateException;
import java.lang.Integer;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class WishDao_Impl implements WishDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<Wish> __insertionAdapterOfWish;

  private final DateTimeConverter __dateTimeConverter = new DateTimeConverter();

  private final EntityDeletionOrUpdateAdapter<Wish> __deletionAdapterOfWish;

  private final EntityDeletionOrUpdateAdapter<Wish> __updateAdapterOfWish;

  private final SharedSQLiteStatement __preparedStmtOfDeleteWishById;

  private final SharedSQLiteStatement __preparedStmtOfArchiveWish;

  private final Converters __converters = new Converters();

  private final SharedSQLiteStatement __preparedStmtOfUnarchiveWish;

  private final SharedSQLiteStatement __preparedStmtOfConvertWishToGoal;

  private final SharedSQLiteStatement __preparedStmtOfMarkWishAsAchieved;

  public WishDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfWish = new EntityInsertionAdapter<Wish>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `wishes` (`id`,`title`,`description`,`category`,`priority`,`inspirationItems`,`imageUris`,`estimatedCost`,`targetTimePeriod`,`tags`,`status`,`relatedGoalId`,`createdAt`,`updatedAt`,`achievedAt`,`archivedAt`,`isArchived`,`difficulty`,`motivation`,`prerequisites`,`notes`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final Wish entity) {
        statement.bindString(1, entity.getId());
        statement.bindString(2, entity.getTitle());
        statement.bindString(3, entity.getDescription());
        statement.bindString(4, entity.getCategory());
        statement.bindLong(5, entity.getPriority());
        statement.bindString(6, entity.getInspirationItems());
        statement.bindString(7, entity.getImageUris());
        if (entity.getEstimatedCost() == null) {
          statement.bindNull(8);
        } else {
          statement.bindDouble(8, entity.getEstimatedCost());
        }
        statement.bindString(9, entity.getTargetTimePeriod());
        statement.bindString(10, entity.getTags());
        statement.bindString(11, entity.getStatus());
        if (entity.getRelatedGoalId() == null) {
          statement.bindNull(12);
        } else {
          statement.bindString(12, entity.getRelatedGoalId());
        }
        final Long _tmp = __dateTimeConverter.fromLocalDateTime(entity.getCreatedAt());
        if (_tmp == null) {
          statement.bindNull(13);
        } else {
          statement.bindLong(13, _tmp);
        }
        final Long _tmp_1 = __dateTimeConverter.fromLocalDateTime(entity.getUpdatedAt());
        if (_tmp_1 == null) {
          statement.bindNull(14);
        } else {
          statement.bindLong(14, _tmp_1);
        }
        final Long _tmp_2 = __dateTimeConverter.fromLocalDateTime(entity.getAchievedAt());
        if (_tmp_2 == null) {
          statement.bindNull(15);
        } else {
          statement.bindLong(15, _tmp_2);
        }
        final Long _tmp_3 = __dateTimeConverter.fromLocalDateTime(entity.getArchivedAt());
        if (_tmp_3 == null) {
          statement.bindNull(16);
        } else {
          statement.bindLong(16, _tmp_3);
        }
        final int _tmp_4 = entity.isArchived() ? 1 : 0;
        statement.bindLong(17, _tmp_4);
        statement.bindString(18, entity.getDifficulty());
        statement.bindString(19, entity.getMotivation());
        statement.bindString(20, entity.getPrerequisites());
        statement.bindString(21, entity.getNotes());
      }
    };
    this.__deletionAdapterOfWish = new EntityDeletionOrUpdateAdapter<Wish>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `wishes` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final Wish entity) {
        statement.bindString(1, entity.getId());
      }
    };
    this.__updateAdapterOfWish = new EntityDeletionOrUpdateAdapter<Wish>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `wishes` SET `id` = ?,`title` = ?,`description` = ?,`category` = ?,`priority` = ?,`inspirationItems` = ?,`imageUris` = ?,`estimatedCost` = ?,`targetTimePeriod` = ?,`tags` = ?,`status` = ?,`relatedGoalId` = ?,`createdAt` = ?,`updatedAt` = ?,`achievedAt` = ?,`archivedAt` = ?,`isArchived` = ?,`difficulty` = ?,`motivation` = ?,`prerequisites` = ?,`notes` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final Wish entity) {
        statement.bindString(1, entity.getId());
        statement.bindString(2, entity.getTitle());
        statement.bindString(3, entity.getDescription());
        statement.bindString(4, entity.getCategory());
        statement.bindLong(5, entity.getPriority());
        statement.bindString(6, entity.getInspirationItems());
        statement.bindString(7, entity.getImageUris());
        if (entity.getEstimatedCost() == null) {
          statement.bindNull(8);
        } else {
          statement.bindDouble(8, entity.getEstimatedCost());
        }
        statement.bindString(9, entity.getTargetTimePeriod());
        statement.bindString(10, entity.getTags());
        statement.bindString(11, entity.getStatus());
        if (entity.getRelatedGoalId() == null) {
          statement.bindNull(12);
        } else {
          statement.bindString(12, entity.getRelatedGoalId());
        }
        final Long _tmp = __dateTimeConverter.fromLocalDateTime(entity.getCreatedAt());
        if (_tmp == null) {
          statement.bindNull(13);
        } else {
          statement.bindLong(13, _tmp);
        }
        final Long _tmp_1 = __dateTimeConverter.fromLocalDateTime(entity.getUpdatedAt());
        if (_tmp_1 == null) {
          statement.bindNull(14);
        } else {
          statement.bindLong(14, _tmp_1);
        }
        final Long _tmp_2 = __dateTimeConverter.fromLocalDateTime(entity.getAchievedAt());
        if (_tmp_2 == null) {
          statement.bindNull(15);
        } else {
          statement.bindLong(15, _tmp_2);
        }
        final Long _tmp_3 = __dateTimeConverter.fromLocalDateTime(entity.getArchivedAt());
        if (_tmp_3 == null) {
          statement.bindNull(16);
        } else {
          statement.bindLong(16, _tmp_3);
        }
        final int _tmp_4 = entity.isArchived() ? 1 : 0;
        statement.bindLong(17, _tmp_4);
        statement.bindString(18, entity.getDifficulty());
        statement.bindString(19, entity.getMotivation());
        statement.bindString(20, entity.getPrerequisites());
        statement.bindString(21, entity.getNotes());
        statement.bindString(22, entity.getId());
      }
    };
    this.__preparedStmtOfDeleteWishById = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM wishes WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfArchiveWish = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE wishes SET isArchived = 1, archivedAt = ? WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfUnarchiveWish = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE wishes SET isArchived = 0, archivedAt = NULL WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfConvertWishToGoal = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE wishes SET status = 'CONVERTED_TO_GOAL', relatedGoalId = ?, updatedAt = ? WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfMarkWishAsAchieved = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE wishes SET status = 'ACHIEVED', achievedAt = ? WHERE id = ?";
        return _query;
      }
    };
  }

  @Override
  public Object insertWish(final Wish wish, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfWish.insert(wish);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object insertWishes(final List<Wish> wishes,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfWish.insert(wishes);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteWish(final Wish wish, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfWish.handle(wish);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateWish(final Wish wish, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfWish.handle(wish);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteWishById(final String wishId, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteWishById.acquire();
        int _argIndex = 1;
        _stmt.bindString(_argIndex, wishId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteWishById.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object archiveWish(final String wishId, final LocalDateTime archivedAt,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfArchiveWish.acquire();
        int _argIndex = 1;
        final Long _tmp = __converters.dateTimeToTimestamp(archivedAt);
        if (_tmp == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindLong(_argIndex, _tmp);
        }
        _argIndex = 2;
        _stmt.bindString(_argIndex, wishId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfArchiveWish.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object unarchiveWish(final String wishId, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUnarchiveWish.acquire();
        int _argIndex = 1;
        _stmt.bindString(_argIndex, wishId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUnarchiveWish.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object convertWishToGoal(final String wishId, final String goalId,
      final LocalDateTime updatedAt, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfConvertWishToGoal.acquire();
        int _argIndex = 1;
        _stmt.bindString(_argIndex, goalId);
        _argIndex = 2;
        final Long _tmp = __converters.dateTimeToTimestamp(updatedAt);
        if (_tmp == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindLong(_argIndex, _tmp);
        }
        _argIndex = 3;
        _stmt.bindString(_argIndex, wishId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfConvertWishToGoal.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object markWishAsAchieved(final String wishId, final LocalDateTime achievedAt,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfMarkWishAsAchieved.acquire();
        int _argIndex = 1;
        final Long _tmp = __converters.dateTimeToTimestamp(achievedAt);
        if (_tmp == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindLong(_argIndex, _tmp);
        }
        _argIndex = 2;
        _stmt.bindString(_argIndex, wishId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfMarkWishAsAchieved.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<Wish>> getAllActiveWishes() {
    final String _sql = "SELECT * FROM wishes WHERE isArchived = 0 ORDER BY createdAt DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"wishes"}, new Callable<List<Wish>>() {
      @Override
      @NonNull
      public List<Wish> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfPriority = CursorUtil.getColumnIndexOrThrow(_cursor, "priority");
          final int _cursorIndexOfInspirationItems = CursorUtil.getColumnIndexOrThrow(_cursor, "inspirationItems");
          final int _cursorIndexOfImageUris = CursorUtil.getColumnIndexOrThrow(_cursor, "imageUris");
          final int _cursorIndexOfEstimatedCost = CursorUtil.getColumnIndexOrThrow(_cursor, "estimatedCost");
          final int _cursorIndexOfTargetTimePeriod = CursorUtil.getColumnIndexOrThrow(_cursor, "targetTimePeriod");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfRelatedGoalId = CursorUtil.getColumnIndexOrThrow(_cursor, "relatedGoalId");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final int _cursorIndexOfAchievedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "achievedAt");
          final int _cursorIndexOfArchivedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "archivedAt");
          final int _cursorIndexOfIsArchived = CursorUtil.getColumnIndexOrThrow(_cursor, "isArchived");
          final int _cursorIndexOfDifficulty = CursorUtil.getColumnIndexOrThrow(_cursor, "difficulty");
          final int _cursorIndexOfMotivation = CursorUtil.getColumnIndexOrThrow(_cursor, "motivation");
          final int _cursorIndexOfPrerequisites = CursorUtil.getColumnIndexOrThrow(_cursor, "prerequisites");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final List<Wish> _result = new ArrayList<Wish>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Wish _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpTitle;
            _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            final String _tmpDescription;
            _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            final String _tmpCategory;
            _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            final int _tmpPriority;
            _tmpPriority = _cursor.getInt(_cursorIndexOfPriority);
            final String _tmpInspirationItems;
            _tmpInspirationItems = _cursor.getString(_cursorIndexOfInspirationItems);
            final String _tmpImageUris;
            _tmpImageUris = _cursor.getString(_cursorIndexOfImageUris);
            final Float _tmpEstimatedCost;
            if (_cursor.isNull(_cursorIndexOfEstimatedCost)) {
              _tmpEstimatedCost = null;
            } else {
              _tmpEstimatedCost = _cursor.getFloat(_cursorIndexOfEstimatedCost);
            }
            final String _tmpTargetTimePeriod;
            _tmpTargetTimePeriod = _cursor.getString(_cursorIndexOfTargetTimePeriod);
            final String _tmpTags;
            _tmpTags = _cursor.getString(_cursorIndexOfTags);
            final String _tmpStatus;
            _tmpStatus = _cursor.getString(_cursorIndexOfStatus);
            final String _tmpRelatedGoalId;
            if (_cursor.isNull(_cursorIndexOfRelatedGoalId)) {
              _tmpRelatedGoalId = null;
            } else {
              _tmpRelatedGoalId = _cursor.getString(_cursorIndexOfRelatedGoalId);
            }
            final LocalDateTime _tmpCreatedAt;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            final LocalDateTime _tmp_1 = __dateTimeConverter.toLocalDateTime(_tmp);
            if (_tmp_1 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpCreatedAt = _tmp_1;
            }
            final LocalDateTime _tmpUpdatedAt;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            final LocalDateTime _tmp_3 = __dateTimeConverter.toLocalDateTime(_tmp_2);
            if (_tmp_3 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpUpdatedAt = _tmp_3;
            }
            final LocalDateTime _tmpAchievedAt;
            final Long _tmp_4;
            if (_cursor.isNull(_cursorIndexOfAchievedAt)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getLong(_cursorIndexOfAchievedAt);
            }
            _tmpAchievedAt = __dateTimeConverter.toLocalDateTime(_tmp_4);
            final LocalDateTime _tmpArchivedAt;
            final Long _tmp_5;
            if (_cursor.isNull(_cursorIndexOfArchivedAt)) {
              _tmp_5 = null;
            } else {
              _tmp_5 = _cursor.getLong(_cursorIndexOfArchivedAt);
            }
            _tmpArchivedAt = __dateTimeConverter.toLocalDateTime(_tmp_5);
            final boolean _tmpIsArchived;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfIsArchived);
            _tmpIsArchived = _tmp_6 != 0;
            final String _tmpDifficulty;
            _tmpDifficulty = _cursor.getString(_cursorIndexOfDifficulty);
            final String _tmpMotivation;
            _tmpMotivation = _cursor.getString(_cursorIndexOfMotivation);
            final String _tmpPrerequisites;
            _tmpPrerequisites = _cursor.getString(_cursorIndexOfPrerequisites);
            final String _tmpNotes;
            _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            _item = new Wish(_tmpId,_tmpTitle,_tmpDescription,_tmpCategory,_tmpPriority,_tmpInspirationItems,_tmpImageUris,_tmpEstimatedCost,_tmpTargetTimePeriod,_tmpTags,_tmpStatus,_tmpRelatedGoalId,_tmpCreatedAt,_tmpUpdatedAt,_tmpAchievedAt,_tmpArchivedAt,_tmpIsArchived,_tmpDifficulty,_tmpMotivation,_tmpPrerequisites,_tmpNotes);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<Wish>> getAllArchivedWishes() {
    final String _sql = "SELECT * FROM wishes WHERE isArchived = 1 ORDER BY archivedAt DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"wishes"}, new Callable<List<Wish>>() {
      @Override
      @NonNull
      public List<Wish> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfPriority = CursorUtil.getColumnIndexOrThrow(_cursor, "priority");
          final int _cursorIndexOfInspirationItems = CursorUtil.getColumnIndexOrThrow(_cursor, "inspirationItems");
          final int _cursorIndexOfImageUris = CursorUtil.getColumnIndexOrThrow(_cursor, "imageUris");
          final int _cursorIndexOfEstimatedCost = CursorUtil.getColumnIndexOrThrow(_cursor, "estimatedCost");
          final int _cursorIndexOfTargetTimePeriod = CursorUtil.getColumnIndexOrThrow(_cursor, "targetTimePeriod");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfRelatedGoalId = CursorUtil.getColumnIndexOrThrow(_cursor, "relatedGoalId");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final int _cursorIndexOfAchievedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "achievedAt");
          final int _cursorIndexOfArchivedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "archivedAt");
          final int _cursorIndexOfIsArchived = CursorUtil.getColumnIndexOrThrow(_cursor, "isArchived");
          final int _cursorIndexOfDifficulty = CursorUtil.getColumnIndexOrThrow(_cursor, "difficulty");
          final int _cursorIndexOfMotivation = CursorUtil.getColumnIndexOrThrow(_cursor, "motivation");
          final int _cursorIndexOfPrerequisites = CursorUtil.getColumnIndexOrThrow(_cursor, "prerequisites");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final List<Wish> _result = new ArrayList<Wish>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Wish _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpTitle;
            _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            final String _tmpDescription;
            _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            final String _tmpCategory;
            _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            final int _tmpPriority;
            _tmpPriority = _cursor.getInt(_cursorIndexOfPriority);
            final String _tmpInspirationItems;
            _tmpInspirationItems = _cursor.getString(_cursorIndexOfInspirationItems);
            final String _tmpImageUris;
            _tmpImageUris = _cursor.getString(_cursorIndexOfImageUris);
            final Float _tmpEstimatedCost;
            if (_cursor.isNull(_cursorIndexOfEstimatedCost)) {
              _tmpEstimatedCost = null;
            } else {
              _tmpEstimatedCost = _cursor.getFloat(_cursorIndexOfEstimatedCost);
            }
            final String _tmpTargetTimePeriod;
            _tmpTargetTimePeriod = _cursor.getString(_cursorIndexOfTargetTimePeriod);
            final String _tmpTags;
            _tmpTags = _cursor.getString(_cursorIndexOfTags);
            final String _tmpStatus;
            _tmpStatus = _cursor.getString(_cursorIndexOfStatus);
            final String _tmpRelatedGoalId;
            if (_cursor.isNull(_cursorIndexOfRelatedGoalId)) {
              _tmpRelatedGoalId = null;
            } else {
              _tmpRelatedGoalId = _cursor.getString(_cursorIndexOfRelatedGoalId);
            }
            final LocalDateTime _tmpCreatedAt;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            final LocalDateTime _tmp_1 = __dateTimeConverter.toLocalDateTime(_tmp);
            if (_tmp_1 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpCreatedAt = _tmp_1;
            }
            final LocalDateTime _tmpUpdatedAt;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            final LocalDateTime _tmp_3 = __dateTimeConverter.toLocalDateTime(_tmp_2);
            if (_tmp_3 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpUpdatedAt = _tmp_3;
            }
            final LocalDateTime _tmpAchievedAt;
            final Long _tmp_4;
            if (_cursor.isNull(_cursorIndexOfAchievedAt)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getLong(_cursorIndexOfAchievedAt);
            }
            _tmpAchievedAt = __dateTimeConverter.toLocalDateTime(_tmp_4);
            final LocalDateTime _tmpArchivedAt;
            final Long _tmp_5;
            if (_cursor.isNull(_cursorIndexOfArchivedAt)) {
              _tmp_5 = null;
            } else {
              _tmp_5 = _cursor.getLong(_cursorIndexOfArchivedAt);
            }
            _tmpArchivedAt = __dateTimeConverter.toLocalDateTime(_tmp_5);
            final boolean _tmpIsArchived;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfIsArchived);
            _tmpIsArchived = _tmp_6 != 0;
            final String _tmpDifficulty;
            _tmpDifficulty = _cursor.getString(_cursorIndexOfDifficulty);
            final String _tmpMotivation;
            _tmpMotivation = _cursor.getString(_cursorIndexOfMotivation);
            final String _tmpPrerequisites;
            _tmpPrerequisites = _cursor.getString(_cursorIndexOfPrerequisites);
            final String _tmpNotes;
            _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            _item = new Wish(_tmpId,_tmpTitle,_tmpDescription,_tmpCategory,_tmpPriority,_tmpInspirationItems,_tmpImageUris,_tmpEstimatedCost,_tmpTargetTimePeriod,_tmpTags,_tmpStatus,_tmpRelatedGoalId,_tmpCreatedAt,_tmpUpdatedAt,_tmpAchievedAt,_tmpArchivedAt,_tmpIsArchived,_tmpDifficulty,_tmpMotivation,_tmpPrerequisites,_tmpNotes);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<Wish>> getAllWishes() {
    final String _sql = "SELECT * FROM wishes ORDER BY createdAt DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"wishes"}, new Callable<List<Wish>>() {
      @Override
      @NonNull
      public List<Wish> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfPriority = CursorUtil.getColumnIndexOrThrow(_cursor, "priority");
          final int _cursorIndexOfInspirationItems = CursorUtil.getColumnIndexOrThrow(_cursor, "inspirationItems");
          final int _cursorIndexOfImageUris = CursorUtil.getColumnIndexOrThrow(_cursor, "imageUris");
          final int _cursorIndexOfEstimatedCost = CursorUtil.getColumnIndexOrThrow(_cursor, "estimatedCost");
          final int _cursorIndexOfTargetTimePeriod = CursorUtil.getColumnIndexOrThrow(_cursor, "targetTimePeriod");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfRelatedGoalId = CursorUtil.getColumnIndexOrThrow(_cursor, "relatedGoalId");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final int _cursorIndexOfAchievedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "achievedAt");
          final int _cursorIndexOfArchivedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "archivedAt");
          final int _cursorIndexOfIsArchived = CursorUtil.getColumnIndexOrThrow(_cursor, "isArchived");
          final int _cursorIndexOfDifficulty = CursorUtil.getColumnIndexOrThrow(_cursor, "difficulty");
          final int _cursorIndexOfMotivation = CursorUtil.getColumnIndexOrThrow(_cursor, "motivation");
          final int _cursorIndexOfPrerequisites = CursorUtil.getColumnIndexOrThrow(_cursor, "prerequisites");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final List<Wish> _result = new ArrayList<Wish>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Wish _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpTitle;
            _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            final String _tmpDescription;
            _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            final String _tmpCategory;
            _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            final int _tmpPriority;
            _tmpPriority = _cursor.getInt(_cursorIndexOfPriority);
            final String _tmpInspirationItems;
            _tmpInspirationItems = _cursor.getString(_cursorIndexOfInspirationItems);
            final String _tmpImageUris;
            _tmpImageUris = _cursor.getString(_cursorIndexOfImageUris);
            final Float _tmpEstimatedCost;
            if (_cursor.isNull(_cursorIndexOfEstimatedCost)) {
              _tmpEstimatedCost = null;
            } else {
              _tmpEstimatedCost = _cursor.getFloat(_cursorIndexOfEstimatedCost);
            }
            final String _tmpTargetTimePeriod;
            _tmpTargetTimePeriod = _cursor.getString(_cursorIndexOfTargetTimePeriod);
            final String _tmpTags;
            _tmpTags = _cursor.getString(_cursorIndexOfTags);
            final String _tmpStatus;
            _tmpStatus = _cursor.getString(_cursorIndexOfStatus);
            final String _tmpRelatedGoalId;
            if (_cursor.isNull(_cursorIndexOfRelatedGoalId)) {
              _tmpRelatedGoalId = null;
            } else {
              _tmpRelatedGoalId = _cursor.getString(_cursorIndexOfRelatedGoalId);
            }
            final LocalDateTime _tmpCreatedAt;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            final LocalDateTime _tmp_1 = __dateTimeConverter.toLocalDateTime(_tmp);
            if (_tmp_1 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpCreatedAt = _tmp_1;
            }
            final LocalDateTime _tmpUpdatedAt;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            final LocalDateTime _tmp_3 = __dateTimeConverter.toLocalDateTime(_tmp_2);
            if (_tmp_3 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpUpdatedAt = _tmp_3;
            }
            final LocalDateTime _tmpAchievedAt;
            final Long _tmp_4;
            if (_cursor.isNull(_cursorIndexOfAchievedAt)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getLong(_cursorIndexOfAchievedAt);
            }
            _tmpAchievedAt = __dateTimeConverter.toLocalDateTime(_tmp_4);
            final LocalDateTime _tmpArchivedAt;
            final Long _tmp_5;
            if (_cursor.isNull(_cursorIndexOfArchivedAt)) {
              _tmp_5 = null;
            } else {
              _tmp_5 = _cursor.getLong(_cursorIndexOfArchivedAt);
            }
            _tmpArchivedAt = __dateTimeConverter.toLocalDateTime(_tmp_5);
            final boolean _tmpIsArchived;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfIsArchived);
            _tmpIsArchived = _tmp_6 != 0;
            final String _tmpDifficulty;
            _tmpDifficulty = _cursor.getString(_cursorIndexOfDifficulty);
            final String _tmpMotivation;
            _tmpMotivation = _cursor.getString(_cursorIndexOfMotivation);
            final String _tmpPrerequisites;
            _tmpPrerequisites = _cursor.getString(_cursorIndexOfPrerequisites);
            final String _tmpNotes;
            _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            _item = new Wish(_tmpId,_tmpTitle,_tmpDescription,_tmpCategory,_tmpPriority,_tmpInspirationItems,_tmpImageUris,_tmpEstimatedCost,_tmpTargetTimePeriod,_tmpTags,_tmpStatus,_tmpRelatedGoalId,_tmpCreatedAt,_tmpUpdatedAt,_tmpAchievedAt,_tmpArchivedAt,_tmpIsArchived,_tmpDifficulty,_tmpMotivation,_tmpPrerequisites,_tmpNotes);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getWishById(final String wishId, final Continuation<? super Wish> $completion) {
    final String _sql = "SELECT * FROM wishes WHERE id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, wishId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Wish>() {
      @Override
      @Nullable
      public Wish call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfPriority = CursorUtil.getColumnIndexOrThrow(_cursor, "priority");
          final int _cursorIndexOfInspirationItems = CursorUtil.getColumnIndexOrThrow(_cursor, "inspirationItems");
          final int _cursorIndexOfImageUris = CursorUtil.getColumnIndexOrThrow(_cursor, "imageUris");
          final int _cursorIndexOfEstimatedCost = CursorUtil.getColumnIndexOrThrow(_cursor, "estimatedCost");
          final int _cursorIndexOfTargetTimePeriod = CursorUtil.getColumnIndexOrThrow(_cursor, "targetTimePeriod");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfRelatedGoalId = CursorUtil.getColumnIndexOrThrow(_cursor, "relatedGoalId");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final int _cursorIndexOfAchievedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "achievedAt");
          final int _cursorIndexOfArchivedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "archivedAt");
          final int _cursorIndexOfIsArchived = CursorUtil.getColumnIndexOrThrow(_cursor, "isArchived");
          final int _cursorIndexOfDifficulty = CursorUtil.getColumnIndexOrThrow(_cursor, "difficulty");
          final int _cursorIndexOfMotivation = CursorUtil.getColumnIndexOrThrow(_cursor, "motivation");
          final int _cursorIndexOfPrerequisites = CursorUtil.getColumnIndexOrThrow(_cursor, "prerequisites");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final Wish _result;
          if (_cursor.moveToFirst()) {
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpTitle;
            _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            final String _tmpDescription;
            _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            final String _tmpCategory;
            _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            final int _tmpPriority;
            _tmpPriority = _cursor.getInt(_cursorIndexOfPriority);
            final String _tmpInspirationItems;
            _tmpInspirationItems = _cursor.getString(_cursorIndexOfInspirationItems);
            final String _tmpImageUris;
            _tmpImageUris = _cursor.getString(_cursorIndexOfImageUris);
            final Float _tmpEstimatedCost;
            if (_cursor.isNull(_cursorIndexOfEstimatedCost)) {
              _tmpEstimatedCost = null;
            } else {
              _tmpEstimatedCost = _cursor.getFloat(_cursorIndexOfEstimatedCost);
            }
            final String _tmpTargetTimePeriod;
            _tmpTargetTimePeriod = _cursor.getString(_cursorIndexOfTargetTimePeriod);
            final String _tmpTags;
            _tmpTags = _cursor.getString(_cursorIndexOfTags);
            final String _tmpStatus;
            _tmpStatus = _cursor.getString(_cursorIndexOfStatus);
            final String _tmpRelatedGoalId;
            if (_cursor.isNull(_cursorIndexOfRelatedGoalId)) {
              _tmpRelatedGoalId = null;
            } else {
              _tmpRelatedGoalId = _cursor.getString(_cursorIndexOfRelatedGoalId);
            }
            final LocalDateTime _tmpCreatedAt;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            final LocalDateTime _tmp_1 = __dateTimeConverter.toLocalDateTime(_tmp);
            if (_tmp_1 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpCreatedAt = _tmp_1;
            }
            final LocalDateTime _tmpUpdatedAt;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            final LocalDateTime _tmp_3 = __dateTimeConverter.toLocalDateTime(_tmp_2);
            if (_tmp_3 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpUpdatedAt = _tmp_3;
            }
            final LocalDateTime _tmpAchievedAt;
            final Long _tmp_4;
            if (_cursor.isNull(_cursorIndexOfAchievedAt)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getLong(_cursorIndexOfAchievedAt);
            }
            _tmpAchievedAt = __dateTimeConverter.toLocalDateTime(_tmp_4);
            final LocalDateTime _tmpArchivedAt;
            final Long _tmp_5;
            if (_cursor.isNull(_cursorIndexOfArchivedAt)) {
              _tmp_5 = null;
            } else {
              _tmp_5 = _cursor.getLong(_cursorIndexOfArchivedAt);
            }
            _tmpArchivedAt = __dateTimeConverter.toLocalDateTime(_tmp_5);
            final boolean _tmpIsArchived;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfIsArchived);
            _tmpIsArchived = _tmp_6 != 0;
            final String _tmpDifficulty;
            _tmpDifficulty = _cursor.getString(_cursorIndexOfDifficulty);
            final String _tmpMotivation;
            _tmpMotivation = _cursor.getString(_cursorIndexOfMotivation);
            final String _tmpPrerequisites;
            _tmpPrerequisites = _cursor.getString(_cursorIndexOfPrerequisites);
            final String _tmpNotes;
            _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            _result = new Wish(_tmpId,_tmpTitle,_tmpDescription,_tmpCategory,_tmpPriority,_tmpInspirationItems,_tmpImageUris,_tmpEstimatedCost,_tmpTargetTimePeriod,_tmpTags,_tmpStatus,_tmpRelatedGoalId,_tmpCreatedAt,_tmpUpdatedAt,_tmpAchievedAt,_tmpArchivedAt,_tmpIsArchived,_tmpDifficulty,_tmpMotivation,_tmpPrerequisites,_tmpNotes);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<Wish>> getWishesByCategory(final String category) {
    final String _sql = "SELECT * FROM wishes WHERE category = ? AND isArchived = 0 ORDER BY createdAt DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, category);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"wishes"}, new Callable<List<Wish>>() {
      @Override
      @NonNull
      public List<Wish> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfPriority = CursorUtil.getColumnIndexOrThrow(_cursor, "priority");
          final int _cursorIndexOfInspirationItems = CursorUtil.getColumnIndexOrThrow(_cursor, "inspirationItems");
          final int _cursorIndexOfImageUris = CursorUtil.getColumnIndexOrThrow(_cursor, "imageUris");
          final int _cursorIndexOfEstimatedCost = CursorUtil.getColumnIndexOrThrow(_cursor, "estimatedCost");
          final int _cursorIndexOfTargetTimePeriod = CursorUtil.getColumnIndexOrThrow(_cursor, "targetTimePeriod");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfRelatedGoalId = CursorUtil.getColumnIndexOrThrow(_cursor, "relatedGoalId");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final int _cursorIndexOfAchievedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "achievedAt");
          final int _cursorIndexOfArchivedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "archivedAt");
          final int _cursorIndexOfIsArchived = CursorUtil.getColumnIndexOrThrow(_cursor, "isArchived");
          final int _cursorIndexOfDifficulty = CursorUtil.getColumnIndexOrThrow(_cursor, "difficulty");
          final int _cursorIndexOfMotivation = CursorUtil.getColumnIndexOrThrow(_cursor, "motivation");
          final int _cursorIndexOfPrerequisites = CursorUtil.getColumnIndexOrThrow(_cursor, "prerequisites");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final List<Wish> _result = new ArrayList<Wish>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Wish _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpTitle;
            _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            final String _tmpDescription;
            _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            final String _tmpCategory;
            _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            final int _tmpPriority;
            _tmpPriority = _cursor.getInt(_cursorIndexOfPriority);
            final String _tmpInspirationItems;
            _tmpInspirationItems = _cursor.getString(_cursorIndexOfInspirationItems);
            final String _tmpImageUris;
            _tmpImageUris = _cursor.getString(_cursorIndexOfImageUris);
            final Float _tmpEstimatedCost;
            if (_cursor.isNull(_cursorIndexOfEstimatedCost)) {
              _tmpEstimatedCost = null;
            } else {
              _tmpEstimatedCost = _cursor.getFloat(_cursorIndexOfEstimatedCost);
            }
            final String _tmpTargetTimePeriod;
            _tmpTargetTimePeriod = _cursor.getString(_cursorIndexOfTargetTimePeriod);
            final String _tmpTags;
            _tmpTags = _cursor.getString(_cursorIndexOfTags);
            final String _tmpStatus;
            _tmpStatus = _cursor.getString(_cursorIndexOfStatus);
            final String _tmpRelatedGoalId;
            if (_cursor.isNull(_cursorIndexOfRelatedGoalId)) {
              _tmpRelatedGoalId = null;
            } else {
              _tmpRelatedGoalId = _cursor.getString(_cursorIndexOfRelatedGoalId);
            }
            final LocalDateTime _tmpCreatedAt;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            final LocalDateTime _tmp_1 = __dateTimeConverter.toLocalDateTime(_tmp);
            if (_tmp_1 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpCreatedAt = _tmp_1;
            }
            final LocalDateTime _tmpUpdatedAt;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            final LocalDateTime _tmp_3 = __dateTimeConverter.toLocalDateTime(_tmp_2);
            if (_tmp_3 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpUpdatedAt = _tmp_3;
            }
            final LocalDateTime _tmpAchievedAt;
            final Long _tmp_4;
            if (_cursor.isNull(_cursorIndexOfAchievedAt)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getLong(_cursorIndexOfAchievedAt);
            }
            _tmpAchievedAt = __dateTimeConverter.toLocalDateTime(_tmp_4);
            final LocalDateTime _tmpArchivedAt;
            final Long _tmp_5;
            if (_cursor.isNull(_cursorIndexOfArchivedAt)) {
              _tmp_5 = null;
            } else {
              _tmp_5 = _cursor.getLong(_cursorIndexOfArchivedAt);
            }
            _tmpArchivedAt = __dateTimeConverter.toLocalDateTime(_tmp_5);
            final boolean _tmpIsArchived;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfIsArchived);
            _tmpIsArchived = _tmp_6 != 0;
            final String _tmpDifficulty;
            _tmpDifficulty = _cursor.getString(_cursorIndexOfDifficulty);
            final String _tmpMotivation;
            _tmpMotivation = _cursor.getString(_cursorIndexOfMotivation);
            final String _tmpPrerequisites;
            _tmpPrerequisites = _cursor.getString(_cursorIndexOfPrerequisites);
            final String _tmpNotes;
            _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            _item = new Wish(_tmpId,_tmpTitle,_tmpDescription,_tmpCategory,_tmpPriority,_tmpInspirationItems,_tmpImageUris,_tmpEstimatedCost,_tmpTargetTimePeriod,_tmpTags,_tmpStatus,_tmpRelatedGoalId,_tmpCreatedAt,_tmpUpdatedAt,_tmpAchievedAt,_tmpArchivedAt,_tmpIsArchived,_tmpDifficulty,_tmpMotivation,_tmpPrerequisites,_tmpNotes);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<Wish>> getWishesByPriority(final int minPriority) {
    final String _sql = "SELECT * FROM wishes WHERE priority >= ? AND isArchived = 0 ORDER BY priority DESC, createdAt DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, minPriority);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"wishes"}, new Callable<List<Wish>>() {
      @Override
      @NonNull
      public List<Wish> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfPriority = CursorUtil.getColumnIndexOrThrow(_cursor, "priority");
          final int _cursorIndexOfInspirationItems = CursorUtil.getColumnIndexOrThrow(_cursor, "inspirationItems");
          final int _cursorIndexOfImageUris = CursorUtil.getColumnIndexOrThrow(_cursor, "imageUris");
          final int _cursorIndexOfEstimatedCost = CursorUtil.getColumnIndexOrThrow(_cursor, "estimatedCost");
          final int _cursorIndexOfTargetTimePeriod = CursorUtil.getColumnIndexOrThrow(_cursor, "targetTimePeriod");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfRelatedGoalId = CursorUtil.getColumnIndexOrThrow(_cursor, "relatedGoalId");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final int _cursorIndexOfAchievedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "achievedAt");
          final int _cursorIndexOfArchivedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "archivedAt");
          final int _cursorIndexOfIsArchived = CursorUtil.getColumnIndexOrThrow(_cursor, "isArchived");
          final int _cursorIndexOfDifficulty = CursorUtil.getColumnIndexOrThrow(_cursor, "difficulty");
          final int _cursorIndexOfMotivation = CursorUtil.getColumnIndexOrThrow(_cursor, "motivation");
          final int _cursorIndexOfPrerequisites = CursorUtil.getColumnIndexOrThrow(_cursor, "prerequisites");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final List<Wish> _result = new ArrayList<Wish>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Wish _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpTitle;
            _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            final String _tmpDescription;
            _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            final String _tmpCategory;
            _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            final int _tmpPriority;
            _tmpPriority = _cursor.getInt(_cursorIndexOfPriority);
            final String _tmpInspirationItems;
            _tmpInspirationItems = _cursor.getString(_cursorIndexOfInspirationItems);
            final String _tmpImageUris;
            _tmpImageUris = _cursor.getString(_cursorIndexOfImageUris);
            final Float _tmpEstimatedCost;
            if (_cursor.isNull(_cursorIndexOfEstimatedCost)) {
              _tmpEstimatedCost = null;
            } else {
              _tmpEstimatedCost = _cursor.getFloat(_cursorIndexOfEstimatedCost);
            }
            final String _tmpTargetTimePeriod;
            _tmpTargetTimePeriod = _cursor.getString(_cursorIndexOfTargetTimePeriod);
            final String _tmpTags;
            _tmpTags = _cursor.getString(_cursorIndexOfTags);
            final String _tmpStatus;
            _tmpStatus = _cursor.getString(_cursorIndexOfStatus);
            final String _tmpRelatedGoalId;
            if (_cursor.isNull(_cursorIndexOfRelatedGoalId)) {
              _tmpRelatedGoalId = null;
            } else {
              _tmpRelatedGoalId = _cursor.getString(_cursorIndexOfRelatedGoalId);
            }
            final LocalDateTime _tmpCreatedAt;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            final LocalDateTime _tmp_1 = __dateTimeConverter.toLocalDateTime(_tmp);
            if (_tmp_1 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpCreatedAt = _tmp_1;
            }
            final LocalDateTime _tmpUpdatedAt;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            final LocalDateTime _tmp_3 = __dateTimeConverter.toLocalDateTime(_tmp_2);
            if (_tmp_3 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpUpdatedAt = _tmp_3;
            }
            final LocalDateTime _tmpAchievedAt;
            final Long _tmp_4;
            if (_cursor.isNull(_cursorIndexOfAchievedAt)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getLong(_cursorIndexOfAchievedAt);
            }
            _tmpAchievedAt = __dateTimeConverter.toLocalDateTime(_tmp_4);
            final LocalDateTime _tmpArchivedAt;
            final Long _tmp_5;
            if (_cursor.isNull(_cursorIndexOfArchivedAt)) {
              _tmp_5 = null;
            } else {
              _tmp_5 = _cursor.getLong(_cursorIndexOfArchivedAt);
            }
            _tmpArchivedAt = __dateTimeConverter.toLocalDateTime(_tmp_5);
            final boolean _tmpIsArchived;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfIsArchived);
            _tmpIsArchived = _tmp_6 != 0;
            final String _tmpDifficulty;
            _tmpDifficulty = _cursor.getString(_cursorIndexOfDifficulty);
            final String _tmpMotivation;
            _tmpMotivation = _cursor.getString(_cursorIndexOfMotivation);
            final String _tmpPrerequisites;
            _tmpPrerequisites = _cursor.getString(_cursorIndexOfPrerequisites);
            final String _tmpNotes;
            _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            _item = new Wish(_tmpId,_tmpTitle,_tmpDescription,_tmpCategory,_tmpPriority,_tmpInspirationItems,_tmpImageUris,_tmpEstimatedCost,_tmpTargetTimePeriod,_tmpTags,_tmpStatus,_tmpRelatedGoalId,_tmpCreatedAt,_tmpUpdatedAt,_tmpAchievedAt,_tmpArchivedAt,_tmpIsArchived,_tmpDifficulty,_tmpMotivation,_tmpPrerequisites,_tmpNotes);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<Wish>> getWishesByStatus(final String status) {
    final String _sql = "SELECT * FROM wishes WHERE status = ? ORDER BY createdAt DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, status);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"wishes"}, new Callable<List<Wish>>() {
      @Override
      @NonNull
      public List<Wish> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfPriority = CursorUtil.getColumnIndexOrThrow(_cursor, "priority");
          final int _cursorIndexOfInspirationItems = CursorUtil.getColumnIndexOrThrow(_cursor, "inspirationItems");
          final int _cursorIndexOfImageUris = CursorUtil.getColumnIndexOrThrow(_cursor, "imageUris");
          final int _cursorIndexOfEstimatedCost = CursorUtil.getColumnIndexOrThrow(_cursor, "estimatedCost");
          final int _cursorIndexOfTargetTimePeriod = CursorUtil.getColumnIndexOrThrow(_cursor, "targetTimePeriod");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfRelatedGoalId = CursorUtil.getColumnIndexOrThrow(_cursor, "relatedGoalId");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final int _cursorIndexOfAchievedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "achievedAt");
          final int _cursorIndexOfArchivedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "archivedAt");
          final int _cursorIndexOfIsArchived = CursorUtil.getColumnIndexOrThrow(_cursor, "isArchived");
          final int _cursorIndexOfDifficulty = CursorUtil.getColumnIndexOrThrow(_cursor, "difficulty");
          final int _cursorIndexOfMotivation = CursorUtil.getColumnIndexOrThrow(_cursor, "motivation");
          final int _cursorIndexOfPrerequisites = CursorUtil.getColumnIndexOrThrow(_cursor, "prerequisites");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final List<Wish> _result = new ArrayList<Wish>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Wish _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpTitle;
            _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            final String _tmpDescription;
            _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            final String _tmpCategory;
            _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            final int _tmpPriority;
            _tmpPriority = _cursor.getInt(_cursorIndexOfPriority);
            final String _tmpInspirationItems;
            _tmpInspirationItems = _cursor.getString(_cursorIndexOfInspirationItems);
            final String _tmpImageUris;
            _tmpImageUris = _cursor.getString(_cursorIndexOfImageUris);
            final Float _tmpEstimatedCost;
            if (_cursor.isNull(_cursorIndexOfEstimatedCost)) {
              _tmpEstimatedCost = null;
            } else {
              _tmpEstimatedCost = _cursor.getFloat(_cursorIndexOfEstimatedCost);
            }
            final String _tmpTargetTimePeriod;
            _tmpTargetTimePeriod = _cursor.getString(_cursorIndexOfTargetTimePeriod);
            final String _tmpTags;
            _tmpTags = _cursor.getString(_cursorIndexOfTags);
            final String _tmpStatus;
            _tmpStatus = _cursor.getString(_cursorIndexOfStatus);
            final String _tmpRelatedGoalId;
            if (_cursor.isNull(_cursorIndexOfRelatedGoalId)) {
              _tmpRelatedGoalId = null;
            } else {
              _tmpRelatedGoalId = _cursor.getString(_cursorIndexOfRelatedGoalId);
            }
            final LocalDateTime _tmpCreatedAt;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            final LocalDateTime _tmp_1 = __dateTimeConverter.toLocalDateTime(_tmp);
            if (_tmp_1 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpCreatedAt = _tmp_1;
            }
            final LocalDateTime _tmpUpdatedAt;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            final LocalDateTime _tmp_3 = __dateTimeConverter.toLocalDateTime(_tmp_2);
            if (_tmp_3 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpUpdatedAt = _tmp_3;
            }
            final LocalDateTime _tmpAchievedAt;
            final Long _tmp_4;
            if (_cursor.isNull(_cursorIndexOfAchievedAt)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getLong(_cursorIndexOfAchievedAt);
            }
            _tmpAchievedAt = __dateTimeConverter.toLocalDateTime(_tmp_4);
            final LocalDateTime _tmpArchivedAt;
            final Long _tmp_5;
            if (_cursor.isNull(_cursorIndexOfArchivedAt)) {
              _tmp_5 = null;
            } else {
              _tmp_5 = _cursor.getLong(_cursorIndexOfArchivedAt);
            }
            _tmpArchivedAt = __dateTimeConverter.toLocalDateTime(_tmp_5);
            final boolean _tmpIsArchived;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfIsArchived);
            _tmpIsArchived = _tmp_6 != 0;
            final String _tmpDifficulty;
            _tmpDifficulty = _cursor.getString(_cursorIndexOfDifficulty);
            final String _tmpMotivation;
            _tmpMotivation = _cursor.getString(_cursorIndexOfMotivation);
            final String _tmpPrerequisites;
            _tmpPrerequisites = _cursor.getString(_cursorIndexOfPrerequisites);
            final String _tmpNotes;
            _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            _item = new Wish(_tmpId,_tmpTitle,_tmpDescription,_tmpCategory,_tmpPriority,_tmpInspirationItems,_tmpImageUris,_tmpEstimatedCost,_tmpTargetTimePeriod,_tmpTags,_tmpStatus,_tmpRelatedGoalId,_tmpCreatedAt,_tmpUpdatedAt,_tmpAchievedAt,_tmpArchivedAt,_tmpIsArchived,_tmpDifficulty,_tmpMotivation,_tmpPrerequisites,_tmpNotes);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getAllCategories(final Continuation<? super List<String>> $completion) {
    final String _sql = "SELECT DISTINCT category FROM wishes WHERE isArchived = 0";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<String>>() {
      @Override
      @NonNull
      public List<String> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final List<String> _result = new ArrayList<String>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final String _item;
            _item = _cursor.getString(0);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getActiveWishCount(final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT COUNT(*) FROM wishes WHERE isArchived = 0";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final int _tmp;
            _tmp = _cursor.getInt(0);
            _result = _tmp;
          } else {
            _result = 0;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getConvertedWishCount(final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT COUNT(*) FROM wishes WHERE status = 'CONVERTED_TO_GOAL'";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final int _tmp;
            _tmp = _cursor.getInt(0);
            _result = _tmp;
          } else {
            _result = 0;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<Wish>> searchWishes(final String searchQuery) {
    final String _sql = "\n"
            + "        SELECT * FROM wishes \n"
            + "        WHERE isArchived = 0 \n"
            + "        AND (title LIKE '%' || ? || '%' \n"
            + "             OR description LIKE '%' || ? || '%'\n"
            + "             OR tags LIKE '%' || ? || '%')\n"
            + "        ORDER BY createdAt DESC\n"
            + "    ";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 3);
    int _argIndex = 1;
    _statement.bindString(_argIndex, searchQuery);
    _argIndex = 2;
    _statement.bindString(_argIndex, searchQuery);
    _argIndex = 3;
    _statement.bindString(_argIndex, searchQuery);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"wishes"}, new Callable<List<Wish>>() {
      @Override
      @NonNull
      public List<Wish> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfPriority = CursorUtil.getColumnIndexOrThrow(_cursor, "priority");
          final int _cursorIndexOfInspirationItems = CursorUtil.getColumnIndexOrThrow(_cursor, "inspirationItems");
          final int _cursorIndexOfImageUris = CursorUtil.getColumnIndexOrThrow(_cursor, "imageUris");
          final int _cursorIndexOfEstimatedCost = CursorUtil.getColumnIndexOrThrow(_cursor, "estimatedCost");
          final int _cursorIndexOfTargetTimePeriod = CursorUtil.getColumnIndexOrThrow(_cursor, "targetTimePeriod");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfRelatedGoalId = CursorUtil.getColumnIndexOrThrow(_cursor, "relatedGoalId");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final int _cursorIndexOfAchievedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "achievedAt");
          final int _cursorIndexOfArchivedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "archivedAt");
          final int _cursorIndexOfIsArchived = CursorUtil.getColumnIndexOrThrow(_cursor, "isArchived");
          final int _cursorIndexOfDifficulty = CursorUtil.getColumnIndexOrThrow(_cursor, "difficulty");
          final int _cursorIndexOfMotivation = CursorUtil.getColumnIndexOrThrow(_cursor, "motivation");
          final int _cursorIndexOfPrerequisites = CursorUtil.getColumnIndexOrThrow(_cursor, "prerequisites");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final List<Wish> _result = new ArrayList<Wish>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Wish _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpTitle;
            _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            final String _tmpDescription;
            _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            final String _tmpCategory;
            _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            final int _tmpPriority;
            _tmpPriority = _cursor.getInt(_cursorIndexOfPriority);
            final String _tmpInspirationItems;
            _tmpInspirationItems = _cursor.getString(_cursorIndexOfInspirationItems);
            final String _tmpImageUris;
            _tmpImageUris = _cursor.getString(_cursorIndexOfImageUris);
            final Float _tmpEstimatedCost;
            if (_cursor.isNull(_cursorIndexOfEstimatedCost)) {
              _tmpEstimatedCost = null;
            } else {
              _tmpEstimatedCost = _cursor.getFloat(_cursorIndexOfEstimatedCost);
            }
            final String _tmpTargetTimePeriod;
            _tmpTargetTimePeriod = _cursor.getString(_cursorIndexOfTargetTimePeriod);
            final String _tmpTags;
            _tmpTags = _cursor.getString(_cursorIndexOfTags);
            final String _tmpStatus;
            _tmpStatus = _cursor.getString(_cursorIndexOfStatus);
            final String _tmpRelatedGoalId;
            if (_cursor.isNull(_cursorIndexOfRelatedGoalId)) {
              _tmpRelatedGoalId = null;
            } else {
              _tmpRelatedGoalId = _cursor.getString(_cursorIndexOfRelatedGoalId);
            }
            final LocalDateTime _tmpCreatedAt;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            final LocalDateTime _tmp_1 = __dateTimeConverter.toLocalDateTime(_tmp);
            if (_tmp_1 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpCreatedAt = _tmp_1;
            }
            final LocalDateTime _tmpUpdatedAt;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            final LocalDateTime _tmp_3 = __dateTimeConverter.toLocalDateTime(_tmp_2);
            if (_tmp_3 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpUpdatedAt = _tmp_3;
            }
            final LocalDateTime _tmpAchievedAt;
            final Long _tmp_4;
            if (_cursor.isNull(_cursorIndexOfAchievedAt)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getLong(_cursorIndexOfAchievedAt);
            }
            _tmpAchievedAt = __dateTimeConverter.toLocalDateTime(_tmp_4);
            final LocalDateTime _tmpArchivedAt;
            final Long _tmp_5;
            if (_cursor.isNull(_cursorIndexOfArchivedAt)) {
              _tmp_5 = null;
            } else {
              _tmp_5 = _cursor.getLong(_cursorIndexOfArchivedAt);
            }
            _tmpArchivedAt = __dateTimeConverter.toLocalDateTime(_tmp_5);
            final boolean _tmpIsArchived;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfIsArchived);
            _tmpIsArchived = _tmp_6 != 0;
            final String _tmpDifficulty;
            _tmpDifficulty = _cursor.getString(_cursorIndexOfDifficulty);
            final String _tmpMotivation;
            _tmpMotivation = _cursor.getString(_cursorIndexOfMotivation);
            final String _tmpPrerequisites;
            _tmpPrerequisites = _cursor.getString(_cursorIndexOfPrerequisites);
            final String _tmpNotes;
            _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            _item = new Wish(_tmpId,_tmpTitle,_tmpDescription,_tmpCategory,_tmpPriority,_tmpInspirationItems,_tmpImageUris,_tmpEstimatedCost,_tmpTargetTimePeriod,_tmpTags,_tmpStatus,_tmpRelatedGoalId,_tmpCreatedAt,_tmpUpdatedAt,_tmpAchievedAt,_tmpArchivedAt,_tmpIsArchived,_tmpDifficulty,_tmpMotivation,_tmpPrerequisites,_tmpNotes);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
