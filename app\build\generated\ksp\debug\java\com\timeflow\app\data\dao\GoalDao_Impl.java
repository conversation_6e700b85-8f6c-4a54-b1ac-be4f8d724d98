package com.timeflow.app.data.dao;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.timeflow.app.data.converter.DateTimeConverter;
import com.timeflow.app.data.entity.Goal;
import com.timeflow.app.data.entity.GoalSubTask;
import java.lang.Class;
import java.lang.Exception;
import java.lang.IllegalStateException;
import java.lang.Integer;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class GoalDao_Impl implements GoalDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<Goal> __insertionAdapterOfGoal;

  private final DateTimeConverter __dateTimeConverter = new DateTimeConverter();

  private final EntityInsertionAdapter<GoalSubTask> __insertionAdapterOfGoalSubTask;

  private final EntityDeletionOrUpdateAdapter<Goal> __deletionAdapterOfGoal;

  private final EntityDeletionOrUpdateAdapter<GoalSubTask> __deletionAdapterOfGoalSubTask;

  private final EntityDeletionOrUpdateAdapter<Goal> __updateAdapterOfGoal;

  private final EntityDeletionOrUpdateAdapter<GoalSubTask> __updateAdapterOfGoalSubTask;

  private final SharedSQLiteStatement __preparedStmtOfDeleteGoalById;

  private final SharedSQLiteStatement __preparedStmtOfDeleteSubTaskById;

  private final SharedSQLiteStatement __preparedStmtOfDeleteAllSubTasksForGoal;

  public GoalDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfGoal = new EntityInsertionAdapter<Goal>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `goals` (`id`,`title`,`description`,`startDate`,`dueDate`,`createdAt`,`updatedAt`,`completedAt`,`progress`,`priority`,`hasAiBreakdown`,`hasAiAnalysis`,`relatedTaskIds`,`aiRecommendationsJson`,`tags`,`status`,`bestTimeSlotsJson`,`metricsJson`,`reviewFrequency`,`categoryId`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final Goal entity) {
        statement.bindString(1, entity.getId());
        statement.bindString(2, entity.getTitle());
        statement.bindString(3, entity.getDescription());
        final Long _tmp = __dateTimeConverter.fromLocalDateTime(entity.getStartDate());
        if (_tmp == null) {
          statement.bindNull(4);
        } else {
          statement.bindLong(4, _tmp);
        }
        final Long _tmp_1 = __dateTimeConverter.fromLocalDateTime(entity.getDueDate());
        if (_tmp_1 == null) {
          statement.bindNull(5);
        } else {
          statement.bindLong(5, _tmp_1);
        }
        final Long _tmp_2 = __dateTimeConverter.fromLocalDateTime(entity.getCreatedAt());
        if (_tmp_2 == null) {
          statement.bindNull(6);
        } else {
          statement.bindLong(6, _tmp_2);
        }
        final Long _tmp_3 = __dateTimeConverter.fromLocalDateTime(entity.getUpdatedAt());
        if (_tmp_3 == null) {
          statement.bindNull(7);
        } else {
          statement.bindLong(7, _tmp_3);
        }
        final Long _tmp_4 = __dateTimeConverter.fromLocalDateTime(entity.getCompletedAt());
        if (_tmp_4 == null) {
          statement.bindNull(8);
        } else {
          statement.bindLong(8, _tmp_4);
        }
        statement.bindDouble(9, entity.getProgress());
        statement.bindString(10, entity.getPriority());
        final int _tmp_5 = entity.getHasAiBreakdown() ? 1 : 0;
        statement.bindLong(11, _tmp_5);
        final int _tmp_6 = entity.getHasAiAnalysis() ? 1 : 0;
        statement.bindLong(12, _tmp_6);
        statement.bindString(13, entity.getRelatedTaskIds());
        statement.bindString(14, entity.getAiRecommendationsJson());
        statement.bindString(15, entity.getTags());
        statement.bindString(16, entity.getStatus());
        statement.bindString(17, entity.getBestTimeSlotsJson());
        statement.bindString(18, entity.getMetricsJson());
        statement.bindString(19, entity.getReviewFrequency());
        statement.bindString(20, entity.getCategoryId());
      }
    };
    this.__insertionAdapterOfGoalSubTask = new EntityInsertionAdapter<GoalSubTask>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `goal_subtasks` (`id`,`goalId`,`title`,`description`,`estimatedDurationDays`,`completedAt`,`createdAt`,`aiRecommendation`,`status`) VALUES (?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final GoalSubTask entity) {
        statement.bindString(1, entity.getId());
        statement.bindString(2, entity.getGoalId());
        statement.bindString(3, entity.getTitle());
        statement.bindString(4, entity.getDescription());
        statement.bindLong(5, entity.getEstimatedDurationDays());
        final Long _tmp = __dateTimeConverter.fromLocalDateTime(entity.getCompletedAt());
        if (_tmp == null) {
          statement.bindNull(6);
        } else {
          statement.bindLong(6, _tmp);
        }
        final Long _tmp_1 = __dateTimeConverter.fromLocalDateTime(entity.getCreatedAt());
        if (_tmp_1 == null) {
          statement.bindNull(7);
        } else {
          statement.bindLong(7, _tmp_1);
        }
        if (entity.getAiRecommendation() == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, entity.getAiRecommendation());
        }
        statement.bindString(9, entity.getStatus());
      }
    };
    this.__deletionAdapterOfGoal = new EntityDeletionOrUpdateAdapter<Goal>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `goals` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final Goal entity) {
        statement.bindString(1, entity.getId());
      }
    };
    this.__deletionAdapterOfGoalSubTask = new EntityDeletionOrUpdateAdapter<GoalSubTask>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `goal_subtasks` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final GoalSubTask entity) {
        statement.bindString(1, entity.getId());
      }
    };
    this.__updateAdapterOfGoal = new EntityDeletionOrUpdateAdapter<Goal>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `goals` SET `id` = ?,`title` = ?,`description` = ?,`startDate` = ?,`dueDate` = ?,`createdAt` = ?,`updatedAt` = ?,`completedAt` = ?,`progress` = ?,`priority` = ?,`hasAiBreakdown` = ?,`hasAiAnalysis` = ?,`relatedTaskIds` = ?,`aiRecommendationsJson` = ?,`tags` = ?,`status` = ?,`bestTimeSlotsJson` = ?,`metricsJson` = ?,`reviewFrequency` = ?,`categoryId` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final Goal entity) {
        statement.bindString(1, entity.getId());
        statement.bindString(2, entity.getTitle());
        statement.bindString(3, entity.getDescription());
        final Long _tmp = __dateTimeConverter.fromLocalDateTime(entity.getStartDate());
        if (_tmp == null) {
          statement.bindNull(4);
        } else {
          statement.bindLong(4, _tmp);
        }
        final Long _tmp_1 = __dateTimeConverter.fromLocalDateTime(entity.getDueDate());
        if (_tmp_1 == null) {
          statement.bindNull(5);
        } else {
          statement.bindLong(5, _tmp_1);
        }
        final Long _tmp_2 = __dateTimeConverter.fromLocalDateTime(entity.getCreatedAt());
        if (_tmp_2 == null) {
          statement.bindNull(6);
        } else {
          statement.bindLong(6, _tmp_2);
        }
        final Long _tmp_3 = __dateTimeConverter.fromLocalDateTime(entity.getUpdatedAt());
        if (_tmp_3 == null) {
          statement.bindNull(7);
        } else {
          statement.bindLong(7, _tmp_3);
        }
        final Long _tmp_4 = __dateTimeConverter.fromLocalDateTime(entity.getCompletedAt());
        if (_tmp_4 == null) {
          statement.bindNull(8);
        } else {
          statement.bindLong(8, _tmp_4);
        }
        statement.bindDouble(9, entity.getProgress());
        statement.bindString(10, entity.getPriority());
        final int _tmp_5 = entity.getHasAiBreakdown() ? 1 : 0;
        statement.bindLong(11, _tmp_5);
        final int _tmp_6 = entity.getHasAiAnalysis() ? 1 : 0;
        statement.bindLong(12, _tmp_6);
        statement.bindString(13, entity.getRelatedTaskIds());
        statement.bindString(14, entity.getAiRecommendationsJson());
        statement.bindString(15, entity.getTags());
        statement.bindString(16, entity.getStatus());
        statement.bindString(17, entity.getBestTimeSlotsJson());
        statement.bindString(18, entity.getMetricsJson());
        statement.bindString(19, entity.getReviewFrequency());
        statement.bindString(20, entity.getCategoryId());
        statement.bindString(21, entity.getId());
      }
    };
    this.__updateAdapterOfGoalSubTask = new EntityDeletionOrUpdateAdapter<GoalSubTask>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `goal_subtasks` SET `id` = ?,`goalId` = ?,`title` = ?,`description` = ?,`estimatedDurationDays` = ?,`completedAt` = ?,`createdAt` = ?,`aiRecommendation` = ?,`status` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final GoalSubTask entity) {
        statement.bindString(1, entity.getId());
        statement.bindString(2, entity.getGoalId());
        statement.bindString(3, entity.getTitle());
        statement.bindString(4, entity.getDescription());
        statement.bindLong(5, entity.getEstimatedDurationDays());
        final Long _tmp = __dateTimeConverter.fromLocalDateTime(entity.getCompletedAt());
        if (_tmp == null) {
          statement.bindNull(6);
        } else {
          statement.bindLong(6, _tmp);
        }
        final Long _tmp_1 = __dateTimeConverter.fromLocalDateTime(entity.getCreatedAt());
        if (_tmp_1 == null) {
          statement.bindNull(7);
        } else {
          statement.bindLong(7, _tmp_1);
        }
        if (entity.getAiRecommendation() == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, entity.getAiRecommendation());
        }
        statement.bindString(9, entity.getStatus());
        statement.bindString(10, entity.getId());
      }
    };
    this.__preparedStmtOfDeleteGoalById = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM goals WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteSubTaskById = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM goal_subtasks WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteAllSubTasksForGoal = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM goal_subtasks WHERE goalId = ?";
        return _query;
      }
    };
  }

  @Override
  public Object insertGoal(final Goal goal, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfGoal.insert(goal);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object insertSubTask(final GoalSubTask subTask,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfGoalSubTask.insert(subTask);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object insertAllSubTasks(final List<GoalSubTask> subTasks,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfGoalSubTask.insert(subTasks);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteGoal(final Goal goal, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfGoal.handle(goal);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteSubTask(final GoalSubTask subTask,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfGoalSubTask.handle(subTask);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateGoal(final Goal goal, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfGoal.handle(goal);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateSubTask(final GoalSubTask subTask,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfGoalSubTask.handle(subTask);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteGoalById(final String goalId, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteGoalById.acquire();
        int _argIndex = 1;
        _stmt.bindString(_argIndex, goalId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteGoalById.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteSubTaskById(final String subTaskId,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteSubTaskById.acquire();
        int _argIndex = 1;
        _stmt.bindString(_argIndex, subTaskId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteSubTaskById.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteAllSubTasksForGoal(final String goalId,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteAllSubTasksForGoal.acquire();
        int _argIndex = 1;
        _stmt.bindString(_argIndex, goalId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteAllSubTasksForGoal.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object getGoalById(final String goalId, final Continuation<? super Goal> $completion) {
    final String _sql = "SELECT * FROM goals WHERE id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, goalId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Goal>() {
      @Override
      @Nullable
      public Goal call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfStartDate = CursorUtil.getColumnIndexOrThrow(_cursor, "startDate");
          final int _cursorIndexOfDueDate = CursorUtil.getColumnIndexOrThrow(_cursor, "dueDate");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final int _cursorIndexOfCompletedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "completedAt");
          final int _cursorIndexOfProgress = CursorUtil.getColumnIndexOrThrow(_cursor, "progress");
          final int _cursorIndexOfPriority = CursorUtil.getColumnIndexOrThrow(_cursor, "priority");
          final int _cursorIndexOfHasAiBreakdown = CursorUtil.getColumnIndexOrThrow(_cursor, "hasAiBreakdown");
          final int _cursorIndexOfHasAiAnalysis = CursorUtil.getColumnIndexOrThrow(_cursor, "hasAiAnalysis");
          final int _cursorIndexOfRelatedTaskIds = CursorUtil.getColumnIndexOrThrow(_cursor, "relatedTaskIds");
          final int _cursorIndexOfAiRecommendationsJson = CursorUtil.getColumnIndexOrThrow(_cursor, "aiRecommendationsJson");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfBestTimeSlotsJson = CursorUtil.getColumnIndexOrThrow(_cursor, "bestTimeSlotsJson");
          final int _cursorIndexOfMetricsJson = CursorUtil.getColumnIndexOrThrow(_cursor, "metricsJson");
          final int _cursorIndexOfReviewFrequency = CursorUtil.getColumnIndexOrThrow(_cursor, "reviewFrequency");
          final int _cursorIndexOfCategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "categoryId");
          final Goal _result;
          if (_cursor.moveToFirst()) {
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpTitle;
            _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            final String _tmpDescription;
            _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            final LocalDateTime _tmpStartDate;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfStartDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfStartDate);
            }
            final LocalDateTime _tmp_1 = __dateTimeConverter.toLocalDateTime(_tmp);
            if (_tmp_1 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpStartDate = _tmp_1;
            }
            final LocalDateTime _tmpDueDate;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfDueDate)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfDueDate);
            }
            _tmpDueDate = __dateTimeConverter.toLocalDateTime(_tmp_2);
            final LocalDateTime _tmpCreatedAt;
            final Long _tmp_3;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            final LocalDateTime _tmp_4 = __dateTimeConverter.toLocalDateTime(_tmp_3);
            if (_tmp_4 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpCreatedAt = _tmp_4;
            }
            final LocalDateTime _tmpUpdatedAt;
            final Long _tmp_5;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmp_5 = null;
            } else {
              _tmp_5 = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            final LocalDateTime _tmp_6 = __dateTimeConverter.toLocalDateTime(_tmp_5);
            if (_tmp_6 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpUpdatedAt = _tmp_6;
            }
            final LocalDateTime _tmpCompletedAt;
            final Long _tmp_7;
            if (_cursor.isNull(_cursorIndexOfCompletedAt)) {
              _tmp_7 = null;
            } else {
              _tmp_7 = _cursor.getLong(_cursorIndexOfCompletedAt);
            }
            _tmpCompletedAt = __dateTimeConverter.toLocalDateTime(_tmp_7);
            final float _tmpProgress;
            _tmpProgress = _cursor.getFloat(_cursorIndexOfProgress);
            final String _tmpPriority;
            _tmpPriority = _cursor.getString(_cursorIndexOfPriority);
            final boolean _tmpHasAiBreakdown;
            final int _tmp_8;
            _tmp_8 = _cursor.getInt(_cursorIndexOfHasAiBreakdown);
            _tmpHasAiBreakdown = _tmp_8 != 0;
            final boolean _tmpHasAiAnalysis;
            final int _tmp_9;
            _tmp_9 = _cursor.getInt(_cursorIndexOfHasAiAnalysis);
            _tmpHasAiAnalysis = _tmp_9 != 0;
            final String _tmpRelatedTaskIds;
            _tmpRelatedTaskIds = _cursor.getString(_cursorIndexOfRelatedTaskIds);
            final String _tmpAiRecommendationsJson;
            _tmpAiRecommendationsJson = _cursor.getString(_cursorIndexOfAiRecommendationsJson);
            final String _tmpTags;
            _tmpTags = _cursor.getString(_cursorIndexOfTags);
            final String _tmpStatus;
            _tmpStatus = _cursor.getString(_cursorIndexOfStatus);
            final String _tmpBestTimeSlotsJson;
            _tmpBestTimeSlotsJson = _cursor.getString(_cursorIndexOfBestTimeSlotsJson);
            final String _tmpMetricsJson;
            _tmpMetricsJson = _cursor.getString(_cursorIndexOfMetricsJson);
            final String _tmpReviewFrequency;
            _tmpReviewFrequency = _cursor.getString(_cursorIndexOfReviewFrequency);
            final String _tmpCategoryId;
            _tmpCategoryId = _cursor.getString(_cursorIndexOfCategoryId);
            _result = new Goal(_tmpId,_tmpTitle,_tmpDescription,_tmpStartDate,_tmpDueDate,_tmpCreatedAt,_tmpUpdatedAt,_tmpCompletedAt,_tmpProgress,_tmpPriority,_tmpHasAiBreakdown,_tmpHasAiAnalysis,_tmpRelatedTaskIds,_tmpAiRecommendationsJson,_tmpTags,_tmpStatus,_tmpBestTimeSlotsJson,_tmpMetricsJson,_tmpReviewFrequency,_tmpCategoryId);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<Goal>> getAllGoals() {
    final String _sql = "SELECT * FROM goals ORDER BY updatedAt DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"goals"}, new Callable<List<Goal>>() {
      @Override
      @NonNull
      public List<Goal> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfStartDate = CursorUtil.getColumnIndexOrThrow(_cursor, "startDate");
          final int _cursorIndexOfDueDate = CursorUtil.getColumnIndexOrThrow(_cursor, "dueDate");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final int _cursorIndexOfCompletedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "completedAt");
          final int _cursorIndexOfProgress = CursorUtil.getColumnIndexOrThrow(_cursor, "progress");
          final int _cursorIndexOfPriority = CursorUtil.getColumnIndexOrThrow(_cursor, "priority");
          final int _cursorIndexOfHasAiBreakdown = CursorUtil.getColumnIndexOrThrow(_cursor, "hasAiBreakdown");
          final int _cursorIndexOfHasAiAnalysis = CursorUtil.getColumnIndexOrThrow(_cursor, "hasAiAnalysis");
          final int _cursorIndexOfRelatedTaskIds = CursorUtil.getColumnIndexOrThrow(_cursor, "relatedTaskIds");
          final int _cursorIndexOfAiRecommendationsJson = CursorUtil.getColumnIndexOrThrow(_cursor, "aiRecommendationsJson");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfBestTimeSlotsJson = CursorUtil.getColumnIndexOrThrow(_cursor, "bestTimeSlotsJson");
          final int _cursorIndexOfMetricsJson = CursorUtil.getColumnIndexOrThrow(_cursor, "metricsJson");
          final int _cursorIndexOfReviewFrequency = CursorUtil.getColumnIndexOrThrow(_cursor, "reviewFrequency");
          final int _cursorIndexOfCategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "categoryId");
          final List<Goal> _result = new ArrayList<Goal>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Goal _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpTitle;
            _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            final String _tmpDescription;
            _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            final LocalDateTime _tmpStartDate;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfStartDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfStartDate);
            }
            final LocalDateTime _tmp_1 = __dateTimeConverter.toLocalDateTime(_tmp);
            if (_tmp_1 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpStartDate = _tmp_1;
            }
            final LocalDateTime _tmpDueDate;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfDueDate)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfDueDate);
            }
            _tmpDueDate = __dateTimeConverter.toLocalDateTime(_tmp_2);
            final LocalDateTime _tmpCreatedAt;
            final Long _tmp_3;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            final LocalDateTime _tmp_4 = __dateTimeConverter.toLocalDateTime(_tmp_3);
            if (_tmp_4 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpCreatedAt = _tmp_4;
            }
            final LocalDateTime _tmpUpdatedAt;
            final Long _tmp_5;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmp_5 = null;
            } else {
              _tmp_5 = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            final LocalDateTime _tmp_6 = __dateTimeConverter.toLocalDateTime(_tmp_5);
            if (_tmp_6 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpUpdatedAt = _tmp_6;
            }
            final LocalDateTime _tmpCompletedAt;
            final Long _tmp_7;
            if (_cursor.isNull(_cursorIndexOfCompletedAt)) {
              _tmp_7 = null;
            } else {
              _tmp_7 = _cursor.getLong(_cursorIndexOfCompletedAt);
            }
            _tmpCompletedAt = __dateTimeConverter.toLocalDateTime(_tmp_7);
            final float _tmpProgress;
            _tmpProgress = _cursor.getFloat(_cursorIndexOfProgress);
            final String _tmpPriority;
            _tmpPriority = _cursor.getString(_cursorIndexOfPriority);
            final boolean _tmpHasAiBreakdown;
            final int _tmp_8;
            _tmp_8 = _cursor.getInt(_cursorIndexOfHasAiBreakdown);
            _tmpHasAiBreakdown = _tmp_8 != 0;
            final boolean _tmpHasAiAnalysis;
            final int _tmp_9;
            _tmp_9 = _cursor.getInt(_cursorIndexOfHasAiAnalysis);
            _tmpHasAiAnalysis = _tmp_9 != 0;
            final String _tmpRelatedTaskIds;
            _tmpRelatedTaskIds = _cursor.getString(_cursorIndexOfRelatedTaskIds);
            final String _tmpAiRecommendationsJson;
            _tmpAiRecommendationsJson = _cursor.getString(_cursorIndexOfAiRecommendationsJson);
            final String _tmpTags;
            _tmpTags = _cursor.getString(_cursorIndexOfTags);
            final String _tmpStatus;
            _tmpStatus = _cursor.getString(_cursorIndexOfStatus);
            final String _tmpBestTimeSlotsJson;
            _tmpBestTimeSlotsJson = _cursor.getString(_cursorIndexOfBestTimeSlotsJson);
            final String _tmpMetricsJson;
            _tmpMetricsJson = _cursor.getString(_cursorIndexOfMetricsJson);
            final String _tmpReviewFrequency;
            _tmpReviewFrequency = _cursor.getString(_cursorIndexOfReviewFrequency);
            final String _tmpCategoryId;
            _tmpCategoryId = _cursor.getString(_cursorIndexOfCategoryId);
            _item = new Goal(_tmpId,_tmpTitle,_tmpDescription,_tmpStartDate,_tmpDueDate,_tmpCreatedAt,_tmpUpdatedAt,_tmpCompletedAt,_tmpProgress,_tmpPriority,_tmpHasAiBreakdown,_tmpHasAiAnalysis,_tmpRelatedTaskIds,_tmpAiRecommendationsJson,_tmpTags,_tmpStatus,_tmpBestTimeSlotsJson,_tmpMetricsJson,_tmpReviewFrequency,_tmpCategoryId);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getAllGoalsList(final Continuation<? super List<Goal>> $completion) {
    final String _sql = "SELECT * FROM goals ORDER BY updatedAt DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<Goal>>() {
      @Override
      @NonNull
      public List<Goal> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfStartDate = CursorUtil.getColumnIndexOrThrow(_cursor, "startDate");
          final int _cursorIndexOfDueDate = CursorUtil.getColumnIndexOrThrow(_cursor, "dueDate");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final int _cursorIndexOfCompletedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "completedAt");
          final int _cursorIndexOfProgress = CursorUtil.getColumnIndexOrThrow(_cursor, "progress");
          final int _cursorIndexOfPriority = CursorUtil.getColumnIndexOrThrow(_cursor, "priority");
          final int _cursorIndexOfHasAiBreakdown = CursorUtil.getColumnIndexOrThrow(_cursor, "hasAiBreakdown");
          final int _cursorIndexOfHasAiAnalysis = CursorUtil.getColumnIndexOrThrow(_cursor, "hasAiAnalysis");
          final int _cursorIndexOfRelatedTaskIds = CursorUtil.getColumnIndexOrThrow(_cursor, "relatedTaskIds");
          final int _cursorIndexOfAiRecommendationsJson = CursorUtil.getColumnIndexOrThrow(_cursor, "aiRecommendationsJson");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfBestTimeSlotsJson = CursorUtil.getColumnIndexOrThrow(_cursor, "bestTimeSlotsJson");
          final int _cursorIndexOfMetricsJson = CursorUtil.getColumnIndexOrThrow(_cursor, "metricsJson");
          final int _cursorIndexOfReviewFrequency = CursorUtil.getColumnIndexOrThrow(_cursor, "reviewFrequency");
          final int _cursorIndexOfCategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "categoryId");
          final List<Goal> _result = new ArrayList<Goal>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Goal _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpTitle;
            _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            final String _tmpDescription;
            _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            final LocalDateTime _tmpStartDate;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfStartDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfStartDate);
            }
            final LocalDateTime _tmp_1 = __dateTimeConverter.toLocalDateTime(_tmp);
            if (_tmp_1 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpStartDate = _tmp_1;
            }
            final LocalDateTime _tmpDueDate;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfDueDate)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfDueDate);
            }
            _tmpDueDate = __dateTimeConverter.toLocalDateTime(_tmp_2);
            final LocalDateTime _tmpCreatedAt;
            final Long _tmp_3;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            final LocalDateTime _tmp_4 = __dateTimeConverter.toLocalDateTime(_tmp_3);
            if (_tmp_4 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpCreatedAt = _tmp_4;
            }
            final LocalDateTime _tmpUpdatedAt;
            final Long _tmp_5;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmp_5 = null;
            } else {
              _tmp_5 = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            final LocalDateTime _tmp_6 = __dateTimeConverter.toLocalDateTime(_tmp_5);
            if (_tmp_6 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpUpdatedAt = _tmp_6;
            }
            final LocalDateTime _tmpCompletedAt;
            final Long _tmp_7;
            if (_cursor.isNull(_cursorIndexOfCompletedAt)) {
              _tmp_7 = null;
            } else {
              _tmp_7 = _cursor.getLong(_cursorIndexOfCompletedAt);
            }
            _tmpCompletedAt = __dateTimeConverter.toLocalDateTime(_tmp_7);
            final float _tmpProgress;
            _tmpProgress = _cursor.getFloat(_cursorIndexOfProgress);
            final String _tmpPriority;
            _tmpPriority = _cursor.getString(_cursorIndexOfPriority);
            final boolean _tmpHasAiBreakdown;
            final int _tmp_8;
            _tmp_8 = _cursor.getInt(_cursorIndexOfHasAiBreakdown);
            _tmpHasAiBreakdown = _tmp_8 != 0;
            final boolean _tmpHasAiAnalysis;
            final int _tmp_9;
            _tmp_9 = _cursor.getInt(_cursorIndexOfHasAiAnalysis);
            _tmpHasAiAnalysis = _tmp_9 != 0;
            final String _tmpRelatedTaskIds;
            _tmpRelatedTaskIds = _cursor.getString(_cursorIndexOfRelatedTaskIds);
            final String _tmpAiRecommendationsJson;
            _tmpAiRecommendationsJson = _cursor.getString(_cursorIndexOfAiRecommendationsJson);
            final String _tmpTags;
            _tmpTags = _cursor.getString(_cursorIndexOfTags);
            final String _tmpStatus;
            _tmpStatus = _cursor.getString(_cursorIndexOfStatus);
            final String _tmpBestTimeSlotsJson;
            _tmpBestTimeSlotsJson = _cursor.getString(_cursorIndexOfBestTimeSlotsJson);
            final String _tmpMetricsJson;
            _tmpMetricsJson = _cursor.getString(_cursorIndexOfMetricsJson);
            final String _tmpReviewFrequency;
            _tmpReviewFrequency = _cursor.getString(_cursorIndexOfReviewFrequency);
            final String _tmpCategoryId;
            _tmpCategoryId = _cursor.getString(_cursorIndexOfCategoryId);
            _item = new Goal(_tmpId,_tmpTitle,_tmpDescription,_tmpStartDate,_tmpDueDate,_tmpCreatedAt,_tmpUpdatedAt,_tmpCompletedAt,_tmpProgress,_tmpPriority,_tmpHasAiBreakdown,_tmpHasAiAnalysis,_tmpRelatedTaskIds,_tmpAiRecommendationsJson,_tmpTags,_tmpStatus,_tmpBestTimeSlotsJson,_tmpMetricsJson,_tmpReviewFrequency,_tmpCategoryId);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<Goal>> getGoalsByStatus(final String status) {
    final String _sql = "SELECT * FROM goals WHERE status = ? ORDER BY updatedAt DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, status);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"goals"}, new Callable<List<Goal>>() {
      @Override
      @NonNull
      public List<Goal> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfStartDate = CursorUtil.getColumnIndexOrThrow(_cursor, "startDate");
          final int _cursorIndexOfDueDate = CursorUtil.getColumnIndexOrThrow(_cursor, "dueDate");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final int _cursorIndexOfCompletedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "completedAt");
          final int _cursorIndexOfProgress = CursorUtil.getColumnIndexOrThrow(_cursor, "progress");
          final int _cursorIndexOfPriority = CursorUtil.getColumnIndexOrThrow(_cursor, "priority");
          final int _cursorIndexOfHasAiBreakdown = CursorUtil.getColumnIndexOrThrow(_cursor, "hasAiBreakdown");
          final int _cursorIndexOfHasAiAnalysis = CursorUtil.getColumnIndexOrThrow(_cursor, "hasAiAnalysis");
          final int _cursorIndexOfRelatedTaskIds = CursorUtil.getColumnIndexOrThrow(_cursor, "relatedTaskIds");
          final int _cursorIndexOfAiRecommendationsJson = CursorUtil.getColumnIndexOrThrow(_cursor, "aiRecommendationsJson");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfBestTimeSlotsJson = CursorUtil.getColumnIndexOrThrow(_cursor, "bestTimeSlotsJson");
          final int _cursorIndexOfMetricsJson = CursorUtil.getColumnIndexOrThrow(_cursor, "metricsJson");
          final int _cursorIndexOfReviewFrequency = CursorUtil.getColumnIndexOrThrow(_cursor, "reviewFrequency");
          final int _cursorIndexOfCategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "categoryId");
          final List<Goal> _result = new ArrayList<Goal>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Goal _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpTitle;
            _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            final String _tmpDescription;
            _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            final LocalDateTime _tmpStartDate;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfStartDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfStartDate);
            }
            final LocalDateTime _tmp_1 = __dateTimeConverter.toLocalDateTime(_tmp);
            if (_tmp_1 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpStartDate = _tmp_1;
            }
            final LocalDateTime _tmpDueDate;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfDueDate)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfDueDate);
            }
            _tmpDueDate = __dateTimeConverter.toLocalDateTime(_tmp_2);
            final LocalDateTime _tmpCreatedAt;
            final Long _tmp_3;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            final LocalDateTime _tmp_4 = __dateTimeConverter.toLocalDateTime(_tmp_3);
            if (_tmp_4 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpCreatedAt = _tmp_4;
            }
            final LocalDateTime _tmpUpdatedAt;
            final Long _tmp_5;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmp_5 = null;
            } else {
              _tmp_5 = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            final LocalDateTime _tmp_6 = __dateTimeConverter.toLocalDateTime(_tmp_5);
            if (_tmp_6 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpUpdatedAt = _tmp_6;
            }
            final LocalDateTime _tmpCompletedAt;
            final Long _tmp_7;
            if (_cursor.isNull(_cursorIndexOfCompletedAt)) {
              _tmp_7 = null;
            } else {
              _tmp_7 = _cursor.getLong(_cursorIndexOfCompletedAt);
            }
            _tmpCompletedAt = __dateTimeConverter.toLocalDateTime(_tmp_7);
            final float _tmpProgress;
            _tmpProgress = _cursor.getFloat(_cursorIndexOfProgress);
            final String _tmpPriority;
            _tmpPriority = _cursor.getString(_cursorIndexOfPriority);
            final boolean _tmpHasAiBreakdown;
            final int _tmp_8;
            _tmp_8 = _cursor.getInt(_cursorIndexOfHasAiBreakdown);
            _tmpHasAiBreakdown = _tmp_8 != 0;
            final boolean _tmpHasAiAnalysis;
            final int _tmp_9;
            _tmp_9 = _cursor.getInt(_cursorIndexOfHasAiAnalysis);
            _tmpHasAiAnalysis = _tmp_9 != 0;
            final String _tmpRelatedTaskIds;
            _tmpRelatedTaskIds = _cursor.getString(_cursorIndexOfRelatedTaskIds);
            final String _tmpAiRecommendationsJson;
            _tmpAiRecommendationsJson = _cursor.getString(_cursorIndexOfAiRecommendationsJson);
            final String _tmpTags;
            _tmpTags = _cursor.getString(_cursorIndexOfTags);
            final String _tmpStatus;
            _tmpStatus = _cursor.getString(_cursorIndexOfStatus);
            final String _tmpBestTimeSlotsJson;
            _tmpBestTimeSlotsJson = _cursor.getString(_cursorIndexOfBestTimeSlotsJson);
            final String _tmpMetricsJson;
            _tmpMetricsJson = _cursor.getString(_cursorIndexOfMetricsJson);
            final String _tmpReviewFrequency;
            _tmpReviewFrequency = _cursor.getString(_cursorIndexOfReviewFrequency);
            final String _tmpCategoryId;
            _tmpCategoryId = _cursor.getString(_cursorIndexOfCategoryId);
            _item = new Goal(_tmpId,_tmpTitle,_tmpDescription,_tmpStartDate,_tmpDueDate,_tmpCreatedAt,_tmpUpdatedAt,_tmpCompletedAt,_tmpProgress,_tmpPriority,_tmpHasAiBreakdown,_tmpHasAiAnalysis,_tmpRelatedTaskIds,_tmpAiRecommendationsJson,_tmpTags,_tmpStatus,_tmpBestTimeSlotsJson,_tmpMetricsJson,_tmpReviewFrequency,_tmpCategoryId);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<Goal>> getGoalsByCategory(final String categoryId) {
    final String _sql = "SELECT * FROM goals WHERE categoryId = ? ORDER BY updatedAt DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, categoryId);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"goals"}, new Callable<List<Goal>>() {
      @Override
      @NonNull
      public List<Goal> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfStartDate = CursorUtil.getColumnIndexOrThrow(_cursor, "startDate");
          final int _cursorIndexOfDueDate = CursorUtil.getColumnIndexOrThrow(_cursor, "dueDate");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final int _cursorIndexOfCompletedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "completedAt");
          final int _cursorIndexOfProgress = CursorUtil.getColumnIndexOrThrow(_cursor, "progress");
          final int _cursorIndexOfPriority = CursorUtil.getColumnIndexOrThrow(_cursor, "priority");
          final int _cursorIndexOfHasAiBreakdown = CursorUtil.getColumnIndexOrThrow(_cursor, "hasAiBreakdown");
          final int _cursorIndexOfHasAiAnalysis = CursorUtil.getColumnIndexOrThrow(_cursor, "hasAiAnalysis");
          final int _cursorIndexOfRelatedTaskIds = CursorUtil.getColumnIndexOrThrow(_cursor, "relatedTaskIds");
          final int _cursorIndexOfAiRecommendationsJson = CursorUtil.getColumnIndexOrThrow(_cursor, "aiRecommendationsJson");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfBestTimeSlotsJson = CursorUtil.getColumnIndexOrThrow(_cursor, "bestTimeSlotsJson");
          final int _cursorIndexOfMetricsJson = CursorUtil.getColumnIndexOrThrow(_cursor, "metricsJson");
          final int _cursorIndexOfReviewFrequency = CursorUtil.getColumnIndexOrThrow(_cursor, "reviewFrequency");
          final int _cursorIndexOfCategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "categoryId");
          final List<Goal> _result = new ArrayList<Goal>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Goal _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpTitle;
            _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            final String _tmpDescription;
            _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            final LocalDateTime _tmpStartDate;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfStartDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfStartDate);
            }
            final LocalDateTime _tmp_1 = __dateTimeConverter.toLocalDateTime(_tmp);
            if (_tmp_1 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpStartDate = _tmp_1;
            }
            final LocalDateTime _tmpDueDate;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfDueDate)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfDueDate);
            }
            _tmpDueDate = __dateTimeConverter.toLocalDateTime(_tmp_2);
            final LocalDateTime _tmpCreatedAt;
            final Long _tmp_3;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            final LocalDateTime _tmp_4 = __dateTimeConverter.toLocalDateTime(_tmp_3);
            if (_tmp_4 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpCreatedAt = _tmp_4;
            }
            final LocalDateTime _tmpUpdatedAt;
            final Long _tmp_5;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmp_5 = null;
            } else {
              _tmp_5 = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            final LocalDateTime _tmp_6 = __dateTimeConverter.toLocalDateTime(_tmp_5);
            if (_tmp_6 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpUpdatedAt = _tmp_6;
            }
            final LocalDateTime _tmpCompletedAt;
            final Long _tmp_7;
            if (_cursor.isNull(_cursorIndexOfCompletedAt)) {
              _tmp_7 = null;
            } else {
              _tmp_7 = _cursor.getLong(_cursorIndexOfCompletedAt);
            }
            _tmpCompletedAt = __dateTimeConverter.toLocalDateTime(_tmp_7);
            final float _tmpProgress;
            _tmpProgress = _cursor.getFloat(_cursorIndexOfProgress);
            final String _tmpPriority;
            _tmpPriority = _cursor.getString(_cursorIndexOfPriority);
            final boolean _tmpHasAiBreakdown;
            final int _tmp_8;
            _tmp_8 = _cursor.getInt(_cursorIndexOfHasAiBreakdown);
            _tmpHasAiBreakdown = _tmp_8 != 0;
            final boolean _tmpHasAiAnalysis;
            final int _tmp_9;
            _tmp_9 = _cursor.getInt(_cursorIndexOfHasAiAnalysis);
            _tmpHasAiAnalysis = _tmp_9 != 0;
            final String _tmpRelatedTaskIds;
            _tmpRelatedTaskIds = _cursor.getString(_cursorIndexOfRelatedTaskIds);
            final String _tmpAiRecommendationsJson;
            _tmpAiRecommendationsJson = _cursor.getString(_cursorIndexOfAiRecommendationsJson);
            final String _tmpTags;
            _tmpTags = _cursor.getString(_cursorIndexOfTags);
            final String _tmpStatus;
            _tmpStatus = _cursor.getString(_cursorIndexOfStatus);
            final String _tmpBestTimeSlotsJson;
            _tmpBestTimeSlotsJson = _cursor.getString(_cursorIndexOfBestTimeSlotsJson);
            final String _tmpMetricsJson;
            _tmpMetricsJson = _cursor.getString(_cursorIndexOfMetricsJson);
            final String _tmpReviewFrequency;
            _tmpReviewFrequency = _cursor.getString(_cursorIndexOfReviewFrequency);
            final String _tmpCategoryId;
            _tmpCategoryId = _cursor.getString(_cursorIndexOfCategoryId);
            _item = new Goal(_tmpId,_tmpTitle,_tmpDescription,_tmpStartDate,_tmpDueDate,_tmpCreatedAt,_tmpUpdatedAt,_tmpCompletedAt,_tmpProgress,_tmpPriority,_tmpHasAiBreakdown,_tmpHasAiAnalysis,_tmpRelatedTaskIds,_tmpAiRecommendationsJson,_tmpTags,_tmpStatus,_tmpBestTimeSlotsJson,_tmpMetricsJson,_tmpReviewFrequency,_tmpCategoryId);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<Goal>> getGoalsByCategoryAndStatus(final String categoryId,
      final String status) {
    final String _sql = "SELECT * FROM goals WHERE categoryId = ? AND status = ? ORDER BY updatedAt DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindString(_argIndex, categoryId);
    _argIndex = 2;
    _statement.bindString(_argIndex, status);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"goals"}, new Callable<List<Goal>>() {
      @Override
      @NonNull
      public List<Goal> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfStartDate = CursorUtil.getColumnIndexOrThrow(_cursor, "startDate");
          final int _cursorIndexOfDueDate = CursorUtil.getColumnIndexOrThrow(_cursor, "dueDate");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final int _cursorIndexOfCompletedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "completedAt");
          final int _cursorIndexOfProgress = CursorUtil.getColumnIndexOrThrow(_cursor, "progress");
          final int _cursorIndexOfPriority = CursorUtil.getColumnIndexOrThrow(_cursor, "priority");
          final int _cursorIndexOfHasAiBreakdown = CursorUtil.getColumnIndexOrThrow(_cursor, "hasAiBreakdown");
          final int _cursorIndexOfHasAiAnalysis = CursorUtil.getColumnIndexOrThrow(_cursor, "hasAiAnalysis");
          final int _cursorIndexOfRelatedTaskIds = CursorUtil.getColumnIndexOrThrow(_cursor, "relatedTaskIds");
          final int _cursorIndexOfAiRecommendationsJson = CursorUtil.getColumnIndexOrThrow(_cursor, "aiRecommendationsJson");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfBestTimeSlotsJson = CursorUtil.getColumnIndexOrThrow(_cursor, "bestTimeSlotsJson");
          final int _cursorIndexOfMetricsJson = CursorUtil.getColumnIndexOrThrow(_cursor, "metricsJson");
          final int _cursorIndexOfReviewFrequency = CursorUtil.getColumnIndexOrThrow(_cursor, "reviewFrequency");
          final int _cursorIndexOfCategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "categoryId");
          final List<Goal> _result = new ArrayList<Goal>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Goal _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpTitle;
            _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            final String _tmpDescription;
            _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            final LocalDateTime _tmpStartDate;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfStartDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfStartDate);
            }
            final LocalDateTime _tmp_1 = __dateTimeConverter.toLocalDateTime(_tmp);
            if (_tmp_1 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpStartDate = _tmp_1;
            }
            final LocalDateTime _tmpDueDate;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfDueDate)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfDueDate);
            }
            _tmpDueDate = __dateTimeConverter.toLocalDateTime(_tmp_2);
            final LocalDateTime _tmpCreatedAt;
            final Long _tmp_3;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            final LocalDateTime _tmp_4 = __dateTimeConverter.toLocalDateTime(_tmp_3);
            if (_tmp_4 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpCreatedAt = _tmp_4;
            }
            final LocalDateTime _tmpUpdatedAt;
            final Long _tmp_5;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmp_5 = null;
            } else {
              _tmp_5 = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            final LocalDateTime _tmp_6 = __dateTimeConverter.toLocalDateTime(_tmp_5);
            if (_tmp_6 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpUpdatedAt = _tmp_6;
            }
            final LocalDateTime _tmpCompletedAt;
            final Long _tmp_7;
            if (_cursor.isNull(_cursorIndexOfCompletedAt)) {
              _tmp_7 = null;
            } else {
              _tmp_7 = _cursor.getLong(_cursorIndexOfCompletedAt);
            }
            _tmpCompletedAt = __dateTimeConverter.toLocalDateTime(_tmp_7);
            final float _tmpProgress;
            _tmpProgress = _cursor.getFloat(_cursorIndexOfProgress);
            final String _tmpPriority;
            _tmpPriority = _cursor.getString(_cursorIndexOfPriority);
            final boolean _tmpHasAiBreakdown;
            final int _tmp_8;
            _tmp_8 = _cursor.getInt(_cursorIndexOfHasAiBreakdown);
            _tmpHasAiBreakdown = _tmp_8 != 0;
            final boolean _tmpHasAiAnalysis;
            final int _tmp_9;
            _tmp_9 = _cursor.getInt(_cursorIndexOfHasAiAnalysis);
            _tmpHasAiAnalysis = _tmp_9 != 0;
            final String _tmpRelatedTaskIds;
            _tmpRelatedTaskIds = _cursor.getString(_cursorIndexOfRelatedTaskIds);
            final String _tmpAiRecommendationsJson;
            _tmpAiRecommendationsJson = _cursor.getString(_cursorIndexOfAiRecommendationsJson);
            final String _tmpTags;
            _tmpTags = _cursor.getString(_cursorIndexOfTags);
            final String _tmpStatus;
            _tmpStatus = _cursor.getString(_cursorIndexOfStatus);
            final String _tmpBestTimeSlotsJson;
            _tmpBestTimeSlotsJson = _cursor.getString(_cursorIndexOfBestTimeSlotsJson);
            final String _tmpMetricsJson;
            _tmpMetricsJson = _cursor.getString(_cursorIndexOfMetricsJson);
            final String _tmpReviewFrequency;
            _tmpReviewFrequency = _cursor.getString(_cursorIndexOfReviewFrequency);
            final String _tmpCategoryId;
            _tmpCategoryId = _cursor.getString(_cursorIndexOfCategoryId);
            _item = new Goal(_tmpId,_tmpTitle,_tmpDescription,_tmpStartDate,_tmpDueDate,_tmpCreatedAt,_tmpUpdatedAt,_tmpCompletedAt,_tmpProgress,_tmpPriority,_tmpHasAiBreakdown,_tmpHasAiAnalysis,_tmpRelatedTaskIds,_tmpAiRecommendationsJson,_tmpTags,_tmpStatus,_tmpBestTimeSlotsJson,_tmpMetricsJson,_tmpReviewFrequency,_tmpCategoryId);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getAllUsedCategories(final Continuation<? super List<String>> $completion) {
    final String _sql = "SELECT DISTINCT categoryId FROM goals ORDER BY categoryId";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<String>>() {
      @Override
      @NonNull
      public List<String> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final List<String> _result = new ArrayList<String>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final String _item;
            _item = _cursor.getString(0);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<Goal>> getActiveGoals() {
    final String _sql = "SELECT * FROM goals WHERE completedAt IS NULL ORDER BY dueDate ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"goals"}, new Callable<List<Goal>>() {
      @Override
      @NonNull
      public List<Goal> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfStartDate = CursorUtil.getColumnIndexOrThrow(_cursor, "startDate");
          final int _cursorIndexOfDueDate = CursorUtil.getColumnIndexOrThrow(_cursor, "dueDate");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final int _cursorIndexOfCompletedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "completedAt");
          final int _cursorIndexOfProgress = CursorUtil.getColumnIndexOrThrow(_cursor, "progress");
          final int _cursorIndexOfPriority = CursorUtil.getColumnIndexOrThrow(_cursor, "priority");
          final int _cursorIndexOfHasAiBreakdown = CursorUtil.getColumnIndexOrThrow(_cursor, "hasAiBreakdown");
          final int _cursorIndexOfHasAiAnalysis = CursorUtil.getColumnIndexOrThrow(_cursor, "hasAiAnalysis");
          final int _cursorIndexOfRelatedTaskIds = CursorUtil.getColumnIndexOrThrow(_cursor, "relatedTaskIds");
          final int _cursorIndexOfAiRecommendationsJson = CursorUtil.getColumnIndexOrThrow(_cursor, "aiRecommendationsJson");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfBestTimeSlotsJson = CursorUtil.getColumnIndexOrThrow(_cursor, "bestTimeSlotsJson");
          final int _cursorIndexOfMetricsJson = CursorUtil.getColumnIndexOrThrow(_cursor, "metricsJson");
          final int _cursorIndexOfReviewFrequency = CursorUtil.getColumnIndexOrThrow(_cursor, "reviewFrequency");
          final int _cursorIndexOfCategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "categoryId");
          final List<Goal> _result = new ArrayList<Goal>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Goal _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpTitle;
            _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            final String _tmpDescription;
            _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            final LocalDateTime _tmpStartDate;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfStartDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfStartDate);
            }
            final LocalDateTime _tmp_1 = __dateTimeConverter.toLocalDateTime(_tmp);
            if (_tmp_1 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpStartDate = _tmp_1;
            }
            final LocalDateTime _tmpDueDate;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfDueDate)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfDueDate);
            }
            _tmpDueDate = __dateTimeConverter.toLocalDateTime(_tmp_2);
            final LocalDateTime _tmpCreatedAt;
            final Long _tmp_3;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            final LocalDateTime _tmp_4 = __dateTimeConverter.toLocalDateTime(_tmp_3);
            if (_tmp_4 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpCreatedAt = _tmp_4;
            }
            final LocalDateTime _tmpUpdatedAt;
            final Long _tmp_5;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmp_5 = null;
            } else {
              _tmp_5 = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            final LocalDateTime _tmp_6 = __dateTimeConverter.toLocalDateTime(_tmp_5);
            if (_tmp_6 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpUpdatedAt = _tmp_6;
            }
            final LocalDateTime _tmpCompletedAt;
            final Long _tmp_7;
            if (_cursor.isNull(_cursorIndexOfCompletedAt)) {
              _tmp_7 = null;
            } else {
              _tmp_7 = _cursor.getLong(_cursorIndexOfCompletedAt);
            }
            _tmpCompletedAt = __dateTimeConverter.toLocalDateTime(_tmp_7);
            final float _tmpProgress;
            _tmpProgress = _cursor.getFloat(_cursorIndexOfProgress);
            final String _tmpPriority;
            _tmpPriority = _cursor.getString(_cursorIndexOfPriority);
            final boolean _tmpHasAiBreakdown;
            final int _tmp_8;
            _tmp_8 = _cursor.getInt(_cursorIndexOfHasAiBreakdown);
            _tmpHasAiBreakdown = _tmp_8 != 0;
            final boolean _tmpHasAiAnalysis;
            final int _tmp_9;
            _tmp_9 = _cursor.getInt(_cursorIndexOfHasAiAnalysis);
            _tmpHasAiAnalysis = _tmp_9 != 0;
            final String _tmpRelatedTaskIds;
            _tmpRelatedTaskIds = _cursor.getString(_cursorIndexOfRelatedTaskIds);
            final String _tmpAiRecommendationsJson;
            _tmpAiRecommendationsJson = _cursor.getString(_cursorIndexOfAiRecommendationsJson);
            final String _tmpTags;
            _tmpTags = _cursor.getString(_cursorIndexOfTags);
            final String _tmpStatus;
            _tmpStatus = _cursor.getString(_cursorIndexOfStatus);
            final String _tmpBestTimeSlotsJson;
            _tmpBestTimeSlotsJson = _cursor.getString(_cursorIndexOfBestTimeSlotsJson);
            final String _tmpMetricsJson;
            _tmpMetricsJson = _cursor.getString(_cursorIndexOfMetricsJson);
            final String _tmpReviewFrequency;
            _tmpReviewFrequency = _cursor.getString(_cursorIndexOfReviewFrequency);
            final String _tmpCategoryId;
            _tmpCategoryId = _cursor.getString(_cursorIndexOfCategoryId);
            _item = new Goal(_tmpId,_tmpTitle,_tmpDescription,_tmpStartDate,_tmpDueDate,_tmpCreatedAt,_tmpUpdatedAt,_tmpCompletedAt,_tmpProgress,_tmpPriority,_tmpHasAiBreakdown,_tmpHasAiAnalysis,_tmpRelatedTaskIds,_tmpAiRecommendationsJson,_tmpTags,_tmpStatus,_tmpBestTimeSlotsJson,_tmpMetricsJson,_tmpReviewFrequency,_tmpCategoryId);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<Goal>> getCompletedGoals() {
    final String _sql = "SELECT * FROM goals WHERE completedAt IS NOT NULL ORDER BY completedAt DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"goals"}, new Callable<List<Goal>>() {
      @Override
      @NonNull
      public List<Goal> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfStartDate = CursorUtil.getColumnIndexOrThrow(_cursor, "startDate");
          final int _cursorIndexOfDueDate = CursorUtil.getColumnIndexOrThrow(_cursor, "dueDate");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final int _cursorIndexOfCompletedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "completedAt");
          final int _cursorIndexOfProgress = CursorUtil.getColumnIndexOrThrow(_cursor, "progress");
          final int _cursorIndexOfPriority = CursorUtil.getColumnIndexOrThrow(_cursor, "priority");
          final int _cursorIndexOfHasAiBreakdown = CursorUtil.getColumnIndexOrThrow(_cursor, "hasAiBreakdown");
          final int _cursorIndexOfHasAiAnalysis = CursorUtil.getColumnIndexOrThrow(_cursor, "hasAiAnalysis");
          final int _cursorIndexOfRelatedTaskIds = CursorUtil.getColumnIndexOrThrow(_cursor, "relatedTaskIds");
          final int _cursorIndexOfAiRecommendationsJson = CursorUtil.getColumnIndexOrThrow(_cursor, "aiRecommendationsJson");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfBestTimeSlotsJson = CursorUtil.getColumnIndexOrThrow(_cursor, "bestTimeSlotsJson");
          final int _cursorIndexOfMetricsJson = CursorUtil.getColumnIndexOrThrow(_cursor, "metricsJson");
          final int _cursorIndexOfReviewFrequency = CursorUtil.getColumnIndexOrThrow(_cursor, "reviewFrequency");
          final int _cursorIndexOfCategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "categoryId");
          final List<Goal> _result = new ArrayList<Goal>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Goal _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpTitle;
            _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            final String _tmpDescription;
            _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            final LocalDateTime _tmpStartDate;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfStartDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfStartDate);
            }
            final LocalDateTime _tmp_1 = __dateTimeConverter.toLocalDateTime(_tmp);
            if (_tmp_1 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpStartDate = _tmp_1;
            }
            final LocalDateTime _tmpDueDate;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfDueDate)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfDueDate);
            }
            _tmpDueDate = __dateTimeConverter.toLocalDateTime(_tmp_2);
            final LocalDateTime _tmpCreatedAt;
            final Long _tmp_3;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            final LocalDateTime _tmp_4 = __dateTimeConverter.toLocalDateTime(_tmp_3);
            if (_tmp_4 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpCreatedAt = _tmp_4;
            }
            final LocalDateTime _tmpUpdatedAt;
            final Long _tmp_5;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmp_5 = null;
            } else {
              _tmp_5 = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            final LocalDateTime _tmp_6 = __dateTimeConverter.toLocalDateTime(_tmp_5);
            if (_tmp_6 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpUpdatedAt = _tmp_6;
            }
            final LocalDateTime _tmpCompletedAt;
            final Long _tmp_7;
            if (_cursor.isNull(_cursorIndexOfCompletedAt)) {
              _tmp_7 = null;
            } else {
              _tmp_7 = _cursor.getLong(_cursorIndexOfCompletedAt);
            }
            _tmpCompletedAt = __dateTimeConverter.toLocalDateTime(_tmp_7);
            final float _tmpProgress;
            _tmpProgress = _cursor.getFloat(_cursorIndexOfProgress);
            final String _tmpPriority;
            _tmpPriority = _cursor.getString(_cursorIndexOfPriority);
            final boolean _tmpHasAiBreakdown;
            final int _tmp_8;
            _tmp_8 = _cursor.getInt(_cursorIndexOfHasAiBreakdown);
            _tmpHasAiBreakdown = _tmp_8 != 0;
            final boolean _tmpHasAiAnalysis;
            final int _tmp_9;
            _tmp_9 = _cursor.getInt(_cursorIndexOfHasAiAnalysis);
            _tmpHasAiAnalysis = _tmp_9 != 0;
            final String _tmpRelatedTaskIds;
            _tmpRelatedTaskIds = _cursor.getString(_cursorIndexOfRelatedTaskIds);
            final String _tmpAiRecommendationsJson;
            _tmpAiRecommendationsJson = _cursor.getString(_cursorIndexOfAiRecommendationsJson);
            final String _tmpTags;
            _tmpTags = _cursor.getString(_cursorIndexOfTags);
            final String _tmpStatus;
            _tmpStatus = _cursor.getString(_cursorIndexOfStatus);
            final String _tmpBestTimeSlotsJson;
            _tmpBestTimeSlotsJson = _cursor.getString(_cursorIndexOfBestTimeSlotsJson);
            final String _tmpMetricsJson;
            _tmpMetricsJson = _cursor.getString(_cursorIndexOfMetricsJson);
            final String _tmpReviewFrequency;
            _tmpReviewFrequency = _cursor.getString(_cursorIndexOfReviewFrequency);
            final String _tmpCategoryId;
            _tmpCategoryId = _cursor.getString(_cursorIndexOfCategoryId);
            _item = new Goal(_tmpId,_tmpTitle,_tmpDescription,_tmpStartDate,_tmpDueDate,_tmpCreatedAt,_tmpUpdatedAt,_tmpCompletedAt,_tmpProgress,_tmpPriority,_tmpHasAiBreakdown,_tmpHasAiAnalysis,_tmpRelatedTaskIds,_tmpAiRecommendationsJson,_tmpTags,_tmpStatus,_tmpBestTimeSlotsJson,_tmpMetricsJson,_tmpReviewFrequency,_tmpCategoryId);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<Goal>> getGoalsWithoutAiBreakdown() {
    final String _sql = "SELECT * FROM goals WHERE hasAiBreakdown = 0 ORDER BY createdAt DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"goals"}, new Callable<List<Goal>>() {
      @Override
      @NonNull
      public List<Goal> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfStartDate = CursorUtil.getColumnIndexOrThrow(_cursor, "startDate");
          final int _cursorIndexOfDueDate = CursorUtil.getColumnIndexOrThrow(_cursor, "dueDate");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final int _cursorIndexOfCompletedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "completedAt");
          final int _cursorIndexOfProgress = CursorUtil.getColumnIndexOrThrow(_cursor, "progress");
          final int _cursorIndexOfPriority = CursorUtil.getColumnIndexOrThrow(_cursor, "priority");
          final int _cursorIndexOfHasAiBreakdown = CursorUtil.getColumnIndexOrThrow(_cursor, "hasAiBreakdown");
          final int _cursorIndexOfHasAiAnalysis = CursorUtil.getColumnIndexOrThrow(_cursor, "hasAiAnalysis");
          final int _cursorIndexOfRelatedTaskIds = CursorUtil.getColumnIndexOrThrow(_cursor, "relatedTaskIds");
          final int _cursorIndexOfAiRecommendationsJson = CursorUtil.getColumnIndexOrThrow(_cursor, "aiRecommendationsJson");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfBestTimeSlotsJson = CursorUtil.getColumnIndexOrThrow(_cursor, "bestTimeSlotsJson");
          final int _cursorIndexOfMetricsJson = CursorUtil.getColumnIndexOrThrow(_cursor, "metricsJson");
          final int _cursorIndexOfReviewFrequency = CursorUtil.getColumnIndexOrThrow(_cursor, "reviewFrequency");
          final int _cursorIndexOfCategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "categoryId");
          final List<Goal> _result = new ArrayList<Goal>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Goal _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpTitle;
            _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            final String _tmpDescription;
            _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            final LocalDateTime _tmpStartDate;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfStartDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfStartDate);
            }
            final LocalDateTime _tmp_1 = __dateTimeConverter.toLocalDateTime(_tmp);
            if (_tmp_1 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpStartDate = _tmp_1;
            }
            final LocalDateTime _tmpDueDate;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfDueDate)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfDueDate);
            }
            _tmpDueDate = __dateTimeConverter.toLocalDateTime(_tmp_2);
            final LocalDateTime _tmpCreatedAt;
            final Long _tmp_3;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            final LocalDateTime _tmp_4 = __dateTimeConverter.toLocalDateTime(_tmp_3);
            if (_tmp_4 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpCreatedAt = _tmp_4;
            }
            final LocalDateTime _tmpUpdatedAt;
            final Long _tmp_5;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmp_5 = null;
            } else {
              _tmp_5 = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            final LocalDateTime _tmp_6 = __dateTimeConverter.toLocalDateTime(_tmp_5);
            if (_tmp_6 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpUpdatedAt = _tmp_6;
            }
            final LocalDateTime _tmpCompletedAt;
            final Long _tmp_7;
            if (_cursor.isNull(_cursorIndexOfCompletedAt)) {
              _tmp_7 = null;
            } else {
              _tmp_7 = _cursor.getLong(_cursorIndexOfCompletedAt);
            }
            _tmpCompletedAt = __dateTimeConverter.toLocalDateTime(_tmp_7);
            final float _tmpProgress;
            _tmpProgress = _cursor.getFloat(_cursorIndexOfProgress);
            final String _tmpPriority;
            _tmpPriority = _cursor.getString(_cursorIndexOfPriority);
            final boolean _tmpHasAiBreakdown;
            final int _tmp_8;
            _tmp_8 = _cursor.getInt(_cursorIndexOfHasAiBreakdown);
            _tmpHasAiBreakdown = _tmp_8 != 0;
            final boolean _tmpHasAiAnalysis;
            final int _tmp_9;
            _tmp_9 = _cursor.getInt(_cursorIndexOfHasAiAnalysis);
            _tmpHasAiAnalysis = _tmp_9 != 0;
            final String _tmpRelatedTaskIds;
            _tmpRelatedTaskIds = _cursor.getString(_cursorIndexOfRelatedTaskIds);
            final String _tmpAiRecommendationsJson;
            _tmpAiRecommendationsJson = _cursor.getString(_cursorIndexOfAiRecommendationsJson);
            final String _tmpTags;
            _tmpTags = _cursor.getString(_cursorIndexOfTags);
            final String _tmpStatus;
            _tmpStatus = _cursor.getString(_cursorIndexOfStatus);
            final String _tmpBestTimeSlotsJson;
            _tmpBestTimeSlotsJson = _cursor.getString(_cursorIndexOfBestTimeSlotsJson);
            final String _tmpMetricsJson;
            _tmpMetricsJson = _cursor.getString(_cursorIndexOfMetricsJson);
            final String _tmpReviewFrequency;
            _tmpReviewFrequency = _cursor.getString(_cursorIndexOfReviewFrequency);
            final String _tmpCategoryId;
            _tmpCategoryId = _cursor.getString(_cursorIndexOfCategoryId);
            _item = new Goal(_tmpId,_tmpTitle,_tmpDescription,_tmpStartDate,_tmpDueDate,_tmpCreatedAt,_tmpUpdatedAt,_tmpCompletedAt,_tmpProgress,_tmpPriority,_tmpHasAiBreakdown,_tmpHasAiAnalysis,_tmpRelatedTaskIds,_tmpAiRecommendationsJson,_tmpTags,_tmpStatus,_tmpBestTimeSlotsJson,_tmpMetricsJson,_tmpReviewFrequency,_tmpCategoryId);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<Goal>> getGoalsNeedingReview() {
    final String _sql = "SELECT * FROM goals WHERE hasAiAnalysis = 0 AND completedAt IS NOT NULL ORDER BY completedAt DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"goals"}, new Callable<List<Goal>>() {
      @Override
      @NonNull
      public List<Goal> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfStartDate = CursorUtil.getColumnIndexOrThrow(_cursor, "startDate");
          final int _cursorIndexOfDueDate = CursorUtil.getColumnIndexOrThrow(_cursor, "dueDate");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final int _cursorIndexOfCompletedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "completedAt");
          final int _cursorIndexOfProgress = CursorUtil.getColumnIndexOrThrow(_cursor, "progress");
          final int _cursorIndexOfPriority = CursorUtil.getColumnIndexOrThrow(_cursor, "priority");
          final int _cursorIndexOfHasAiBreakdown = CursorUtil.getColumnIndexOrThrow(_cursor, "hasAiBreakdown");
          final int _cursorIndexOfHasAiAnalysis = CursorUtil.getColumnIndexOrThrow(_cursor, "hasAiAnalysis");
          final int _cursorIndexOfRelatedTaskIds = CursorUtil.getColumnIndexOrThrow(_cursor, "relatedTaskIds");
          final int _cursorIndexOfAiRecommendationsJson = CursorUtil.getColumnIndexOrThrow(_cursor, "aiRecommendationsJson");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfBestTimeSlotsJson = CursorUtil.getColumnIndexOrThrow(_cursor, "bestTimeSlotsJson");
          final int _cursorIndexOfMetricsJson = CursorUtil.getColumnIndexOrThrow(_cursor, "metricsJson");
          final int _cursorIndexOfReviewFrequency = CursorUtil.getColumnIndexOrThrow(_cursor, "reviewFrequency");
          final int _cursorIndexOfCategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "categoryId");
          final List<Goal> _result = new ArrayList<Goal>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Goal _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpTitle;
            _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            final String _tmpDescription;
            _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            final LocalDateTime _tmpStartDate;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfStartDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfStartDate);
            }
            final LocalDateTime _tmp_1 = __dateTimeConverter.toLocalDateTime(_tmp);
            if (_tmp_1 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpStartDate = _tmp_1;
            }
            final LocalDateTime _tmpDueDate;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfDueDate)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfDueDate);
            }
            _tmpDueDate = __dateTimeConverter.toLocalDateTime(_tmp_2);
            final LocalDateTime _tmpCreatedAt;
            final Long _tmp_3;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            final LocalDateTime _tmp_4 = __dateTimeConverter.toLocalDateTime(_tmp_3);
            if (_tmp_4 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpCreatedAt = _tmp_4;
            }
            final LocalDateTime _tmpUpdatedAt;
            final Long _tmp_5;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmp_5 = null;
            } else {
              _tmp_5 = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            final LocalDateTime _tmp_6 = __dateTimeConverter.toLocalDateTime(_tmp_5);
            if (_tmp_6 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpUpdatedAt = _tmp_6;
            }
            final LocalDateTime _tmpCompletedAt;
            final Long _tmp_7;
            if (_cursor.isNull(_cursorIndexOfCompletedAt)) {
              _tmp_7 = null;
            } else {
              _tmp_7 = _cursor.getLong(_cursorIndexOfCompletedAt);
            }
            _tmpCompletedAt = __dateTimeConverter.toLocalDateTime(_tmp_7);
            final float _tmpProgress;
            _tmpProgress = _cursor.getFloat(_cursorIndexOfProgress);
            final String _tmpPriority;
            _tmpPriority = _cursor.getString(_cursorIndexOfPriority);
            final boolean _tmpHasAiBreakdown;
            final int _tmp_8;
            _tmp_8 = _cursor.getInt(_cursorIndexOfHasAiBreakdown);
            _tmpHasAiBreakdown = _tmp_8 != 0;
            final boolean _tmpHasAiAnalysis;
            final int _tmp_9;
            _tmp_9 = _cursor.getInt(_cursorIndexOfHasAiAnalysis);
            _tmpHasAiAnalysis = _tmp_9 != 0;
            final String _tmpRelatedTaskIds;
            _tmpRelatedTaskIds = _cursor.getString(_cursorIndexOfRelatedTaskIds);
            final String _tmpAiRecommendationsJson;
            _tmpAiRecommendationsJson = _cursor.getString(_cursorIndexOfAiRecommendationsJson);
            final String _tmpTags;
            _tmpTags = _cursor.getString(_cursorIndexOfTags);
            final String _tmpStatus;
            _tmpStatus = _cursor.getString(_cursorIndexOfStatus);
            final String _tmpBestTimeSlotsJson;
            _tmpBestTimeSlotsJson = _cursor.getString(_cursorIndexOfBestTimeSlotsJson);
            final String _tmpMetricsJson;
            _tmpMetricsJson = _cursor.getString(_cursorIndexOfMetricsJson);
            final String _tmpReviewFrequency;
            _tmpReviewFrequency = _cursor.getString(_cursorIndexOfReviewFrequency);
            final String _tmpCategoryId;
            _tmpCategoryId = _cursor.getString(_cursorIndexOfCategoryId);
            _item = new Goal(_tmpId,_tmpTitle,_tmpDescription,_tmpStartDate,_tmpDueDate,_tmpCreatedAt,_tmpUpdatedAt,_tmpCompletedAt,_tmpProgress,_tmpPriority,_tmpHasAiBreakdown,_tmpHasAiAnalysis,_tmpRelatedTaskIds,_tmpAiRecommendationsJson,_tmpTags,_tmpStatus,_tmpBestTimeSlotsJson,_tmpMetricsJson,_tmpReviewFrequency,_tmpCategoryId);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getSubTasksForGoal(final String goalId,
      final Continuation<? super List<GoalSubTask>> $completion) {
    final String _sql = "SELECT * FROM goal_subtasks WHERE goalId = ? ORDER BY createdAt ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, goalId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<GoalSubTask>>() {
      @Override
      @NonNull
      public List<GoalSubTask> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfGoalId = CursorUtil.getColumnIndexOrThrow(_cursor, "goalId");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfEstimatedDurationDays = CursorUtil.getColumnIndexOrThrow(_cursor, "estimatedDurationDays");
          final int _cursorIndexOfCompletedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "completedAt");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfAiRecommendation = CursorUtil.getColumnIndexOrThrow(_cursor, "aiRecommendation");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final List<GoalSubTask> _result = new ArrayList<GoalSubTask>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final GoalSubTask _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpGoalId;
            _tmpGoalId = _cursor.getString(_cursorIndexOfGoalId);
            final String _tmpTitle;
            _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            final String _tmpDescription;
            _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            final int _tmpEstimatedDurationDays;
            _tmpEstimatedDurationDays = _cursor.getInt(_cursorIndexOfEstimatedDurationDays);
            final LocalDateTime _tmpCompletedAt;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfCompletedAt)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfCompletedAt);
            }
            _tmpCompletedAt = __dateTimeConverter.toLocalDateTime(_tmp);
            final LocalDateTime _tmpCreatedAt;
            final Long _tmp_1;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            final LocalDateTime _tmp_2 = __dateTimeConverter.toLocalDateTime(_tmp_1);
            if (_tmp_2 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpCreatedAt = _tmp_2;
            }
            final String _tmpAiRecommendation;
            if (_cursor.isNull(_cursorIndexOfAiRecommendation)) {
              _tmpAiRecommendation = null;
            } else {
              _tmpAiRecommendation = _cursor.getString(_cursorIndexOfAiRecommendation);
            }
            final String _tmpStatus;
            _tmpStatus = _cursor.getString(_cursorIndexOfStatus);
            _item = new GoalSubTask(_tmpId,_tmpGoalId,_tmpTitle,_tmpDescription,_tmpEstimatedDurationDays,_tmpCompletedAt,_tmpCreatedAt,_tmpAiRecommendation,_tmpStatus);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getAllSubTasksList(final Continuation<? super List<GoalSubTask>> $completion) {
    final String _sql = "SELECT * FROM goal_subtasks ORDER BY createdAt ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<GoalSubTask>>() {
      @Override
      @NonNull
      public List<GoalSubTask> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfGoalId = CursorUtil.getColumnIndexOrThrow(_cursor, "goalId");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfEstimatedDurationDays = CursorUtil.getColumnIndexOrThrow(_cursor, "estimatedDurationDays");
          final int _cursorIndexOfCompletedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "completedAt");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfAiRecommendation = CursorUtil.getColumnIndexOrThrow(_cursor, "aiRecommendation");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final List<GoalSubTask> _result = new ArrayList<GoalSubTask>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final GoalSubTask _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpGoalId;
            _tmpGoalId = _cursor.getString(_cursorIndexOfGoalId);
            final String _tmpTitle;
            _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            final String _tmpDescription;
            _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            final int _tmpEstimatedDurationDays;
            _tmpEstimatedDurationDays = _cursor.getInt(_cursorIndexOfEstimatedDurationDays);
            final LocalDateTime _tmpCompletedAt;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfCompletedAt)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfCompletedAt);
            }
            _tmpCompletedAt = __dateTimeConverter.toLocalDateTime(_tmp);
            final LocalDateTime _tmpCreatedAt;
            final Long _tmp_1;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            final LocalDateTime _tmp_2 = __dateTimeConverter.toLocalDateTime(_tmp_1);
            if (_tmp_2 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpCreatedAt = _tmp_2;
            }
            final String _tmpAiRecommendation;
            if (_cursor.isNull(_cursorIndexOfAiRecommendation)) {
              _tmpAiRecommendation = null;
            } else {
              _tmpAiRecommendation = _cursor.getString(_cursorIndexOfAiRecommendation);
            }
            final String _tmpStatus;
            _tmpStatus = _cursor.getString(_cursorIndexOfStatus);
            _item = new GoalSubTask(_tmpId,_tmpGoalId,_tmpTitle,_tmpDescription,_tmpEstimatedDurationDays,_tmpCompletedAt,_tmpCreatedAt,_tmpAiRecommendation,_tmpStatus);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object countSubTasksForGoal(final String goalId,
      final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT COUNT(*) FROM goal_subtasks WHERE goalId = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, goalId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final int _tmp;
            _tmp = _cursor.getInt(0);
            _result = _tmp;
          } else {
            _result = 0;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object countCompletedSubTasksForGoal(final String goalId,
      final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT COUNT(*) FROM goal_subtasks WHERE goalId = ? AND completedAt IS NOT NULL";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, goalId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final int _tmp;
            _tmp = _cursor.getInt(0);
            _result = _tmp;
          } else {
            _result = 0;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getTotalGoalsCount(final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT COUNT(*) FROM goals";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final int _tmp;
            _tmp = _cursor.getInt(0);
            _result = _tmp;
          } else {
            _result = 0;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getAiBreakdownGoalsCount(final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT COUNT(*) FROM goals WHERE hasAiBreakdown = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final int _tmp;
            _tmp = _cursor.getInt(0);
            _result = _tmp;
          } else {
            _result = 0;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getTotalSubTasksCount(final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT COUNT(*) FROM goal_subtasks";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final int _tmp;
            _tmp = _cursor.getInt(0);
            _result = _tmp;
          } else {
            _result = 0;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getReviewReportsCount(final long since,
      final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT COUNT(*) FROM goals WHERE completedAt IS NOT NULL AND updatedAt > ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, since);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final int _tmp;
            _tmp = _cursor.getInt(0);
            _result = _tmp;
          } else {
            _result = 0;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getCompletedGoalsByLimit(final int limit,
      final Continuation<? super List<Goal>> $completion) {
    final String _sql = "SELECT * FROM goals WHERE completedAt IS NOT NULL ORDER BY completedAt DESC LIMIT ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, limit);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<Goal>>() {
      @Override
      @NonNull
      public List<Goal> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfStartDate = CursorUtil.getColumnIndexOrThrow(_cursor, "startDate");
          final int _cursorIndexOfDueDate = CursorUtil.getColumnIndexOrThrow(_cursor, "dueDate");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final int _cursorIndexOfCompletedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "completedAt");
          final int _cursorIndexOfProgress = CursorUtil.getColumnIndexOrThrow(_cursor, "progress");
          final int _cursorIndexOfPriority = CursorUtil.getColumnIndexOrThrow(_cursor, "priority");
          final int _cursorIndexOfHasAiBreakdown = CursorUtil.getColumnIndexOrThrow(_cursor, "hasAiBreakdown");
          final int _cursorIndexOfHasAiAnalysis = CursorUtil.getColumnIndexOrThrow(_cursor, "hasAiAnalysis");
          final int _cursorIndexOfRelatedTaskIds = CursorUtil.getColumnIndexOrThrow(_cursor, "relatedTaskIds");
          final int _cursorIndexOfAiRecommendationsJson = CursorUtil.getColumnIndexOrThrow(_cursor, "aiRecommendationsJson");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfBestTimeSlotsJson = CursorUtil.getColumnIndexOrThrow(_cursor, "bestTimeSlotsJson");
          final int _cursorIndexOfMetricsJson = CursorUtil.getColumnIndexOrThrow(_cursor, "metricsJson");
          final int _cursorIndexOfReviewFrequency = CursorUtil.getColumnIndexOrThrow(_cursor, "reviewFrequency");
          final int _cursorIndexOfCategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "categoryId");
          final List<Goal> _result = new ArrayList<Goal>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Goal _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpTitle;
            _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            final String _tmpDescription;
            _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            final LocalDateTime _tmpStartDate;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfStartDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfStartDate);
            }
            final LocalDateTime _tmp_1 = __dateTimeConverter.toLocalDateTime(_tmp);
            if (_tmp_1 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpStartDate = _tmp_1;
            }
            final LocalDateTime _tmpDueDate;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfDueDate)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfDueDate);
            }
            _tmpDueDate = __dateTimeConverter.toLocalDateTime(_tmp_2);
            final LocalDateTime _tmpCreatedAt;
            final Long _tmp_3;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            final LocalDateTime _tmp_4 = __dateTimeConverter.toLocalDateTime(_tmp_3);
            if (_tmp_4 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpCreatedAt = _tmp_4;
            }
            final LocalDateTime _tmpUpdatedAt;
            final Long _tmp_5;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmp_5 = null;
            } else {
              _tmp_5 = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            final LocalDateTime _tmp_6 = __dateTimeConverter.toLocalDateTime(_tmp_5);
            if (_tmp_6 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpUpdatedAt = _tmp_6;
            }
            final LocalDateTime _tmpCompletedAt;
            final Long _tmp_7;
            if (_cursor.isNull(_cursorIndexOfCompletedAt)) {
              _tmp_7 = null;
            } else {
              _tmp_7 = _cursor.getLong(_cursorIndexOfCompletedAt);
            }
            _tmpCompletedAt = __dateTimeConverter.toLocalDateTime(_tmp_7);
            final float _tmpProgress;
            _tmpProgress = _cursor.getFloat(_cursorIndexOfProgress);
            final String _tmpPriority;
            _tmpPriority = _cursor.getString(_cursorIndexOfPriority);
            final boolean _tmpHasAiBreakdown;
            final int _tmp_8;
            _tmp_8 = _cursor.getInt(_cursorIndexOfHasAiBreakdown);
            _tmpHasAiBreakdown = _tmp_8 != 0;
            final boolean _tmpHasAiAnalysis;
            final int _tmp_9;
            _tmp_9 = _cursor.getInt(_cursorIndexOfHasAiAnalysis);
            _tmpHasAiAnalysis = _tmp_9 != 0;
            final String _tmpRelatedTaskIds;
            _tmpRelatedTaskIds = _cursor.getString(_cursorIndexOfRelatedTaskIds);
            final String _tmpAiRecommendationsJson;
            _tmpAiRecommendationsJson = _cursor.getString(_cursorIndexOfAiRecommendationsJson);
            final String _tmpTags;
            _tmpTags = _cursor.getString(_cursorIndexOfTags);
            final String _tmpStatus;
            _tmpStatus = _cursor.getString(_cursorIndexOfStatus);
            final String _tmpBestTimeSlotsJson;
            _tmpBestTimeSlotsJson = _cursor.getString(_cursorIndexOfBestTimeSlotsJson);
            final String _tmpMetricsJson;
            _tmpMetricsJson = _cursor.getString(_cursorIndexOfMetricsJson);
            final String _tmpReviewFrequency;
            _tmpReviewFrequency = _cursor.getString(_cursorIndexOfReviewFrequency);
            final String _tmpCategoryId;
            _tmpCategoryId = _cursor.getString(_cursorIndexOfCategoryId);
            _item = new Goal(_tmpId,_tmpTitle,_tmpDescription,_tmpStartDate,_tmpDueDate,_tmpCreatedAt,_tmpUpdatedAt,_tmpCompletedAt,_tmpProgress,_tmpPriority,_tmpHasAiBreakdown,_tmpHasAiAnalysis,_tmpRelatedTaskIds,_tmpAiRecommendationsJson,_tmpTags,_tmpStatus,_tmpBestTimeSlotsJson,_tmpMetricsJson,_tmpReviewFrequency,_tmpCategoryId);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getGoalsWithAiAnalysis(final int limit,
      final Continuation<? super List<Goal>> $completion) {
    final String _sql = "SELECT * FROM goals WHERE hasAiAnalysis = 1 ORDER BY updatedAt DESC LIMIT ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, limit);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<Goal>>() {
      @Override
      @NonNull
      public List<Goal> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfStartDate = CursorUtil.getColumnIndexOrThrow(_cursor, "startDate");
          final int _cursorIndexOfDueDate = CursorUtil.getColumnIndexOrThrow(_cursor, "dueDate");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final int _cursorIndexOfCompletedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "completedAt");
          final int _cursorIndexOfProgress = CursorUtil.getColumnIndexOrThrow(_cursor, "progress");
          final int _cursorIndexOfPriority = CursorUtil.getColumnIndexOrThrow(_cursor, "priority");
          final int _cursorIndexOfHasAiBreakdown = CursorUtil.getColumnIndexOrThrow(_cursor, "hasAiBreakdown");
          final int _cursorIndexOfHasAiAnalysis = CursorUtil.getColumnIndexOrThrow(_cursor, "hasAiAnalysis");
          final int _cursorIndexOfRelatedTaskIds = CursorUtil.getColumnIndexOrThrow(_cursor, "relatedTaskIds");
          final int _cursorIndexOfAiRecommendationsJson = CursorUtil.getColumnIndexOrThrow(_cursor, "aiRecommendationsJson");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfBestTimeSlotsJson = CursorUtil.getColumnIndexOrThrow(_cursor, "bestTimeSlotsJson");
          final int _cursorIndexOfMetricsJson = CursorUtil.getColumnIndexOrThrow(_cursor, "metricsJson");
          final int _cursorIndexOfReviewFrequency = CursorUtil.getColumnIndexOrThrow(_cursor, "reviewFrequency");
          final int _cursorIndexOfCategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "categoryId");
          final List<Goal> _result = new ArrayList<Goal>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Goal _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpTitle;
            _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            final String _tmpDescription;
            _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            final LocalDateTime _tmpStartDate;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfStartDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfStartDate);
            }
            final LocalDateTime _tmp_1 = __dateTimeConverter.toLocalDateTime(_tmp);
            if (_tmp_1 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpStartDate = _tmp_1;
            }
            final LocalDateTime _tmpDueDate;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfDueDate)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfDueDate);
            }
            _tmpDueDate = __dateTimeConverter.toLocalDateTime(_tmp_2);
            final LocalDateTime _tmpCreatedAt;
            final Long _tmp_3;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            final LocalDateTime _tmp_4 = __dateTimeConverter.toLocalDateTime(_tmp_3);
            if (_tmp_4 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpCreatedAt = _tmp_4;
            }
            final LocalDateTime _tmpUpdatedAt;
            final Long _tmp_5;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmp_5 = null;
            } else {
              _tmp_5 = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            final LocalDateTime _tmp_6 = __dateTimeConverter.toLocalDateTime(_tmp_5);
            if (_tmp_6 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpUpdatedAt = _tmp_6;
            }
            final LocalDateTime _tmpCompletedAt;
            final Long _tmp_7;
            if (_cursor.isNull(_cursorIndexOfCompletedAt)) {
              _tmp_7 = null;
            } else {
              _tmp_7 = _cursor.getLong(_cursorIndexOfCompletedAt);
            }
            _tmpCompletedAt = __dateTimeConverter.toLocalDateTime(_tmp_7);
            final float _tmpProgress;
            _tmpProgress = _cursor.getFloat(_cursorIndexOfProgress);
            final String _tmpPriority;
            _tmpPriority = _cursor.getString(_cursorIndexOfPriority);
            final boolean _tmpHasAiBreakdown;
            final int _tmp_8;
            _tmp_8 = _cursor.getInt(_cursorIndexOfHasAiBreakdown);
            _tmpHasAiBreakdown = _tmp_8 != 0;
            final boolean _tmpHasAiAnalysis;
            final int _tmp_9;
            _tmp_9 = _cursor.getInt(_cursorIndexOfHasAiAnalysis);
            _tmpHasAiAnalysis = _tmp_9 != 0;
            final String _tmpRelatedTaskIds;
            _tmpRelatedTaskIds = _cursor.getString(_cursorIndexOfRelatedTaskIds);
            final String _tmpAiRecommendationsJson;
            _tmpAiRecommendationsJson = _cursor.getString(_cursorIndexOfAiRecommendationsJson);
            final String _tmpTags;
            _tmpTags = _cursor.getString(_cursorIndexOfTags);
            final String _tmpStatus;
            _tmpStatus = _cursor.getString(_cursorIndexOfStatus);
            final String _tmpBestTimeSlotsJson;
            _tmpBestTimeSlotsJson = _cursor.getString(_cursorIndexOfBestTimeSlotsJson);
            final String _tmpMetricsJson;
            _tmpMetricsJson = _cursor.getString(_cursorIndexOfMetricsJson);
            final String _tmpReviewFrequency;
            _tmpReviewFrequency = _cursor.getString(_cursorIndexOfReviewFrequency);
            final String _tmpCategoryId;
            _tmpCategoryId = _cursor.getString(_cursorIndexOfCategoryId);
            _item = new Goal(_tmpId,_tmpTitle,_tmpDescription,_tmpStartDate,_tmpDueDate,_tmpCreatedAt,_tmpUpdatedAt,_tmpCompletedAt,_tmpProgress,_tmpPriority,_tmpHasAiBreakdown,_tmpHasAiAnalysis,_tmpRelatedTaskIds,_tmpAiRecommendationsJson,_tmpTags,_tmpStatus,_tmpBestTimeSlotsJson,_tmpMetricsJson,_tmpReviewFrequency,_tmpCategoryId);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getCompletedGoalsList(final Continuation<? super List<Goal>> $completion) {
    final String _sql = "SELECT * FROM goals WHERE completedAt IS NOT NULL ORDER BY completedAt DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<Goal>>() {
      @Override
      @NonNull
      public List<Goal> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfStartDate = CursorUtil.getColumnIndexOrThrow(_cursor, "startDate");
          final int _cursorIndexOfDueDate = CursorUtil.getColumnIndexOrThrow(_cursor, "dueDate");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final int _cursorIndexOfCompletedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "completedAt");
          final int _cursorIndexOfProgress = CursorUtil.getColumnIndexOrThrow(_cursor, "progress");
          final int _cursorIndexOfPriority = CursorUtil.getColumnIndexOrThrow(_cursor, "priority");
          final int _cursorIndexOfHasAiBreakdown = CursorUtil.getColumnIndexOrThrow(_cursor, "hasAiBreakdown");
          final int _cursorIndexOfHasAiAnalysis = CursorUtil.getColumnIndexOrThrow(_cursor, "hasAiAnalysis");
          final int _cursorIndexOfRelatedTaskIds = CursorUtil.getColumnIndexOrThrow(_cursor, "relatedTaskIds");
          final int _cursorIndexOfAiRecommendationsJson = CursorUtil.getColumnIndexOrThrow(_cursor, "aiRecommendationsJson");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfBestTimeSlotsJson = CursorUtil.getColumnIndexOrThrow(_cursor, "bestTimeSlotsJson");
          final int _cursorIndexOfMetricsJson = CursorUtil.getColumnIndexOrThrow(_cursor, "metricsJson");
          final int _cursorIndexOfReviewFrequency = CursorUtil.getColumnIndexOrThrow(_cursor, "reviewFrequency");
          final int _cursorIndexOfCategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "categoryId");
          final List<Goal> _result = new ArrayList<Goal>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Goal _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpTitle;
            _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            final String _tmpDescription;
            _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            final LocalDateTime _tmpStartDate;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfStartDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfStartDate);
            }
            final LocalDateTime _tmp_1 = __dateTimeConverter.toLocalDateTime(_tmp);
            if (_tmp_1 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpStartDate = _tmp_1;
            }
            final LocalDateTime _tmpDueDate;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfDueDate)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfDueDate);
            }
            _tmpDueDate = __dateTimeConverter.toLocalDateTime(_tmp_2);
            final LocalDateTime _tmpCreatedAt;
            final Long _tmp_3;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            final LocalDateTime _tmp_4 = __dateTimeConverter.toLocalDateTime(_tmp_3);
            if (_tmp_4 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpCreatedAt = _tmp_4;
            }
            final LocalDateTime _tmpUpdatedAt;
            final Long _tmp_5;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmp_5 = null;
            } else {
              _tmp_5 = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            final LocalDateTime _tmp_6 = __dateTimeConverter.toLocalDateTime(_tmp_5);
            if (_tmp_6 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpUpdatedAt = _tmp_6;
            }
            final LocalDateTime _tmpCompletedAt;
            final Long _tmp_7;
            if (_cursor.isNull(_cursorIndexOfCompletedAt)) {
              _tmp_7 = null;
            } else {
              _tmp_7 = _cursor.getLong(_cursorIndexOfCompletedAt);
            }
            _tmpCompletedAt = __dateTimeConverter.toLocalDateTime(_tmp_7);
            final float _tmpProgress;
            _tmpProgress = _cursor.getFloat(_cursorIndexOfProgress);
            final String _tmpPriority;
            _tmpPriority = _cursor.getString(_cursorIndexOfPriority);
            final boolean _tmpHasAiBreakdown;
            final int _tmp_8;
            _tmp_8 = _cursor.getInt(_cursorIndexOfHasAiBreakdown);
            _tmpHasAiBreakdown = _tmp_8 != 0;
            final boolean _tmpHasAiAnalysis;
            final int _tmp_9;
            _tmp_9 = _cursor.getInt(_cursorIndexOfHasAiAnalysis);
            _tmpHasAiAnalysis = _tmp_9 != 0;
            final String _tmpRelatedTaskIds;
            _tmpRelatedTaskIds = _cursor.getString(_cursorIndexOfRelatedTaskIds);
            final String _tmpAiRecommendationsJson;
            _tmpAiRecommendationsJson = _cursor.getString(_cursorIndexOfAiRecommendationsJson);
            final String _tmpTags;
            _tmpTags = _cursor.getString(_cursorIndexOfTags);
            final String _tmpStatus;
            _tmpStatus = _cursor.getString(_cursorIndexOfStatus);
            final String _tmpBestTimeSlotsJson;
            _tmpBestTimeSlotsJson = _cursor.getString(_cursorIndexOfBestTimeSlotsJson);
            final String _tmpMetricsJson;
            _tmpMetricsJson = _cursor.getString(_cursorIndexOfMetricsJson);
            final String _tmpReviewFrequency;
            _tmpReviewFrequency = _cursor.getString(_cursorIndexOfReviewFrequency);
            final String _tmpCategoryId;
            _tmpCategoryId = _cursor.getString(_cursorIndexOfCategoryId);
            _item = new Goal(_tmpId,_tmpTitle,_tmpDescription,_tmpStartDate,_tmpDueDate,_tmpCreatedAt,_tmpUpdatedAt,_tmpCompletedAt,_tmpProgress,_tmpPriority,_tmpHasAiBreakdown,_tmpHasAiAnalysis,_tmpRelatedTaskIds,_tmpAiRecommendationsJson,_tmpTags,_tmpStatus,_tmpBestTimeSlotsJson,_tmpMetricsJson,_tmpReviewFrequency,_tmpCategoryId);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getInProgressGoalsCount(final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT COUNT(*) FROM goals WHERE completedAt IS NULL";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final int _tmp;
            _tmp = _cursor.getInt(0);
            _result = _tmp;
          } else {
            _result = 0;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
