package com.timeflow.app.domain.usecase.goal;

import com.timeflow.app.data.repository.GoalRepository;
import com.timeflow.app.data.repository.UserPreferenceRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class QuickGoalCreationUseCase_Factory implements Factory<QuickGoalCreationUseCase> {
  private final Provider<GoalRepository> goalRepositoryProvider;

  private final Provider<UserPreferenceRepository> userPreferenceRepositoryProvider;

  public QuickGoalCreationUseCase_Factory(Provider<GoalRepository> goalRepositoryProvider,
      Provider<UserPreferenceRepository> userPreferenceRepositoryProvider) {
    this.goalRepositoryProvider = goalRepositoryProvider;
    this.userPreferenceRepositoryProvider = userPreferenceRepositoryProvider;
  }

  @Override
  public QuickGoalCreationUseCase get() {
    return newInstance(goalRepositoryProvider.get(), userPreferenceRepositoryProvider.get());
  }

  public static QuickGoalCreationUseCase_Factory create(
      Provider<GoalRepository> goalRepositoryProvider,
      Provider<UserPreferenceRepository> userPreferenceRepositoryProvider) {
    return new QuickGoalCreationUseCase_Factory(goalRepositoryProvider, userPreferenceRepositoryProvider);
  }

  public static QuickGoalCreationUseCase newInstance(GoalRepository goalRepository,
      UserPreferenceRepository userPreferenceRepository) {
    return new QuickGoalCreationUseCase(goalRepository, userPreferenceRepository);
  }
}
