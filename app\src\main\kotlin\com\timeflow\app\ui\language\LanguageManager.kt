package com.timeflow.app.ui.language

import android.app.Activity
import android.content.Context
import android.content.res.Configuration
import android.os.Build
import android.util.Log
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.edit
import com.timeflow.app.di.PreferenceKeys
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import java.util.Locale

/**
 * 语言管理器
 * 负责管理应用的多语言设置和切换
 */
object LanguageManager {
    private const val TAG = "LanguageManager"
    
    // 支持的语言
    enum class SupportedLanguage(
        val code: String,
        val displayName: String,
        val locale: Locale
    ) {
        SYSTEM("system", "跟随系统", Locale.getDefault()),
        SIMPLIFIED_CHINESE("zh-CN", "简体中文", Locale.SIMPLIFIED_CHINESE),
        TRADITIONAL_CHINESE("zh-TW", "繁體中文", Locale.TRADITIONAL_CHINESE),
        ENGLISH("en", "English", Locale.ENGLISH)
    }
    
    // 当前语言状态
    private val _currentLanguage = MutableStateFlow(SupportedLanguage.SYSTEM)
    val currentLanguage: StateFlow<SupportedLanguage> = _currentLanguage.asStateFlow()
    
    // 是否使用系统语言
    private val _useSystemLanguage = MutableStateFlow(true)
    val useSystemLanguage: StateFlow<Boolean> = _useSystemLanguage.asStateFlow()
    
    private var dataStore: DataStore<Preferences>? = null
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    /**
     * 初始化语言管理器
     */
    fun initialize(context: Context, languageDataStore: DataStore<Preferences>) {
        dataStore = languageDataStore
        
        scope.launch {
            try {
                Log.d(TAG, "开始初始化语言管理器")
                
                languageDataStore.data.collectLatest { preferences ->
                    // 读取语言设置
                    val useSystemLang = preferences[PreferenceKeys.USE_SYSTEM_LANGUAGE] ?: true
                    val languageCode = preferences[PreferenceKeys.APP_LANGUAGE] ?: SupportedLanguage.SYSTEM.code
                    
                    _useSystemLanguage.value = useSystemLang
                    
                    // 根据语言代码找到对应的语言
                    val language = SupportedLanguage.values().find { it.code == languageCode }
                        ?: SupportedLanguage.SYSTEM
                    
                    _currentLanguage.value = language
                    
                    Log.d(TAG, "✅ 语言设置已加载: useSystem=$useSystemLang, language=${language.displayName}")
                }
            } catch (e: Exception) {
                Log.e(TAG, "初始化语言管理器失败: ${e.message}", e)
            }
        }
        
        Log.d(TAG, "语言管理器初始化完成")
    }
    
    /**
     * 设置应用语言
     */
    suspend fun setLanguage(language: SupportedLanguage) {
        try {
            Log.d(TAG, "设置应用语言: ${language.displayName}")
            
            val store = dataStore ?: return
            
            store.edit { preferences ->
                preferences[PreferenceKeys.APP_LANGUAGE] = language.code
                preferences[PreferenceKeys.USE_SYSTEM_LANGUAGE] = (language == SupportedLanguage.SYSTEM)
            }
            
            _currentLanguage.value = language
            _useSystemLanguage.value = (language == SupportedLanguage.SYSTEM)
            
            Log.d(TAG, "✅ 语言设置已保存: ${language.displayName}")
        } catch (e: Exception) {
            Log.e(TAG, "设置语言失败: ${e.message}", e)
            throw e
        }
    }
    
    /**
     * 获取当前应该使用的Locale
     */
    fun getCurrentLocale(): Locale {
        return when (val language = _currentLanguage.value) {
            SupportedLanguage.SYSTEM -> {
                // 使用系统语言，但如果系统语言不在支持列表中，则使用简体中文
                val systemLocale = Locale.getDefault()
                when {
                    systemLocale.language == "zh" -> {
                        when (systemLocale.country.uppercase()) {
                            "TW", "HK", "MO" -> SupportedLanguage.TRADITIONAL_CHINESE.locale
                            else -> SupportedLanguage.SIMPLIFIED_CHINESE.locale
                        }
                    }
                    systemLocale.language == "en" -> SupportedLanguage.ENGLISH.locale
                    else -> SupportedLanguage.SIMPLIFIED_CHINESE.locale // 默认使用简体中文
                }
            }
            else -> language.locale
        }
    }
    
    /**
     * 应用语言到Context
     */
    fun applyLanguageToContext(context: Context): Context {
        val locale = getCurrentLocale()
        return updateContextLocale(context, locale)
    }
    
    /**
     * 更新Context的语言设置
     */
    private fun updateContextLocale(context: Context, locale: Locale): Context {
        Locale.setDefault(locale)
        
        val configuration = Configuration(context.resources.configuration)
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            configuration.setLocale(locale)
            configuration.setLocales(android.os.LocaleList(locale))
        } else {
            @Suppress("DEPRECATION")
            configuration.locale = locale
        }
        
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N_MR1) {
            context.createConfigurationContext(configuration)
        } else {
            @Suppress("DEPRECATION")
            context.resources.updateConfiguration(configuration, context.resources.displayMetrics)
            context
        }
    }
    
    /**
     * 重新创建Activity以应用新语言
     */
    fun recreateActivity(activity: Activity) {
        try {
            Log.d(TAG, "重新创建Activity以应用新语言")
            activity.recreate()
        } catch (e: Exception) {
            Log.e(TAG, "重新创建Activity失败: ${e.message}", e)
        }
    }
    
    /**
     * 获取所有支持的语言
     */
    fun getSupportedLanguages(): List<SupportedLanguage> {
        return SupportedLanguage.values().toList()
    }
    
    /**
     * 根据语言代码获取语言
     */
    fun getLanguageByCode(code: String): SupportedLanguage? {
        return SupportedLanguage.values().find { it.code == code }
    }
    
    /**
     * 获取语言的显示名称（根据当前语言环境）
     */
    fun getLanguageDisplayName(language: SupportedLanguage, context: Context): String {
        // 这里可以根据当前语言环境返回本地化的显示名称
        // 暂时返回固定的显示名称
        return when (language) {
            SupportedLanguage.SYSTEM -> when (getCurrentLocale().language) {
                "zh" -> "跟随系统"
                "en" -> "Follow System"
                else -> "跟随系统"
            }
            SupportedLanguage.SIMPLIFIED_CHINESE -> "简体中文"
            SupportedLanguage.TRADITIONAL_CHINESE -> "繁體中文"
            SupportedLanguage.ENGLISH -> "English"
        }
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        // 取消所有协程
        scope.coroutineContext[kotlinx.coroutines.Job]?.cancel()
        Log.d(TAG, "LanguageManager资源已清理")
    }
}
