package com.timeflow.app.ui.task;

import android.content.Context;
import androidx.work.WorkManager;
import com.timeflow.app.data.repository.GoalRepository;
import com.timeflow.app.data.repository.SharedFilterState;
import com.timeflow.app.data.repository.SharedPendingDeletionState;
import com.timeflow.app.data.repository.TaskRepository;
import com.timeflow.app.data.repository.UserPreferenceRepository;
import com.timeflow.app.ui.screen.reflection.ReflectionRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class TaskListViewModel_Factory implements Factory<TaskListViewModel> {
  private final Provider<TaskRepository> taskRepositoryProvider;

  private final Provider<GoalRepository> goalRepositoryProvider;

  private final Provider<UserPreferenceRepository> userPreferenceRepositoryProvider;

  private final Provider<WorkManager> workManagerProvider;

  private final Provider<Context> contextProvider;

  private final Provider<SharedFilterState> sharedFilterStateProvider;

  private final Provider<ReflectionRepository> reflectionRepositoryProvider;

  private final Provider<SharedPendingDeletionState> sharedPendingDeletionStateProvider;

  public TaskListViewModel_Factory(Provider<TaskRepository> taskRepositoryProvider,
      Provider<GoalRepository> goalRepositoryProvider,
      Provider<UserPreferenceRepository> userPreferenceRepositoryProvider,
      Provider<WorkManager> workManagerProvider, Provider<Context> contextProvider,
      Provider<SharedFilterState> sharedFilterStateProvider,
      Provider<ReflectionRepository> reflectionRepositoryProvider,
      Provider<SharedPendingDeletionState> sharedPendingDeletionStateProvider) {
    this.taskRepositoryProvider = taskRepositoryProvider;
    this.goalRepositoryProvider = goalRepositoryProvider;
    this.userPreferenceRepositoryProvider = userPreferenceRepositoryProvider;
    this.workManagerProvider = workManagerProvider;
    this.contextProvider = contextProvider;
    this.sharedFilterStateProvider = sharedFilterStateProvider;
    this.reflectionRepositoryProvider = reflectionRepositoryProvider;
    this.sharedPendingDeletionStateProvider = sharedPendingDeletionStateProvider;
  }

  @Override
  public TaskListViewModel get() {
    return newInstance(taskRepositoryProvider.get(), goalRepositoryProvider.get(), userPreferenceRepositoryProvider.get(), workManagerProvider.get(), contextProvider.get(), sharedFilterStateProvider.get(), reflectionRepositoryProvider.get(), sharedPendingDeletionStateProvider.get());
  }

  public static TaskListViewModel_Factory create(Provider<TaskRepository> taskRepositoryProvider,
      Provider<GoalRepository> goalRepositoryProvider,
      Provider<UserPreferenceRepository> userPreferenceRepositoryProvider,
      Provider<WorkManager> workManagerProvider, Provider<Context> contextProvider,
      Provider<SharedFilterState> sharedFilterStateProvider,
      Provider<ReflectionRepository> reflectionRepositoryProvider,
      Provider<SharedPendingDeletionState> sharedPendingDeletionStateProvider) {
    return new TaskListViewModel_Factory(taskRepositoryProvider, goalRepositoryProvider, userPreferenceRepositoryProvider, workManagerProvider, contextProvider, sharedFilterStateProvider, reflectionRepositoryProvider, sharedPendingDeletionStateProvider);
  }

  public static TaskListViewModel newInstance(TaskRepository taskRepository,
      GoalRepository goalRepository, UserPreferenceRepository userPreferenceRepository,
      WorkManager workManager, Context context, SharedFilterState sharedFilterState,
      ReflectionRepository reflectionRepository,
      SharedPendingDeletionState sharedPendingDeletionState) {
    return new TaskListViewModel(taskRepository, goalRepository, userPreferenceRepository, workManager, context, sharedFilterState, reflectionRepository, sharedPendingDeletionState);
  }
}
