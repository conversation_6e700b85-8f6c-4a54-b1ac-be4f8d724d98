package com.timeflow.app.utils;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class TaskReminderUtils_Factory implements Factory<TaskReminderUtils> {
  private final Provider<Context> contextProvider;

  private final Provider<NotificationPermissionHelper> notificationPermissionHelperProvider;

  public TaskReminderUtils_Factory(Provider<Context> contextProvider,
      Provider<NotificationPermissionHelper> notificationPermissionHelperProvider) {
    this.contextProvider = contextProvider;
    this.notificationPermissionHelperProvider = notificationPermissionHelperProvider;
  }

  @Override
  public TaskReminderUtils get() {
    return newInstance(contextProvider.get(), notificationPermissionHelperProvider.get());
  }

  public static TaskReminderUtils_Factory create(Provider<Context> contextProvider,
      Provider<NotificationPermissionHelper> notificationPermissionHelperProvider) {
    return new TaskReminderUtils_Factory(contextProvider, notificationPermissionHelperProvider);
  }

  public static TaskReminderUtils newInstance(Context context,
      NotificationPermissionHelper notificationPermissionHelper) {
    return new TaskReminderUtils(context, notificationPermissionHelper);
  }
}
