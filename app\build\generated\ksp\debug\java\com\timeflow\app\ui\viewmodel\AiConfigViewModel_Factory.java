package com.timeflow.app.ui.viewmodel;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AiConfigViewModel_Factory implements Factory<AiConfigViewModel> {
  @Override
  public AiConfigViewModel get() {
    return newInstance();
  }

  public static AiConfigViewModel_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static AiConfigViewModel newInstance() {
    return new AiConfigViewModel();
  }

  private static final class InstanceHolder {
    private static final AiConfigViewModel_Factory INSTANCE = new AiConfigViewModel_Factory();
  }
}
