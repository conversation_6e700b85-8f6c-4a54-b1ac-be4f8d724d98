package com.timeflow.app.ui.screen.reflection.data;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SearchSuggestionServiceImpl_Factory implements Factory<SearchSuggestionServiceImpl> {
  @Override
  public SearchSuggestionServiceImpl get() {
    return newInstance();
  }

  public static SearchSuggestionServiceImpl_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static SearchSuggestionServiceImpl newInstance() {
    return new SearchSuggestionServiceImpl();
  }

  private static final class InstanceHolder {
    private static final SearchSuggestionServiceImpl_Factory INSTANCE = new SearchSuggestionServiceImpl_Factory();
  }
}
