package com.timeflow.app.ui.screen.reflection.data;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SearchSuggestionServiceImpl_Factory implements Factory<SearchSuggestionServiceImpl> {
  private final Provider<Context> contextProvider;

  public SearchSuggestionServiceImpl_Factory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public SearchSuggestionServiceImpl get() {
    return newInstance(contextProvider.get());
  }

  public static SearchSuggestionServiceImpl_Factory create(Provider<Context> contextProvider) {
    return new SearchSuggestionServiceImpl_Factory(contextProvider);
  }

  public static SearchSuggestionServiceImpl newInstance(Context context) {
    return new SearchSuggestionServiceImpl(context);
  }
}
