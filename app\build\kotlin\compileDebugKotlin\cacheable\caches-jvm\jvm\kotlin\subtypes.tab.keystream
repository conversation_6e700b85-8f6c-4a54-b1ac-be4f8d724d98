,com.timeflow.app.data.model.PaymentException(com.timeflow.app.data.model.EndCondition$com.timeflow.app.data.model.TaskTypeandroidx.startup.Initializerandroid.app.Serviceandroid.os.Binder#androidx.activity.ComponentActivity"com.timeflow.app.ui.base.ViewState%com.timeflow.app.ui.navigation.ScreenEcom.timeflow.app.ui.screen.calendar.CalendarViewModel.CalendarUiEvent/com.timeflow.app.data.repository.GoalRepository/com.timeflow.app.ui.screen.goal.TemplateUiState+com.timeflow.app.ui.screen.goal.GoalUiState.com.timeflow.app.ui.screen.goal.BreakdownState)com.timeflow.app.ui.screen.goal.GoalState<EMAIL>kotlin.Annotation9com.timeflow.app.ui.screen.task.TaskViewModel.TaskUiState/com.timeflow.app.data.repository.TaskRepositoryCcom.timeflow.app.ui.task.components.common.event.AppEvent.BaseEvent:com.timeflow.app.ui.task.components.common.state.LoadStateGcom.timeflow.app.ui.viewmodel.GoalCreationViewModel.GoalCreationUiState)com.timeflow.app.ui.viewmodel.GoalUiState.com.timeflow.app.ui.viewmodel.ValidationResult1com.timeflow.app.ui.viewmodel.ConflictCheckResult-com.timeflow.app.ui.viewmodel.OperationResulttimber.log.Timber.Treekotlin.Enum!android.content.BroadcastReceiverandroidx.lifecycle.ViewModel2kotlinx.serialization.internal.GeneratedSerializer#android.appwidget.AppWidgetProviderandroidx.work.CoroutineWorkerandroid.app.Application$androidx.work.Configuration.Providerandroidx.room.RoomDatabasejava.io.Serializablejava.lang.Exception1com.timeflow.app.data.repository.AiTaskRepository8com.timeflow.app.data.repository.EmotionRecordRepository0com.timeflow.app.data.repository.EventRepository7com.timeflow.app.data.repository.GoalTemplateRepository0com.timeflow.app.data.repository.HabitRepository6com.timeflow.app.data.repository.KanbanBoardRepository7com.timeflow.app.data.repository.KanbanColumnRepository5com.timeflow.app.data.repository.MedicationRepository/com.timeflow.app.data.repository.BaseRepository8com.timeflow.app.data.repository.TimeAnalyticsRepository9com.timeflow.app.data.repository.UserPreferenceRepository/com.timeflow.app.data.repository.WishRepository$com.timeflow.app.di.AnalyticsTracker!com.timeflow.app.di.CrashReporter0com.timeflow.app.ui.navigation.TimeFlowNavigator#androidx.lifecycle.AndroidViewModel:com.timeflow.app.ui.screen.reflection.ReflectionRepository=com.timeflow.app.ui.screen.reflection.SearchSuggestionService2com.timeflow.app.ui.screen.settings.SyncRepository)java.lang.Thread.UncaughtExceptionHandler                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       