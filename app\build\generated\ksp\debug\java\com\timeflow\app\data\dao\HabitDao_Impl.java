package com.timeflow.app.data.dao;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.timeflow.app.data.converter.Converters;
import com.timeflow.app.data.converter.DateTimeConverter;
import com.timeflow.app.data.entity.Habit;
import com.timeflow.app.data.entity.HabitRecord;
import com.timeflow.app.data.entity.HabitReminder;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Float;
import java.lang.IllegalStateException;
import java.lang.Integer;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class HabitDao_Impl implements HabitDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<Habit> __insertionAdapterOfHabit;

  private final DateTimeConverter __dateTimeConverter = new DateTimeConverter();

  private final EntityInsertionAdapter<HabitRecord> __insertionAdapterOfHabitRecord;

  private final Converters __converters = new Converters();

  private final EntityInsertionAdapter<HabitReminder> __insertionAdapterOfHabitReminder;

  private final EntityDeletionOrUpdateAdapter<Habit> __deletionAdapterOfHabit;

  private final EntityDeletionOrUpdateAdapter<HabitRecord> __deletionAdapterOfHabitRecord;

  private final EntityDeletionOrUpdateAdapter<HabitReminder> __deletionAdapterOfHabitReminder;

  private final EntityDeletionOrUpdateAdapter<Habit> __updateAdapterOfHabit;

  private final EntityDeletionOrUpdateAdapter<HabitRecord> __updateAdapterOfHabitRecord;

  private final EntityDeletionOrUpdateAdapter<HabitReminder> __updateAdapterOfHabitReminder;

  private final SharedSQLiteStatement __preparedStmtOfDeleteHabitById;

  private final SharedSQLiteStatement __preparedStmtOfDeactivateHabit;

  private final SharedSQLiteStatement __preparedStmtOfActivateHabit;

  private final SharedSQLiteStatement __preparedStmtOfUpdateHabitSortOrder;

  private final SharedSQLiteStatement __preparedStmtOfDeleteHabitRecordById;

  private final SharedSQLiteStatement __preparedStmtOfDeleteAllRecordsForHabit;

  private final SharedSQLiteStatement __preparedStmtOfDeleteAllRemindersForHabit;

  public HabitDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfHabit = new EntityInsertionAdapter<Habit>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `habits` (`id`,`name`,`description`,`iconName`,`colorHex`,`category`,`customCategoryId`,`frequencyType`,`frequencyDays`,`targetCount`,`reminderEnabled`,`reminderTime`,`fixedTime`,`difficulty`,`relatedGoalId`,`currentStreak`,`longestStreak`,`totalCompletions`,`createdAt`,`updatedAt`,`isActive`,`customEmoji`,`notes`,`sortOrder`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final Habit entity) {
        statement.bindString(1, entity.getId());
        statement.bindString(2, entity.getName());
        statement.bindString(3, entity.getDescription());
        statement.bindString(4, entity.getIconName());
        statement.bindString(5, entity.getColorHex());
        statement.bindString(6, entity.getCategory());
        if (entity.getCustomCategoryId() == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, entity.getCustomCategoryId());
        }
        statement.bindString(8, entity.getFrequencyType());
        statement.bindString(9, entity.getFrequencyDays());
        statement.bindLong(10, entity.getTargetCount());
        final int _tmp = entity.getReminderEnabled() ? 1 : 0;
        statement.bindLong(11, _tmp);
        if (entity.getReminderTime() == null) {
          statement.bindNull(12);
        } else {
          statement.bindString(12, entity.getReminderTime());
        }
        if (entity.getFixedTime() == null) {
          statement.bindNull(13);
        } else {
          statement.bindString(13, entity.getFixedTime());
        }
        statement.bindString(14, entity.getDifficulty());
        if (entity.getRelatedGoalId() == null) {
          statement.bindNull(15);
        } else {
          statement.bindString(15, entity.getRelatedGoalId());
        }
        statement.bindLong(16, entity.getCurrentStreak());
        statement.bindLong(17, entity.getLongestStreak());
        statement.bindLong(18, entity.getTotalCompletions());
        final Long _tmp_1 = __dateTimeConverter.fromLocalDateTime(entity.getCreatedAt());
        if (_tmp_1 == null) {
          statement.bindNull(19);
        } else {
          statement.bindLong(19, _tmp_1);
        }
        final Long _tmp_2 = __dateTimeConverter.fromLocalDateTime(entity.getUpdatedAt());
        if (_tmp_2 == null) {
          statement.bindNull(20);
        } else {
          statement.bindLong(20, _tmp_2);
        }
        final int _tmp_3 = entity.isActive() ? 1 : 0;
        statement.bindLong(21, _tmp_3);
        statement.bindString(22, entity.getCustomEmoji());
        statement.bindString(23, entity.getNotes());
        statement.bindLong(24, entity.getSortOrder());
      }
    };
    this.__insertionAdapterOfHabitRecord = new EntityInsertionAdapter<HabitRecord>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `habit_records` (`id`,`habitId`,`date`,`completed`,`completedAt`,`completionCount`,`notes`,`skipReason`,`mood`,`createdAt`,`updatedAt`) VALUES (?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final HabitRecord entity) {
        statement.bindString(1, entity.getId());
        statement.bindString(2, entity.getHabitId());
        final Long _tmp = __converters.dateToEpochDay(entity.getDate());
        if (_tmp == null) {
          statement.bindNull(3);
        } else {
          statement.bindLong(3, _tmp);
        }
        final int _tmp_1 = entity.getCompleted() ? 1 : 0;
        statement.bindLong(4, _tmp_1);
        final Long _tmp_2 = __dateTimeConverter.fromLocalDateTime(entity.getCompletedAt());
        if (_tmp_2 == null) {
          statement.bindNull(5);
        } else {
          statement.bindLong(5, _tmp_2);
        }
        statement.bindLong(6, entity.getCompletionCount());
        statement.bindString(7, entity.getNotes());
        statement.bindString(8, entity.getSkipReason());
        statement.bindString(9, entity.getMood());
        final Long _tmp_3 = __dateTimeConverter.fromLocalDateTime(entity.getCreatedAt());
        if (_tmp_3 == null) {
          statement.bindNull(10);
        } else {
          statement.bindLong(10, _tmp_3);
        }
        final Long _tmp_4 = __dateTimeConverter.fromLocalDateTime(entity.getUpdatedAt());
        if (_tmp_4 == null) {
          statement.bindNull(11);
        } else {
          statement.bindLong(11, _tmp_4);
        }
      }
    };
    this.__insertionAdapterOfHabitReminder = new EntityInsertionAdapter<HabitReminder>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `habit_reminders` (`id`,`habitId`,`time`,`days`,`enabled`,`message`,`createdAt`,`updatedAt`) VALUES (?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final HabitReminder entity) {
        statement.bindString(1, entity.getId());
        statement.bindString(2, entity.getHabitId());
        statement.bindString(3, entity.getTime());
        statement.bindString(4, entity.getDays());
        final int _tmp = entity.getEnabled() ? 1 : 0;
        statement.bindLong(5, _tmp);
        statement.bindString(6, entity.getMessage());
        final Long _tmp_1 = __dateTimeConverter.fromLocalDateTime(entity.getCreatedAt());
        if (_tmp_1 == null) {
          statement.bindNull(7);
        } else {
          statement.bindLong(7, _tmp_1);
        }
        final Long _tmp_2 = __dateTimeConverter.fromLocalDateTime(entity.getUpdatedAt());
        if (_tmp_2 == null) {
          statement.bindNull(8);
        } else {
          statement.bindLong(8, _tmp_2);
        }
      }
    };
    this.__deletionAdapterOfHabit = new EntityDeletionOrUpdateAdapter<Habit>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `habits` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final Habit entity) {
        statement.bindString(1, entity.getId());
      }
    };
    this.__deletionAdapterOfHabitRecord = new EntityDeletionOrUpdateAdapter<HabitRecord>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `habit_records` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final HabitRecord entity) {
        statement.bindString(1, entity.getId());
      }
    };
    this.__deletionAdapterOfHabitReminder = new EntityDeletionOrUpdateAdapter<HabitReminder>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `habit_reminders` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final HabitReminder entity) {
        statement.bindString(1, entity.getId());
      }
    };
    this.__updateAdapterOfHabit = new EntityDeletionOrUpdateAdapter<Habit>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `habits` SET `id` = ?,`name` = ?,`description` = ?,`iconName` = ?,`colorHex` = ?,`category` = ?,`customCategoryId` = ?,`frequencyType` = ?,`frequencyDays` = ?,`targetCount` = ?,`reminderEnabled` = ?,`reminderTime` = ?,`fixedTime` = ?,`difficulty` = ?,`relatedGoalId` = ?,`currentStreak` = ?,`longestStreak` = ?,`totalCompletions` = ?,`createdAt` = ?,`updatedAt` = ?,`isActive` = ?,`customEmoji` = ?,`notes` = ?,`sortOrder` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final Habit entity) {
        statement.bindString(1, entity.getId());
        statement.bindString(2, entity.getName());
        statement.bindString(3, entity.getDescription());
        statement.bindString(4, entity.getIconName());
        statement.bindString(5, entity.getColorHex());
        statement.bindString(6, entity.getCategory());
        if (entity.getCustomCategoryId() == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, entity.getCustomCategoryId());
        }
        statement.bindString(8, entity.getFrequencyType());
        statement.bindString(9, entity.getFrequencyDays());
        statement.bindLong(10, entity.getTargetCount());
        final int _tmp = entity.getReminderEnabled() ? 1 : 0;
        statement.bindLong(11, _tmp);
        if (entity.getReminderTime() == null) {
          statement.bindNull(12);
        } else {
          statement.bindString(12, entity.getReminderTime());
        }
        if (entity.getFixedTime() == null) {
          statement.bindNull(13);
        } else {
          statement.bindString(13, entity.getFixedTime());
        }
        statement.bindString(14, entity.getDifficulty());
        if (entity.getRelatedGoalId() == null) {
          statement.bindNull(15);
        } else {
          statement.bindString(15, entity.getRelatedGoalId());
        }
        statement.bindLong(16, entity.getCurrentStreak());
        statement.bindLong(17, entity.getLongestStreak());
        statement.bindLong(18, entity.getTotalCompletions());
        final Long _tmp_1 = __dateTimeConverter.fromLocalDateTime(entity.getCreatedAt());
        if (_tmp_1 == null) {
          statement.bindNull(19);
        } else {
          statement.bindLong(19, _tmp_1);
        }
        final Long _tmp_2 = __dateTimeConverter.fromLocalDateTime(entity.getUpdatedAt());
        if (_tmp_2 == null) {
          statement.bindNull(20);
        } else {
          statement.bindLong(20, _tmp_2);
        }
        final int _tmp_3 = entity.isActive() ? 1 : 0;
        statement.bindLong(21, _tmp_3);
        statement.bindString(22, entity.getCustomEmoji());
        statement.bindString(23, entity.getNotes());
        statement.bindLong(24, entity.getSortOrder());
        statement.bindString(25, entity.getId());
      }
    };
    this.__updateAdapterOfHabitRecord = new EntityDeletionOrUpdateAdapter<HabitRecord>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `habit_records` SET `id` = ?,`habitId` = ?,`date` = ?,`completed` = ?,`completedAt` = ?,`completionCount` = ?,`notes` = ?,`skipReason` = ?,`mood` = ?,`createdAt` = ?,`updatedAt` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final HabitRecord entity) {
        statement.bindString(1, entity.getId());
        statement.bindString(2, entity.getHabitId());
        final Long _tmp = __converters.dateToEpochDay(entity.getDate());
        if (_tmp == null) {
          statement.bindNull(3);
        } else {
          statement.bindLong(3, _tmp);
        }
        final int _tmp_1 = entity.getCompleted() ? 1 : 0;
        statement.bindLong(4, _tmp_1);
        final Long _tmp_2 = __dateTimeConverter.fromLocalDateTime(entity.getCompletedAt());
        if (_tmp_2 == null) {
          statement.bindNull(5);
        } else {
          statement.bindLong(5, _tmp_2);
        }
        statement.bindLong(6, entity.getCompletionCount());
        statement.bindString(7, entity.getNotes());
        statement.bindString(8, entity.getSkipReason());
        statement.bindString(9, entity.getMood());
        final Long _tmp_3 = __dateTimeConverter.fromLocalDateTime(entity.getCreatedAt());
        if (_tmp_3 == null) {
          statement.bindNull(10);
        } else {
          statement.bindLong(10, _tmp_3);
        }
        final Long _tmp_4 = __dateTimeConverter.fromLocalDateTime(entity.getUpdatedAt());
        if (_tmp_4 == null) {
          statement.bindNull(11);
        } else {
          statement.bindLong(11, _tmp_4);
        }
        statement.bindString(12, entity.getId());
      }
    };
    this.__updateAdapterOfHabitReminder = new EntityDeletionOrUpdateAdapter<HabitReminder>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `habit_reminders` SET `id` = ?,`habitId` = ?,`time` = ?,`days` = ?,`enabled` = ?,`message` = ?,`createdAt` = ?,`updatedAt` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final HabitReminder entity) {
        statement.bindString(1, entity.getId());
        statement.bindString(2, entity.getHabitId());
        statement.bindString(3, entity.getTime());
        statement.bindString(4, entity.getDays());
        final int _tmp = entity.getEnabled() ? 1 : 0;
        statement.bindLong(5, _tmp);
        statement.bindString(6, entity.getMessage());
        final Long _tmp_1 = __dateTimeConverter.fromLocalDateTime(entity.getCreatedAt());
        if (_tmp_1 == null) {
          statement.bindNull(7);
        } else {
          statement.bindLong(7, _tmp_1);
        }
        final Long _tmp_2 = __dateTimeConverter.fromLocalDateTime(entity.getUpdatedAt());
        if (_tmp_2 == null) {
          statement.bindNull(8);
        } else {
          statement.bindLong(8, _tmp_2);
        }
        statement.bindString(9, entity.getId());
      }
    };
    this.__preparedStmtOfDeleteHabitById = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM habits WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeactivateHabit = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE habits SET isActive = 0 WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfActivateHabit = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE habits SET isActive = 1 WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateHabitSortOrder = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE habits SET sortOrder = ? WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteHabitRecordById = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM habit_records WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteAllRecordsForHabit = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM habit_records WHERE habitId = ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteAllRemindersForHabit = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM habit_reminders WHERE habitId = ?";
        return _query;
      }
    };
  }

  @Override
  public Object insertHabit(final Habit habit, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfHabit.insert(habit);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object insertHabits(final List<Habit> habits,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfHabit.insert(habits);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object insertHabitRecord(final HabitRecord record,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfHabitRecord.insert(record);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object insertHabitRecords(final List<HabitRecord> records,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfHabitRecord.insert(records);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object insertHabitReminder(final HabitReminder reminder,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfHabitReminder.insert(reminder);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteHabit(final Habit habit, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfHabit.handle(habit);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteHabitRecord(final HabitRecord record,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfHabitRecord.handle(record);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteHabitReminder(final HabitReminder reminder,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfHabitReminder.handle(reminder);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateHabit(final Habit habit, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfHabit.handle(habit);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateHabitRecord(final HabitRecord record,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfHabitRecord.handle(record);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateHabitReminder(final HabitReminder reminder,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfHabitReminder.handle(reminder);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteHabitById(final String habitId,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteHabitById.acquire();
        int _argIndex = 1;
        _stmt.bindString(_argIndex, habitId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteHabitById.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object deactivateHabit(final String habitId,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeactivateHabit.acquire();
        int _argIndex = 1;
        _stmt.bindString(_argIndex, habitId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeactivateHabit.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object activateHabit(final String habitId, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfActivateHabit.acquire();
        int _argIndex = 1;
        _stmt.bindString(_argIndex, habitId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfActivateHabit.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateHabitSortOrder(final String habitId, final int sortOrder,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateHabitSortOrder.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, sortOrder);
        _argIndex = 2;
        _stmt.bindString(_argIndex, habitId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateHabitSortOrder.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteHabitRecordById(final String recordId,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteHabitRecordById.acquire();
        int _argIndex = 1;
        _stmt.bindString(_argIndex, recordId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteHabitRecordById.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteAllRecordsForHabit(final String habitId,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteAllRecordsForHabit.acquire();
        int _argIndex = 1;
        _stmt.bindString(_argIndex, habitId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteAllRecordsForHabit.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteAllRemindersForHabit(final String habitId,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteAllRemindersForHabit.acquire();
        int _argIndex = 1;
        _stmt.bindString(_argIndex, habitId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteAllRemindersForHabit.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<Habit>> getAllActiveHabits() {
    final String _sql = "SELECT * FROM habits WHERE isActive = 1 ORDER BY sortOrder ASC, createdAt DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"habits"}, new Callable<List<Habit>>() {
      @Override
      @NonNull
      public List<Habit> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfIconName = CursorUtil.getColumnIndexOrThrow(_cursor, "iconName");
          final int _cursorIndexOfColorHex = CursorUtil.getColumnIndexOrThrow(_cursor, "colorHex");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfCustomCategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "customCategoryId");
          final int _cursorIndexOfFrequencyType = CursorUtil.getColumnIndexOrThrow(_cursor, "frequencyType");
          final int _cursorIndexOfFrequencyDays = CursorUtil.getColumnIndexOrThrow(_cursor, "frequencyDays");
          final int _cursorIndexOfTargetCount = CursorUtil.getColumnIndexOrThrow(_cursor, "targetCount");
          final int _cursorIndexOfReminderEnabled = CursorUtil.getColumnIndexOrThrow(_cursor, "reminderEnabled");
          final int _cursorIndexOfReminderTime = CursorUtil.getColumnIndexOrThrow(_cursor, "reminderTime");
          final int _cursorIndexOfFixedTime = CursorUtil.getColumnIndexOrThrow(_cursor, "fixedTime");
          final int _cursorIndexOfDifficulty = CursorUtil.getColumnIndexOrThrow(_cursor, "difficulty");
          final int _cursorIndexOfRelatedGoalId = CursorUtil.getColumnIndexOrThrow(_cursor, "relatedGoalId");
          final int _cursorIndexOfCurrentStreak = CursorUtil.getColumnIndexOrThrow(_cursor, "currentStreak");
          final int _cursorIndexOfLongestStreak = CursorUtil.getColumnIndexOrThrow(_cursor, "longestStreak");
          final int _cursorIndexOfTotalCompletions = CursorUtil.getColumnIndexOrThrow(_cursor, "totalCompletions");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "isActive");
          final int _cursorIndexOfCustomEmoji = CursorUtil.getColumnIndexOrThrow(_cursor, "customEmoji");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfSortOrder = CursorUtil.getColumnIndexOrThrow(_cursor, "sortOrder");
          final List<Habit> _result = new ArrayList<Habit>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Habit _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpDescription;
            _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            final String _tmpIconName;
            _tmpIconName = _cursor.getString(_cursorIndexOfIconName);
            final String _tmpColorHex;
            _tmpColorHex = _cursor.getString(_cursorIndexOfColorHex);
            final String _tmpCategory;
            _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            final String _tmpCustomCategoryId;
            if (_cursor.isNull(_cursorIndexOfCustomCategoryId)) {
              _tmpCustomCategoryId = null;
            } else {
              _tmpCustomCategoryId = _cursor.getString(_cursorIndexOfCustomCategoryId);
            }
            final String _tmpFrequencyType;
            _tmpFrequencyType = _cursor.getString(_cursorIndexOfFrequencyType);
            final String _tmpFrequencyDays;
            _tmpFrequencyDays = _cursor.getString(_cursorIndexOfFrequencyDays);
            final int _tmpTargetCount;
            _tmpTargetCount = _cursor.getInt(_cursorIndexOfTargetCount);
            final boolean _tmpReminderEnabled;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfReminderEnabled);
            _tmpReminderEnabled = _tmp != 0;
            final String _tmpReminderTime;
            if (_cursor.isNull(_cursorIndexOfReminderTime)) {
              _tmpReminderTime = null;
            } else {
              _tmpReminderTime = _cursor.getString(_cursorIndexOfReminderTime);
            }
            final String _tmpFixedTime;
            if (_cursor.isNull(_cursorIndexOfFixedTime)) {
              _tmpFixedTime = null;
            } else {
              _tmpFixedTime = _cursor.getString(_cursorIndexOfFixedTime);
            }
            final String _tmpDifficulty;
            _tmpDifficulty = _cursor.getString(_cursorIndexOfDifficulty);
            final String _tmpRelatedGoalId;
            if (_cursor.isNull(_cursorIndexOfRelatedGoalId)) {
              _tmpRelatedGoalId = null;
            } else {
              _tmpRelatedGoalId = _cursor.getString(_cursorIndexOfRelatedGoalId);
            }
            final int _tmpCurrentStreak;
            _tmpCurrentStreak = _cursor.getInt(_cursorIndexOfCurrentStreak);
            final int _tmpLongestStreak;
            _tmpLongestStreak = _cursor.getInt(_cursorIndexOfLongestStreak);
            final int _tmpTotalCompletions;
            _tmpTotalCompletions = _cursor.getInt(_cursorIndexOfTotalCompletions);
            final LocalDateTime _tmpCreatedAt;
            final Long _tmp_1;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            final LocalDateTime _tmp_2 = __dateTimeConverter.toLocalDateTime(_tmp_1);
            if (_tmp_2 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpCreatedAt = _tmp_2;
            }
            final LocalDateTime _tmpUpdatedAt;
            final Long _tmp_3;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            final LocalDateTime _tmp_4 = __dateTimeConverter.toLocalDateTime(_tmp_3);
            if (_tmp_4 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpUpdatedAt = _tmp_4;
            }
            final boolean _tmpIsActive;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfIsActive);
            _tmpIsActive = _tmp_5 != 0;
            final String _tmpCustomEmoji;
            _tmpCustomEmoji = _cursor.getString(_cursorIndexOfCustomEmoji);
            final String _tmpNotes;
            _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            final int _tmpSortOrder;
            _tmpSortOrder = _cursor.getInt(_cursorIndexOfSortOrder);
            _item = new Habit(_tmpId,_tmpName,_tmpDescription,_tmpIconName,_tmpColorHex,_tmpCategory,_tmpCustomCategoryId,_tmpFrequencyType,_tmpFrequencyDays,_tmpTargetCount,_tmpReminderEnabled,_tmpReminderTime,_tmpFixedTime,_tmpDifficulty,_tmpRelatedGoalId,_tmpCurrentStreak,_tmpLongestStreak,_tmpTotalCompletions,_tmpCreatedAt,_tmpUpdatedAt,_tmpIsActive,_tmpCustomEmoji,_tmpNotes,_tmpSortOrder);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getAllHabits(final Continuation<? super List<Habit>> $completion) {
    final String _sql = "SELECT * FROM habits ORDER BY sortOrder ASC, createdAt DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<Habit>>() {
      @Override
      @NonNull
      public List<Habit> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfIconName = CursorUtil.getColumnIndexOrThrow(_cursor, "iconName");
          final int _cursorIndexOfColorHex = CursorUtil.getColumnIndexOrThrow(_cursor, "colorHex");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfCustomCategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "customCategoryId");
          final int _cursorIndexOfFrequencyType = CursorUtil.getColumnIndexOrThrow(_cursor, "frequencyType");
          final int _cursorIndexOfFrequencyDays = CursorUtil.getColumnIndexOrThrow(_cursor, "frequencyDays");
          final int _cursorIndexOfTargetCount = CursorUtil.getColumnIndexOrThrow(_cursor, "targetCount");
          final int _cursorIndexOfReminderEnabled = CursorUtil.getColumnIndexOrThrow(_cursor, "reminderEnabled");
          final int _cursorIndexOfReminderTime = CursorUtil.getColumnIndexOrThrow(_cursor, "reminderTime");
          final int _cursorIndexOfFixedTime = CursorUtil.getColumnIndexOrThrow(_cursor, "fixedTime");
          final int _cursorIndexOfDifficulty = CursorUtil.getColumnIndexOrThrow(_cursor, "difficulty");
          final int _cursorIndexOfRelatedGoalId = CursorUtil.getColumnIndexOrThrow(_cursor, "relatedGoalId");
          final int _cursorIndexOfCurrentStreak = CursorUtil.getColumnIndexOrThrow(_cursor, "currentStreak");
          final int _cursorIndexOfLongestStreak = CursorUtil.getColumnIndexOrThrow(_cursor, "longestStreak");
          final int _cursorIndexOfTotalCompletions = CursorUtil.getColumnIndexOrThrow(_cursor, "totalCompletions");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "isActive");
          final int _cursorIndexOfCustomEmoji = CursorUtil.getColumnIndexOrThrow(_cursor, "customEmoji");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfSortOrder = CursorUtil.getColumnIndexOrThrow(_cursor, "sortOrder");
          final List<Habit> _result = new ArrayList<Habit>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Habit _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpDescription;
            _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            final String _tmpIconName;
            _tmpIconName = _cursor.getString(_cursorIndexOfIconName);
            final String _tmpColorHex;
            _tmpColorHex = _cursor.getString(_cursorIndexOfColorHex);
            final String _tmpCategory;
            _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            final String _tmpCustomCategoryId;
            if (_cursor.isNull(_cursorIndexOfCustomCategoryId)) {
              _tmpCustomCategoryId = null;
            } else {
              _tmpCustomCategoryId = _cursor.getString(_cursorIndexOfCustomCategoryId);
            }
            final String _tmpFrequencyType;
            _tmpFrequencyType = _cursor.getString(_cursorIndexOfFrequencyType);
            final String _tmpFrequencyDays;
            _tmpFrequencyDays = _cursor.getString(_cursorIndexOfFrequencyDays);
            final int _tmpTargetCount;
            _tmpTargetCount = _cursor.getInt(_cursorIndexOfTargetCount);
            final boolean _tmpReminderEnabled;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfReminderEnabled);
            _tmpReminderEnabled = _tmp != 0;
            final String _tmpReminderTime;
            if (_cursor.isNull(_cursorIndexOfReminderTime)) {
              _tmpReminderTime = null;
            } else {
              _tmpReminderTime = _cursor.getString(_cursorIndexOfReminderTime);
            }
            final String _tmpFixedTime;
            if (_cursor.isNull(_cursorIndexOfFixedTime)) {
              _tmpFixedTime = null;
            } else {
              _tmpFixedTime = _cursor.getString(_cursorIndexOfFixedTime);
            }
            final String _tmpDifficulty;
            _tmpDifficulty = _cursor.getString(_cursorIndexOfDifficulty);
            final String _tmpRelatedGoalId;
            if (_cursor.isNull(_cursorIndexOfRelatedGoalId)) {
              _tmpRelatedGoalId = null;
            } else {
              _tmpRelatedGoalId = _cursor.getString(_cursorIndexOfRelatedGoalId);
            }
            final int _tmpCurrentStreak;
            _tmpCurrentStreak = _cursor.getInt(_cursorIndexOfCurrentStreak);
            final int _tmpLongestStreak;
            _tmpLongestStreak = _cursor.getInt(_cursorIndexOfLongestStreak);
            final int _tmpTotalCompletions;
            _tmpTotalCompletions = _cursor.getInt(_cursorIndexOfTotalCompletions);
            final LocalDateTime _tmpCreatedAt;
            final Long _tmp_1;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            final LocalDateTime _tmp_2 = __dateTimeConverter.toLocalDateTime(_tmp_1);
            if (_tmp_2 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpCreatedAt = _tmp_2;
            }
            final LocalDateTime _tmpUpdatedAt;
            final Long _tmp_3;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            final LocalDateTime _tmp_4 = __dateTimeConverter.toLocalDateTime(_tmp_3);
            if (_tmp_4 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpUpdatedAt = _tmp_4;
            }
            final boolean _tmpIsActive;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfIsActive);
            _tmpIsActive = _tmp_5 != 0;
            final String _tmpCustomEmoji;
            _tmpCustomEmoji = _cursor.getString(_cursorIndexOfCustomEmoji);
            final String _tmpNotes;
            _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            final int _tmpSortOrder;
            _tmpSortOrder = _cursor.getInt(_cursorIndexOfSortOrder);
            _item = new Habit(_tmpId,_tmpName,_tmpDescription,_tmpIconName,_tmpColorHex,_tmpCategory,_tmpCustomCategoryId,_tmpFrequencyType,_tmpFrequencyDays,_tmpTargetCount,_tmpReminderEnabled,_tmpReminderTime,_tmpFixedTime,_tmpDifficulty,_tmpRelatedGoalId,_tmpCurrentStreak,_tmpLongestStreak,_tmpTotalCompletions,_tmpCreatedAt,_tmpUpdatedAt,_tmpIsActive,_tmpCustomEmoji,_tmpNotes,_tmpSortOrder);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getHabitById(final String habitId, final Continuation<? super Habit> $completion) {
    final String _sql = "SELECT * FROM habits WHERE id = ? AND isActive = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, habitId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Habit>() {
      @Override
      @Nullable
      public Habit call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfIconName = CursorUtil.getColumnIndexOrThrow(_cursor, "iconName");
          final int _cursorIndexOfColorHex = CursorUtil.getColumnIndexOrThrow(_cursor, "colorHex");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfCustomCategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "customCategoryId");
          final int _cursorIndexOfFrequencyType = CursorUtil.getColumnIndexOrThrow(_cursor, "frequencyType");
          final int _cursorIndexOfFrequencyDays = CursorUtil.getColumnIndexOrThrow(_cursor, "frequencyDays");
          final int _cursorIndexOfTargetCount = CursorUtil.getColumnIndexOrThrow(_cursor, "targetCount");
          final int _cursorIndexOfReminderEnabled = CursorUtil.getColumnIndexOrThrow(_cursor, "reminderEnabled");
          final int _cursorIndexOfReminderTime = CursorUtil.getColumnIndexOrThrow(_cursor, "reminderTime");
          final int _cursorIndexOfFixedTime = CursorUtil.getColumnIndexOrThrow(_cursor, "fixedTime");
          final int _cursorIndexOfDifficulty = CursorUtil.getColumnIndexOrThrow(_cursor, "difficulty");
          final int _cursorIndexOfRelatedGoalId = CursorUtil.getColumnIndexOrThrow(_cursor, "relatedGoalId");
          final int _cursorIndexOfCurrentStreak = CursorUtil.getColumnIndexOrThrow(_cursor, "currentStreak");
          final int _cursorIndexOfLongestStreak = CursorUtil.getColumnIndexOrThrow(_cursor, "longestStreak");
          final int _cursorIndexOfTotalCompletions = CursorUtil.getColumnIndexOrThrow(_cursor, "totalCompletions");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "isActive");
          final int _cursorIndexOfCustomEmoji = CursorUtil.getColumnIndexOrThrow(_cursor, "customEmoji");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfSortOrder = CursorUtil.getColumnIndexOrThrow(_cursor, "sortOrder");
          final Habit _result;
          if (_cursor.moveToFirst()) {
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpDescription;
            _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            final String _tmpIconName;
            _tmpIconName = _cursor.getString(_cursorIndexOfIconName);
            final String _tmpColorHex;
            _tmpColorHex = _cursor.getString(_cursorIndexOfColorHex);
            final String _tmpCategory;
            _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            final String _tmpCustomCategoryId;
            if (_cursor.isNull(_cursorIndexOfCustomCategoryId)) {
              _tmpCustomCategoryId = null;
            } else {
              _tmpCustomCategoryId = _cursor.getString(_cursorIndexOfCustomCategoryId);
            }
            final String _tmpFrequencyType;
            _tmpFrequencyType = _cursor.getString(_cursorIndexOfFrequencyType);
            final String _tmpFrequencyDays;
            _tmpFrequencyDays = _cursor.getString(_cursorIndexOfFrequencyDays);
            final int _tmpTargetCount;
            _tmpTargetCount = _cursor.getInt(_cursorIndexOfTargetCount);
            final boolean _tmpReminderEnabled;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfReminderEnabled);
            _tmpReminderEnabled = _tmp != 0;
            final String _tmpReminderTime;
            if (_cursor.isNull(_cursorIndexOfReminderTime)) {
              _tmpReminderTime = null;
            } else {
              _tmpReminderTime = _cursor.getString(_cursorIndexOfReminderTime);
            }
            final String _tmpFixedTime;
            if (_cursor.isNull(_cursorIndexOfFixedTime)) {
              _tmpFixedTime = null;
            } else {
              _tmpFixedTime = _cursor.getString(_cursorIndexOfFixedTime);
            }
            final String _tmpDifficulty;
            _tmpDifficulty = _cursor.getString(_cursorIndexOfDifficulty);
            final String _tmpRelatedGoalId;
            if (_cursor.isNull(_cursorIndexOfRelatedGoalId)) {
              _tmpRelatedGoalId = null;
            } else {
              _tmpRelatedGoalId = _cursor.getString(_cursorIndexOfRelatedGoalId);
            }
            final int _tmpCurrentStreak;
            _tmpCurrentStreak = _cursor.getInt(_cursorIndexOfCurrentStreak);
            final int _tmpLongestStreak;
            _tmpLongestStreak = _cursor.getInt(_cursorIndexOfLongestStreak);
            final int _tmpTotalCompletions;
            _tmpTotalCompletions = _cursor.getInt(_cursorIndexOfTotalCompletions);
            final LocalDateTime _tmpCreatedAt;
            final Long _tmp_1;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            final LocalDateTime _tmp_2 = __dateTimeConverter.toLocalDateTime(_tmp_1);
            if (_tmp_2 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpCreatedAt = _tmp_2;
            }
            final LocalDateTime _tmpUpdatedAt;
            final Long _tmp_3;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            final LocalDateTime _tmp_4 = __dateTimeConverter.toLocalDateTime(_tmp_3);
            if (_tmp_4 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpUpdatedAt = _tmp_4;
            }
            final boolean _tmpIsActive;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfIsActive);
            _tmpIsActive = _tmp_5 != 0;
            final String _tmpCustomEmoji;
            _tmpCustomEmoji = _cursor.getString(_cursorIndexOfCustomEmoji);
            final String _tmpNotes;
            _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            final int _tmpSortOrder;
            _tmpSortOrder = _cursor.getInt(_cursorIndexOfSortOrder);
            _result = new Habit(_tmpId,_tmpName,_tmpDescription,_tmpIconName,_tmpColorHex,_tmpCategory,_tmpCustomCategoryId,_tmpFrequencyType,_tmpFrequencyDays,_tmpTargetCount,_tmpReminderEnabled,_tmpReminderTime,_tmpFixedTime,_tmpDifficulty,_tmpRelatedGoalId,_tmpCurrentStreak,_tmpLongestStreak,_tmpTotalCompletions,_tmpCreatedAt,_tmpUpdatedAt,_tmpIsActive,_tmpCustomEmoji,_tmpNotes,_tmpSortOrder);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getHabitByIdIncludeInactive(final String habitId,
      final Continuation<? super Habit> $completion) {
    final String _sql = "SELECT * FROM habits WHERE id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, habitId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Habit>() {
      @Override
      @Nullable
      public Habit call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfIconName = CursorUtil.getColumnIndexOrThrow(_cursor, "iconName");
          final int _cursorIndexOfColorHex = CursorUtil.getColumnIndexOrThrow(_cursor, "colorHex");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfCustomCategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "customCategoryId");
          final int _cursorIndexOfFrequencyType = CursorUtil.getColumnIndexOrThrow(_cursor, "frequencyType");
          final int _cursorIndexOfFrequencyDays = CursorUtil.getColumnIndexOrThrow(_cursor, "frequencyDays");
          final int _cursorIndexOfTargetCount = CursorUtil.getColumnIndexOrThrow(_cursor, "targetCount");
          final int _cursorIndexOfReminderEnabled = CursorUtil.getColumnIndexOrThrow(_cursor, "reminderEnabled");
          final int _cursorIndexOfReminderTime = CursorUtil.getColumnIndexOrThrow(_cursor, "reminderTime");
          final int _cursorIndexOfFixedTime = CursorUtil.getColumnIndexOrThrow(_cursor, "fixedTime");
          final int _cursorIndexOfDifficulty = CursorUtil.getColumnIndexOrThrow(_cursor, "difficulty");
          final int _cursorIndexOfRelatedGoalId = CursorUtil.getColumnIndexOrThrow(_cursor, "relatedGoalId");
          final int _cursorIndexOfCurrentStreak = CursorUtil.getColumnIndexOrThrow(_cursor, "currentStreak");
          final int _cursorIndexOfLongestStreak = CursorUtil.getColumnIndexOrThrow(_cursor, "longestStreak");
          final int _cursorIndexOfTotalCompletions = CursorUtil.getColumnIndexOrThrow(_cursor, "totalCompletions");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "isActive");
          final int _cursorIndexOfCustomEmoji = CursorUtil.getColumnIndexOrThrow(_cursor, "customEmoji");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfSortOrder = CursorUtil.getColumnIndexOrThrow(_cursor, "sortOrder");
          final Habit _result;
          if (_cursor.moveToFirst()) {
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpDescription;
            _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            final String _tmpIconName;
            _tmpIconName = _cursor.getString(_cursorIndexOfIconName);
            final String _tmpColorHex;
            _tmpColorHex = _cursor.getString(_cursorIndexOfColorHex);
            final String _tmpCategory;
            _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            final String _tmpCustomCategoryId;
            if (_cursor.isNull(_cursorIndexOfCustomCategoryId)) {
              _tmpCustomCategoryId = null;
            } else {
              _tmpCustomCategoryId = _cursor.getString(_cursorIndexOfCustomCategoryId);
            }
            final String _tmpFrequencyType;
            _tmpFrequencyType = _cursor.getString(_cursorIndexOfFrequencyType);
            final String _tmpFrequencyDays;
            _tmpFrequencyDays = _cursor.getString(_cursorIndexOfFrequencyDays);
            final int _tmpTargetCount;
            _tmpTargetCount = _cursor.getInt(_cursorIndexOfTargetCount);
            final boolean _tmpReminderEnabled;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfReminderEnabled);
            _tmpReminderEnabled = _tmp != 0;
            final String _tmpReminderTime;
            if (_cursor.isNull(_cursorIndexOfReminderTime)) {
              _tmpReminderTime = null;
            } else {
              _tmpReminderTime = _cursor.getString(_cursorIndexOfReminderTime);
            }
            final String _tmpFixedTime;
            if (_cursor.isNull(_cursorIndexOfFixedTime)) {
              _tmpFixedTime = null;
            } else {
              _tmpFixedTime = _cursor.getString(_cursorIndexOfFixedTime);
            }
            final String _tmpDifficulty;
            _tmpDifficulty = _cursor.getString(_cursorIndexOfDifficulty);
            final String _tmpRelatedGoalId;
            if (_cursor.isNull(_cursorIndexOfRelatedGoalId)) {
              _tmpRelatedGoalId = null;
            } else {
              _tmpRelatedGoalId = _cursor.getString(_cursorIndexOfRelatedGoalId);
            }
            final int _tmpCurrentStreak;
            _tmpCurrentStreak = _cursor.getInt(_cursorIndexOfCurrentStreak);
            final int _tmpLongestStreak;
            _tmpLongestStreak = _cursor.getInt(_cursorIndexOfLongestStreak);
            final int _tmpTotalCompletions;
            _tmpTotalCompletions = _cursor.getInt(_cursorIndexOfTotalCompletions);
            final LocalDateTime _tmpCreatedAt;
            final Long _tmp_1;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            final LocalDateTime _tmp_2 = __dateTimeConverter.toLocalDateTime(_tmp_1);
            if (_tmp_2 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpCreatedAt = _tmp_2;
            }
            final LocalDateTime _tmpUpdatedAt;
            final Long _tmp_3;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            final LocalDateTime _tmp_4 = __dateTimeConverter.toLocalDateTime(_tmp_3);
            if (_tmp_4 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpUpdatedAt = _tmp_4;
            }
            final boolean _tmpIsActive;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfIsActive);
            _tmpIsActive = _tmp_5 != 0;
            final String _tmpCustomEmoji;
            _tmpCustomEmoji = _cursor.getString(_cursorIndexOfCustomEmoji);
            final String _tmpNotes;
            _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            final int _tmpSortOrder;
            _tmpSortOrder = _cursor.getInt(_cursorIndexOfSortOrder);
            _result = new Habit(_tmpId,_tmpName,_tmpDescription,_tmpIconName,_tmpColorHex,_tmpCategory,_tmpCustomCategoryId,_tmpFrequencyType,_tmpFrequencyDays,_tmpTargetCount,_tmpReminderEnabled,_tmpReminderTime,_tmpFixedTime,_tmpDifficulty,_tmpRelatedGoalId,_tmpCurrentStreak,_tmpLongestStreak,_tmpTotalCompletions,_tmpCreatedAt,_tmpUpdatedAt,_tmpIsActive,_tmpCustomEmoji,_tmpNotes,_tmpSortOrder);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<Habit>> getHabitsByCategory(final String category) {
    final String _sql = "SELECT * FROM habits WHERE category = ? AND isActive = 1 ORDER BY sortOrder ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, category);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"habits"}, new Callable<List<Habit>>() {
      @Override
      @NonNull
      public List<Habit> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfIconName = CursorUtil.getColumnIndexOrThrow(_cursor, "iconName");
          final int _cursorIndexOfColorHex = CursorUtil.getColumnIndexOrThrow(_cursor, "colorHex");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfCustomCategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "customCategoryId");
          final int _cursorIndexOfFrequencyType = CursorUtil.getColumnIndexOrThrow(_cursor, "frequencyType");
          final int _cursorIndexOfFrequencyDays = CursorUtil.getColumnIndexOrThrow(_cursor, "frequencyDays");
          final int _cursorIndexOfTargetCount = CursorUtil.getColumnIndexOrThrow(_cursor, "targetCount");
          final int _cursorIndexOfReminderEnabled = CursorUtil.getColumnIndexOrThrow(_cursor, "reminderEnabled");
          final int _cursorIndexOfReminderTime = CursorUtil.getColumnIndexOrThrow(_cursor, "reminderTime");
          final int _cursorIndexOfFixedTime = CursorUtil.getColumnIndexOrThrow(_cursor, "fixedTime");
          final int _cursorIndexOfDifficulty = CursorUtil.getColumnIndexOrThrow(_cursor, "difficulty");
          final int _cursorIndexOfRelatedGoalId = CursorUtil.getColumnIndexOrThrow(_cursor, "relatedGoalId");
          final int _cursorIndexOfCurrentStreak = CursorUtil.getColumnIndexOrThrow(_cursor, "currentStreak");
          final int _cursorIndexOfLongestStreak = CursorUtil.getColumnIndexOrThrow(_cursor, "longestStreak");
          final int _cursorIndexOfTotalCompletions = CursorUtil.getColumnIndexOrThrow(_cursor, "totalCompletions");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "isActive");
          final int _cursorIndexOfCustomEmoji = CursorUtil.getColumnIndexOrThrow(_cursor, "customEmoji");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfSortOrder = CursorUtil.getColumnIndexOrThrow(_cursor, "sortOrder");
          final List<Habit> _result = new ArrayList<Habit>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Habit _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpDescription;
            _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            final String _tmpIconName;
            _tmpIconName = _cursor.getString(_cursorIndexOfIconName);
            final String _tmpColorHex;
            _tmpColorHex = _cursor.getString(_cursorIndexOfColorHex);
            final String _tmpCategory;
            _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            final String _tmpCustomCategoryId;
            if (_cursor.isNull(_cursorIndexOfCustomCategoryId)) {
              _tmpCustomCategoryId = null;
            } else {
              _tmpCustomCategoryId = _cursor.getString(_cursorIndexOfCustomCategoryId);
            }
            final String _tmpFrequencyType;
            _tmpFrequencyType = _cursor.getString(_cursorIndexOfFrequencyType);
            final String _tmpFrequencyDays;
            _tmpFrequencyDays = _cursor.getString(_cursorIndexOfFrequencyDays);
            final int _tmpTargetCount;
            _tmpTargetCount = _cursor.getInt(_cursorIndexOfTargetCount);
            final boolean _tmpReminderEnabled;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfReminderEnabled);
            _tmpReminderEnabled = _tmp != 0;
            final String _tmpReminderTime;
            if (_cursor.isNull(_cursorIndexOfReminderTime)) {
              _tmpReminderTime = null;
            } else {
              _tmpReminderTime = _cursor.getString(_cursorIndexOfReminderTime);
            }
            final String _tmpFixedTime;
            if (_cursor.isNull(_cursorIndexOfFixedTime)) {
              _tmpFixedTime = null;
            } else {
              _tmpFixedTime = _cursor.getString(_cursorIndexOfFixedTime);
            }
            final String _tmpDifficulty;
            _tmpDifficulty = _cursor.getString(_cursorIndexOfDifficulty);
            final String _tmpRelatedGoalId;
            if (_cursor.isNull(_cursorIndexOfRelatedGoalId)) {
              _tmpRelatedGoalId = null;
            } else {
              _tmpRelatedGoalId = _cursor.getString(_cursorIndexOfRelatedGoalId);
            }
            final int _tmpCurrentStreak;
            _tmpCurrentStreak = _cursor.getInt(_cursorIndexOfCurrentStreak);
            final int _tmpLongestStreak;
            _tmpLongestStreak = _cursor.getInt(_cursorIndexOfLongestStreak);
            final int _tmpTotalCompletions;
            _tmpTotalCompletions = _cursor.getInt(_cursorIndexOfTotalCompletions);
            final LocalDateTime _tmpCreatedAt;
            final Long _tmp_1;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            final LocalDateTime _tmp_2 = __dateTimeConverter.toLocalDateTime(_tmp_1);
            if (_tmp_2 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpCreatedAt = _tmp_2;
            }
            final LocalDateTime _tmpUpdatedAt;
            final Long _tmp_3;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            final LocalDateTime _tmp_4 = __dateTimeConverter.toLocalDateTime(_tmp_3);
            if (_tmp_4 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpUpdatedAt = _tmp_4;
            }
            final boolean _tmpIsActive;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfIsActive);
            _tmpIsActive = _tmp_5 != 0;
            final String _tmpCustomEmoji;
            _tmpCustomEmoji = _cursor.getString(_cursorIndexOfCustomEmoji);
            final String _tmpNotes;
            _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            final int _tmpSortOrder;
            _tmpSortOrder = _cursor.getInt(_cursorIndexOfSortOrder);
            _item = new Habit(_tmpId,_tmpName,_tmpDescription,_tmpIconName,_tmpColorHex,_tmpCategory,_tmpCustomCategoryId,_tmpFrequencyType,_tmpFrequencyDays,_tmpTargetCount,_tmpReminderEnabled,_tmpReminderTime,_tmpFixedTime,_tmpDifficulty,_tmpRelatedGoalId,_tmpCurrentStreak,_tmpLongestStreak,_tmpTotalCompletions,_tmpCreatedAt,_tmpUpdatedAt,_tmpIsActive,_tmpCustomEmoji,_tmpNotes,_tmpSortOrder);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getHabitsByGoalId(final String goalId,
      final Continuation<? super List<Habit>> $completion) {
    final String _sql = "SELECT * FROM habits WHERE relatedGoalId = ? AND isActive = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, goalId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<Habit>>() {
      @Override
      @NonNull
      public List<Habit> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfIconName = CursorUtil.getColumnIndexOrThrow(_cursor, "iconName");
          final int _cursorIndexOfColorHex = CursorUtil.getColumnIndexOrThrow(_cursor, "colorHex");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfCustomCategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "customCategoryId");
          final int _cursorIndexOfFrequencyType = CursorUtil.getColumnIndexOrThrow(_cursor, "frequencyType");
          final int _cursorIndexOfFrequencyDays = CursorUtil.getColumnIndexOrThrow(_cursor, "frequencyDays");
          final int _cursorIndexOfTargetCount = CursorUtil.getColumnIndexOrThrow(_cursor, "targetCount");
          final int _cursorIndexOfReminderEnabled = CursorUtil.getColumnIndexOrThrow(_cursor, "reminderEnabled");
          final int _cursorIndexOfReminderTime = CursorUtil.getColumnIndexOrThrow(_cursor, "reminderTime");
          final int _cursorIndexOfFixedTime = CursorUtil.getColumnIndexOrThrow(_cursor, "fixedTime");
          final int _cursorIndexOfDifficulty = CursorUtil.getColumnIndexOrThrow(_cursor, "difficulty");
          final int _cursorIndexOfRelatedGoalId = CursorUtil.getColumnIndexOrThrow(_cursor, "relatedGoalId");
          final int _cursorIndexOfCurrentStreak = CursorUtil.getColumnIndexOrThrow(_cursor, "currentStreak");
          final int _cursorIndexOfLongestStreak = CursorUtil.getColumnIndexOrThrow(_cursor, "longestStreak");
          final int _cursorIndexOfTotalCompletions = CursorUtil.getColumnIndexOrThrow(_cursor, "totalCompletions");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "isActive");
          final int _cursorIndexOfCustomEmoji = CursorUtil.getColumnIndexOrThrow(_cursor, "customEmoji");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfSortOrder = CursorUtil.getColumnIndexOrThrow(_cursor, "sortOrder");
          final List<Habit> _result = new ArrayList<Habit>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Habit _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpDescription;
            _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            final String _tmpIconName;
            _tmpIconName = _cursor.getString(_cursorIndexOfIconName);
            final String _tmpColorHex;
            _tmpColorHex = _cursor.getString(_cursorIndexOfColorHex);
            final String _tmpCategory;
            _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            final String _tmpCustomCategoryId;
            if (_cursor.isNull(_cursorIndexOfCustomCategoryId)) {
              _tmpCustomCategoryId = null;
            } else {
              _tmpCustomCategoryId = _cursor.getString(_cursorIndexOfCustomCategoryId);
            }
            final String _tmpFrequencyType;
            _tmpFrequencyType = _cursor.getString(_cursorIndexOfFrequencyType);
            final String _tmpFrequencyDays;
            _tmpFrequencyDays = _cursor.getString(_cursorIndexOfFrequencyDays);
            final int _tmpTargetCount;
            _tmpTargetCount = _cursor.getInt(_cursorIndexOfTargetCount);
            final boolean _tmpReminderEnabled;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfReminderEnabled);
            _tmpReminderEnabled = _tmp != 0;
            final String _tmpReminderTime;
            if (_cursor.isNull(_cursorIndexOfReminderTime)) {
              _tmpReminderTime = null;
            } else {
              _tmpReminderTime = _cursor.getString(_cursorIndexOfReminderTime);
            }
            final String _tmpFixedTime;
            if (_cursor.isNull(_cursorIndexOfFixedTime)) {
              _tmpFixedTime = null;
            } else {
              _tmpFixedTime = _cursor.getString(_cursorIndexOfFixedTime);
            }
            final String _tmpDifficulty;
            _tmpDifficulty = _cursor.getString(_cursorIndexOfDifficulty);
            final String _tmpRelatedGoalId;
            if (_cursor.isNull(_cursorIndexOfRelatedGoalId)) {
              _tmpRelatedGoalId = null;
            } else {
              _tmpRelatedGoalId = _cursor.getString(_cursorIndexOfRelatedGoalId);
            }
            final int _tmpCurrentStreak;
            _tmpCurrentStreak = _cursor.getInt(_cursorIndexOfCurrentStreak);
            final int _tmpLongestStreak;
            _tmpLongestStreak = _cursor.getInt(_cursorIndexOfLongestStreak);
            final int _tmpTotalCompletions;
            _tmpTotalCompletions = _cursor.getInt(_cursorIndexOfTotalCompletions);
            final LocalDateTime _tmpCreatedAt;
            final Long _tmp_1;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            final LocalDateTime _tmp_2 = __dateTimeConverter.toLocalDateTime(_tmp_1);
            if (_tmp_2 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpCreatedAt = _tmp_2;
            }
            final LocalDateTime _tmpUpdatedAt;
            final Long _tmp_3;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            final LocalDateTime _tmp_4 = __dateTimeConverter.toLocalDateTime(_tmp_3);
            if (_tmp_4 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpUpdatedAt = _tmp_4;
            }
            final boolean _tmpIsActive;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfIsActive);
            _tmpIsActive = _tmp_5 != 0;
            final String _tmpCustomEmoji;
            _tmpCustomEmoji = _cursor.getString(_cursorIndexOfCustomEmoji);
            final String _tmpNotes;
            _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            final int _tmpSortOrder;
            _tmpSortOrder = _cursor.getInt(_cursorIndexOfSortOrder);
            _item = new Habit(_tmpId,_tmpName,_tmpDescription,_tmpIconName,_tmpColorHex,_tmpCategory,_tmpCustomCategoryId,_tmpFrequencyType,_tmpFrequencyDays,_tmpTargetCount,_tmpReminderEnabled,_tmpReminderTime,_tmpFixedTime,_tmpDifficulty,_tmpRelatedGoalId,_tmpCurrentStreak,_tmpLongestStreak,_tmpTotalCompletions,_tmpCreatedAt,_tmpUpdatedAt,_tmpIsActive,_tmpCustomEmoji,_tmpNotes,_tmpSortOrder);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<Habit>> getHabitsByGoalIdFlow(final String goalId) {
    final String _sql = "SELECT * FROM habits WHERE relatedGoalId = ? AND isActive = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, goalId);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"habits"}, new Callable<List<Habit>>() {
      @Override
      @NonNull
      public List<Habit> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfIconName = CursorUtil.getColumnIndexOrThrow(_cursor, "iconName");
          final int _cursorIndexOfColorHex = CursorUtil.getColumnIndexOrThrow(_cursor, "colorHex");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfCustomCategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "customCategoryId");
          final int _cursorIndexOfFrequencyType = CursorUtil.getColumnIndexOrThrow(_cursor, "frequencyType");
          final int _cursorIndexOfFrequencyDays = CursorUtil.getColumnIndexOrThrow(_cursor, "frequencyDays");
          final int _cursorIndexOfTargetCount = CursorUtil.getColumnIndexOrThrow(_cursor, "targetCount");
          final int _cursorIndexOfReminderEnabled = CursorUtil.getColumnIndexOrThrow(_cursor, "reminderEnabled");
          final int _cursorIndexOfReminderTime = CursorUtil.getColumnIndexOrThrow(_cursor, "reminderTime");
          final int _cursorIndexOfFixedTime = CursorUtil.getColumnIndexOrThrow(_cursor, "fixedTime");
          final int _cursorIndexOfDifficulty = CursorUtil.getColumnIndexOrThrow(_cursor, "difficulty");
          final int _cursorIndexOfRelatedGoalId = CursorUtil.getColumnIndexOrThrow(_cursor, "relatedGoalId");
          final int _cursorIndexOfCurrentStreak = CursorUtil.getColumnIndexOrThrow(_cursor, "currentStreak");
          final int _cursorIndexOfLongestStreak = CursorUtil.getColumnIndexOrThrow(_cursor, "longestStreak");
          final int _cursorIndexOfTotalCompletions = CursorUtil.getColumnIndexOrThrow(_cursor, "totalCompletions");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "isActive");
          final int _cursorIndexOfCustomEmoji = CursorUtil.getColumnIndexOrThrow(_cursor, "customEmoji");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfSortOrder = CursorUtil.getColumnIndexOrThrow(_cursor, "sortOrder");
          final List<Habit> _result = new ArrayList<Habit>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Habit _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpDescription;
            _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            final String _tmpIconName;
            _tmpIconName = _cursor.getString(_cursorIndexOfIconName);
            final String _tmpColorHex;
            _tmpColorHex = _cursor.getString(_cursorIndexOfColorHex);
            final String _tmpCategory;
            _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            final String _tmpCustomCategoryId;
            if (_cursor.isNull(_cursorIndexOfCustomCategoryId)) {
              _tmpCustomCategoryId = null;
            } else {
              _tmpCustomCategoryId = _cursor.getString(_cursorIndexOfCustomCategoryId);
            }
            final String _tmpFrequencyType;
            _tmpFrequencyType = _cursor.getString(_cursorIndexOfFrequencyType);
            final String _tmpFrequencyDays;
            _tmpFrequencyDays = _cursor.getString(_cursorIndexOfFrequencyDays);
            final int _tmpTargetCount;
            _tmpTargetCount = _cursor.getInt(_cursorIndexOfTargetCount);
            final boolean _tmpReminderEnabled;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfReminderEnabled);
            _tmpReminderEnabled = _tmp != 0;
            final String _tmpReminderTime;
            if (_cursor.isNull(_cursorIndexOfReminderTime)) {
              _tmpReminderTime = null;
            } else {
              _tmpReminderTime = _cursor.getString(_cursorIndexOfReminderTime);
            }
            final String _tmpFixedTime;
            if (_cursor.isNull(_cursorIndexOfFixedTime)) {
              _tmpFixedTime = null;
            } else {
              _tmpFixedTime = _cursor.getString(_cursorIndexOfFixedTime);
            }
            final String _tmpDifficulty;
            _tmpDifficulty = _cursor.getString(_cursorIndexOfDifficulty);
            final String _tmpRelatedGoalId;
            if (_cursor.isNull(_cursorIndexOfRelatedGoalId)) {
              _tmpRelatedGoalId = null;
            } else {
              _tmpRelatedGoalId = _cursor.getString(_cursorIndexOfRelatedGoalId);
            }
            final int _tmpCurrentStreak;
            _tmpCurrentStreak = _cursor.getInt(_cursorIndexOfCurrentStreak);
            final int _tmpLongestStreak;
            _tmpLongestStreak = _cursor.getInt(_cursorIndexOfLongestStreak);
            final int _tmpTotalCompletions;
            _tmpTotalCompletions = _cursor.getInt(_cursorIndexOfTotalCompletions);
            final LocalDateTime _tmpCreatedAt;
            final Long _tmp_1;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            final LocalDateTime _tmp_2 = __dateTimeConverter.toLocalDateTime(_tmp_1);
            if (_tmp_2 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpCreatedAt = _tmp_2;
            }
            final LocalDateTime _tmpUpdatedAt;
            final Long _tmp_3;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            final LocalDateTime _tmp_4 = __dateTimeConverter.toLocalDateTime(_tmp_3);
            if (_tmp_4 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpUpdatedAt = _tmp_4;
            }
            final boolean _tmpIsActive;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfIsActive);
            _tmpIsActive = _tmp_5 != 0;
            final String _tmpCustomEmoji;
            _tmpCustomEmoji = _cursor.getString(_cursorIndexOfCustomEmoji);
            final String _tmpNotes;
            _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            final int _tmpSortOrder;
            _tmpSortOrder = _cursor.getInt(_cursorIndexOfSortOrder);
            _item = new Habit(_tmpId,_tmpName,_tmpDescription,_tmpIconName,_tmpColorHex,_tmpCategory,_tmpCustomCategoryId,_tmpFrequencyType,_tmpFrequencyDays,_tmpTargetCount,_tmpReminderEnabled,_tmpReminderTime,_tmpFixedTime,_tmpDifficulty,_tmpRelatedGoalId,_tmpCurrentStreak,_tmpLongestStreak,_tmpTotalCompletions,_tmpCreatedAt,_tmpUpdatedAt,_tmpIsActive,_tmpCustomEmoji,_tmpNotes,_tmpSortOrder);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getRecordsForHabit(final String habitId,
      final Continuation<? super List<HabitRecord>> $completion) {
    final String _sql = "SELECT * FROM habit_records WHERE habitId = ? ORDER BY date DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, habitId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<HabitRecord>>() {
      @Override
      @NonNull
      public List<HabitRecord> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfHabitId = CursorUtil.getColumnIndexOrThrow(_cursor, "habitId");
          final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
          final int _cursorIndexOfCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "completed");
          final int _cursorIndexOfCompletedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "completedAt");
          final int _cursorIndexOfCompletionCount = CursorUtil.getColumnIndexOrThrow(_cursor, "completionCount");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfSkipReason = CursorUtil.getColumnIndexOrThrow(_cursor, "skipReason");
          final int _cursorIndexOfMood = CursorUtil.getColumnIndexOrThrow(_cursor, "mood");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final List<HabitRecord> _result = new ArrayList<HabitRecord>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final HabitRecord _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpHabitId;
            _tmpHabitId = _cursor.getString(_cursorIndexOfHabitId);
            final LocalDate _tmpDate;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfDate);
            }
            final LocalDate _tmp_1 = __converters.fromEpochDayToDate(_tmp);
            if (_tmp_1 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDate', but it was NULL.");
            } else {
              _tmpDate = _tmp_1;
            }
            final boolean _tmpCompleted;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfCompleted);
            _tmpCompleted = _tmp_2 != 0;
            final LocalDateTime _tmpCompletedAt;
            final Long _tmp_3;
            if (_cursor.isNull(_cursorIndexOfCompletedAt)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getLong(_cursorIndexOfCompletedAt);
            }
            _tmpCompletedAt = __dateTimeConverter.toLocalDateTime(_tmp_3);
            final int _tmpCompletionCount;
            _tmpCompletionCount = _cursor.getInt(_cursorIndexOfCompletionCount);
            final String _tmpNotes;
            _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            final String _tmpSkipReason;
            _tmpSkipReason = _cursor.getString(_cursorIndexOfSkipReason);
            final String _tmpMood;
            _tmpMood = _cursor.getString(_cursorIndexOfMood);
            final LocalDateTime _tmpCreatedAt;
            final Long _tmp_4;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            final LocalDateTime _tmp_5 = __dateTimeConverter.toLocalDateTime(_tmp_4);
            if (_tmp_5 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpCreatedAt = _tmp_5;
            }
            final LocalDateTime _tmpUpdatedAt;
            final Long _tmp_6;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmp_6 = null;
            } else {
              _tmp_6 = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            final LocalDateTime _tmp_7 = __dateTimeConverter.toLocalDateTime(_tmp_6);
            if (_tmp_7 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpUpdatedAt = _tmp_7;
            }
            _item = new HabitRecord(_tmpId,_tmpHabitId,_tmpDate,_tmpCompleted,_tmpCompletedAt,_tmpCompletionCount,_tmpNotes,_tmpSkipReason,_tmpMood,_tmpCreatedAt,_tmpUpdatedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getAllHabitRecords(final Continuation<? super List<HabitRecord>> $completion) {
    final String _sql = "SELECT * FROM habit_records ORDER BY date DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<HabitRecord>>() {
      @Override
      @NonNull
      public List<HabitRecord> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfHabitId = CursorUtil.getColumnIndexOrThrow(_cursor, "habitId");
          final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
          final int _cursorIndexOfCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "completed");
          final int _cursorIndexOfCompletedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "completedAt");
          final int _cursorIndexOfCompletionCount = CursorUtil.getColumnIndexOrThrow(_cursor, "completionCount");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfSkipReason = CursorUtil.getColumnIndexOrThrow(_cursor, "skipReason");
          final int _cursorIndexOfMood = CursorUtil.getColumnIndexOrThrow(_cursor, "mood");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final List<HabitRecord> _result = new ArrayList<HabitRecord>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final HabitRecord _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpHabitId;
            _tmpHabitId = _cursor.getString(_cursorIndexOfHabitId);
            final LocalDate _tmpDate;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfDate);
            }
            final LocalDate _tmp_1 = __converters.fromEpochDayToDate(_tmp);
            if (_tmp_1 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDate', but it was NULL.");
            } else {
              _tmpDate = _tmp_1;
            }
            final boolean _tmpCompleted;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfCompleted);
            _tmpCompleted = _tmp_2 != 0;
            final LocalDateTime _tmpCompletedAt;
            final Long _tmp_3;
            if (_cursor.isNull(_cursorIndexOfCompletedAt)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getLong(_cursorIndexOfCompletedAt);
            }
            _tmpCompletedAt = __dateTimeConverter.toLocalDateTime(_tmp_3);
            final int _tmpCompletionCount;
            _tmpCompletionCount = _cursor.getInt(_cursorIndexOfCompletionCount);
            final String _tmpNotes;
            _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            final String _tmpSkipReason;
            _tmpSkipReason = _cursor.getString(_cursorIndexOfSkipReason);
            final String _tmpMood;
            _tmpMood = _cursor.getString(_cursorIndexOfMood);
            final LocalDateTime _tmpCreatedAt;
            final Long _tmp_4;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            final LocalDateTime _tmp_5 = __dateTimeConverter.toLocalDateTime(_tmp_4);
            if (_tmp_5 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpCreatedAt = _tmp_5;
            }
            final LocalDateTime _tmpUpdatedAt;
            final Long _tmp_6;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmp_6 = null;
            } else {
              _tmp_6 = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            final LocalDateTime _tmp_7 = __dateTimeConverter.toLocalDateTime(_tmp_6);
            if (_tmp_7 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpUpdatedAt = _tmp_7;
            }
            _item = new HabitRecord(_tmpId,_tmpHabitId,_tmpDate,_tmpCompleted,_tmpCompletedAt,_tmpCompletionCount,_tmpNotes,_tmpSkipReason,_tmpMood,_tmpCreatedAt,_tmpUpdatedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getRecordForDate(final String habitId, final LocalDate date,
      final Continuation<? super HabitRecord> $completion) {
    final String _sql = "SELECT * FROM habit_records WHERE habitId = ? AND date = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindString(_argIndex, habitId);
    _argIndex = 2;
    final Long _tmp = __converters.dateToEpochDay(date);
    if (_tmp == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindLong(_argIndex, _tmp);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<HabitRecord>() {
      @Override
      @Nullable
      public HabitRecord call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfHabitId = CursorUtil.getColumnIndexOrThrow(_cursor, "habitId");
          final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
          final int _cursorIndexOfCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "completed");
          final int _cursorIndexOfCompletedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "completedAt");
          final int _cursorIndexOfCompletionCount = CursorUtil.getColumnIndexOrThrow(_cursor, "completionCount");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfSkipReason = CursorUtil.getColumnIndexOrThrow(_cursor, "skipReason");
          final int _cursorIndexOfMood = CursorUtil.getColumnIndexOrThrow(_cursor, "mood");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final HabitRecord _result;
          if (_cursor.moveToFirst()) {
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpHabitId;
            _tmpHabitId = _cursor.getString(_cursorIndexOfHabitId);
            final LocalDate _tmpDate;
            final Long _tmp_1;
            if (_cursor.isNull(_cursorIndexOfDate)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getLong(_cursorIndexOfDate);
            }
            final LocalDate _tmp_2 = __converters.fromEpochDayToDate(_tmp_1);
            if (_tmp_2 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDate', but it was NULL.");
            } else {
              _tmpDate = _tmp_2;
            }
            final boolean _tmpCompleted;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfCompleted);
            _tmpCompleted = _tmp_3 != 0;
            final LocalDateTime _tmpCompletedAt;
            final Long _tmp_4;
            if (_cursor.isNull(_cursorIndexOfCompletedAt)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getLong(_cursorIndexOfCompletedAt);
            }
            _tmpCompletedAt = __dateTimeConverter.toLocalDateTime(_tmp_4);
            final int _tmpCompletionCount;
            _tmpCompletionCount = _cursor.getInt(_cursorIndexOfCompletionCount);
            final String _tmpNotes;
            _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            final String _tmpSkipReason;
            _tmpSkipReason = _cursor.getString(_cursorIndexOfSkipReason);
            final String _tmpMood;
            _tmpMood = _cursor.getString(_cursorIndexOfMood);
            final LocalDateTime _tmpCreatedAt;
            final Long _tmp_5;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_5 = null;
            } else {
              _tmp_5 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            final LocalDateTime _tmp_6 = __dateTimeConverter.toLocalDateTime(_tmp_5);
            if (_tmp_6 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpCreatedAt = _tmp_6;
            }
            final LocalDateTime _tmpUpdatedAt;
            final Long _tmp_7;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmp_7 = null;
            } else {
              _tmp_7 = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            final LocalDateTime _tmp_8 = __dateTimeConverter.toLocalDateTime(_tmp_7);
            if (_tmp_8 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpUpdatedAt = _tmp_8;
            }
            _result = new HabitRecord(_tmpId,_tmpHabitId,_tmpDate,_tmpCompleted,_tmpCompletedAt,_tmpCompletionCount,_tmpNotes,_tmpSkipReason,_tmpMood,_tmpCreatedAt,_tmpUpdatedAt);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getRecordsForDateRange(final String habitId, final LocalDate startDate,
      final LocalDate endDate, final Continuation<? super List<HabitRecord>> $completion) {
    final String _sql = "SELECT * FROM habit_records WHERE habitId = ? AND date BETWEEN ? AND ? ORDER BY date DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 3);
    int _argIndex = 1;
    _statement.bindString(_argIndex, habitId);
    _argIndex = 2;
    final Long _tmp = __converters.dateToEpochDay(startDate);
    if (_tmp == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindLong(_argIndex, _tmp);
    }
    _argIndex = 3;
    final Long _tmp_1 = __converters.dateToEpochDay(endDate);
    if (_tmp_1 == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindLong(_argIndex, _tmp_1);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<HabitRecord>>() {
      @Override
      @NonNull
      public List<HabitRecord> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfHabitId = CursorUtil.getColumnIndexOrThrow(_cursor, "habitId");
          final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
          final int _cursorIndexOfCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "completed");
          final int _cursorIndexOfCompletedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "completedAt");
          final int _cursorIndexOfCompletionCount = CursorUtil.getColumnIndexOrThrow(_cursor, "completionCount");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfSkipReason = CursorUtil.getColumnIndexOrThrow(_cursor, "skipReason");
          final int _cursorIndexOfMood = CursorUtil.getColumnIndexOrThrow(_cursor, "mood");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final List<HabitRecord> _result = new ArrayList<HabitRecord>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final HabitRecord _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpHabitId;
            _tmpHabitId = _cursor.getString(_cursorIndexOfHabitId);
            final LocalDate _tmpDate;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfDate)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfDate);
            }
            final LocalDate _tmp_3 = __converters.fromEpochDayToDate(_tmp_2);
            if (_tmp_3 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDate', but it was NULL.");
            } else {
              _tmpDate = _tmp_3;
            }
            final boolean _tmpCompleted;
            final int _tmp_4;
            _tmp_4 = _cursor.getInt(_cursorIndexOfCompleted);
            _tmpCompleted = _tmp_4 != 0;
            final LocalDateTime _tmpCompletedAt;
            final Long _tmp_5;
            if (_cursor.isNull(_cursorIndexOfCompletedAt)) {
              _tmp_5 = null;
            } else {
              _tmp_5 = _cursor.getLong(_cursorIndexOfCompletedAt);
            }
            _tmpCompletedAt = __dateTimeConverter.toLocalDateTime(_tmp_5);
            final int _tmpCompletionCount;
            _tmpCompletionCount = _cursor.getInt(_cursorIndexOfCompletionCount);
            final String _tmpNotes;
            _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            final String _tmpSkipReason;
            _tmpSkipReason = _cursor.getString(_cursorIndexOfSkipReason);
            final String _tmpMood;
            _tmpMood = _cursor.getString(_cursorIndexOfMood);
            final LocalDateTime _tmpCreatedAt;
            final Long _tmp_6;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_6 = null;
            } else {
              _tmp_6 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            final LocalDateTime _tmp_7 = __dateTimeConverter.toLocalDateTime(_tmp_6);
            if (_tmp_7 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpCreatedAt = _tmp_7;
            }
            final LocalDateTime _tmpUpdatedAt;
            final Long _tmp_8;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmp_8 = null;
            } else {
              _tmp_8 = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            final LocalDateTime _tmp_9 = __dateTimeConverter.toLocalDateTime(_tmp_8);
            if (_tmp_9 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpUpdatedAt = _tmp_9;
            }
            _item = new HabitRecord(_tmpId,_tmpHabitId,_tmpDate,_tmpCompleted,_tmpCompletedAt,_tmpCompletionCount,_tmpNotes,_tmpSkipReason,_tmpMood,_tmpCreatedAt,_tmpUpdatedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getAllRecordsForDate(final LocalDate date,
      final Continuation<? super List<HabitRecord>> $completion) {
    final String _sql = "SELECT * FROM habit_records WHERE date = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    final Long _tmp = __converters.dateToEpochDay(date);
    if (_tmp == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindLong(_argIndex, _tmp);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<HabitRecord>>() {
      @Override
      @NonNull
      public List<HabitRecord> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfHabitId = CursorUtil.getColumnIndexOrThrow(_cursor, "habitId");
          final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
          final int _cursorIndexOfCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "completed");
          final int _cursorIndexOfCompletedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "completedAt");
          final int _cursorIndexOfCompletionCount = CursorUtil.getColumnIndexOrThrow(_cursor, "completionCount");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfSkipReason = CursorUtil.getColumnIndexOrThrow(_cursor, "skipReason");
          final int _cursorIndexOfMood = CursorUtil.getColumnIndexOrThrow(_cursor, "mood");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final List<HabitRecord> _result = new ArrayList<HabitRecord>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final HabitRecord _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpHabitId;
            _tmpHabitId = _cursor.getString(_cursorIndexOfHabitId);
            final LocalDate _tmpDate;
            final Long _tmp_1;
            if (_cursor.isNull(_cursorIndexOfDate)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getLong(_cursorIndexOfDate);
            }
            final LocalDate _tmp_2 = __converters.fromEpochDayToDate(_tmp_1);
            if (_tmp_2 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDate', but it was NULL.");
            } else {
              _tmpDate = _tmp_2;
            }
            final boolean _tmpCompleted;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfCompleted);
            _tmpCompleted = _tmp_3 != 0;
            final LocalDateTime _tmpCompletedAt;
            final Long _tmp_4;
            if (_cursor.isNull(_cursorIndexOfCompletedAt)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getLong(_cursorIndexOfCompletedAt);
            }
            _tmpCompletedAt = __dateTimeConverter.toLocalDateTime(_tmp_4);
            final int _tmpCompletionCount;
            _tmpCompletionCount = _cursor.getInt(_cursorIndexOfCompletionCount);
            final String _tmpNotes;
            _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            final String _tmpSkipReason;
            _tmpSkipReason = _cursor.getString(_cursorIndexOfSkipReason);
            final String _tmpMood;
            _tmpMood = _cursor.getString(_cursorIndexOfMood);
            final LocalDateTime _tmpCreatedAt;
            final Long _tmp_5;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_5 = null;
            } else {
              _tmp_5 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            final LocalDateTime _tmp_6 = __dateTimeConverter.toLocalDateTime(_tmp_5);
            if (_tmp_6 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpCreatedAt = _tmp_6;
            }
            final LocalDateTime _tmpUpdatedAt;
            final Long _tmp_7;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmp_7 = null;
            } else {
              _tmp_7 = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            final LocalDateTime _tmp_8 = __dateTimeConverter.toLocalDateTime(_tmp_7);
            if (_tmp_8 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpUpdatedAt = _tmp_8;
            }
            _item = new HabitRecord(_tmpId,_tmpHabitId,_tmpDate,_tmpCompleted,_tmpCompletedAt,_tmpCompletionCount,_tmpNotes,_tmpSkipReason,_tmpMood,_tmpCreatedAt,_tmpUpdatedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getCompletedRecordsForHabit(final String habitId,
      final Continuation<? super List<HabitRecord>> $completion) {
    final String _sql = "SELECT * FROM habit_records WHERE habitId = ? AND completed = 1 ORDER BY date DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, habitId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<HabitRecord>>() {
      @Override
      @NonNull
      public List<HabitRecord> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfHabitId = CursorUtil.getColumnIndexOrThrow(_cursor, "habitId");
          final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
          final int _cursorIndexOfCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "completed");
          final int _cursorIndexOfCompletedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "completedAt");
          final int _cursorIndexOfCompletionCount = CursorUtil.getColumnIndexOrThrow(_cursor, "completionCount");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfSkipReason = CursorUtil.getColumnIndexOrThrow(_cursor, "skipReason");
          final int _cursorIndexOfMood = CursorUtil.getColumnIndexOrThrow(_cursor, "mood");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final List<HabitRecord> _result = new ArrayList<HabitRecord>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final HabitRecord _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpHabitId;
            _tmpHabitId = _cursor.getString(_cursorIndexOfHabitId);
            final LocalDate _tmpDate;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfDate);
            }
            final LocalDate _tmp_1 = __converters.fromEpochDayToDate(_tmp);
            if (_tmp_1 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDate', but it was NULL.");
            } else {
              _tmpDate = _tmp_1;
            }
            final boolean _tmpCompleted;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfCompleted);
            _tmpCompleted = _tmp_2 != 0;
            final LocalDateTime _tmpCompletedAt;
            final Long _tmp_3;
            if (_cursor.isNull(_cursorIndexOfCompletedAt)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getLong(_cursorIndexOfCompletedAt);
            }
            _tmpCompletedAt = __dateTimeConverter.toLocalDateTime(_tmp_3);
            final int _tmpCompletionCount;
            _tmpCompletionCount = _cursor.getInt(_cursorIndexOfCompletionCount);
            final String _tmpNotes;
            _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            final String _tmpSkipReason;
            _tmpSkipReason = _cursor.getString(_cursorIndexOfSkipReason);
            final String _tmpMood;
            _tmpMood = _cursor.getString(_cursorIndexOfMood);
            final LocalDateTime _tmpCreatedAt;
            final Long _tmp_4;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            final LocalDateTime _tmp_5 = __dateTimeConverter.toLocalDateTime(_tmp_4);
            if (_tmp_5 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpCreatedAt = _tmp_5;
            }
            final LocalDateTime _tmpUpdatedAt;
            final Long _tmp_6;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmp_6 = null;
            } else {
              _tmp_6 = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            final LocalDateTime _tmp_7 = __dateTimeConverter.toLocalDateTime(_tmp_6);
            if (_tmp_7 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpUpdatedAt = _tmp_7;
            }
            _item = new HabitRecord(_tmpId,_tmpHabitId,_tmpDate,_tmpCompleted,_tmpCompletedAt,_tmpCompletionCount,_tmpNotes,_tmpSkipReason,_tmpMood,_tmpCreatedAt,_tmpUpdatedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getTotalCompletionsForHabit(final String habitId,
      final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT COUNT(*) FROM habit_records WHERE habitId = ? AND completed = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, habitId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final int _tmp;
            _tmp = _cursor.getInt(0);
            _result = _tmp;
          } else {
            _result = 0;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getCompletionsForDateRange(final String habitId, final LocalDate startDate,
      final LocalDate endDate, final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT COUNT(*) FROM habit_records WHERE habitId = ? AND completed = 1 AND date BETWEEN ? AND ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 3);
    int _argIndex = 1;
    _statement.bindString(_argIndex, habitId);
    _argIndex = 2;
    final Long _tmp = __converters.dateToEpochDay(startDate);
    if (_tmp == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindLong(_argIndex, _tmp);
    }
    _argIndex = 3;
    final Long _tmp_1 = __converters.dateToEpochDay(endDate);
    if (_tmp_1 == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindLong(_argIndex, _tmp_1);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(0);
            _result = _tmp_2;
          } else {
            _result = 0;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getRemindersForHabit(final String habitId,
      final Continuation<? super List<HabitReminder>> $completion) {
    final String _sql = "SELECT * FROM habit_reminders WHERE habitId = ? AND enabled = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, habitId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<HabitReminder>>() {
      @Override
      @NonNull
      public List<HabitReminder> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfHabitId = CursorUtil.getColumnIndexOrThrow(_cursor, "habitId");
          final int _cursorIndexOfTime = CursorUtil.getColumnIndexOrThrow(_cursor, "time");
          final int _cursorIndexOfDays = CursorUtil.getColumnIndexOrThrow(_cursor, "days");
          final int _cursorIndexOfEnabled = CursorUtil.getColumnIndexOrThrow(_cursor, "enabled");
          final int _cursorIndexOfMessage = CursorUtil.getColumnIndexOrThrow(_cursor, "message");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final List<HabitReminder> _result = new ArrayList<HabitReminder>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final HabitReminder _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpHabitId;
            _tmpHabitId = _cursor.getString(_cursorIndexOfHabitId);
            final String _tmpTime;
            _tmpTime = _cursor.getString(_cursorIndexOfTime);
            final String _tmpDays;
            _tmpDays = _cursor.getString(_cursorIndexOfDays);
            final boolean _tmpEnabled;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfEnabled);
            _tmpEnabled = _tmp != 0;
            final String _tmpMessage;
            _tmpMessage = _cursor.getString(_cursorIndexOfMessage);
            final LocalDateTime _tmpCreatedAt;
            final Long _tmp_1;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            final LocalDateTime _tmp_2 = __dateTimeConverter.toLocalDateTime(_tmp_1);
            if (_tmp_2 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpCreatedAt = _tmp_2;
            }
            final LocalDateTime _tmpUpdatedAt;
            final Long _tmp_3;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            final LocalDateTime _tmp_4 = __dateTimeConverter.toLocalDateTime(_tmp_3);
            if (_tmp_4 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpUpdatedAt = _tmp_4;
            }
            _item = new HabitReminder(_tmpId,_tmpHabitId,_tmpTime,_tmpDays,_tmpEnabled,_tmpMessage,_tmpCreatedAt,_tmpUpdatedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getAllActiveReminders(final Continuation<? super List<HabitReminder>> $completion) {
    final String _sql = "SELECT * FROM habit_reminders WHERE enabled = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<HabitReminder>>() {
      @Override
      @NonNull
      public List<HabitReminder> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfHabitId = CursorUtil.getColumnIndexOrThrow(_cursor, "habitId");
          final int _cursorIndexOfTime = CursorUtil.getColumnIndexOrThrow(_cursor, "time");
          final int _cursorIndexOfDays = CursorUtil.getColumnIndexOrThrow(_cursor, "days");
          final int _cursorIndexOfEnabled = CursorUtil.getColumnIndexOrThrow(_cursor, "enabled");
          final int _cursorIndexOfMessage = CursorUtil.getColumnIndexOrThrow(_cursor, "message");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final List<HabitReminder> _result = new ArrayList<HabitReminder>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final HabitReminder _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpHabitId;
            _tmpHabitId = _cursor.getString(_cursorIndexOfHabitId);
            final String _tmpTime;
            _tmpTime = _cursor.getString(_cursorIndexOfTime);
            final String _tmpDays;
            _tmpDays = _cursor.getString(_cursorIndexOfDays);
            final boolean _tmpEnabled;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfEnabled);
            _tmpEnabled = _tmp != 0;
            final String _tmpMessage;
            _tmpMessage = _cursor.getString(_cursorIndexOfMessage);
            final LocalDateTime _tmpCreatedAt;
            final Long _tmp_1;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            final LocalDateTime _tmp_2 = __dateTimeConverter.toLocalDateTime(_tmp_1);
            if (_tmp_2 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpCreatedAt = _tmp_2;
            }
            final LocalDateTime _tmpUpdatedAt;
            final Long _tmp_3;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            final LocalDateTime _tmp_4 = __dateTimeConverter.toLocalDateTime(_tmp_3);
            if (_tmp_4 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpUpdatedAt = _tmp_4;
            }
            _item = new HabitReminder(_tmpId,_tmpHabitId,_tmpTime,_tmpDays,_tmpEnabled,_tmpMessage,_tmpCreatedAt,_tmpUpdatedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<Habit>> getHabitsWithStats() {
    final String _sql = "\n"
            + "        SELECT h.*, \n"
            + "               COALESCE(total_completions.count, 0) as totalCompletions,\n"
            + "               COALESCE(current_streak.streak, 0) as currentStreak\n"
            + "        FROM habits h\n"
            + "        LEFT JOIN (\n"
            + "            SELECT habitId, COUNT(*) as count \n"
            + "            FROM habit_records \n"
            + "            WHERE completed = 1 \n"
            + "            GROUP BY habitId\n"
            + "        ) total_completions ON h.id = total_completions.habitId\n"
            + "        LEFT JOIN (\n"
            + "            SELECT habitId, COUNT(*) as streak\n"
            + "            FROM habit_records \n"
            + "            WHERE completed = 1 AND date >= date('now', '-30 days')\n"
            + "            GROUP BY habitId\n"
            + "        ) current_streak ON h.id = current_streak.habitId\n"
            + "        WHERE h.isActive = 1\n"
            + "        ORDER BY h.sortOrder ASC, h.createdAt DESC\n"
            + "    ";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"habits",
        "habit_records"}, new Callable<List<Habit>>() {
      @Override
      @NonNull
      public List<Habit> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfIconName = CursorUtil.getColumnIndexOrThrow(_cursor, "iconName");
          final int _cursorIndexOfColorHex = CursorUtil.getColumnIndexOrThrow(_cursor, "colorHex");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfCustomCategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "customCategoryId");
          final int _cursorIndexOfFrequencyType = CursorUtil.getColumnIndexOrThrow(_cursor, "frequencyType");
          final int _cursorIndexOfFrequencyDays = CursorUtil.getColumnIndexOrThrow(_cursor, "frequencyDays");
          final int _cursorIndexOfTargetCount = CursorUtil.getColumnIndexOrThrow(_cursor, "targetCount");
          final int _cursorIndexOfReminderEnabled = CursorUtil.getColumnIndexOrThrow(_cursor, "reminderEnabled");
          final int _cursorIndexOfReminderTime = CursorUtil.getColumnIndexOrThrow(_cursor, "reminderTime");
          final int _cursorIndexOfFixedTime = CursorUtil.getColumnIndexOrThrow(_cursor, "fixedTime");
          final int _cursorIndexOfDifficulty = CursorUtil.getColumnIndexOrThrow(_cursor, "difficulty");
          final int _cursorIndexOfRelatedGoalId = CursorUtil.getColumnIndexOrThrow(_cursor, "relatedGoalId");
          final int _cursorIndexOfCurrentStreak = CursorUtil.getColumnIndexOrThrow(_cursor, "currentStreak");
          final int _cursorIndexOfLongestStreak = CursorUtil.getColumnIndexOrThrow(_cursor, "longestStreak");
          final int _cursorIndexOfTotalCompletions = CursorUtil.getColumnIndexOrThrow(_cursor, "totalCompletions");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "isActive");
          final int _cursorIndexOfCustomEmoji = CursorUtil.getColumnIndexOrThrow(_cursor, "customEmoji");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfSortOrder = CursorUtil.getColumnIndexOrThrow(_cursor, "sortOrder");
          final List<Habit> _result = new ArrayList<Habit>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Habit _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpDescription;
            _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            final String _tmpIconName;
            _tmpIconName = _cursor.getString(_cursorIndexOfIconName);
            final String _tmpColorHex;
            _tmpColorHex = _cursor.getString(_cursorIndexOfColorHex);
            final String _tmpCategory;
            _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            final String _tmpCustomCategoryId;
            if (_cursor.isNull(_cursorIndexOfCustomCategoryId)) {
              _tmpCustomCategoryId = null;
            } else {
              _tmpCustomCategoryId = _cursor.getString(_cursorIndexOfCustomCategoryId);
            }
            final String _tmpFrequencyType;
            _tmpFrequencyType = _cursor.getString(_cursorIndexOfFrequencyType);
            final String _tmpFrequencyDays;
            _tmpFrequencyDays = _cursor.getString(_cursorIndexOfFrequencyDays);
            final int _tmpTargetCount;
            _tmpTargetCount = _cursor.getInt(_cursorIndexOfTargetCount);
            final boolean _tmpReminderEnabled;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfReminderEnabled);
            _tmpReminderEnabled = _tmp != 0;
            final String _tmpReminderTime;
            if (_cursor.isNull(_cursorIndexOfReminderTime)) {
              _tmpReminderTime = null;
            } else {
              _tmpReminderTime = _cursor.getString(_cursorIndexOfReminderTime);
            }
            final String _tmpFixedTime;
            if (_cursor.isNull(_cursorIndexOfFixedTime)) {
              _tmpFixedTime = null;
            } else {
              _tmpFixedTime = _cursor.getString(_cursorIndexOfFixedTime);
            }
            final String _tmpDifficulty;
            _tmpDifficulty = _cursor.getString(_cursorIndexOfDifficulty);
            final String _tmpRelatedGoalId;
            if (_cursor.isNull(_cursorIndexOfRelatedGoalId)) {
              _tmpRelatedGoalId = null;
            } else {
              _tmpRelatedGoalId = _cursor.getString(_cursorIndexOfRelatedGoalId);
            }
            final int _tmpCurrentStreak;
            _tmpCurrentStreak = _cursor.getInt(_cursorIndexOfCurrentStreak);
            final int _tmpLongestStreak;
            _tmpLongestStreak = _cursor.getInt(_cursorIndexOfLongestStreak);
            final int _tmpTotalCompletions;
            _tmpTotalCompletions = _cursor.getInt(_cursorIndexOfTotalCompletions);
            final LocalDateTime _tmpCreatedAt;
            final Long _tmp_1;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            final LocalDateTime _tmp_2 = __dateTimeConverter.toLocalDateTime(_tmp_1);
            if (_tmp_2 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpCreatedAt = _tmp_2;
            }
            final LocalDateTime _tmpUpdatedAt;
            final Long _tmp_3;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            final LocalDateTime _tmp_4 = __dateTimeConverter.toLocalDateTime(_tmp_3);
            if (_tmp_4 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpUpdatedAt = _tmp_4;
            }
            final boolean _tmpIsActive;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfIsActive);
            _tmpIsActive = _tmp_5 != 0;
            final String _tmpCustomEmoji;
            _tmpCustomEmoji = _cursor.getString(_cursorIndexOfCustomEmoji);
            final String _tmpNotes;
            _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            final int _tmpSortOrder;
            _tmpSortOrder = _cursor.getInt(_cursorIndexOfSortOrder);
            _item = new Habit(_tmpId,_tmpName,_tmpDescription,_tmpIconName,_tmpColorHex,_tmpCategory,_tmpCustomCategoryId,_tmpFrequencyType,_tmpFrequencyDays,_tmpTargetCount,_tmpReminderEnabled,_tmpReminderTime,_tmpFixedTime,_tmpDifficulty,_tmpRelatedGoalId,_tmpCurrentStreak,_tmpLongestStreak,_tmpTotalCompletions,_tmpCreatedAt,_tmpUpdatedAt,_tmpIsActive,_tmpCustomEmoji,_tmpNotes,_tmpSortOrder);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getCompletedHabitsCountForDate(final LocalDate date,
      final Continuation<? super Integer> $completion) {
    final String _sql = "\n"
            + "        SELECT COUNT(DISTINCT habitId) \n"
            + "        FROM habit_records \n"
            + "        WHERE completed = 1 AND date = ?\n"
            + "    ";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    final Long _tmp = __converters.dateToEpochDay(date);
    if (_tmp == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindLong(_argIndex, _tmp);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(0);
            _result = _tmp_1;
          } else {
            _result = 0;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getAverageCompletionRate(final Continuation<? super Float> $completion) {
    final String _sql = "\n"
            + "        SELECT AVG(completion_rate) as avgCompletionRate\n"
            + "        FROM (\n"
            + "            SELECT habitId, \n"
            + "                   CAST(COUNT(CASE WHEN completed = 1 THEN 1 END) AS FLOAT) / COUNT(*) as completion_rate\n"
            + "            FROM habit_records \n"
            + "            WHERE date >= date('now', '-30 days')\n"
            + "            GROUP BY habitId\n"
            + "        )\n"
            + "    ";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Float>() {
      @Override
      @Nullable
      public Float call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Float _result;
          if (_cursor.moveToFirst()) {
            final Float _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getFloat(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getTopHabitsByCompletions(final int limit,
      final Continuation<? super List<HabitCompletionStat>> $completion) {
    final String _sql = "\n"
            + "        SELECT h.name, COUNT(hr.id) as completions\n"
            + "        FROM habits h\n"
            + "        LEFT JOIN habit_records hr ON h.id = hr.habitId AND hr.completed = 1\n"
            + "        WHERE h.isActive = 1\n"
            + "        GROUP BY h.id, h.name\n"
            + "        ORDER BY completions DESC\n"
            + "        LIMIT ?\n"
            + "    ";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, limit);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<HabitCompletionStat>>() {
      @Override
      @NonNull
      public List<HabitCompletionStat> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfName = 0;
          final int _cursorIndexOfCompletions = 1;
          final List<HabitCompletionStat> _result = new ArrayList<HabitCompletionStat>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final HabitCompletionStat _item;
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final int _tmpCompletions;
            _tmpCompletions = _cursor.getInt(_cursorIndexOfCompletions);
            _item = new HabitCompletionStat(_tmpName,_tmpCompletions);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getCurrentStreakForHabit(final String habitId,
      final Continuation<? super Integer> $completion) {
    final String _sql = "\n"
            + "        SELECT COUNT(*) \n"
            + "        FROM habit_records \n"
            + "        WHERE habitId = ? \n"
            + "        AND completed = 1 \n"
            + "        AND date >= (\n"
            + "            SELECT COALESCE(MAX(date), date('now', '-1000 days'))\n"
            + "            FROM habit_records \n"
            + "            WHERE habitId = ? \n"
            + "            AND completed = 0 \n"
            + "            AND date <= date('now')\n"
            + "        )\n"
            + "        AND date <= date('now')\n"
            + "    ";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindString(_argIndex, habitId);
    _argIndex = 2;
    _statement.bindString(_argIndex, habitId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final int _tmp;
            _tmp = _cursor.getInt(0);
            _result = _tmp;
          } else {
            _result = 0;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getLongestStreakForHabit(final String habitId,
      final Continuation<? super Integer> $completion) {
    final String _sql = "\n"
            + "        SELECT COUNT(*) \n"
            + "        FROM habit_records \n"
            + "        WHERE habitId = ? \n"
            + "        AND completed = 1\n"
            + "    ";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, habitId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @Nullable
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final Integer _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getInt(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getRecentRecordsForStreak(final String habitId,
      final Continuation<? super List<HabitRecord>> $completion) {
    final String _sql = "\n"
            + "        SELECT * \n"
            + "        FROM habit_records \n"
            + "        WHERE habitId = ? \n"
            + "        AND date <= date('now')\n"
            + "        ORDER BY date DESC \n"
            + "        LIMIT 30\n"
            + "    ";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, habitId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<HabitRecord>>() {
      @Override
      @NonNull
      public List<HabitRecord> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfHabitId = CursorUtil.getColumnIndexOrThrow(_cursor, "habitId");
          final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
          final int _cursorIndexOfCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "completed");
          final int _cursorIndexOfCompletedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "completedAt");
          final int _cursorIndexOfCompletionCount = CursorUtil.getColumnIndexOrThrow(_cursor, "completionCount");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfSkipReason = CursorUtil.getColumnIndexOrThrow(_cursor, "skipReason");
          final int _cursorIndexOfMood = CursorUtil.getColumnIndexOrThrow(_cursor, "mood");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final List<HabitRecord> _result = new ArrayList<HabitRecord>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final HabitRecord _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpHabitId;
            _tmpHabitId = _cursor.getString(_cursorIndexOfHabitId);
            final LocalDate _tmpDate;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfDate);
            }
            final LocalDate _tmp_1 = __converters.fromEpochDayToDate(_tmp);
            if (_tmp_1 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDate', but it was NULL.");
            } else {
              _tmpDate = _tmp_1;
            }
            final boolean _tmpCompleted;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfCompleted);
            _tmpCompleted = _tmp_2 != 0;
            final LocalDateTime _tmpCompletedAt;
            final Long _tmp_3;
            if (_cursor.isNull(_cursorIndexOfCompletedAt)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getLong(_cursorIndexOfCompletedAt);
            }
            _tmpCompletedAt = __dateTimeConverter.toLocalDateTime(_tmp_3);
            final int _tmpCompletionCount;
            _tmpCompletionCount = _cursor.getInt(_cursorIndexOfCompletionCount);
            final String _tmpNotes;
            _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            final String _tmpSkipReason;
            _tmpSkipReason = _cursor.getString(_cursorIndexOfSkipReason);
            final String _tmpMood;
            _tmpMood = _cursor.getString(_cursorIndexOfMood);
            final LocalDateTime _tmpCreatedAt;
            final Long _tmp_4;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            final LocalDateTime _tmp_5 = __dateTimeConverter.toLocalDateTime(_tmp_4);
            if (_tmp_5 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpCreatedAt = _tmp_5;
            }
            final LocalDateTime _tmpUpdatedAt;
            final Long _tmp_6;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmp_6 = null;
            } else {
              _tmp_6 = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            final LocalDateTime _tmp_7 = __dateTimeConverter.toLocalDateTime(_tmp_6);
            if (_tmp_7 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpUpdatedAt = _tmp_7;
            }
            _item = new HabitRecord(_tmpId,_tmpHabitId,_tmpDate,_tmpCompleted,_tmpCompletedAt,_tmpCompletionCount,_tmpNotes,_tmpSkipReason,_tmpMood,_tmpCreatedAt,_tmpUpdatedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
