package com.timeflow.app.di;

import com.timeflow.app.data.dao.GoalTemplateDao;
import com.timeflow.app.data.db.AppDatabase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DatabaseModule_ProvideGoalTemplateDaoFactory implements Factory<GoalTemplateDao> {
  private final Provider<AppDatabase> databaseProvider;

  public DatabaseModule_ProvideGoalTemplateDaoFactory(Provider<AppDatabase> databaseProvider) {
    this.databaseProvider = databaseProvider;
  }

  @Override
  public GoalTemplateDao get() {
    return provideGoalTemplateDao(databaseProvider.get());
  }

  public static DatabaseModule_ProvideGoalTemplateDaoFactory create(
      Provider<AppDatabase> databaseProvider) {
    return new DatabaseModule_ProvideGoalTemplateDaoFactory(databaseProvider);
  }

  public static GoalTemplateDao provideGoalTemplateDao(AppDatabase database) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.provideGoalTemplateDao(database));
  }
}
