package com.timeflow.app.data.repository;

import com.timeflow.app.data.dao.AppUsageDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class TimeAnalyticsRepositoryImpl_Factory implements Factory<TimeAnalyticsRepositoryImpl> {
  private final Provider<AppUsageDao> appUsageDaoProvider;

  public TimeAnalyticsRepositoryImpl_Factory(Provider<AppUsageDao> appUsageDaoProvider) {
    this.appUsageDaoProvider = appUsageDaoProvider;
  }

  @Override
  public TimeAnalyticsRepositoryImpl get() {
    return newInstance(appUsageDaoProvider.get());
  }

  public static TimeAnalyticsRepositoryImpl_Factory create(
      Provider<AppUsageDao> appUsageDaoProvider) {
    return new TimeAnalyticsRepositoryImpl_Factory(appUsageDaoProvider);
  }

  public static TimeAnalyticsRepositoryImpl newInstance(AppUsageDao appUsageDao) {
    return new TimeAnalyticsRepositoryImpl(appUsageDao);
  }
}
