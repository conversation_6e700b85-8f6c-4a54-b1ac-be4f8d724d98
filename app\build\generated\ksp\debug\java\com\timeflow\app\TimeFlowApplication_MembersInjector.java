package com.timeflow.app;

import androidx.room.RoomDatabase;
import com.timeflow.app.di.AppInitializer;
import com.timeflow.app.service.AiSuggestionScheduler;
import com.timeflow.app.service.TaskPersistentNotificationManager;
import com.timeflow.app.ui.screen.reflection.ReflectionRepository;
import com.timeflow.app.utils.AppExceptionHandler;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class TimeFlowApplication_MembersInjector implements MembersInjector<TimeFlowApplication> {
  private final Provider<AppExceptionHandler> exceptionHandlerProvider;

  private final Provider<AppInitializer> appInitializerProvider;

  private final Provider<RoomDatabase> appDatabaseProvider;

  private final Provider<ReflectionRepository> reflectionRepositoryProvider;

  private final Provider<AiSuggestionScheduler> aiSuggestionSchedulerProvider;

  private final Provider<TaskPersistentNotificationManager> taskPersistentNotificationManagerProvider;

  public TimeFlowApplication_MembersInjector(Provider<AppExceptionHandler> exceptionHandlerProvider,
      Provider<AppInitializer> appInitializerProvider, Provider<RoomDatabase> appDatabaseProvider,
      Provider<ReflectionRepository> reflectionRepositoryProvider,
      Provider<AiSuggestionScheduler> aiSuggestionSchedulerProvider,
      Provider<TaskPersistentNotificationManager> taskPersistentNotificationManagerProvider) {
    this.exceptionHandlerProvider = exceptionHandlerProvider;
    this.appInitializerProvider = appInitializerProvider;
    this.appDatabaseProvider = appDatabaseProvider;
    this.reflectionRepositoryProvider = reflectionRepositoryProvider;
    this.aiSuggestionSchedulerProvider = aiSuggestionSchedulerProvider;
    this.taskPersistentNotificationManagerProvider = taskPersistentNotificationManagerProvider;
  }

  public static MembersInjector<TimeFlowApplication> create(
      Provider<AppExceptionHandler> exceptionHandlerProvider,
      Provider<AppInitializer> appInitializerProvider, Provider<RoomDatabase> appDatabaseProvider,
      Provider<ReflectionRepository> reflectionRepositoryProvider,
      Provider<AiSuggestionScheduler> aiSuggestionSchedulerProvider,
      Provider<TaskPersistentNotificationManager> taskPersistentNotificationManagerProvider) {
    return new TimeFlowApplication_MembersInjector(exceptionHandlerProvider, appInitializerProvider, appDatabaseProvider, reflectionRepositoryProvider, aiSuggestionSchedulerProvider, taskPersistentNotificationManagerProvider);
  }

  @Override
  public void injectMembers(TimeFlowApplication instance) {
    injectExceptionHandler(instance, exceptionHandlerProvider.get());
    injectAppInitializer(instance, appInitializerProvider.get());
    injectAppDatabase(instance, appDatabaseProvider.get());
    injectReflectionRepository(instance, reflectionRepositoryProvider.get());
    injectAiSuggestionScheduler(instance, aiSuggestionSchedulerProvider.get());
    injectTaskPersistentNotificationManager(instance, taskPersistentNotificationManagerProvider.get());
  }

  @InjectedFieldSignature("com.timeflow.app.TimeFlowApplication.exceptionHandler")
  public static void injectExceptionHandler(TimeFlowApplication instance,
      AppExceptionHandler exceptionHandler) {
    instance.exceptionHandler = exceptionHandler;
  }

  @InjectedFieldSignature("com.timeflow.app.TimeFlowApplication.appInitializer")
  public static void injectAppInitializer(TimeFlowApplication instance,
      AppInitializer appInitializer) {
    instance.appInitializer = appInitializer;
  }

  @InjectedFieldSignature("com.timeflow.app.TimeFlowApplication.appDatabase")
  public static void injectAppDatabase(TimeFlowApplication instance, RoomDatabase appDatabase) {
    instance.appDatabase = appDatabase;
  }

  @InjectedFieldSignature("com.timeflow.app.TimeFlowApplication.reflectionRepository")
  public static void injectReflectionRepository(TimeFlowApplication instance,
      ReflectionRepository reflectionRepository) {
    instance.reflectionRepository = reflectionRepository;
  }

  @InjectedFieldSignature("com.timeflow.app.TimeFlowApplication.aiSuggestionScheduler")
  public static void injectAiSuggestionScheduler(TimeFlowApplication instance,
      AiSuggestionScheduler aiSuggestionScheduler) {
    instance.aiSuggestionScheduler = aiSuggestionScheduler;
  }

  @InjectedFieldSignature("com.timeflow.app.TimeFlowApplication.taskPersistentNotificationManager")
  public static void injectTaskPersistentNotificationManager(TimeFlowApplication instance,
      TaskPersistentNotificationManager taskPersistentNotificationManager) {
    instance.taskPersistentNotificationManager = taskPersistentNotificationManager;
  }
}
