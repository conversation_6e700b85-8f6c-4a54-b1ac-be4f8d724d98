package com.timeflow.app.ui.screen.health;

import android.content.Context;
import androidx.datastore.core.DataStore;
import androidx.datastore.preferences.core.Preferences;
import com.timeflow.app.data.repository.MedicationRepository;
import com.timeflow.app.service.MedicationReminderManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata({
    "dagger.hilt.android.qualifiers.ApplicationContext",
    "com.timeflow.app.di.DataStoreModule.MedicationDataStore"
})
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ProfessionalMedicationViewModel_Factory implements Factory<ProfessionalMedicationViewModel> {
  private final Provider<MedicationReminderManager> medicationReminderManagerProvider;

  private final Provider<Context> contextProvider;

  private final Provider<DataStore<Preferences>> medicationDataStoreProvider;

  private final Provider<MedicationRepository> medicationRepositoryProvider;

  public ProfessionalMedicationViewModel_Factory(
      Provider<MedicationReminderManager> medicationReminderManagerProvider,
      Provider<Context> contextProvider,
      Provider<DataStore<Preferences>> medicationDataStoreProvider,
      Provider<MedicationRepository> medicationRepositoryProvider) {
    this.medicationReminderManagerProvider = medicationReminderManagerProvider;
    this.contextProvider = contextProvider;
    this.medicationDataStoreProvider = medicationDataStoreProvider;
    this.medicationRepositoryProvider = medicationRepositoryProvider;
  }

  @Override
  public ProfessionalMedicationViewModel get() {
    return newInstance(medicationReminderManagerProvider.get(), contextProvider.get(), medicationDataStoreProvider.get(), medicationRepositoryProvider.get());
  }

  public static ProfessionalMedicationViewModel_Factory create(
      Provider<MedicationReminderManager> medicationReminderManagerProvider,
      Provider<Context> contextProvider,
      Provider<DataStore<Preferences>> medicationDataStoreProvider,
      Provider<MedicationRepository> medicationRepositoryProvider) {
    return new ProfessionalMedicationViewModel_Factory(medicationReminderManagerProvider, contextProvider, medicationDataStoreProvider, medicationRepositoryProvider);
  }

  public static ProfessionalMedicationViewModel newInstance(
      MedicationReminderManager medicationReminderManager, Context context,
      DataStore<Preferences> medicationDataStore, MedicationRepository medicationRepository) {
    return new ProfessionalMedicationViewModel(medicationReminderManager, context, medicationDataStore, medicationRepository);
  }
}
