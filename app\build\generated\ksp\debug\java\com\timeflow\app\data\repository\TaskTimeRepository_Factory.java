package com.timeflow.app.data.repository;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class TaskTimeRepository_Factory implements Factory<TaskTimeRepository> {
  @Override
  public TaskTimeRepository get() {
    return newInstance();
  }

  public static TaskTimeRepository_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static TaskTimeRepository newInstance() {
    return new TaskTimeRepository();
  }

  private static final class InstanceHolder {
    private static final TaskTimeRepository_Factory INSTANCE = new TaskTimeRepository_Factory();
  }
}
