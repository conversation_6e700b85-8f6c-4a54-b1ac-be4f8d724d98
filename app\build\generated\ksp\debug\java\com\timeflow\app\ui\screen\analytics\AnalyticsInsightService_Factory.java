package com.timeflow.app.ui.screen.analytics;

import com.timeflow.app.data.repository.GoalRepository;
import com.timeflow.app.data.repository.HabitRepository;
import com.timeflow.app.data.repository.TaskRepository;
import com.timeflow.app.data.repository.TimeSessionRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AnalyticsInsightService_Factory implements Factory<AnalyticsInsightService> {
  private final Provider<TaskRepository> taskRepositoryProvider;

  private final Provider<TimeSessionRepository> timeSessionRepositoryProvider;

  private final Provider<GoalRepository> goalRepositoryProvider;

  private final Provider<HabitRepository> habitRepositoryProvider;

  public AnalyticsInsightService_Factory(Provider<TaskRepository> taskRepositoryProvider,
      Provider<TimeSessionRepository> timeSessionRepositoryProvider,
      Provider<GoalRepository> goalRepositoryProvider,
      Provider<HabitRepository> habitRepositoryProvider) {
    this.taskRepositoryProvider = taskRepositoryProvider;
    this.timeSessionRepositoryProvider = timeSessionRepositoryProvider;
    this.goalRepositoryProvider = goalRepositoryProvider;
    this.habitRepositoryProvider = habitRepositoryProvider;
  }

  @Override
  public AnalyticsInsightService get() {
    return newInstance(taskRepositoryProvider.get(), timeSessionRepositoryProvider.get(), goalRepositoryProvider.get(), habitRepositoryProvider.get());
  }

  public static AnalyticsInsightService_Factory create(
      Provider<TaskRepository> taskRepositoryProvider,
      Provider<TimeSessionRepository> timeSessionRepositoryProvider,
      Provider<GoalRepository> goalRepositoryProvider,
      Provider<HabitRepository> habitRepositoryProvider) {
    return new AnalyticsInsightService_Factory(taskRepositoryProvider, timeSessionRepositoryProvider, goalRepositoryProvider, habitRepositoryProvider);
  }

  public static AnalyticsInsightService newInstance(TaskRepository taskRepository,
      TimeSessionRepository timeSessionRepository, GoalRepository goalRepository,
      HabitRepository habitRepository) {
    return new AnalyticsInsightService(taskRepository, timeSessionRepository, goalRepository, habitRepository);
  }
}
