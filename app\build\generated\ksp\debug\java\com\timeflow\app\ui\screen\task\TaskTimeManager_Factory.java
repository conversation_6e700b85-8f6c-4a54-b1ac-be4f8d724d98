package com.timeflow.app.ui.screen.task;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class TaskTimeManager_Factory implements Factory<TaskTimeManager> {
  @Override
  public TaskTimeManager get() {
    return newInstance();
  }

  public static TaskTimeManager_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static TaskTimeManager newInstance() {
    return new TaskTimeManager();
  }

  private static final class InstanceHolder {
    private static final TaskTimeManager_Factory INSTANCE = new TaskTimeManager_Factory();
  }
}
