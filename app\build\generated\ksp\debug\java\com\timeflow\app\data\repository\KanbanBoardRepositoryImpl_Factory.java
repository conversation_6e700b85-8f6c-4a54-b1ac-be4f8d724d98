package com.timeflow.app.data.repository;

import com.timeflow.app.data.dao.KanbanBoardDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class KanbanBoardRepositoryImpl_Factory implements Factory<KanbanBoardRepositoryImpl> {
  private final Provider<KanbanBoardDao> kanbanBoardDaoProvider;

  public KanbanBoardRepositoryImpl_Factory(Provider<KanbanBoardDao> kanbanBoardDaoProvider) {
    this.kanbanBoardDaoProvider = kanbanBoardDaoProvider;
  }

  @Override
  public KanbanBoardRepositoryImpl get() {
    return newInstance(kanbanBoardDaoProvider.get());
  }

  public static KanbanBoardRepositoryImpl_Factory create(
      Provider<KanbanBoardDao> kanbanBoardDaoProvider) {
    return new KanbanBoardRepositoryImpl_Factory(kanbanBoardDaoProvider);
  }

  public static KanbanBoardRepositoryImpl newInstance(KanbanBoardDao kanbanBoardDao) {
    return new KanbanBoardRepositoryImpl(kanbanBoardDao);
  }
}
