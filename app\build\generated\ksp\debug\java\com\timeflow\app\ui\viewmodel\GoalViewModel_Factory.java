package com.timeflow.app.ui.viewmodel;

import com.timeflow.app.data.repository.GoalRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class GoalViewModel_Factory implements Factory<GoalViewModel> {
  private final Provider<GoalRepository> goalRepositoryProvider;

  public GoalViewModel_Factory(Provider<GoalRepository> goalRepositoryProvider) {
    this.goalRepositoryProvider = goalRepositoryProvider;
  }

  @Override
  public GoalViewModel get() {
    return newInstance(goalRepositoryProvider.get());
  }

  public static GoalViewModel_Factory create(Provider<GoalRepository> goalRepositoryProvider) {
    return new GoalViewModel_Factory(goalRepositoryProvider);
  }

  public static GoalViewModel newInstance(GoalRepository goalRepository) {
    return new GoalViewModel(goalRepository);
  }
}
