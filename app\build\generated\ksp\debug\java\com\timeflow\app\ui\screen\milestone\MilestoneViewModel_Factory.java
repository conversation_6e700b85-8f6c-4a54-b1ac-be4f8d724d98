package com.timeflow.app.ui.screen.milestone;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class MilestoneViewModel_Factory implements Factory<MilestoneViewModel> {
  @Override
  public MilestoneViewModel get() {
    return newInstance();
  }

  public static MilestoneViewModel_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static MilestoneViewModel newInstance() {
    return new MilestoneViewModel();
  }

  private static final class InstanceHolder {
    private static final MilestoneViewModel_Factory INSTANCE = new MilestoneViewModel_Factory();
  }
}
