package com.timeflow.app.worker;

import android.content.Context;
import androidx.work.WorkerParameters;
import dagger.internal.DaggerGenerated;
import dagger.internal.InstanceFactory;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class RecurringTaskWorker_AssistedFactory_Impl implements RecurringTaskWorker_AssistedFactory {
  private final RecurringTaskWorker_Factory delegateFactory;

  RecurringTaskWorker_AssistedFactory_Impl(RecurringTaskWorker_Factory delegateFactory) {
    this.delegateFactory = delegateFactory;
  }

  @Override
  public RecurringTaskWorker create(Context p0, WorkerParameters p1) {
    return delegateFactory.get(p0, p1);
  }

  public static Provider<RecurringTaskWorker_AssistedFactory> create(
      RecurringTaskWorker_Factory delegateFactory) {
    return InstanceFactory.create(new RecurringTaskWorker_AssistedFactory_Impl(delegateFactory));
  }

  public static dagger.internal.Provider<RecurringTaskWorker_AssistedFactory> createFactoryProvider(
      RecurringTaskWorker_Factory delegateFactory) {
    return InstanceFactory.create(new RecurringTaskWorker_AssistedFactory_Impl(delegateFactory));
  }
}
