﻿package com.timeflow.app.ui.screen.ai

import android.content.Intent
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.*
import androidx.compose.animation.expandVertically
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.shrinkVertically
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.PressInteraction
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material.icons.outlined.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import com.timeflow.app.R
import com.timeflow.app.data.ai.model.*
import com.timeflow.app.ui.theme.*
import com.timeflow.app.ui.viewmodel.AiAssistantViewModel
import com.timeflow.app.utils.SystemBarManager
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import java.time.format.DateTimeFormatter
import androidx.compose.foundation.BorderStroke
import androidx.compose.ui.layout.Layout
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.ui.hapticfeedback.HapticFeedbackType
import androidx.compose.ui.platform.LocalHapticFeedback
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.draw.drawWithContent
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.text.TextStyle
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.material3.TopAppBar
import androidx.compose.animation.animateColorAsState
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import android.net.Uri
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.DpOffset
import android.widget.Toast
import com.timeflow.app.ui.viewmodel.AiConfigViewModel
import com.timeflow.app.ui.navigation.AppDestinations
import androidx.navigation.NavController
import androidx.navigation.NavHostController
import androidx.navigation.compose.rememberNavController
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.interaction.collectIsPressedAsState
import com.timeflow.app.ui.viewmodel.AiSettingsViewModel
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.foundation.lazy.items
import android.util.Log
import com.timeflow.app.ui.screen.task.SubTask
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.runtime.compositionLocalOf
import androidx.compose.foundation.verticalScroll
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.ime
import androidx.compose.foundation.layout.windowInsetsPadding
import android.app.Activity
import androidx.compose.foundation.layout.imePadding
import androidx.compose.material.ripple.rememberRipple
import androidx.compose.ui.draw.alpha

// 定义新的主题颜色
private val PastelPink = Color(0xFFF5F0FF)
private val PastelLavender = Color(0xFFC3B0D8)
private val DarkPurple = Color(0xFF6D6A75)
private val LightPurple = Color(0xFFA59AB0)
private val CreamWhite = Color(0xFFFBFBFB)
private val LightBackground = Color(0xFFF5F3F7)
private val DarkGray = Color(0xFF2D2A32)
private val LightTeal = Color(0xFF4DB6AC)

// 添加CompositionLocal
val LocalAiAssistantViewModel = compositionLocalOf<AiAssistantViewModel> { error("No AiAssistantViewModel provided") }

// --- MOVED HELPER FUNCTIONS START HERE ---

/**
 * 简化的欢迎卡片
 */
@Composable
fun SimpleWelcomeCard(
    onSuggestionClick: (String) -> Unit,
    onSettingsClick: () -> Unit,
    onClose: () -> Unit,
    aiSettings: AiSettings,
    onAvatarLongClick: () -> Unit,
    onEmojiClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    val suggestions = listOf(
        "帮我规划今天的任务",
        "创建一个新任务：健身30分钟",
        "提醒我明天上午9点开会",
        "推荐一个提高效率的方法"
    )
    
    val context = LocalContext.current
    val viewModel = LocalAiAssistantViewModel.current
    var showDiagnosisDialog by remember { mutableStateOf(false) }
    var diagnosisResult by remember { mutableStateOf<AiAssistantViewModel.DiagnosisResult?>(null) }
    var isDiagnosing by remember { mutableStateOf(false) }
    
    // 诊断对话框
    if (showDiagnosisDialog) {
        AlertDialog(
            onDismissRequest = { showDiagnosisDialog = false },
            title = { 
                Row(verticalAlignment = Alignment.CenterVertically) {
                    Icon(
                        imageVector = Icons.Default.HealthAndSafety,
                        contentDescription = null,
                        tint = DustyLavender
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("AI配置诊断") 
                }
            },
            text = { 
                if (isDiagnosing) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally,
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        CircularProgressIndicator(color = DustyLavender)
                        Spacer(modifier = Modifier.height(16.dp))
                        Text("正在诊断配置...")
                    }
                } else {
                    diagnosisResult?.let { result ->
                        Column {
                            // 状态图标
                            Row(
                                verticalAlignment = Alignment.CenterVertically,
                                modifier = Modifier.fillMaxWidth()
                            ) {
                                Icon(
                                    imageVector = if (result.isConfigured) 
                                        Icons.Default.CheckCircle else Icons.Default.Error,
                                    contentDescription = null,
                                    tint = if (result.isConfigured) Color.Green else Color.Red
                                )
                                Spacer(modifier = Modifier.width(8.dp))
                                Text(
                                    text = if (result.isConfigured) "配置正常" else "配置异常",
                                    fontWeight = FontWeight.Bold
                                )
                            }
                            
                            Spacer(modifier = Modifier.height(16.dp))
                            
                            // 诊断详情
                            Text(
                                text = "诊断信息:",
                                fontWeight = FontWeight.Medium
                            )
                            Spacer(modifier = Modifier.height(8.dp))
                            
                            // 可滚动的诊断详情
                            Surface(
                                shape = RoundedCornerShape(8.dp),
                                border = BorderStroke(1.dp, Color.LightGray.copy(alpha = 0.5f)),
                                modifier = Modifier.heightIn(max = 300.dp)
                            ) {
                                Column(
                                    modifier = Modifier
                                        .padding(8.dp)
                                        .verticalScroll(rememberScrollState())
                                ) {
                                    Text(
                                        text = result.details,
                                        style = MaterialTheme.typography.bodySmall,
                                        fontFamily = FontFamily.Monospace
                                    )
                                }
                            }
                            
                            if (!result.isConfigured) {
                                Spacer(modifier = Modifier.height(16.dp))
                                Button(
                                    onClick = {
                                        showDiagnosisDialog = false
                                        // 导航到AI设置页面
                                        val intent = Intent(context, Class.forName("com.timeflow.app.MainActivity"))
                                        intent.putExtra("NAVIGATE_TO", "ai_model_settings")
                                        context.startActivity(intent)
                                    },
                                    colors = ButtonDefaults.buttonColors(
                                        containerColor = DustyLavender
                                    ),
                                    modifier = Modifier.align(Alignment.End)
                                ) {
                                    Icon(
                                        imageVector = Icons.Default.Settings,
                                        contentDescription = null,
                                        modifier = Modifier.size(16.dp)
                                    )
                                    Spacer(modifier = Modifier.width(4.dp))
                                    Text("前往设置")
                                }
                            }
                        }
                    } ?: Text("未获取到诊断结果")
                }
            },
            confirmButton = {
                Button(
                    onClick = { 
                        if (isDiagnosing) {
                            // 忽略，正在诊断中
                        } else if (diagnosisResult == null) {
                            // 开始诊断
                            isDiagnosing = true
                            viewModel.diagnoseAiConfig(context) { result ->
                                diagnosisResult = result
                                isDiagnosing = false
                            }
                        } else {
                            // 关闭对话框
                            showDiagnosisDialog = false
                        }
                    },
                    colors = ButtonDefaults.buttonColors(
                        containerColor = DustyLavender
                    )
                ) {
                    if (isDiagnosing) {
                        Text("正在诊断...")
                    } else if (diagnosisResult == null) {
                        Text("开始诊断")
                    } else {
                        Text("关闭")
                    }
                }
            },
            dismissButton = {
                if (!isDiagnosing && diagnosisResult != null) {
                    OutlinedButton(
                        onClick = { 
                            // 重新诊断
                            isDiagnosing = true
                            diagnosisResult = null
                            viewModel.diagnoseAiConfig(context) { result ->
                                diagnosisResult = result
                                isDiagnosing = false
                            }
                        }
                    ) {
                        Icon(
                            imageVector = Icons.Default.Refresh,
                            contentDescription = null,
                            modifier = Modifier.size(16.dp)
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text("重新诊断")
                    }
                }
            }
        )
    }
    
    Surface(
        shape = RoundedCornerShape(20.dp),
        color = Color.White,
        tonalElevation = 0.dp,
        shadowElevation = 0.dp,
        border = BorderStroke(
            width = 1.dp,
            color = LightPurple.copy(alpha = 0.1f)
        ),
        modifier = modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            // 头部区域：欢迎消息和关闭按钮
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.Top
            ) {
                // 欢迎文本和头像
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.weight(1f)
                ) {
                    // 机器人头像 - 添加长按手势
                    Box(
                        modifier = Modifier
                            .size(48.dp)
                            .background(
                                color = PastelPink.copy(alpha = 0.3f),
                                shape = CircleShape
                            )
                            .border(
                                width = 1.dp,
                                color = PastelLavender.copy(alpha = 0.5f),
                                shape = CircleShape
                            )
                            .pointerInput(Unit) {
                                detectTapGestures(
                                    onLongPress = { onAvatarLongClick() }
                                )
                            },
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = aiSettings.assistantAvatar,
                            fontSize = 24.sp
                        )
                    }
                    
                    Spacer(modifier = Modifier.width(12.dp))
                    
                    // 欢迎文本
                    Column {
                        Text(
                            text = "你好呀！我是你的${aiSettings.assistantName}～",
                            style = MaterialTheme.typography.titleMedium.copy(
                                fontWeight = FontWeight.SemiBold
                            ),
                            color = DarkGray
                        )
                        
                        Spacer(modifier = Modifier.height(4.dp))
                        
                        Text(
                            text = "我可以帮你做这些事：",
                            style = MaterialTheme.typography.bodyMedium,
                            color = DarkGray.copy(alpha = 0.7f)
                        )
                    }
                }
                
                // 按钮栏
                Row {
                    // 添加诊断按钮
                    IconButton(
                        onClick = { 
                            showDiagnosisDialog = true
                            isDiagnosing = true
                            diagnosisResult = null
                            viewModel.diagnoseAiConfig(context) { result ->
                                diagnosisResult = result
                                isDiagnosing = false
                            }
                        },
                        modifier = Modifier.size(32.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.HealthAndSafety,
                            contentDescription = "AI配置诊断",
                            tint = DustyLavender,
                            modifier = Modifier.size(20.dp)
                        )
                    }
                    
                    // 关闭按钮
                    IconButton(
                        onClick = onClose,
                        modifier = Modifier.size(32.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Close,
                            contentDescription = "关闭",
                            tint = DarkGray.copy(alpha = 0.6f),
                            modifier = Modifier.size(20.dp)
                        )
                    }
                }
            }
            
            // 添加分隔线
            Spacer(modifier = Modifier.height(16.dp))
            Divider(
                color = LightPurple.copy(alpha = 0.1f),
                thickness = 1.dp,
                modifier = Modifier.padding(bottom = 16.dp)
            )
            
            // 建议列表
            Text(
                text = "✨ 尝试",
                style = MaterialTheme.typography.titleSmall.copy(
                    fontWeight = FontWeight.SemiBold,
                    letterSpacing = 0.4.sp
                ),
                color = DarkPurple,
                modifier = Modifier.padding(bottom = 12.dp)
            )
            
            // 建议内容区域
            Column(
                modifier = Modifier.padding(top = 16.dp)
            ) {
                suggestions.forEach { suggestion ->
                    SuggestionItem(
                        suggestion = suggestion,
                        onClick = { onSuggestionClick(suggestion) },
                        promptEmoji = aiSettings.promptEmoji,
                        onEmojiClick = onEmojiClick
                    )
                    
                    Spacer(modifier = Modifier.height(8.dp))
                }
            }
            
            // 底部提示文本
            Text(
                text = "点击任意建议开始对话",
                style = MaterialTheme.typography.bodySmall.copy(
                    fontSize = 11.sp,
                    color = DarkGray.copy(alpha = 0.5f)
                ),
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 16.dp),
                textAlign = TextAlign.Center
            )
        }
    }
}

/**
 * 简化的消息项
 */
@Composable
fun SimpleMessageItem(
    message: ConversationMessage,
    isUserMessage: Boolean,
    modifier: Modifier = Modifier
) {
    val aiSettingsViewModel: AiSettingsViewModel = hiltViewModel()
    val aiSettings by aiSettingsViewModel.aiSettings.collectAsState()
    
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = if (isUserMessage) Arrangement.End else Arrangement.Start
    ) {
        if (!isUserMessage) {
            // AI头像
            Box(
                modifier = Modifier
                    .padding(end = 8.dp, top = 4.dp)
                    .size(36.dp)
                    .background(
                        color = Color.White,
                        shape = CircleShape
                    )
                    .border(
                        width = 1.dp,
                        color = LightPurple.copy(alpha = 0.3f),
                        shape = CircleShape
                    ),
                contentAlignment = Alignment.Center
            ) {
                Text(text = aiSettings.assistantAvatar, fontSize = 18.sp)
            }
        }
        
        // 消息气泡
                    val bubbleShape = when {
                        isUserMessage -> RoundedCornerShape(
                            topStart = 16.dp,
                            topEnd = 4.dp,
                            bottomStart = 16.dp,
                            bottomEnd = 16.dp
                        )
                        else -> RoundedCornerShape(
                            topStart = 4.dp,
                            topEnd = 16.dp,
                            bottomStart = 16.dp,
                            bottomEnd = 16.dp
                        )
                    }
                    
                    Box(
                        modifier = Modifier
                            .shadow(
                                elevation = if (isUserMessage) 0.dp else 2.dp,
                                shape = bubbleShape,
                                spotColor = LightPurple.copy(alpha = 0.1f)
                            )
                            .clip(bubbleShape)
                            .background(
                                if (isUserMessage) 
                                    Color.White
                                else 
                                    PastelPink
                            )
                            .border(
                                width = if (isUserMessage) 1.dp else 0.dp,
                                color = LightPurple.copy(alpha = 0.15f),
                                shape = bubbleShape
                            )
                            .padding(horizontal = 16.dp, vertical = 12.dp)
                    ) {
                        // 格式化消息内容，特别是AI回复
                        val formattedContent = if (isUserMessage) {
                            message.content
                        } else {
                            com.timeflow.app.util.MarkdownFormatter.formatMarkdownText(message.content)
                        }
                        
                        Text(
                            text = formattedContent,
                            color = DarkGray,
                            style = MaterialTheme.typography.bodyMedium,
                            lineHeight = 20.sp
            )
        }
        
        if (isUserMessage) {
            // 用户头像
            Box(
                modifier = Modifier
                    .padding(start = 8.dp, top = 4.dp)
                    .size(36.dp)
                    .background(
                        color = Color.White,
                        shape = CircleShape
                    )
                    .border(
                        width = 1.dp,
                        color = LightPurple.copy(alpha = 0.3f),
                        shape = CircleShape
                    ),
                contentAlignment = Alignment.Center
            ) {
                Text(text = "😊", fontSize = 18.sp)
            }
        }
    }
}

/**
 * 简化的输入指示器
 */
@Composable
fun SimpleTypingIndicator() {
    val aiSettingsViewModel: AiSettingsViewModel = hiltViewModel()
    val aiSettings by aiSettingsViewModel.aiSettings.collectAsState()
    
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        horizontalArrangement = Arrangement.Start
    ) {
        // AI头像
        Box(
            modifier = Modifier
                .padding(end = 8.dp)
                .size(36.dp)
                .background(
                    color = Color.White,
                    shape = CircleShape
                )
                .border(
                    width = 1.dp,
                    color = LightPurple.copy(alpha = 0.3f),
                    shape = CircleShape
                ),
            contentAlignment = Alignment.Center
        ) {
            Text(text = aiSettings.assistantAvatar, fontSize = 18.sp)
        }
        
        // 加载动画
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier
                .clip(RoundedCornerShape(16.dp))
                .background(PastelPink)
                .padding(horizontal = 16.dp, vertical = 12.dp)
            ) {
                Text(
                text = "正在思考",
                style = MaterialTheme.typography.bodyMedium,
                color = DarkGray.copy(alpha = 0.7f)
            )
            
            Spacer(modifier = Modifier.width(8.dp))
            
            // 自定义三点加载动画
            ThreeDotsLoadingAnimation(
                dotColor = DustyLavender,
                dotSize = 6.dp,
                spaceBetween = 4.dp
            )
        }
    }
}

/**
 * 三点加载动画组件
 */
@Composable
fun ThreeDotsLoadingAnimation(
    dotColor: Color,
    dotSize: Dp = 6.dp,
    spaceBetween: Dp = 4.dp,
    animationDuration: Int = 1200
) {
    val dots = 3
    // 创建点的动画值列表
    val animationValues = List(dots) { index ->
        // 为每个点设置不同的动画延迟，创建波浪效果
        val delay = index * (animationDuration / dots) / 2
        
        // 使用无限重复动画
        val infiniteTransition = rememberInfiniteTransition(label = "dot$index")
        val animatedValue by infiniteTransition.animateFloat(
            initialValue = 0f,
            targetValue = 1f, 
            animationSpec = infiniteRepeatable(
                animation = keyframes {
                    durationMillis = animationDuration
                    0f at delay with LinearEasing
                    1f at delay + animationDuration / 3 with LinearEasing
                    0f at delay + (animationDuration / 3) * 2 with LinearEasing
                    0f at animationDuration
                },
                repeatMode = RepeatMode.Restart
            ),
            label = "dotValue$index"
        )
        animatedValue
    }
    
    Row(
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(spaceBetween)
    ) {
        animationValues.forEach { animValue ->
            Box(
                modifier = Modifier
                    .size(dotSize)
                    .scale(1f + animValue * 0.5f) // 缩放效果
                    .alpha(0.5f + animValue * 0.5f) // 透明度变化
                    .background(
                        color = dotColor,
                        shape = CircleShape
                    )
            )
        }
    }
}

/**
 * 简化的错误提示
 */
@Composable
fun SimpleErrorSnackbar(
    errorMessage: String,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier
) {
    Surface(
        modifier = modifier
            .padding(16.dp)
            .fillMaxWidth(),
        shape = RoundedCornerShape(8.dp),
        color = Color(0xFFFFEBEE),
        border = BorderStroke(
            width = 1.dp,
            color = Color(0xFFEF9A9A)
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = errorMessage,
                style = MaterialTheme.typography.bodyMedium,
                color = Color(0xFFB71C1C),
                modifier = Modifier.weight(1f)
            )
            
            IconButton(
                onClick = onDismiss,
                modifier = Modifier.size(24.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.Close,
                    contentDescription = "关闭",
                    tint = Color(0xFFB71C1C)
                )
            }
        }
    }
}

@Composable
private fun SuggestionItem(
    suggestion: String,
    onClick: () -> Unit,
    promptEmoji: String,
    onEmojiClick: () -> Unit
) {
    Card(
        onClick = onClick,
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        ),
        border = BorderStroke(
            width = 1.dp,
            color = Color.LightGray.copy(alpha = 0.3f)
        ),
        modifier = Modifier.fillMaxWidth()
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 12.dp, vertical = 10.dp)
) {
    Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.fillMaxWidth()
            ) {
                // 使用自定义emoji，并添加点击处理
        Box(
            modifier = Modifier
                        .size(32.dp)
                .background(
                            color = PastelPink.copy(alpha = 0.5f),
                    shape = CircleShape
                )
                        .clickable(onClick = onEmojiClick),
            contentAlignment = Alignment.Center
        ) {
                    Text(
                        text = promptEmoji,
                        fontSize = 16.sp
                    )
                }
                
                Spacer(modifier = Modifier.width(12.dp))
                
                // 文本部分
                Text(
                    text = suggestion,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    style = MaterialTheme.typography.bodyMedium.copy(
                        fontWeight = FontWeight.Medium,
                        letterSpacing = 0.2.sp
                    ),
                    color = DarkGray,
                    modifier = Modifier.weight(1f)
                )
                
                // 右侧箭头
                Icon(
                    imageVector = Icons.Default.KeyboardArrowRight,
                    contentDescription = null,
                    tint = LightPurple,
                    modifier = Modifier.size(16.dp)
                )
            }
        }
    }
}

/**
 * 任务优化请求卡片
 */
@Composable
private fun OptimizationRequestCard(
    message: ConversationMessage,
    assistantAvatar: String
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 4.dp),
        horizontalArrangement = if (message.sender == MessageSender.USER) 
            Arrangement.End else Arrangement.Start
    ) {
        if (message.sender == MessageSender.AI) {
            // AI头像
        Box(
            modifier = Modifier
                    .size(36.dp)
                    .clip(CircleShape)
                    .background(LightPurple.copy(alpha = 0.3f))
                    .padding(4.dp),
            contentAlignment = Alignment.Center
        ) {
                Text(
                    text = assistantAvatar,
                    fontSize = 18.sp
                )
            }
            Spacer(modifier = Modifier.width(8.dp))
        }
        
        Column(
            modifier = Modifier.weight(0.85f)
        ) {
            // 任务名称标题
            message.relatedTaskTitle?.let { title ->
                Surface(
                    color = DustyLavender.copy(alpha = 0.1f),
                    shape = RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp),
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Row(
                modifier = Modifier
                            .fillMaxWidth()
                            .padding(8.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Default.Task,
                            contentDescription = null,
                            tint = DustyLavender,
                            modifier = Modifier.size(16.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                Text(
                            text = "优化任务：$title",
                            style = MaterialTheme.typography.labelMedium,
                            fontWeight = FontWeight.Medium,
                            color = DustyLavender
                        )
                    }
                }
            }
            
            // 消息内容
            Surface(
                color = if (message.sender == MessageSender.USER) 
                    Color(0xFFE3F2FD) else Color.White,
                shape = RoundedCornerShape(
                    topStart = if (message.sender == MessageSender.USER) 16.dp else 0.dp,
                    topEnd = if (message.sender == MessageSender.USER) 0.dp else 16.dp,
                    bottomStart = 16.dp,
                    bottomEnd = 16.dp
                ),
                shadowElevation = 1.dp
            ) {
        Text(
                    text = message.content,
                    style = MaterialTheme.typography.bodyMedium,
                    color = Color.DarkGray,
                    modifier = Modifier.padding(12.dp)
                )
            }
            
            // 时间戳
        Text(
                text = message.timestamp.format(DateTimeFormatter.ofPattern("HH:mm")),
                style = MaterialTheme.typography.labelSmall,
                color = Color.Gray,
                modifier = Modifier.padding(start = 4.dp, top = 2.dp)
            )
        }
        
        if (message.sender == MessageSender.USER) {
            Spacer(modifier = Modifier.width(8.dp))
            // 用户头像
            Box(
                modifier = Modifier
                    .size(36.dp)
                    .clip(CircleShape)
                    .background(Color(0xFFE3F2FD))
                    .padding(4.dp),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = Icons.Default.Person,
                    contentDescription = null,
                    tint = Color(0xFF2196F3),
                    modifier = Modifier.size(24.dp)
                )
            }
        }
    }
}

/**
 * 子任务结果卡片
 */
@Composable
private fun SubtasksResultCard(
    message: ConversationMessage,
    assistantAvatar: String,
    navController: NavController
) {
    // 在Composable函数级别获取ViewModel引用
    val viewModel = LocalAiAssistantViewModel.current
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()
    val uiState by viewModel.uiState.collectAsState()
    
    // 处理错误信息 - 监听uiState的错误变化
    LaunchedEffect(uiState.error) {
        uiState.error?.let { errorMessage ->
            // 显示错误信息Toast
            Toast.makeText(
                context,
                errorMessage,
                Toast.LENGTH_SHORT
            ).show()
            
            // 清除错误状态
            viewModel.clearError()
        }
    }
    
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 4.dp),
        horizontalArrangement = Arrangement.Start
    ) {
        // AI头像
        Box(
            modifier = Modifier
                .size(36.dp)
                .clip(CircleShape)
                .background(LightPurple.copy(alpha = 0.3f))
                .padding(4.dp),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = assistantAvatar,
                fontSize = 18.sp
            )
        }
        Spacer(modifier = Modifier.width(8.dp))
        
        Column(
            modifier = Modifier.weight(0.85f)
        ) {
            // 任务名称标题
            if (message.relatedTaskTitle != null) {
                val title = message.relatedTaskTitle
                Surface(
                    color = DustyLavender.copy(alpha = 0.1f),
                    shape = RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp),
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 12.dp, vertical = 8.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Default.CheckCircle,
                            contentDescription = null,
                            tint = DustyLavender,
                            modifier = Modifier.size(16.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = "已拆分任务：$title",
                            style = MaterialTheme.typography.labelMedium,
                            fontWeight = FontWeight.Medium,
                            color = DustyLavender
                        )
                    }
                }
            }
            
            // 子任务内容
            Surface(
                color = Color.White,
                shape = if (message.relatedTaskTitle != null) 
                    RoundedCornerShape(bottomStart = 16.dp, bottomEnd = 16.dp)
                else
                    RoundedCornerShape(16.dp),
                shadowElevation = 1.dp
            ) {
                Column(
                    modifier = Modifier.padding(12.dp)
                ) {
                    // 分割消息内容为行
                    val lines = message.content.split("\n")
                    val subtaskLines = lines.filter { it.matches(Regex("^\\d+\\. .*")) }
                    
                    Text(
                        text = "子任务列表：",
                        style = MaterialTheme.typography.bodyMedium,
                        fontWeight = FontWeight.Medium,
                        color = Color.DarkGray
                    )
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    // 显示子任务列表
                    subtaskLines.forEach { line ->
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(vertical = 4.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Box(
                modifier = Modifier
                                    .size(24.dp)
                    .background(
                                        DustyLavender.copy(alpha = 0.1f),
                                        CircleShape
                    ),
                contentAlignment = Alignment.Center
            ) {
                                val taskNumber = line.substringBefore(".").trim()
                Text(
                                    text = taskNumber,
                                    style = MaterialTheme.typography.labelMedium,
                                    color = DustyLavender
                )
            }
            
                            Spacer(modifier = Modifier.width(8.dp))
            
                Text(
                                text = line.substringAfter(". "),
                                style = MaterialTheme.typography.bodyMedium,
                                color = Color.DarkGray,
                                modifier = Modifier.weight(1f)
                            )
                        }
                    }
                    
                    // 显示其余内容
                    val otherLines = lines.filterNot { it.matches(Regex("^\\d+\\. .*")) }
                        .filter { it.isNotEmpty() }
                    
                    if (otherLines.isNotEmpty()) {
                        Spacer(modifier = Modifier.height(12.dp))
                        Divider(color = Color.LightGray.copy(alpha = 0.5f))
                        Spacer(modifier = Modifier.height(12.dp))
                
                Text(
                            text = otherLines.joinToString("\n"),
                            style = MaterialTheme.typography.bodyMedium,
                            color = Color.DarkGray
                        )
                    }
                    
                    Spacer(modifier = Modifier.height(12.dp))
                    
                    // 修改按钮组布局
                    message.relatedTaskId?.let { taskId ->  // 添加let作用域
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(top = 12.dp),
                            horizontalArrangement = Arrangement.spacedBy(8.dp) // 减小按钮间距
                        ) {
                            // 添加子任务按钮
                            Button(
                                onClick = {
                                    // 处理点击事件，用协程处理可能的异常
                                    coroutineScope.launch {
                                        try {
                                            // 立即禁用按钮，防止重复点击
                                            viewModel.setProcessing(true)
                                            
                                            // 先检查任务是否存在
                                            val task = viewModel.checkTaskExists(taskId)
                                            if (task == null) {
                                                Toast.makeText(
                                                    context,
                                                    "找不到任务，无法添加子任务",
                                                    Toast.LENGTH_SHORT
                                                ).show()
                                                viewModel.setProcessing(false)
                                            } else {
                                                // 提取消息内容中的子任务
                                                val subtaskTitles = extractSubtasksFromMessage(message.content)
                                                if (subtaskTitles.isEmpty()) {
                                                    Toast.makeText(
                                                        context,
                                                        "未找到有效的子任务内容",
                                                        Toast.LENGTH_SHORT
                                                    ).show()
                                                    viewModel.setProcessing(false)
                                                } else {
                                                    // 添加子任务到父任务
                                                    Toast.makeText(
                                                        context,
                                                        "正在添加子任务到「${task.title}」...",
                                                        Toast.LENGTH_SHORT
                                                    ).show()
                                                    
                                                    // 调用ViewModel方法添加子任务
                                                    viewModel.addSubTasksToParent(taskId, subtaskTitles) { success ->
                                                        coroutineScope.launch {
                                                            try {
                                                                // 无论成功失败，延迟后都返回任务详情页
                                                                delay(2000) // 延长延迟，确保后台操作有时间完成
                                                                
                                                                // 添加成功，导航回任务详情页
                                                                Toast.makeText(
                                                                    context,
                                                                    "正在返回任务详情页...",
                                                                    Toast.LENGTH_SHORT
                                                                ).show()
                                                                
                                                                // 强制发送另一个刷新事件，确保任务详情页能够捕获这个变化
                                                                com.timeflow.app.util.NotificationCenter.post(com.timeflow.app.util.TaskRefreshEvent(taskId))
                                                                Log.d("AiAssistantScreen", "强制发送额外的刷新事件: $taskId")
                                                                
                                                                // 导航到任务详情页并清除当前页面
                                                                navController.navigate(AppDestinations.taskDetailRoute(taskId)) {
                                                                    // 清除最近的后退栈，确保不会回到AI助手页面
                                                                    popUpTo(navController.graph.startDestinationId)
                                                                    launchSingleTop = true // 避免创建多个实例
                                                                }
                                                            } catch (e: Exception) {
                                                                Log.e("AiAssistantScreen", "导航失败", e)
                                                                // 尝试简单导航
                                                                try {
                                                                    navController.popBackStack()
                                                                } catch (e2: Exception) {
                                                                    Log.e("AiAssistantScreen", "回退导航也失败", e2)
                                                                }
                                                                // 重新启用按钮
                                                                viewModel.setProcessing(false)
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        } catch (e: Exception) {
                                            // 异常处理
                                            Toast.makeText(
                                                context,
                                                "添加子任务失败: ${e.localizedMessage}",
                                                Toast.LENGTH_SHORT
                                            ).show()
                                            Log.e("AiAssistantScreen", "添加子任务时发生异常", e)
                                            // 重新启用按钮
                                            viewModel.setProcessing(false)
                                            
                                            // 发生异常也尝试导航回去
                                            delay(1000)
                                            try {
                                                navController.popBackStack()
                                            } catch (e2: Exception) {
                                                Log.e("AiAssistantScreen", "错误处理中的导航失败", e2)
                                            }
                                        }
                                    }
                                },
                                modifier = Modifier
                                    .weight(1f) // 平均分配空间
                                    .height(36.dp), // 减小按钮高度
                                shape = RoundedCornerShape(8.dp),
                                colors = ButtonDefaults.buttonColors(
                                    containerColor = LightTeal
                                ),
                                contentPadding = PaddingValues(horizontal = 12.dp), // 减小内边距
                                // 禁用按钮如果正在处理中
                                enabled = !uiState.isProcessing
                            ) {
                                Row(
                                    horizontalArrangement = Arrangement.Center,
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    Icon(
                                        imageVector = Icons.Default.AddTask,
                                        contentDescription = null,
                                        modifier = Modifier.size(14.dp) // 减小图标大小
                                    )
                                    Spacer(modifier = Modifier.width(4.dp))
                                    Text(
                                        text = if (uiState.isProcessing) "添加中..." else "添加子任务",
                                        fontSize = 13.sp // 减小文字大小
                                    )
                                }
                            }
                            
                            // 查看任务按钮
                            Button(
                                onClick = {
                                    // 导航到任务详情页
                                    navController.navigate(AppDestinations.taskDetailRoute(taskId))
                                },
                                modifier = Modifier
                                    .weight(1f) // 平均分配空间
                                    .height(36.dp), // 减小按钮高度
                                shape = RoundedCornerShape(8.dp),
                                colors = ButtonDefaults.buttonColors(
                                    containerColor = DustyLavender
                                ),
                                contentPadding = PaddingValues(horizontal = 12.dp) // 减小内边距
                            ) {
                                Row(
                                    horizontalArrangement = Arrangement.Center,
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    Icon(
                                        imageVector = Icons.Default.TaskAlt,
                                        contentDescription = null,
                                        modifier = Modifier.size(14.dp) // 减小图标大小
                                    )
                                    Spacer(modifier = Modifier.width(4.dp))
                                    Text(
                                        text = "查看任务",
                                        fontSize = 13.sp // 减小文字大小
                                    )
                                }
                            }
                        }
                    }
                }
            }
            
            // 时间戳
            Text(
                text = message.timestamp.format(DateTimeFormatter.ofPattern("HH:mm")),
                style = MaterialTheme.typography.labelSmall,
                color = Color.Gray,
                modifier = Modifier.padding(start = 4.dp, top = 2.dp)
            )
        }
    }
}

/**
 * 构建错误消息样式，改进404错误的显示
 */
@Composable
fun ErrorMessage(message: ConversationMessage) {
    val isApiError = message.content.contains("API错误") || 
                     message.content.contains("404") ||
                     message.content.contains("连接失败")
    
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 8.dp)
            .clip(RoundedCornerShape(12.dp))
            .background(
                if (isApiError) Color(0xFFFDE8E8) else Color(0xFFFFF3E0)
            )
            .padding(16.dp)
    ) {
        Text(
            text = if (isApiError) "AI服务连接问题" else "错误提示",
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Bold,
            color = if (isApiError) Color(0xFFB71C1C) else Color(0xFFE65100)
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        val displayMessage = if (isApiError && message.content.contains("404")) {
            val parts = message.content.split("：")
            if (parts.size > 1) {
                "AI服务无法连接 (404)。请检查：\n\n" +
                "1. 网络连接是否正常\n" +
                "2. API设置中的服务器地址是否正确\n" +
                "3. 稍后再试或联系支持团队"
            } else {
                message.content
            }
        } else {
            message.content
        }
        
        Text(
            text = displayMessage,
            style = MaterialTheme.typography.bodyMedium
        )
        
        if (isApiError) {
            Spacer(modifier = Modifier.height(12.dp))
            
            Row(verticalAlignment = Alignment.CenterVertically) {
                Icon(
                    imageVector = Icons.Default.Info,
                    contentDescription = null,
                    tint = Color(0xFFB71C1C),
                    modifier = Modifier.size(16.dp)
                )
                
                Spacer(modifier = Modifier.width(8.dp))
                
                Text(
                    text = "点击设置图标进入AI设置检查配置",
                    style = MaterialTheme.typography.labelSmall,
                    fontWeight = FontWeight.Medium,
                    color = Color(0xFF880E4F)
                )
            }
        }
    }
}

/**
 * 消息气泡组件
 */
@Composable
private fun MessageBubble(
    message: ConversationMessage,
    assistantAvatar: String,
    onLongClick: () -> Unit
) {
    val isUserMessage = message.sender == MessageSender.USER
    
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp, horizontal = 8.dp)
            .clickable(
                interactionSource = remember { MutableInteractionSource() },
                indication = null
            ) { /* 点击不执行操作 */ }
            .pointerInput(Unit) {
                detectTapGestures(
                    onLongPress = { onLongClick() }
                )
            },
        horizontalArrangement = if (isUserMessage) Arrangement.End else Arrangement.Start
    ) {
        if (message.messageType == MessageType.ERROR) {
            // 使用新的错误消息组件显示
            ErrorMessage(message)
        } else {
            // 正常消息显示逻辑
            if (!isUserMessage) {
                // AI头像
                Box(
                    modifier = Modifier
                        .padding(end = 8.dp, top = 4.dp)
                        .size(36.dp)
                        .background(
                            color = Color.White,
                            shape = CircleShape
                        )
                        .border(
                            width = 1.dp,
                            color = LightPurple.copy(alpha = 0.3f),
                            shape = CircleShape
                        ),
                    contentAlignment = Alignment.Center
                ) {
                    Text(text = assistantAvatar, fontSize = 18.sp)
                }
            }
            
            // 消息气泡
            val bubbleShape = when {
                isUserMessage -> RoundedCornerShape(
                    topStart = 16.dp,
                    topEnd = 4.dp,
                    bottomStart = 16.dp,
                    bottomEnd = 16.dp
                )
                else -> RoundedCornerShape(
                    topStart = 4.dp,
                    topEnd = 16.dp,
                    bottomStart = 16.dp,
                    bottomEnd = 16.dp
                )
            }
            
            Box(
                modifier = Modifier
                    .shadow(
                        elevation = if (isUserMessage) 0.dp else 2.dp,
                        shape = bubbleShape,
                        spotColor = LightPurple.copy(alpha = 0.1f)
                    )
                    .clip(bubbleShape)
                    .background(
                        if (isUserMessage) 
                            Color.White
                        else 
                            PastelPink
                    )
                    .border(
                        width = if (isUserMessage) 1.dp else 0.dp,
                        color = LightPurple.copy(alpha = 0.15f),
                        shape = bubbleShape
                    )
                    .padding(horizontal = 16.dp, vertical = 12.dp)
            ) {
                // 格式化消息内容，特别是AI回复
                val formattedContent = if (isUserMessage) {
                    message.content
                } else {
                    com.timeflow.app.util.MarkdownFormatter.formatMarkdownText(message.content)
                }
                
                // 如果是AI消息且包含多个段落，使用分段显示
                if (!isUserMessage && formattedContent.contains("\n\n")) {
                    val paragraphs = com.timeflow.app.util.MarkdownFormatter.smartParagraphSplit(formattedContent)
                    
                    Column(
                        verticalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        paragraphs.forEach { paragraph ->
                            Text(
                                text = paragraph,
                                color = DarkGray,
                                style = MaterialTheme.typography.bodyMedium,
                                lineHeight = 20.sp
                            )
                        }
                    }
                } else {
                    Text(
                        text = formattedContent,
                        color = DarkGray,
                        style = MaterialTheme.typography.bodyMedium,
                        lineHeight = 20.sp
                    )
                }
            }
            
            if (isUserMessage) {
                // 用户头像
                Box(
                    modifier = Modifier
                        .padding(start = 8.dp, top = 4.dp)
                        .size(36.dp)
                        .background(
                            color = Color.White,
                            shape = CircleShape
                        )
                        .border(
                            width = 1.dp,
                            color = LightPurple.copy(alpha = 0.3f),
                            shape = CircleShape
                        ),
                    contentAlignment = Alignment.Center
                ) {
                    Text(text = "😊", fontSize = 18.sp)
                }
            }
        }
    }
}

/**
 * 从AI消息中提取子任务列表
 */
private fun extractSubtasksFromMessage(messageContent: String): List<String> {
    val subtasks = mutableListOf<String>()
    val lines = messageContent.split("\n")
    
    // 查找包含编号（如"1. "）的行，这些通常是子任务
    val taskPattern = """^\s*(\d+)\.\s+(.+)$""".toRegex()
    
    for (line in lines) {
        val match = taskPattern.find(line)
        match?.groupValues?.getOrNull(2)?.let { taskTitle ->
            if (taskTitle.isNotBlank()) {
                subtasks.add(taskTitle.trim())
            }
        }
    }
    
    return subtasks
}

/**
 * AI助手屏幕 - 提供智能任务拆分和自然语言交互
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AiAssistantScreen(
    onBackClick: () -> Unit,
    viewModel: AiAssistantViewModel = hiltViewModel(),
    aiSettingsViewModel: AiSettingsViewModel = hiltViewModel(),
    navController: NavController,
    taskId: String? = null
) {
    val uiState by viewModel.uiState.collectAsState()
    val messages by viewModel.messages.collectAsState()
    val aiSettings by aiSettingsViewModel.aiSettings.collectAsState()
    
    // 提供CompositionLocal
    CompositionLocalProvider(LocalAiAssistantViewModel provides viewModel) {
        val context = LocalContext.current
    
    val listState = rememberLazyListState()
    val scope = rememberCoroutineScope()
    val focusRequester = remember { FocusRequester() }
    val focusManager = LocalFocusManager.current
    val hapticFeedback = LocalHapticFeedback.current
    val keyboardController = LocalSoftwareKeyboardController.current
    
    var inputText by remember { mutableStateOf("") }
        var showWelcomeCard by remember { mutableStateOf(messages.isEmpty()) }
        
        // 添加对话框状态
        var showEmojiDialog by remember { mutableStateOf(false) }
        var showNameDialog by remember { mutableStateOf(false) }
        var showAvatarDialog by remember { mutableStateOf(false) }
        var showClearConfirmDialog by remember { mutableStateOf(false) }
        var tempName by remember { mutableStateOf(aiSettings.assistantName) }
        
        // 处理taskId，如果有taskId，自动发送优化该任务的提示
        LaunchedEffect(taskId) {
            taskId?.let { id ->
                // 如果有taskId，查询任务并生成初始消息
                viewModel.loadTaskAndGeneratePrompt(id)
            }
        }

        // 配置系统栏，确保状态栏不与顶部标题重叠
        DisposableEffect(Unit) {
            val activity = context as? Activity
            activity?.let {
                SystemBarManager.setupLightModeSystemBars(it)
            }

            onDispose {
                activity?.let {
                    SystemBarManager.resetActivitySystemBars(it)
                }
            }
        }
    
        // 自动滚动到最新消息
        LaunchedEffect(messages.size) {
            if (messages.isNotEmpty()) {
                delay(100)
                listState.animateScrollToItem(messages.size - 1)
            } else {
                // If messages are cleared, show welcome card again
                showWelcomeCard = true
            }
        }
        
        // 监听键盘状态，当键盘弹出时自动滚动到最新消息
        val keyboardVisible = WindowInsets.ime.asPaddingValues().calculateBottomPadding() > 0.dp
        LaunchedEffect(keyboardVisible) {
            if (keyboardVisible && messages.isNotEmpty()) {
                delay(100)
                listState.animateScrollToItem(messages.size - 1)
            }
        }
        
        // 加载AI设置
        LaunchedEffect(Unit) {
            aiSettingsViewModel.loadAiSettings(context)
        }
        
        // 更新tempName当aiSettings变化时
        LaunchedEffect(aiSettings) {
            tempName = aiSettings.assistantName
        }
        
        // AI名称修改对话框
        if (showNameDialog) {
            AlertDialog(
                onDismissRequest = { showNameDialog = false },
                title = { Text(stringResource(R.string.set_ai_assistant_name)) },
                text = {
                    TextField(
                        value = tempName,
                        onValueChange = { tempName = it },
                        placeholder = { Text(stringResource(R.string.enter_ai_assistant_name)) },
                        keyboardOptions = KeyboardOptions(imeAction = ImeAction.Done),
                        colors = TextFieldDefaults.colors(
                            focusedContainerColor = Color.Transparent,
                            unfocusedContainerColor = Color.Transparent
                        )
                    )
                },
                confirmButton = {
                    TextButton(onClick = {
                        if (tempName.isNotBlank()) {
                            aiSettingsViewModel.updateAssistantName(context, tempName)
                            hapticFeedback.performHapticFeedback(HapticFeedbackType.TextHandleMove)
                        }
                        showNameDialog = false
                    }) {
                        Text(stringResource(R.string.confirm))
                    }
                },
                dismissButton = {
                    TextButton(onClick = { showNameDialog = false }) {
                        Text(stringResource(R.string.cancel))
                    }
                }
            )
        }
        
        // Emoji选择对话框
        if (showEmojiDialog) {
            val emojis = listOf("✨", "🌟", "💫", "⭐", "🚀", "🌈", "🔮", "💡", "🎯", "🎨", "🎭", "🎬")
            
            AlertDialog(
                onDismissRequest = { showEmojiDialog = false },
                title = { Text("选择提示Emoji") },
                text = {
                    LazyRow(
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        items(items = emojis) { emoji ->
                Box(
                    modifier = Modifier
                                    .size(48.dp)
                                    .background(
                                        color = if (aiSettings.promptEmoji == emoji) 
                                            LightPurple.copy(alpha = 0.2f) 
                                        else 
                                            Color.Transparent,
                                        shape = CircleShape
                                    )
                                    .border(
                                        width = 1.dp,
                                        color = if (aiSettings.promptEmoji == emoji) 
                                            DustyLavender 
                                        else 
                                            Color.LightGray.copy(alpha = 0.3f),
                                        shape = CircleShape
                                    )
                                    .clickable {
                                        aiSettingsViewModel.updatePromptEmoji(context, emoji)
                                        hapticFeedback.performHapticFeedback(HapticFeedbackType.TextHandleMove)
                                        showEmojiDialog = false
                                    },
                                contentAlignment = Alignment.Center
                            ) {
                                Text(
                                    text = emoji,
                                    fontSize = 24.sp
                                )
                            }
                        }
                    }
                },
                confirmButton = {
                    TextButton(onClick = { showEmojiDialog = false }) {
                        Text("关闭")
                    }
                }
            )
        }
        
        // 头像选择对话框
        if (showAvatarDialog) {
            val avatars = listOf("🤖", "🧠", "👾", "👨‍💻", "👩‍💻", "🦾", "🤓", "😎", "🧙‍♂️", "🧚‍♀️", "🦄", "🐼", "🦊")
            
            AlertDialog(
                onDismissRequest = { showAvatarDialog = false },
                title = { Text("选择AI助手头像") },
                text = {
                    LazyRow(
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        items(items = avatars) { avatar ->
                        Box(
                            modifier = Modifier
                                    .size(48.dp)
                                .background(
                                        color = if (aiSettings.assistantAvatar == avatar) 
                                            LightPurple.copy(alpha = 0.2f) 
                                        else 
                                            Color.Transparent,
                                    shape = CircleShape
                                )
                                .border(
                                    width = 1.dp,
                                        color = if (aiSettings.assistantAvatar == avatar) 
                                            DustyLavender 
                                        else 
                                            Color.LightGray.copy(alpha = 0.3f),
                                    shape = CircleShape
                                    )
                                    .clickable {
                                        aiSettingsViewModel.updateAssistantAvatar(context, avatar)
                                        hapticFeedback.performHapticFeedback(HapticFeedbackType.TextHandleMove)
                                        showAvatarDialog = false
                                    },
                            contentAlignment = Alignment.Center
                        ) {
                            Text(
                                    text = avatar,
                                    fontSize = 24.sp
                                )
                            }
                        }
                    }
                },
                confirmButton = {
                    TextButton(onClick = { showAvatarDialog = false }) {
                        Text("关闭")
                    }
                }
            )
        }
        
        // 添加确认清空对话的对话框
        if (showClearConfirmDialog) {
            AlertDialog(
                onDismissRequest = { showClearConfirmDialog = false },
                title = { Text("确认清空") },
                text = { Text("确定要清空所有对话内容吗？此操作无法恢复。") },
                confirmButton = {
                    TextButton(
                        onClick = {
                            // 清空对话
                            viewModel.clearConversation()
                            // 显示欢迎卡片
                            showWelcomeCard = true
                            // 显示通知
                            Toast.makeText(context, "对话已清空", Toast.LENGTH_SHORT).show()
                            showClearConfirmDialog = false
                        }
                    ) {
                        Text("确认清空")
                    }
                },
                dismissButton = {
                    TextButton(onClick = { showClearConfirmDialog = false }) {
                        Text("取消")
                    }
                }
            )
        }
        
        // 使用实际的状态栏高度，而不是固定值
        val statusBarPadding = WindowInsets.statusBars.asPaddingValues().calculateTopPadding()
        
        Scaffold(
            containerColor = LightBackground,
            contentWindowInsets = WindowInsets(0, 0, 0, 0),
            topBar = {
                Surface(
                    color = Color.White,
                    shadowElevation = 0.dp,
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Column(modifier = Modifier.fillMaxWidth()) {
                        // 保持足够的状态栏空间以避免系统状态栏遮挡
                        Spacer(modifier = Modifier.height(statusBarPadding))
                        
                        // 现代化的顶部标题栏设计，参考Grok3和ChatGPT
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(56.dp)
                                .padding(horizontal = 16.dp),
                            contentAlignment = Alignment.Center
                        ) {
                            // 返回按钮区域
                            Box(
                                modifier = Modifier
                                    .align(Alignment.CenterStart)
                                    .size(40.dp)
                                    .clip(CircleShape)
                                    .background(Color.White)
                                    .clickable(
                                        indication = rememberRipple(bounded = true, color = LightPurple),
                                        interactionSource = remember { MutableInteractionSource() },
                                        onClick = {
                                            hapticFeedback.performHapticFeedback(HapticFeedbackType.LongPress)
                                            onBackClick()
                                        }
                                    ),
                                contentAlignment = Alignment.Center
                            ) {
                                Icon(
                                    imageVector = Icons.Default.ArrowBack,
                                    contentDescription = "返回",
                                    tint = DarkGray,
                                    modifier = Modifier.size(24.dp)
                                )
                            }
                            
                            // 中间AI助手头像和名称
                            Row(
                                modifier = Modifier
                                    .align(Alignment.Center)
                                    .clip(RoundedCornerShape(20.dp))
                                    .background(Color.White)
                                    .clickable(
                                        indication = rememberRipple(bounded = true, color = LightPurple),
                                        interactionSource = remember { MutableInteractionSource() },
                                        onClick = { showNameDialog = true }
                                    )
                                    .padding(horizontal = 12.dp, vertical = 6.dp),
                                verticalAlignment = Alignment.CenterVertically,
                                horizontalArrangement = Arrangement.Center
                            ) {
                                // AI头像
                                Box(
                                    modifier = Modifier
                                        .size(28.dp)
                                        .background(
                                            color = PastelPink.copy(alpha = 0.3f),
                                            shape = CircleShape
                                        )
                                        .pointerInput(Unit) {
                                            detectTapGestures(
                                                onLongPress = { showAvatarDialog = true }
                                            )
                                        },
                                    contentAlignment = Alignment.Center
                                ) {
                                    Text(
                                        text = aiSettings.assistantAvatar,
                                        fontSize = 16.sp
                                    )
                                }
                                
                                Spacer(modifier = Modifier.width(8.dp))
                                
                                // AI名称
                                Text(
                                    text = aiSettings.assistantName,
                                    style = MaterialTheme.typography.titleMedium.copy(
                                        fontWeight = FontWeight.SemiBold,
                                        fontSize = 16.sp
                                    ),
                                    color = DarkGray
                                )
                                
                                Spacer(modifier = Modifier.width(4.dp))
                                
                                // 下拉图标
                                Icon(
                                    imageVector = Icons.Default.KeyboardArrowDown,
                                    contentDescription = "更改设置",
                                    tint = LightPurple,
                                    modifier = Modifier.size(16.dp)
                                )
                            }
                            
                            // 右侧更多选项按钮
                            Row(
                                modifier = Modifier.align(Alignment.CenterEnd)
                            ) {
                                // 添加显示欢迎卡片的按钮
                                if (!showWelcomeCard && messages.isNotEmpty()) {
                                    Box(
                                        modifier = Modifier
                                            .size(40.dp)
                                            .clip(CircleShape)
                                            .background(Color.White)
                                            .clickable(
                                                indication = rememberRipple(bounded = true, color = LightPurple),
                                                interactionSource = remember { MutableInteractionSource() },
                                                onClick = { showWelcomeCard = true }
                                            ),
                                        contentAlignment = Alignment.Center
                                    ) {
                                        Icon(
                                            imageVector = Icons.Default.Lightbulb,
                                            contentDescription = "显示建议",
                                            tint = DustyLavender,
                                            modifier = Modifier.size(24.dp)
                                        )
                                    }
                                    
                                    Spacer(modifier = Modifier.width(8.dp))
                                }
                                
                                // 更多选项按钮
                                Box(
                                    modifier = Modifier
                                        .size(40.dp)
                                        .clip(CircleShape)
                                        .background(Color.White),
                                    contentAlignment = Alignment.Center
                                ) {
                                    // 添加菜单状态
                                    var showDropdownMenu by remember { mutableStateOf(false) }
                                    
                                    // 添加文档选择器
                                    val documentPickerLauncher = rememberLauncherForActivityResult(
                                        contract = ActivityResultContracts.GetContent()
                                    ) { uri: Uri? ->
                                        uri?.let { selectedUri ->
                                            // 处理选择的文档
                                            viewModel.importDocument(context, selectedUri)
                                        }
                                    }
                                    
                                    IconButton(onClick = {
                                        hapticFeedback.performHapticFeedback(HapticFeedbackType.LongPress)
                                        showDropdownMenu = true
                                    }) {
                                        Icon(
                                            imageVector = Icons.Default.MoreVert,
                                            contentDescription = "更多选项",
                                            tint = DarkGray,
                                            modifier = Modifier.size(24.dp)
                                        )
                                    }
                                    
                                    // 下拉菜单
                                    DropdownMenu(
                                        expanded = showDropdownMenu,
                                        onDismissRequest = { showDropdownMenu = false },
                                        offset = DpOffset(0.dp, 8.dp),
                                        modifier = Modifier.background(Color.White)
                                    ) {
                                        // 导出对话内容
                                        DropdownMenuItem(
                                            text = {
                                                Row(verticalAlignment = Alignment.CenterVertically) {
                                                    Icon(
                                                        imageVector = Icons.Default.SaveAlt,
                                                        contentDescription = null,
                                                        tint = DarkPurple,
                                                        modifier = Modifier.size(20.dp)
                                                    )
                                                    Spacer(modifier = Modifier.width(12.dp))
                                                    Text(
                                                        text = "导出对话内容",
                                                        style = MaterialTheme.typography.bodyMedium,
                                                        color = DarkGray
                                                    )
                                                }
                                            },
                                            onClick = {
                                                hapticFeedback.performHapticFeedback(HapticFeedbackType.LongPress)
                                                showDropdownMenu = false
                                                viewModel.exportConversation(context)
                                            }
                                        )
                                        
                                        // 导入文档
                                        DropdownMenuItem(
                                            text = {
                                                Row(verticalAlignment = Alignment.CenterVertically) {
                                                    Icon(
                                                        imageVector = Icons.Default.Upload,
                                                        contentDescription = null,
                                                        tint = DarkPurple,
                                                        modifier = Modifier.size(20.dp)
                                                    )
                                                    Spacer(modifier = Modifier.width(12.dp))
                                                    Text(
                                                        text = "导入文档",
                                                        style = MaterialTheme.typography.bodyMedium,
                                                        color = DarkGray
                                                    )
                                                }
                                            },
                                            onClick = {
                                                hapticFeedback.performHapticFeedback(HapticFeedbackType.LongPress)
                                                showDropdownMenu = false
                                                // 启动文档选择器
                                                documentPickerLauncher.launch("*/*")
                                            }
                                        )
                                        
                                        // 清空对话
                                        DropdownMenuItem(
                                            text = {
                                                Row(verticalAlignment = Alignment.CenterVertically) {
                                                    Icon(
                                                        imageVector = Icons.Default.Delete,
                                                        contentDescription = null,
                                                        tint = DarkPurple,
                                                        modifier = Modifier.size(20.dp)
                                                    )
                                                    Spacer(modifier = Modifier.width(12.dp))
                                                    Text(
                                                        text = "清空对话",
                                                        style = MaterialTheme.typography.bodyMedium,
                                                        color = DarkGray
                                                    )
                                                }
                                            },
                                            onClick = {
                                                hapticFeedback.performHapticFeedback(HapticFeedbackType.LongPress)
                                                showDropdownMenu = false
                                                showClearConfirmDialog = true
                                            }
                                        )
                                        
                                        // AI模型配置
                                        DropdownMenuItem(
                                            text = {
                                                Row(verticalAlignment = Alignment.CenterVertically) {
                                                    Icon(
                                                        imageVector = Icons.Default.Settings,
                                                        contentDescription = null,
                                                        tint = DarkPurple,
                                                        modifier = Modifier.size(20.dp)
                                                    )
                                                    Spacer(modifier = Modifier.width(12.dp))
                                                    Text(
                                                        text = "AI模型配置",
                                                        style = MaterialTheme.typography.bodyMedium,
                                                        color = DarkGray
                                                    )
                                                }
                                            },
                                            onClick = {
                                                hapticFeedback.performHapticFeedback(HapticFeedbackType.LongPress)
                                                showDropdownMenu = false
                                                // 导航到AI模型配置
                                                navController.navigate(AppDestinations.AI_MODEL_SETTINGS_ROUTE)
                                            }
                                        )
                                    }
                                }
                            }
                        }
                        
                        // 添加底部分割线
                        Divider(
                            color = LightPurple.copy(alpha = 0.1f),
                            thickness = 1.dp
                        )
                    }
                }
            }
        ) { paddingValues ->
            // 使用Box包裹内容区域，以便于处理输入法和错误消息
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues)
                    .background(LightBackground)
            ) {
                // 主内容区域
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        // 使用imePadding来处理输入法弹出
                        .imePadding()
                ) {
                    // 对话消息列表
                    LazyColumn(
                        state = listState,
                        modifier = Modifier
                            .weight(1f)
                            .fillMaxWidth()
                            .padding(horizontal = 16.dp),
                        contentPadding = PaddingValues(top = if (showWelcomeCard) 8.dp else 16.dp, bottom = 16.dp)
                    ) {
                        if (showWelcomeCard) {
                            item {
                                SimpleWelcomeCard(
                                    onSuggestionClick = { suggestion ->
                                        inputText = suggestion
                                        viewModel.sendMessage(suggestion, MessageType.TEXT)
                                        showWelcomeCard = false
                                    },
                                    onSettingsClick = {
                                        // 显示设置对话框
                                        showNameDialog = true
                                    },
                                    onClose = {
                                        showWelcomeCard = false
                                    },
                                    aiSettings = aiSettings,
                                    onAvatarLongClick = {
                                        // 显示头像选择对话框
                                        hapticFeedback.performHapticFeedback(HapticFeedbackType.LongPress)
                                        showAvatarDialog = true
                                    },
                                    onEmojiClick = {
                                        // 显示Emoji选择对话框
                                        hapticFeedback.performHapticFeedback(HapticFeedbackType.TextHandleMove)
                                        showEmojiDialog = true
                                    },
                                    modifier = Modifier.padding(top = 16.dp, bottom = 8.dp)
                                )
                            }
                        }
                    
                        if (messages.isEmpty()) {
                            item {
                                Box(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(vertical = 24.dp),
                                    contentAlignment = Alignment.Center
                                ) {
                                    Text(
                                        text = "点击左下角灯泡图标查看建议",
                                        style = MaterialTheme.typography.bodyMedium,
                                        color = DarkGray.copy(alpha = 0.5f)
                                    )
                                }
                            }
                        } else {
                            items(messages) { message ->
                                when (message.messageType) {
                                    MessageType.TASK_OPTIMIZATION -> {
                                        OptimizationRequestCard(
                                            message = message,
                                            assistantAvatar = aiSettings.assistantAvatar
                                        )
                                    }
                                    MessageType.SUBTASKS -> {
                                        SubtasksResultCard(
                                            message = message,
                                            assistantAvatar = aiSettings.assistantAvatar,
                                            navController = navController
                                        )
                                    }
                                    else -> {
                                        MessageBubble(
                                            message = message,
                                            assistantAvatar = aiSettings.assistantAvatar,
                                            onLongClick = { /* 处理长按事件 */ }
                                        )
                                    }
                                }
                                Spacer(modifier = Modifier.height(8.dp))
                            }
                        }
                        
                        if (uiState.isProcessing) {
                            item {
                                SimpleTypingIndicator()
                            }
                        }
                    }
                    
                    // 输入区域
                    OutlinedTextField(
                        value = inputText,
                        onValueChange = { inputText = it },
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 16.dp, vertical = 8.dp)
                            .focusRequester(focusRequester),
                        placeholder = { Text("请输入消息...") },
                        trailingIcon = {
                            var isProcessing by remember { mutableStateOf(false) }
                            LaunchedEffect(Unit) {
                                viewModel.uiState.collect { state ->
                                    isProcessing = state.isProcessing
                                }
                            }
                            IconButton(
                                onClick = {
                                    if (inputText.isNotBlank() && !isProcessing) {
                                        hapticFeedback.performHapticFeedback(HapticFeedbackType.TextHandleMove)
                                        viewModel.sendMessage(inputText.trim())
                                        inputText = ""
                                        focusManager.clearFocus()
                                    }
                                },
                                modifier = Modifier
                                    .padding(start = 4.dp, end = 8.dp)
                                    .size(36.dp)
                                    .background(
                                        shape = CircleShape,
                                        color = DustyLavender.copy(alpha = if (isProcessing) 0.6f else 1f)
                                    ),
                                enabled = !isProcessing && inputText.isNotBlank()
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Send,
                                    contentDescription = "发送",
                                    tint = Color.White.copy(alpha = if (isProcessing) 0.7f else 1f)
                                )
                            }
                        },
                        keyboardOptions = KeyboardOptions(
                            imeAction = ImeAction.Send
                        ),
                        keyboardActions = KeyboardActions(
                            onSend = {
                                if (inputText.isNotBlank()) {
                                    hapticFeedback.performHapticFeedback(HapticFeedbackType.LongPress)
                                    viewModel.sendMessage(inputText, MessageType.TEXT)
                                    inputText = ""
                                    focusManager.clearFocus()
                                }
                            }
                        ),
                        colors = OutlinedTextFieldDefaults.colors(
                            focusedBorderColor = DustyLavender.copy(alpha = 0.5f),
                            unfocusedBorderColor = TextSecondary.copy(alpha = 0.2f),
                            focusedContainerColor = Color.White,
                            unfocusedContainerColor = Color.White
                        ),
                        shape = RoundedCornerShape(24.dp),
                        maxLines = 3
                    )
                }
                
                // 错误消息显示在底部
                if (uiState.error != null) {
                    SimpleErrorSnackbar(
                        errorMessage = uiState.error!!,
                        onDismiss = { viewModel.clearError() },
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp)
                            .align(Alignment.BottomCenter)
                    )
                }
            }
        }
    }
}