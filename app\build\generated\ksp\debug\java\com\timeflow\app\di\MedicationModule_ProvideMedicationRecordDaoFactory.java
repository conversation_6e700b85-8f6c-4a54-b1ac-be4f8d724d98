package com.timeflow.app.di;

import com.timeflow.app.data.dao.MedicationRecordDao;
import com.timeflow.app.data.db.AppDatabase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class MedicationModule_ProvideMedicationRecordDaoFactory implements Factory<MedicationRecordDao> {
  private final Provider<AppDatabase> appDatabaseProvider;

  public MedicationModule_ProvideMedicationRecordDaoFactory(
      Provider<AppDatabase> appDatabaseProvider) {
    this.appDatabaseProvider = appDatabaseProvider;
  }

  @Override
  public MedicationRecordDao get() {
    return provideMedicationRecordDao(appDatabaseProvider.get());
  }

  public static MedicationModule_ProvideMedicationRecordDaoFactory create(
      Provider<AppDatabase> appDatabaseProvider) {
    return new MedicationModule_ProvideMedicationRecordDaoFactory(appDatabaseProvider);
  }

  public static MedicationRecordDao provideMedicationRecordDao(AppDatabase appDatabase) {
    return Preconditions.checkNotNullFromProvides(MedicationModule.INSTANCE.provideMedicationRecordDao(appDatabase));
  }
}
