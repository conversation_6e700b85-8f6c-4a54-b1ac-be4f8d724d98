package com.timeflow.app.service;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DailyReviewNotificationGenerator_Factory implements Factory<DailyReviewNotificationGenerator> {
  private final Provider<Context> contextProvider;

  public DailyReviewNotificationGenerator_Factory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public DailyReviewNotificationGenerator get() {
    return newInstance(contextProvider.get());
  }

  public static DailyReviewNotificationGenerator_Factory create(Provider<Context> contextProvider) {
    return new DailyReviewNotificationGenerator_Factory(contextProvider);
  }

  public static DailyReviewNotificationGenerator newInstance(Context context) {
    return new DailyReviewNotificationGenerator(context);
  }
}
