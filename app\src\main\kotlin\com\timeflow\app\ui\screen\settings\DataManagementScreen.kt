package com.timeflow.app.ui.screen.settings

import android.app.Activity
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.animation.*
import androidx.compose.animation.core.tween
import androidx.compose.foundation.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material.icons.outlined.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import com.timeflow.app.R
import com.timeflow.app.ui.theme.*
import com.timeflow.app.utils.DatabaseBackupManager
import com.timeflow.app.utils.SystemBarManager
import com.timeflow.app.viewmodel.DataManagementViewModel
import com.timeflow.app.viewmodel.DataManagementUiState
import java.text.SimpleDateFormat
import java.util.*

/**
 * 统一的数据管理页面
 * 整合备份、恢复、导入导出、存储管理等功能
 * 参照微信、支付宝等知名应用的数据管理设计
 */
@OptIn(ExperimentalMaterial3Api::class, ExperimentalAnimationApi::class)
@Composable
fun DataManagementScreen(
    navController: NavController,
    viewModel: DataManagementViewModel = hiltViewModel()
) {
    val context = LocalContext.current
    val activity = context as? Activity
    
    // 状态管理
    val uiState by viewModel.uiState.collectAsState()
    val storageInfo by viewModel.storageInfo.collectAsState()
    var selectedTab by remember { mutableStateOf(DataManagementTab.BACKUP) }
    var showDeleteConfirmDialog by remember { mutableStateOf<String?>(null) }
    var showRestartDialog by remember { mutableStateOf(false) }
    
    // 处理状态栏 - 使用标准页面设置
    SideEffect {
        activity?.let {
            SystemBarManager.forceOpaqueStatusBar(it, false)
        }
    }

    // 初始化数据
    LaunchedEffect(Unit) {
        viewModel.loadData()
    }

    // 使用Column布局，手动处理状态栏间距，参考ProfileScreen的成功实现
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(FreshBackground)
            .padding(top = SystemBarManager.getFixedStatusBarHeight()) // 🔧 修复：使用固定状态栏高度
    ) {
        // 顶部标题栏
        TopAppBar(
            title = {
                Text(
                    "数据管理",
                    fontWeight = FontWeight.SemiBold
                )
            },
            navigationIcon = {
                IconButton(onClick = { navController.navigateUp() }) {
                    Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                }
            },
            colors = TopAppBarDefaults.topAppBarColors(
                containerColor = FreshBackground
            )
        )

        // 主要内容区域
        Column(
            modifier = Modifier.fillMaxSize()
        ) {
            // 存储概览卡片
            StorageOverviewCard(
                storageInfo = storageInfo,
                modifier = Modifier.padding(horizontal = 16.dp)
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Tab选择器
            DataManagementTabRow(
                selectedTab = selectedTab,
                onTabSelected = { selectedTab = it },
                modifier = Modifier.padding(horizontal = 16.dp)
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 内容区域 - 修复重影问题
            AnimatedContent(
                targetState = selectedTab,
                transitionSpec = {
                    // 使用淡入淡出动画替代滑动动画，避免重影问题
                    fadeIn(
                        animationSpec = tween(200, delayMillis = 100)
                    ) with fadeOut(
                        animationSpec = tween(200)
                    )
                },
                label = "tab_content"
            ) { tab ->
                when (tab) {
                    DataManagementTab.BACKUP -> BackupTabContent(
                        uiState = uiState,
                        onManualBackup = { viewModel.createBackup() },
                        onToggleAutoBackup = { viewModel.toggleAutoBackup() },
                        onSetBackupFrequency = { frequency -> viewModel.setBackupFrequency(frequency) },
                        modifier = Modifier.fillMaxSize()
                    )
                    DataManagementTab.RESTORE -> RestoreTabContent(
                        uiState = uiState,
                        onRestoreBackup = { fileName -> 
                            viewModel.restoreBackup(fileName)
                            showRestartDialog = true
                        },
                        onDeleteBackup = { fileName -> showDeleteConfirmDialog = fileName },
                        onQuickRestore = { viewModel.quickRestore() },
                        modifier = Modifier.fillMaxSize()
                    )
                    DataManagementTab.IMPORT_EXPORT -> ImportExportTabContent(
                        uiState = uiState,
                        onExportData = { viewModel.exportData() },
                        onImportData = { uri -> viewModel.importData(uri) },
                        modifier = Modifier.fillMaxSize()
                    )
                }
            }
        } // 关闭主要内容区域的Column
    } // 关闭外层Column

    // 删除确认对话框
    showDeleteConfirmDialog?.let { fileName ->
        AlertDialog(
            onDismissRequest = { showDeleteConfirmDialog = null },
            title = { Text(stringResource(R.string.delete_backup)) },
            text = { Text(stringResource(R.string.delete_backup_confirm)) },
            confirmButton = {
                TextButton(
                    onClick = {
                        viewModel.deleteBackup(fileName)
                        showDeleteConfirmDialog = null
                    }
                ) {
                    Text(stringResource(R.string.delete), color = Color.Red)
                }
            },
            dismissButton = {
                TextButton(onClick = { showDeleteConfirmDialog = null }) {
                    Text(stringResource(R.string.cancel))
                }
            }
        )
    }
    
    // 重启应用对话框
    if (showRestartDialog || uiState.restoreCompleted) {
        AlertDialog(
            onDismissRequest = {},
            title = { Text("恢复成功") },
            text = { Text("数据已恢复，请重启应用以加载恢复的数据") },
            confirmButton = {
                TextButton(
                    onClick = {
                        // 重启应用
                        val packageManager = context.packageManager
                        val intent = packageManager.getLaunchIntentForPackage(context.packageName)
                        intent?.addFlags(android.content.Intent.FLAG_ACTIVITY_CLEAR_TOP)
                        intent?.addFlags(android.content.Intent.FLAG_ACTIVITY_NEW_TASK)
                        context.startActivity(intent)
                        android.os.Process.killProcess(android.os.Process.myPid())
                    }
                ) {
                    Text("重启应用")
                }
            }
        )
    }
    
    // 错误提示
    uiState.error?.let { error ->
        LaunchedEffect(error) {
            // 这里可以显示Snackbar
            viewModel.clearError()
        }
    }
}

/**
 * 数据管理标签页枚举 - 移除清理功能，专注核心数据管理
 */
enum class DataManagementTab(val titleKey: String, val icon: ImageVector) {
    BACKUP("backup", Icons.Outlined.Backup),
    RESTORE("restore", Icons.Outlined.Restore),
    IMPORT_EXPORT("import_export", Icons.Outlined.ImportExport)
}

/**
 * 获取标签页的本地化标题
 */
@Composable
private fun getTabTitle(tab: DataManagementTab): String {
    return when (tab) {
        DataManagementTab.BACKUP -> stringResource(R.string.backup)
        DataManagementTab.RESTORE -> stringResource(R.string.restore)
        DataManagementTab.IMPORT_EXPORT -> stringResource(R.string.import_export)
    }
}

/**
 * 存储概览卡片
 */
@Composable
private fun StorageOverviewCard(
    storageInfo: StorageInfo,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "存储概览",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.SemiBold,
                    color = MaterialTheme.colorScheme.primary
                )
                
                Icon(
                    imageVector = Icons.Outlined.Storage,
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.size(24.dp)
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 存储使用情况
            StorageUsageBar(
                usedSpace = storageInfo.usedSpace,
                totalSpace = storageInfo.totalSpace,
                modifier = Modifier.fillMaxWidth()
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 存储详情
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                StorageDetailItem(
                    label = stringResource(R.string.app_data),
                    value = formatFileSize(storageInfo.appDataSize),
                    color = FreshPrimary
                )
                StorageDetailItem(
                    label = stringResource(R.string.backup_files),
                    value = formatFileSize(storageInfo.backupSize),
                    color = DustyLavender
                )
                StorageDetailItem(
                    label = stringResource(R.string.cache),
                    value = formatFileSize(storageInfo.cacheSize),
                    color = Color(0xFFFFB74D)
                )
            }
        }
    }
}

/**
 * 存储使用情况进度条
 */
@Composable
private fun StorageUsageBar(
    usedSpace: Long,
    totalSpace: Long,
    modifier: Modifier = Modifier
) {
    val usagePercentage = if (totalSpace > 0) (usedSpace.toFloat() / totalSpace.toFloat()) else 0f
    
    Column(modifier = modifier) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Text(
                text = "已使用 ${formatFileSize(usedSpace)}",
                style = MaterialTheme.typography.bodyMedium,
                color = Color.Gray
            )
            Text(
                text = "总计 ${formatFileSize(totalSpace)}",
                style = MaterialTheme.typography.bodyMedium,
                color = Color.Gray
            )
        }
        
        Spacer(modifier = Modifier.height(8.dp))
        
        LinearProgressIndicator(
            progress = usagePercentage,
            modifier = Modifier
                .fillMaxWidth()
                .height(8.dp)
                .clip(RoundedCornerShape(4.dp)),
            color = when {
                usagePercentage > 0.9f -> Color.Red
                usagePercentage > 0.7f -> Color(0xFFFF9800)
                else -> FreshPrimary
            },
            trackColor = Color.LightGray.copy(alpha = 0.3f)
        )
    }
}

/**
 * 存储详情项
 */
@Composable
private fun StorageDetailItem(
    label: String,
    value: String,
    color: Color
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Box(
            modifier = Modifier
                .size(8.dp)
                .background(color, CircleShape)
        )
        Spacer(modifier = Modifier.height(4.dp))
        Text(
            text = value,
            style = MaterialTheme.typography.bodyMedium,
            fontWeight = FontWeight.Medium
        )
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = Color.Gray
        )
    }
}

/**
 * 格式化文件大小
 */
private fun formatFileSize(bytes: Long): String {
    if (bytes < 1024) return "${bytes}B"
    val kb = bytes / 1024.0
    if (kb < 1024) return String.format("%.1fKB", kb)
    val mb = kb / 1024.0
    if (mb < 1024) return String.format("%.1fMB", mb)
    val gb = mb / 1024.0
    return String.format("%.1fGB", gb)
}

/**
 * Tab选择器
 */
@Composable
private fun DataManagementTabRow(
    selectedTab: DataManagementTab,
    onTabSelected: (DataManagementTab) -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(4.dp),
            horizontalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            DataManagementTab.values().forEach { tab ->
                val isSelected = selectedTab == tab

                Box(
                    modifier = Modifier
                        .weight(1f)
                        .clip(RoundedCornerShape(8.dp))
                        .background(
                            if (isSelected) FreshPrimary.copy(alpha = 0.1f) else Color.Transparent
                        )
                        .clickable { onTabSelected(tab) }
                        .padding(vertical = 12.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Icon(
                            imageVector = tab.icon,
                            contentDescription = getTabTitle(tab),
                            tint = if (isSelected) FreshPrimary else Color.Gray,
                            modifier = Modifier.size(20.dp)
                        )
                        Spacer(modifier = Modifier.height(4.dp))
                        Text(
                            text = getTabTitle(tab),
                            style = MaterialTheme.typography.bodySmall,
                            color = if (isSelected) FreshPrimary else Color.Gray,
                            fontWeight = if (isSelected) FontWeight.Medium else FontWeight.Normal
                        )
                    }
                }
            }
        }
    }
}

/**
 * 备份Tab内容
 */
@Composable
private fun BackupTabContent(
    uiState: DataManagementUiState,
    onManualBackup: () -> Unit,
    onToggleAutoBackup: () -> Unit,
    onSetBackupFrequency: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    LazyColumn(
        modifier = modifier.padding(horizontal = 16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 快速备份卡片
        item {
            QuickBackupCard(
                isBackingUp = uiState.isLoading,
                lastBackupTime = uiState.lastBackupTime,
                onBackup = onManualBackup
            )
        }

        // 自动备份设置
        item {
            AutoBackupSettingsCard(
                isEnabled = uiState.autoBackupEnabled,
                frequency = uiState.backupFrequency,
                onToggle = onToggleAutoBackup,
                onFrequencyChange = onSetBackupFrequency
            )
        }

        // 备份历史
        item {
            BackupHistoryCard(
                backupFiles = uiState.backupFiles,
                onViewAll = { /* 展开查看所有备份 */ }
            )
        }
    }
}

/**
 * 恢复Tab内容
 */
@Composable
private fun RestoreTabContent(
    uiState: DataManagementUiState,
    onRestoreBackup: (String) -> Unit,
    onDeleteBackup: (String) -> Unit,
    onQuickRestore: () -> Unit,
    modifier: Modifier = Modifier
) {
    LazyColumn(
        modifier = modifier.padding(horizontal = 16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 快速恢复卡片
        item {
            QuickRestoreCard(
                hasBackups = uiState.backupFiles.isNotEmpty(),
                isRestoring = uiState.isLoading,
                onQuickRestore = onQuickRestore
            )
        }

        // 备份文件列表
        if (uiState.backupFiles.isNotEmpty()) {
            item {
                Text(
                    text = "可用备份 (${uiState.backupFiles.size})",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.SemiBold,
                    modifier = Modifier.padding(vertical = 8.dp)
                )
            }

            items(uiState.backupFiles) { backupFile ->
                BackupFileCard(
                    backupFile = backupFile,
                    onRestore = { onRestoreBackup(backupFile.fileName) },
                    onDelete = { onDeleteBackup(backupFile.fileName) },
                    isRestoring = uiState.isLoading
                )
            }
        } else {
            item {
                EmptyBackupCard()
            }
        }
    }
}

/**
 * 导入导出Tab内容
 */
@Composable
private fun ImportExportTabContent(
    uiState: DataManagementUiState,
    onExportData: () -> Unit,
    onImportData: (android.net.Uri?) -> Unit,
    modifier: Modifier = Modifier
) {
    // 文件选择器状态
    val filePickerLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.GetContent()
    ) { uri ->
        onImportData(uri)
    }

    LazyColumn(
        modifier = modifier.padding(horizontal = 16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 导出数据卡片
        item {
            ExportDataCard(
                isExporting = uiState.isLoading,
                onExport = onExportData
            )
        }

        // 导入数据卡片
        item {
            ImportDataCard(
                isImporting = uiState.isLoading,
                onImport = { filePickerLauncher.launch("application/json") }
            )
        }

        // 数据格式说明
        item {
            DataFormatInfoCard()
        }
    }
}



/**
 * 快速备份卡片
 */
@Composable
private fun QuickBackupCard(
    isBackingUp: Boolean,
    lastBackupTime: Long?,
    onBackup: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column {
                    Text(
                        text = "立即备份",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.SemiBold
                    )
                    Text(
                        text = if (lastBackupTime != null) {
                            "上次备份：${formatBackupTime(lastBackupTime)}"
                        } else {
                            "还未进行过备份"
                        },
                        style = MaterialTheme.typography.bodySmall,
                        color = Color.Gray
                    )
                }

                Button(
                    onClick = onBackup,
                    enabled = !isBackingUp,
                    colors = ButtonDefaults.buttonColors(containerColor = FreshPrimary),
                    shape = RoundedCornerShape(12.dp)
                ) {
                    if (isBackingUp) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(16.dp),
                            color = Color.White,
                            strokeWidth = 2.dp
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                    }
                    Text(if (isBackingUp) "备份中..." else "开始备份")
                }
            }
        }
    }
}

/**
 * 自动备份设置卡片
 */
@Composable
private fun AutoBackupSettingsCard(
    isEnabled: Boolean,
    frequency: String,
    onToggle: () -> Unit,
    onFrequencyChange: (String) -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "自动备份",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.SemiBold
                )

                Switch(
                    checked = isEnabled,
                    onCheckedChange = { onToggle() },
                    colors = SwitchDefaults.colors(
                        checkedThumbColor = Color.White,
                        checkedTrackColor = FreshPrimary
                    )
                )
            }

            if (isEnabled) {
                Spacer(modifier = Modifier.height(16.dp))

                Text(
                    text = stringResource(R.string.backup_frequency),
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Medium
                )

                Spacer(modifier = Modifier.height(8.dp))

                Row(
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    listOf(
                        stringResource(R.string.daily),
                        stringResource(R.string.weekly),
                        stringResource(R.string.monthly)
                    ).forEach { freq ->
                        FilterChip(
                            onClick = { onFrequencyChange(freq) },
                            label = { Text(freq) },
                            selected = frequency == freq,
                            colors = FilterChipDefaults.filterChipColors(
                                selectedContainerColor = FreshPrimary.copy(alpha = 0.2f),
                                selectedLabelColor = FreshPrimary
                            )
                        )
                    }
                }
            }
        }
    }
}

/**
 * 快速恢复卡片
 */
@Composable
private fun QuickRestoreCard(
    hasBackups: Boolean,
    isRestoring: Boolean,
    onQuickRestore: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = if (hasBackups) DustyLavender.copy(alpha = 0.1f) else Color.Gray.copy(alpha = 0.1f)
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
    ) {
        Column(
            modifier = Modifier.padding(20.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                imageVector = Icons.Outlined.Restore,
                contentDescription = null,
                tint = if (hasBackups) DustyLavender else Color.Gray,
                modifier = Modifier.size(48.dp)
            )

            Spacer(modifier = Modifier.height(12.dp))

            Text(
                text = if (hasBackups) stringResource(R.string.one_click_restore) else stringResource(R.string.no_backups_available),
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.SemiBold,
                color = if (hasBackups) DustyLavender else Color.Gray
            )

            Text(
                text = if (hasBackups) stringResource(R.string.restore_to_latest_backup) else stringResource(R.string.create_backup_first),
                style = MaterialTheme.typography.bodyMedium,
                color = Color.Gray,
                textAlign = TextAlign.Center
            )

            if (hasBackups) {
                Spacer(modifier = Modifier.height(16.dp))

                Button(
                    onClick = onQuickRestore,
                    enabled = !isRestoring,
                    colors = ButtonDefaults.buttonColors(containerColor = DustyLavender),
                    shape = RoundedCornerShape(12.dp)
                ) {
                    if (isRestoring) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(16.dp),
                            color = Color.White,
                            strokeWidth = 2.dp
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                    }
                    Text(if (isRestoring) "恢复中..." else "立即恢复")
                }
            }
        }
    }
}

/**
 * 格式化备份时间
 */
private fun formatBackupTime(timestamp: Long): String {
    val sdf = SimpleDateFormat("MM-dd HH:mm", Locale.getDefault())
    return sdf.format(Date(timestamp))
}

/**
 * 备份文件卡片
 */
@Composable
private fun BackupFileCard(
    backupFile: DatabaseBackupManager.BackupFileInfo,
    onRestore: () -> Unit,
    onDelete: () -> Unit,
    isRestoring: Boolean
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = formatBackupTime(backupFile.timestamp),
                    style = MaterialTheme.typography.bodyLarge,
                    fontWeight = FontWeight.Medium
                )
                Text(
                    text = "${formatFileSize(backupFile.sizeBytes)} • ${backupFile.fileName}",
                    style = MaterialTheme.typography.bodySmall,
                    color = Color.Gray
                )
            }

            Row(horizontalArrangement = Arrangement.spacedBy(8.dp)) {
                IconButton(
                    onClick = onRestore,
                    enabled = !isRestoring,
                    modifier = Modifier.size(28.dp) // 🔧 移除背景色
                ) {
                    Icon(
                        imageVector = Icons.Default.Restore,
                        contentDescription = "恢复",
                        tint = DustyLavender,
                        modifier = Modifier.size(16.dp) // 🔧 稍微增大图标
                    )
                }

                IconButton(
                    onClick = onDelete,
                    enabled = !isRestoring,
                    modifier = Modifier.size(28.dp) // 🔧 移除背景色
                ) {
                    Icon(
                        imageVector = Icons.Default.Delete,
                        contentDescription = "删除",
                        tint = Color.Red,
                        modifier = Modifier.size(16.dp) // 🔧 稍微增大图标
                    )
                }
            }
        }
    }
}

/**
 * 空备份卡片
 */
@Composable
private fun EmptyBackupCard() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(containerColor = Color.Gray.copy(alpha = 0.05f)),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(32.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                imageVector = Icons.Outlined.FolderOpen,
                contentDescription = null,
                tint = Color.Gray,
                modifier = Modifier.size(48.dp)
            )

            Spacer(modifier = Modifier.height(12.dp))

            Text(
                text = "暂无备份文件",
                style = MaterialTheme.typography.titleMedium,
                color = Color.Gray
            )

            Text(
                text = "创建第一个备份来保护您的数据",
                style = MaterialTheme.typography.bodyMedium,
                color = Color.Gray,
                textAlign = TextAlign.Center
            )
        }
    }
}

/**
 * 导出数据卡片
 */
@Composable
private fun ExportDataCard(
    isExporting: Boolean,
    onExport: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column {
                    Text(
                        text = "导出数据",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.SemiBold
                    )
                    Text(
                        text = "将数据导出为可读格式",
                        style = MaterialTheme.typography.bodySmall,
                        color = Color.Gray
                    )
                }

                Button(
                    onClick = onExport,
                    enabled = !isExporting,
                    colors = ButtonDefaults.buttonColors(containerColor = Color(0xFF4CAF50)),
                    shape = RoundedCornerShape(12.dp)
                ) {
                    if (isExporting) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(16.dp),
                            color = Color.White,
                            strokeWidth = 2.dp
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                    }
                    Text(if (isExporting) "导出中..." else "开始导出")
                }
            }
        }
    }
}

/**
 * 导入数据卡片
 */
@Composable
private fun ImportDataCard(
    isImporting: Boolean,
    onImport: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column {
                    Text(
                        text = "导入数据",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.SemiBold
                    )
                    Text(
                        text = "从文件导入数据",
                        style = MaterialTheme.typography.bodySmall,
                        color = Color.Gray
                    )
                }

                Button(
                    onClick = onImport,
                    enabled = !isImporting,
                    colors = ButtonDefaults.buttonColors(containerColor = Color(0xFF2196F3)),
                    shape = RoundedCornerShape(12.dp)
                ) {
                    if (isImporting) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(16.dp),
                            color = Color.White,
                            strokeWidth = 2.dp
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                    }
                    Text(if (isImporting) "导入中..." else "选择文件")
                }
            }
        }
    }
}

/**
 * 数据格式说明卡片
 */
@Composable
private fun DataFormatInfoCard() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(containerColor = Color(0xFFF3F4F6)),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Outlined.Info,
                    contentDescription = null,
                    tint = Color(0xFF6B7280),
                    modifier = Modifier.size(20.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "支持的格式",
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Medium,
                    color = Color(0xFF374151)
                )
            }

            Spacer(modifier = Modifier.height(8.dp))

            Text(
                text = stringResource(R.string.data_format_info),
                style = MaterialTheme.typography.bodySmall,
                color = Color(0xFF6B7280),
                lineHeight = 18.sp
            )
        }
    }
}







/**
 * 备份历史卡片
 */
@Composable
private fun BackupHistoryCard(
    backupFiles: List<DatabaseBackupManager.BackupFileInfo>,
    onViewAll: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "备份历史",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.SemiBold
                )

                if (backupFiles.size > 3) {
                    TextButton(onClick = onViewAll) {
                        Text(
                            text = "查看全部",
                            style = MaterialTheme.typography.bodySmall,
                            color = FreshPrimary
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(12.dp))

            if (backupFiles.isEmpty()) {
                Text(
                    text = "暂无备份记录",
                    style = MaterialTheme.typography.bodyMedium,
                    color = Color.Gray,
                    modifier = Modifier.padding(vertical = 16.dp)
                )
            } else {
                backupFiles.take(3).forEach { backupFile ->
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 4.dp),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Column {
                            Text(
                                text = formatBackupTime(backupFile.timestamp),
                                style = MaterialTheme.typography.bodyMedium,
                                fontWeight = FontWeight.Medium
                            )
                            Text(
                                text = formatFileSize(backupFile.sizeBytes),
                                style = MaterialTheme.typography.bodySmall,
                                color = Color.Gray
                            )
                        }

                        Icon(
                            imageVector = Icons.Outlined.CheckCircle,
                            contentDescription = null,
                            tint = FreshPrimary,
                            modifier = Modifier.size(16.dp)
                        )
                    }

                    if (backupFile != backupFiles.take(3).last()) {
                        Spacer(modifier = Modifier.height(8.dp))
                    }
                }
            }
        }
    }
}

/**
 * 数据类：存储信息
 */
data class StorageInfo(
    val usedSpace: Long = 0L,
    val totalSpace: Long = 0L,
    val appDataSize: Long = 0L,
    val backupSize: Long = 0L,
    val cacheSize: Long = 0L
)
