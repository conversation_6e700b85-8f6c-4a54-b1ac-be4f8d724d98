package com.timeflow.app.di;

import com.timeflow.app.data.dao.WishDao;
import com.timeflow.app.data.repository.WishRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class WishModule_ProvideWishRepositoryFactory implements Factory<WishRepository> {
  private final Provider<WishDao> wishDaoProvider;

  public WishModule_ProvideWishRepositoryFactory(Provider<WishDao> wishDaoProvider) {
    this.wishDaoProvider = wishDaoProvider;
  }

  @Override
  public WishRepository get() {
    return provideWishRepository(wishDaoProvider.get());
  }

  public static WishModule_ProvideWishRepositoryFactory create(Provider<WishDao> wishDaoProvider) {
    return new WishModule_ProvideWishRepositoryFactory(wishDaoProvider);
  }

  public static WishRepository provideWishRepository(WishDao wishDao) {
    return Preconditions.checkNotNullFromProvides(WishModule.INSTANCE.provideWishRepository(wishDao));
  }
}
