package com.timeflow.app.di;

import com.timeflow.app.data.repository.SharedFilterState;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class RepositoryModule_ProvideSharedFilterStateFactory implements Factory<SharedFilterState> {
  @Override
  public SharedFilterState get() {
    return provideSharedFilterState();
  }

  public static RepositoryModule_ProvideSharedFilterStateFactory create() {
    return InstanceHolder.INSTANCE;
  }

  public static SharedFilterState provideSharedFilterState() {
    return Preconditions.checkNotNullFromProvides(RepositoryModule.INSTANCE.provideSharedFilterState());
  }

  private static final class InstanceHolder {
    private static final RepositoryModule_ProvideSharedFilterStateFactory INSTANCE = new RepositoryModule_ProvideSharedFilterStateFactory();
  }
}
