package com.timeflow.app.ui.screen.task.model

import android.content.Context
import com.timeflow.app.R
import java.time.LocalDateTime

/**
 * 任务完成后的反馈数据模型
 */
data class FeedbackData(
    val emotion: String? = null,  // 情绪emoji
    val comment: String? = null,  // 用户感想/评论
    val imagePaths: List<String> = emptyList(),  // 图片文件路径列表
    val timestamp: LocalDateTime = LocalDateTime.now()  // 反馈时间戳
)

// 预定义的emoji选项
val EMOTION_OPTIONS = listOf("😊", "😐", "😢", "😡", "😰")

/**
 * 获取本地化的智能建议配置
 */
fun getFeedbackSuggestions(context: Context): Map<String, List<String>> {
    return mapOf(
        context.getString(R.string.feedback_feeling_tag) to listOf(
            context.getString(R.string.feeling_accomplished),
            context.getString(R.string.feeling_struggling),
            context.getString(R.string.feeling_need_help),
            context.getString(R.string.feeling_very_accomplished),
            context.getString(R.string.feeling_smooth)
        ),
        context.getString(R.string.feedback_followup_tag) to listOf(
            context.getString(R.string.followup_need_tracking),
            context.getString(R.string.followup_completed_archived),
            context.getString(R.string.followup_to_share),
            context.getString(R.string.followup_need_improvement),
            context.getString(R.string.followup_worth_recording)
        ),
        context.getString(R.string.feedback_difficulty_tag) to listOf(
            context.getString(R.string.difficulty_easier_than_expected),
            context.getString(R.string.difficulty_as_expected),
            context.getString(R.string.difficulty_harder_than_expected),
            context.getString(R.string.difficulty_need_more_learning)
        ),
        context.getString(R.string.feedback_time_tag) to listOf(
            context.getString(R.string.time_completed_early),
            context.getString(R.string.time_completed_on_time),
            context.getString(R.string.time_slightly_delayed),
            context.getString(R.string.time_significantly_delayed)
        )
    )
}