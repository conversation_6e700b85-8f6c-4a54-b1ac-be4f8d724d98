<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="app_name">TimeFlow</string>
    <string name="home">Home</string>
    <string name="tasks">Tasks</string>
    <string name="tasks_3d">3D Tasks</string>
    <string name="time">Time</string>
    <string name="analytics">Analytics</string>
    <string name="discover">Discover</string>
    <string name="profile">Profile</string>
    <string name="calendar">Calendar</string>
    <string name="wish_pool">Wish Pool</string>
    
    <!-- AI Assistant -->
    <string name="ai_assistant_header">AI Assistant</string>
    <string name="ai_review">AI Review</string>
    
    <!-- Menstrual Cycle -->
    <string name="menstrual_cycle">Menstrual Cycle</string>
    <string name="menstrual_period">Period</string>
    <string name="predicted_period">Predicted Period</string>
    <string name="fertile_period">Fertile Period</string>
    <string name="period_started">Did your period start?</string>
    <string name="sexual_activity">Sexual Activity</string>
    <string name="physical_symptoms">Physical Symptoms</string>
    <string name="cycle_view">Cycle View</string>
    <string name="calendar_view">Calendar View</string>
    <string name="day_x">Day %1$d</string>
    <string name="cycle_days">%1$d days total</string>
    <string name="prompt_input_hint">Enter your question...</string>
    
    <!-- Theme Settings -->
    <string name="theme_settings">Theme Settings</string>
    <string name="material_you">Material You</string>
    <string name="material_you_description">Enable dynamic colors</string>
    <string name="material_you_desc">Use dynamic color scheme, automatically generate theme colors based on wallpaper</string>
    <string name="color_scheme">Color Scheme</string>
    <string name="color_theme_settings">Color Theme Settings</string>
    <string name="custom_theme_colors">Custom Theme Colors</string>
    <string name="primary_color">Primary Color</string>
    <string name="primary_color_desc">Main accent color of the app</string>
    <string name="secondary_color">Secondary Color</string>
    <string name="secondary_color_desc">Secondary accent color of the theme</string>
    <string name="tertiary_color">Tertiary Color</string>
    <string name="tertiary_color_desc">Tertiary accent color of the theme</string>
    <string name="background_color">Background Color</string>
    <string name="background_color_desc">Default background color of the app</string>
    <string name="background_colors">Background Color Settings</string>
    <string name="unified_background">Unified Background</string>
    <string name="unified_background_desc">Use the same background color for all pages</string>
    
    <!-- Language Settings -->
    <string name="language_settings">Language Settings</string>
    <string name="app_language">App Language</string>
    <string name="app_language_desc">Choose your preferred language</string>
    <string name="follow_system">Follow System</string>
    <string name="simplified_chinese">Simplified Chinese</string>
    <string name="traditional_chinese">Traditional Chinese</string>
    <string name="english">English</string>
    <string name="language_changed">Language changed</string>
    <string name="language_change_desc">The app will restart to apply the new language</string>
    <string name="restart_now">Restart Now</string>
    <string name="restart_later">Restart Later</string>
    
    <!-- Settings -->
    <string name="settings">Settings</string>
    <string name="general_settings">General Settings</string>
    <string name="appearance_settings">Appearance Settings</string>
    <string name="notification_settings">Notification Settings</string>
    <string name="privacy_settings">Privacy Settings</string>
    <string name="about">About</string>
    <string name="version">Version</string>
    <string name="save">Save</string>
    <string name="cancel">Cancel</string>
    <string name="confirm">Confirm</string>
    <string name="ok">OK</string>
    <string name="yes">Yes</string>
    <string name="no">No</string>
    <string name="done">Done</string>
    <string name="edit">Edit</string>
    <string name="delete">Delete</string>
    <string name="add">Add</string>
    <string name="remove">Remove</string>
    <string name="clear">Clear</string>
    <string name="reset">Reset</string>
    <string name="apply">Apply</string>
    <string name="close">Close</string>
    <string name="back">Back</string>
    <string name="next">Next</string>
    <string name="previous">Previous</string>
    <string name="finish">Finish</string>
    <string name="loading">Loading...</string>
    <string name="error">Error</string>
    <string name="success">Success</string>
    <string name="warning">Warning</string>
    <string name="info">Information</string>

    <!-- Task Feedback -->
    <string name="feedback_feeling_tag">#Feeling</string>
    <string name="feedback_followup_tag">#Followup</string>
    <string name="feedback_difficulty_tag">#Difficulty</string>
    <string name="feedback_time_tag">#Time</string>

    <string name="feeling_accomplished">Feeling accomplished</string>
    <string name="feeling_struggling">A bit challenging</string>
    <string name="feeling_need_help">Need help</string>
    <string name="feeling_very_accomplished">Very accomplished</string>
    <string name="feeling_smooth">Completed smoothly</string>

    <string name="followup_need_tracking">Need follow-up</string>
    <string name="followup_completed_archived">Completed and archived</string>
    <string name="followup_to_share">To share</string>
    <string name="followup_need_improvement">Need improvement</string>
    <string name="followup_worth_recording">Worth recording</string>

    <string name="difficulty_easier_than_expected">Easier than expected</string>
    <string name="difficulty_as_expected">As expected</string>
    <string name="difficulty_harder_than_expected">Harder than expected</string>
    <string name="difficulty_need_more_learning">Need more learning</string>

    <string name="time_completed_early">Completed early</string>
    <string name="time_completed_on_time">Completed on time</string>
    <string name="time_slightly_delayed">Slightly delayed</string>
    <string name="time_significantly_delayed">Significantly delayed</string>

    <!-- AI Assistant -->
    <string name="set_ai_assistant_name">Set AI Assistant Name</string>
    <string name="ai_assistant_name">AI Assistant Name</string>
    <string name="ai_assistant_name_example">e.g.: Assistant</string>
    <string name="enter_ai_assistant_name">Enter AI assistant name</string>

    <!-- Task Feedback Input -->
    <string name="feedback_input_hint">Enter thoughts or use #tags for smart suggestions...</string>

    <!-- Date Time -->
    <string name="monday">Mon</string>
    <string name="tuesday">Tue</string>
    <string name="wednesday">Wed</string>
    <string name="thursday">Thu</string>
    <string name="friday">Fri</string>
    <string name="saturday">Sat</string>
    <string name="sunday">Sun</string>

    <string name="month_day_format">%1$s %2$d</string>
    <string name="week_month_day_format">%1$s · %2$s</string>

    <!-- Common Actions -->
    <string name="reflection">Reflection</string>

    <!-- Content Descriptions -->
    <string name="content_desc_reflection">Reflection</string>
    <string name="content_desc_settings">Settings</string>

    <!-- Wish Statistics Encouragement -->
    <string name="encouragement_master">🌟 You\'ve become a true wish master! Keep this passion, your energy will inspire others!</string>
    <string name="encouragement_amazing">🚀 Wow! Your wish fulfillment ability is amazing! Just a few more steps to master level!</string>
    <string name="encouragement_great">💫 Fantastic! You\'re on the fast track to wish fulfillment! Each achievement builds the foundation for the next!</string>
    <string name="encouragement_good_start">🌱 Great start! Your wish seeds are growing strong, keep this persistence and passion!</string>
    <string name="encouragement_progress">✨ Every step is progress! You\'ve started your wish fulfillment journey, believe in your power!</string>
    <string name="encouragement_ready">🎯 The wish achievement system is ready for you! Start your first wish and unlock your exclusive achievement badges!</string>

    <!-- Notification Categories -->
    <string name="notification_category_task_management">Task Management</string>
    <string name="notification_category_task_management_desc">Manage task-related reminder notifications</string>
    <string name="notification_item_task_reminders">Task Reminders</string>
    <string name="notification_item_task_reminders_desc">Enable reminders before tasks are due</string>
    <string name="notification_item_deadline_reminders">Deadline Reminders</string>
    <string name="notification_item_deadline_reminders_desc">Notify when tasks are about to expire</string>
    <string name="notification_item_overdue_reminders">Overdue Task Reminders</string>
    <string name="notification_item_overdue_reminders_desc">Remind about overdue important tasks</string>

    <!-- Page Titles -->
    <string name="task_management">Task Management</string>
    <string name="time_melody">Time Melody</string>
    <string name="focus_mode_enter">Enter Focus Mode</string>
    <string name="focus_mode_exit">Exit Focus Mode</string>

    <!-- Search Suggestions -->
    <string name="search_suggestion_exercise">Exercise</string>
    <string name="search_suggestion_exercise_effect">Exercise Effect</string>
    <string name="search_suggestion_exercise_reflection">Exercise Reflection</string>
    <string name="search_suggestion_exercise_plan">Exercise Plan</string>
    <string name="search_suggestion_work">Work</string>
    <string name="search_suggestion_work_pressure">Work Pressure</string>
    <string name="search_suggestion_work_progress">Work Progress</string>
    <string name="search_suggestion_work_reflection">Work Reflection</string>
    <string name="search_suggestion_work_result">Work Result</string>
    <string name="search_suggestion_study">Study</string>
    <string name="search_suggestion_study_method">Study Method</string>
    <string name="search_suggestion_study_notes">Study Notes</string>
    <string name="search_suggestion_study_progress">Study Progress</string>
    <string name="search_suggestion_study_plan">Study Plan</string>
    <string name="search_suggestion_reading">Reading</string>
    <string name="search_suggestion_reading_notes">Reading Notes</string>
    <string name="search_suggestion_reading_thoughts">Reading Thoughts</string>
    <string name="search_suggestion_reading_plan">Reading Plan</string>
    <string name="search_suggestion_meditation">Meditation</string>
    <string name="search_suggestion_meditation_experience">Meditation Experience</string>
    <string name="search_suggestion_meditation_effect">Meditation Effect</string>
    <string name="search_suggestion_meditation_method">Meditation Method</string>

    <!-- Extended Search Suggestions -->
    <string name="search_suggestion_morning_run">Morning Run</string>
    <string name="search_suggestion_fitness">Fitness</string>
    <string name="search_suggestion_swimming">Swimming</string>
    <string name="search_suggestion_basketball">Basketball</string>
    <string name="search_suggestion_running">Running</string>
    <string name="search_suggestion_yoga">Yoga</string>
    <string name="search_suggestion_meeting">Meeting</string>
    <string name="search_suggestion_project">Project</string>
    <string name="search_suggestion_task">Task</string>
    <string name="search_suggestion_team">Team</string>
    <string name="search_suggestion_efficiency">Efficiency</string>
    <string name="search_suggestion_pressure">Pressure</string>
    <string name="search_suggestion_course">Course</string>
    <string name="search_suggestion_notes">Notes</string>
    <string name="search_suggestion_exam">Exam</string>
    <string name="search_suggestion_review">Review</string>
    <string name="search_suggestion_understand">Understand</string>
    <string name="search_suggestion_master">Master</string>
    <string name="search_suggestion_novel">Novel</string>
    <string name="search_suggestion_self_help">Self Help</string>
    <string name="search_suggestion_history">History</string>
    <string name="search_suggestion_science">Science</string>
    <string name="search_suggestion_psychology">Psychology</string>
    <string name="search_suggestion_philosophy">Philosophy</string>
    <string name="search_suggestion_breathing">Breathing</string>
    <string name="search_suggestion_focus">Focus</string>
    <string name="search_suggestion_relax">Relax</string>
    <string name="search_suggestion_introspection">Introspection</string>
    <string name="search_suggestion_clarity">Clarity</string>

    <!-- AI Suggestions -->
    <string name="ai_suggestion_weekly_title">Weekly Efficiency Improvement Suggestions</string>
    <string name="ai_suggestion_weekly_content">Based on your task completion this week, AI assistant suggests:\n\n1. 🎯 Focus Time Optimization: Schedule important tasks between 9-11 AM\n2. ⏰ Task Time Estimation: Consider adding 15% buffer time\n3. 🔄 Work Rhythm Adjustment: Work for 50 minutes, rest for 10 minutes for better results\n\nKeep up the good work habits!</string>
    <string name="ai_suggestion_monthly_title">Monthly Work Pattern Analysis</string>
    <string name="ai_suggestion_monthly_content">Based on your data analysis over the past month, here are some suggestions:\n\n1. 📈 Efficiency Trends: Your work efficiency is highest on Wednesdays and Thursdays\n2. 🎯 Goal Achievement: Consider breaking large goals into smaller milestones\n3. 💪 Habit Formation: You excel at maintaining habits, try adding new challenges\n\n%s, keep going!</string>
    <string name="ai_suggestion_smart_reminder_title">Smart Reminder</string>
    <string name="ai_suggestion_smart_reminder_content">Based on your usage patterns, AI assistant has recommended some optimization suggestions for you. Click to view details.</string>

    <!-- AI Suggestion Examples -->
    <string name="ai_suggestion_demo_morning_review">9:00 AM Review yesterday\'s work</string>
    <string name="ai_suggestion_demo_afternoon_meeting">3:00 PM Meeting</string>
    <string name="ai_suggestion_demo_conflict_message">Time conflict with task \'Project Discussion\'</string>
    <string name="ai_suggestion_demo_presentation">Prepare tomorrow\'s presentation</string>

    <!-- Search Related -->
    <string name="search_suggestions">Search Suggestions</string>
    <string name="you_might_be_looking_for">You might be looking for</string>

    <!-- Settings Page Titles and Descriptions -->
    <string name="theme_settings_desc">Customize app theme and colors</string>
    <string name="language_settings_desc">Choose app display language</string>
    <string name="notification_settings_desc">Manage app notifications and reminders</string>
    <string name="privacy_settings_desc">Manage privacy and security options</string>

    <!-- User Interface Related -->
    <string name="change_background">Change Background</string>
    <string name="remove_background">Remove Background</string>
    <string name="card_background">Card Background</string>
    <string name="user_avatar">User Avatar</string>
    <string name="avatar">Avatar</string>
    <string name="long_press_card_set_background">Long press card to set background image</string>
    <string name="click_avatar_to_change">Click avatar to change avatar</string>

    <!-- Membership Types -->
    <string name="lifetime_member">Lifetime Member</string>
    <string name="monthly_member">Monthly Member</string>
    <string name="regular_user">Regular User</string>

    <!-- Profile Page Related -->
    <string name="my_space">My Space</string>
    <string name="emotion_type_joy">Happy</string>
    <string name="emotion_type_calm">Calm</string>
    <string name="emotion_type_sad">Sad</string>
    <string name="emotion_type_angry">Angry</string>
    <string name="emotion_type_anxious">Anxious</string>

    <!-- Settings Page Related -->
    <string name="sync_settings">Sync Settings</string>
    <string name="sync_settings_desc">Manage data sync and cloud storage</string>
    <string name="data_management">Data Management</string>
    <string name="data_management_desc">Backup, restore, import/export and storage cleanup</string>
    <string name="ai_settings">AI Settings</string>
    <string name="ai_settings_desc">Customize AI service providers</string>
    <string name="about_desc">App version and information</string>

    <!-- Data Management Tabs -->
    <string name="backup">Backup</string>
    <string name="restore">Restore</string>
    <string name="import_export">Import/Export</string>

    <!-- Notification Settings Related -->
    <string name="reminder_time">Reminder Time</string>
    <string name="reminder_time_desc">Set general reminder advance time</string>
    <string name="default_reminder_time">Default Reminder Time</string>
    <string name="not_set">Not Set</string>

    <!-- Home Card Related -->
    <string name="wish_cloud">Wish Cloud</string>
    <string name="view_wish_pool">View Wish Pool</string>
    <string name="no_wishes_yet">No wishes yet, click to add your first wish!</string>
    <string name="add_wish">Add Wish</string>

    <!-- About Page Related -->
    <string name="core_features">Core Features</string>
    <string name="feature_ai_split">AI Smart Split</string>
    <string name="feature_ai_split_desc">Automatically break down complex tasks into executable subtasks</string>
    <string name="feature_time_tracking">Automatic Time Tracking</string>
    <string name="feature_time_tracking_desc">Intelligently record focus time and deeply analyze work habits</string>
    <string name="feature_personalized_theme">Personalized Theme</string>
    <string name="feature_personalized_theme_desc">Multiple theme colors to create your exclusive visual experience</string>
    <string name="feature_data_analysis">Data Statistics Analysis</string>
    <string name="feature_data_analysis_desc">Visual efficiency reports to understand time usage patterns</string>
    <string name="feature_calendar_sync">Calendar Sync</string>
    <string name="feature_calendar_sync_desc">Seamlessly integrate system calendar for unified time management</string>
    <string name="feature_emotion_record">Emotion Record</string>
    <string name="feature_emotion_record_desc">Record daily mood changes and focus on mental health</string>
    <string name="feature_goal_tracking">Goal Tracking</string>
    <string name="feature_goal_tracking_desc">Set goals and track progress for continuous self-improvement</string>
    <string name="feature_kanban_management">Kanban Management</string>
    <string name="feature_kanban_management_desc">Trello-like task board for intuitive workflow management</string>
</resources>
