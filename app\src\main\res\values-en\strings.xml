<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="app_name">TimeFlow</string>
    <string name="home">Home</string>
    <string name="tasks">Tasks</string>
    <string name="tasks_3d">3D Tasks</string>
    <string name="time">Time</string>
    <string name="analytics">Analytics</string>
    <string name="discover">Discover</string>
    <string name="profile">Profile</string>
    <string name="calendar">Calendar</string>
    <string name="wish_pool">Wish Pool</string>
    
    <!-- AI Assistant -->
    <string name="ai_assistant_header">AI Assistant</string>
    <string name="ai_review">AI Review</string>
    
    <!-- Menstrual Cycle -->
    <string name="menstrual_cycle">Menstrual Cycle</string>
    <string name="menstrual_period">Period</string>
    <string name="predicted_period">Predicted Period</string>
    <string name="fertile_period">Fertile Period</string>
    <string name="period_started">Did your period start?</string>
    <string name="sexual_activity">Sexual Activity</string>
    <string name="physical_symptoms">Physical Symptoms</string>
    <string name="cycle_view">Cycle View</string>
    <string name="calendar_view">Calendar View</string>
    <string name="day_x">Day %1$d</string>
    <string name="cycle_days">%1$d days total</string>
    <string name="prompt_input_hint">Enter your question...</string>
    
    <!-- Theme Settings -->
    <string name="theme_settings">Theme Settings</string>
    <string name="material_you">Material You</string>
    <string name="material_you_description">Enable dynamic colors</string>
    <string name="material_you_desc">Use dynamic color scheme, automatically generate theme colors based on wallpaper</string>
    <string name="color_scheme">Color Scheme</string>
    <string name="color_theme_settings">Color Theme Settings</string>
    <string name="custom_theme_colors">Custom Theme Colors</string>
    <string name="primary_color">Primary Color</string>
    <string name="primary_color_desc">Main accent color of the app</string>
    <string name="secondary_color">Secondary Color</string>
    <string name="secondary_color_desc">Secondary accent color of the theme</string>
    <string name="tertiary_color">Tertiary Color</string>
    <string name="tertiary_color_desc">Tertiary accent color of the theme</string>
    <string name="background_color">Background Color</string>
    <string name="background_color_desc">Default background color of the app</string>
    <string name="background_colors">Background Color Settings</string>
    <string name="unified_background">Unified Background</string>
    <string name="unified_background_desc">Use the same background color for all pages</string>
    
    <!-- Language Settings -->
    <string name="language_settings">Language Settings</string>
    <string name="app_language">App Language</string>
    <string name="app_language_desc">Choose your preferred language</string>
    <string name="follow_system">Follow System</string>
    <string name="simplified_chinese">Simplified Chinese</string>
    <string name="traditional_chinese">Traditional Chinese</string>
    <string name="english">English</string>
    <string name="language_changed">Language changed</string>
    <string name="language_change_desc">The app will restart to apply the new language</string>
    <string name="restart_now">Restart Now</string>
    <string name="restart_later">Restart Later</string>
    
    <!-- Settings -->
    <string name="settings">Settings</string>
    <string name="general_settings">General Settings</string>
    <string name="appearance_settings">Appearance Settings</string>
    <string name="notification_settings">Notification Settings</string>
    <string name="privacy_settings">Privacy Settings</string>
    <string name="about">About</string>
    <string name="version">Version</string>
    <string name="save">Save</string>
    <string name="cancel">Cancel</string>
    <string name="confirm">Confirm</string>
    <string name="ok">OK</string>
    <string name="yes">Yes</string>
    <string name="no">No</string>
    <string name="done">Done</string>
    <string name="edit">Edit</string>
    <string name="delete">Delete</string>
    <string name="add">Add</string>
    <string name="remove">Remove</string>
    <string name="clear">Clear</string>
    <string name="reset">Reset</string>
    <string name="apply">Apply</string>
    <string name="close">Close</string>
    <string name="back">Back</string>
    <string name="next">Next</string>
    <string name="previous">Previous</string>
    <string name="finish">Finish</string>
    <string name="loading">Loading...</string>
    <string name="error">Error</string>
    <string name="success">Success</string>
    <string name="warning">Warning</string>
    <string name="info">Information</string>

    <!-- Task Feedback -->
    <string name="feedback_feeling_tag">#Feeling</string>
    <string name="feedback_followup_tag">#Followup</string>
    <string name="feedback_difficulty_tag">#Difficulty</string>
    <string name="feedback_time_tag">#Time</string>

    <string name="feeling_accomplished">Feeling accomplished</string>
    <string name="feeling_struggling">A bit challenging</string>
    <string name="feeling_need_help">Need help</string>
    <string name="feeling_very_accomplished">Very accomplished</string>
    <string name="feeling_smooth">Completed smoothly</string>

    <string name="followup_need_tracking">Need follow-up</string>
    <string name="followup_completed_archived">Completed and archived</string>
    <string name="followup_to_share">To share</string>
    <string name="followup_need_improvement">Need improvement</string>
    <string name="followup_worth_recording">Worth recording</string>

    <string name="difficulty_easier_than_expected">Easier than expected</string>
    <string name="difficulty_as_expected">As expected</string>
    <string name="difficulty_harder_than_expected">Harder than expected</string>
    <string name="difficulty_need_more_learning">Need more learning</string>

    <string name="time_completed_early">Completed early</string>
    <string name="time_completed_on_time">Completed on time</string>
    <string name="time_slightly_delayed">Slightly delayed</string>
    <string name="time_significantly_delayed">Significantly delayed</string>

    <!-- AI Assistant -->
    <string name="set_ai_assistant_name">Set AI Assistant Name</string>
    <string name="ai_assistant_name">AI Assistant Name</string>
    <string name="ai_assistant_name_example">e.g.: Assistant</string>
    <string name="enter_ai_assistant_name">Enter AI assistant name</string>

    <!-- Task Feedback Input -->
    <string name="feedback_input_hint">Enter thoughts or use #tags for smart suggestions...</string>

    <!-- Date Time -->
    <string name="monday">Mon</string>
    <string name="tuesday">Tue</string>
    <string name="wednesday">Wed</string>
    <string name="thursday">Thu</string>
    <string name="friday">Fri</string>
    <string name="saturday">Sat</string>
    <string name="sunday">Sun</string>

    <string name="month_day_format">%1$s %2$d</string>
    <string name="week_month_day_format">%1$s · %2$s</string>

    <!-- Common Actions -->
    <string name="reflection">Reflection</string>

    <!-- Content Descriptions -->
    <string name="content_desc_reflection">Reflection</string>
    <string name="content_desc_settings">Settings</string>

    <!-- Wish Statistics Encouragement -->
    <string name="encouragement_master">🌟 You\'ve become a true wish master! Keep this passion, your energy will inspire others!</string>
    <string name="encouragement_amazing">🚀 Wow! Your wish fulfillment ability is amazing! Just a few more steps to master level!</string>
    <string name="encouragement_great">💫 Fantastic! You\'re on the fast track to wish fulfillment! Each achievement builds the foundation for the next!</string>
    <string name="encouragement_good_start">🌱 Great start! Your wish seeds are growing strong, keep this persistence and passion!</string>
    <string name="encouragement_progress">✨ Every step is progress! You\'ve started your wish fulfillment journey, believe in your power!</string>
    <string name="encouragement_ready">🎯 The wish achievement system is ready for you! Start your first wish and unlock your exclusive achievement badges!</string>

    <!-- Notification Categories -->
    <string name="notification_category_task_management">Task Management</string>
    <string name="notification_category_task_management_desc">Manage task-related reminder notifications</string>
    <string name="notification_item_task_reminders">Task Reminders</string>
    <string name="notification_item_task_reminders_desc">Enable reminders before tasks are due</string>
    <string name="notification_item_deadline_reminders">Deadline Reminders</string>
    <string name="notification_item_deadline_reminders_desc">Notify when tasks are about to expire</string>
    <string name="notification_item_overdue_reminders">Overdue Task Reminders</string>
    <string name="notification_item_overdue_reminders_desc">Remind about overdue important tasks</string>

    <!-- Page Titles -->
    <string name="task_management">Task Management</string>
    <string name="time_melody">Time Melody</string>
    <string name="focus_mode_enter">Enter Focus Mode</string>
    <string name="focus_mode_exit">Exit Focus Mode</string>

    <!-- Search Suggestions -->
    <string name="search_suggestion_exercise">Exercise</string>
    <string name="search_suggestion_exercise_effect">Exercise Effect</string>
    <string name="search_suggestion_exercise_reflection">Exercise Reflection</string>
    <string name="search_suggestion_exercise_plan">Exercise Plan</string>
    <string name="search_suggestion_work">Work</string>
    <string name="search_suggestion_work_pressure">Work Pressure</string>
    <string name="search_suggestion_work_progress">Work Progress</string>
    <string name="search_suggestion_work_reflection">Work Reflection</string>
    <string name="search_suggestion_work_result">Work Result</string>
    <string name="search_suggestion_study">Study</string>
    <string name="search_suggestion_study_method">Study Method</string>
    <string name="search_suggestion_study_notes">Study Notes</string>
    <string name="search_suggestion_study_progress">Study Progress</string>
    <string name="search_suggestion_study_plan">Study Plan</string>
    <string name="search_suggestion_reading">Reading</string>
    <string name="search_suggestion_reading_notes">Reading Notes</string>
    <string name="search_suggestion_reading_thoughts">Reading Thoughts</string>
    <string name="search_suggestion_reading_plan">Reading Plan</string>
    <string name="search_suggestion_meditation">Meditation</string>
    <string name="search_suggestion_meditation_experience">Meditation Experience</string>
    <string name="search_suggestion_meditation_effect">Meditation Effect</string>
    <string name="search_suggestion_meditation_method">Meditation Method</string>

    <!-- Extended Search Suggestions -->
    <string name="search_suggestion_morning_run">Morning Run</string>
    <string name="search_suggestion_fitness">Fitness</string>
    <string name="search_suggestion_swimming">Swimming</string>
    <string name="search_suggestion_basketball">Basketball</string>
    <string name="search_suggestion_running">Running</string>
    <string name="search_suggestion_yoga">Yoga</string>
    <string name="search_suggestion_meeting">Meeting</string>
    <string name="search_suggestion_project">Project</string>
    <string name="search_suggestion_task">Task</string>
    <string name="search_suggestion_team">Team</string>
    <string name="search_suggestion_efficiency">Efficiency</string>
    <string name="search_suggestion_pressure">Pressure</string>
    <string name="search_suggestion_course">Course</string>
    <string name="search_suggestion_notes">Notes</string>
    <string name="search_suggestion_exam">Exam</string>
    <string name="search_suggestion_review">Review</string>
    <string name="search_suggestion_understand">Understand</string>
    <string name="search_suggestion_master">Master</string>
    <string name="search_suggestion_novel">Novel</string>
    <string name="search_suggestion_self_help">Self Help</string>
    <string name="search_suggestion_history">History</string>
    <string name="search_suggestion_science">Science</string>
    <string name="search_suggestion_psychology">Psychology</string>
    <string name="search_suggestion_philosophy">Philosophy</string>
    <string name="search_suggestion_breathing">Breathing</string>
    <string name="search_suggestion_focus">Focus</string>
    <string name="search_suggestion_relax">Relax</string>
    <string name="search_suggestion_introspection">Introspection</string>
    <string name="search_suggestion_clarity">Clarity</string>

    <!-- AI Suggestions -->
    <string name="ai_suggestion_weekly_title">Weekly Efficiency Improvement Suggestions</string>
    <string name="ai_suggestion_weekly_content">Based on your task completion this week, AI assistant suggests:\n\n1. 🎯 Focus Time Optimization: Schedule important tasks between 9-11 AM\n2. ⏰ Task Time Estimation: Consider adding 15% buffer time\n3. 🔄 Work Rhythm Adjustment: Work for 50 minutes, rest for 10 minutes for better results\n\nKeep up the good work habits!</string>
    <string name="ai_suggestion_monthly_title">Monthly Work Pattern Analysis</string>
    <string name="ai_suggestion_monthly_content">Based on your data analysis over the past month, here are some suggestions:\n\n1. 📈 Efficiency Trends: Your work efficiency is highest on Wednesdays and Thursdays\n2. 🎯 Goal Achievement: Consider breaking large goals into smaller milestones\n3. 💪 Habit Formation: You excel at maintaining habits, try adding new challenges\n\n%s, keep going!</string>
    <string name="ai_suggestion_smart_reminder_title">Smart Reminder</string>
    <string name="ai_suggestion_smart_reminder_content">Based on your usage patterns, AI assistant has recommended some optimization suggestions for you. Click to view details.</string>

    <!-- AI Suggestion Examples -->
    <string name="ai_suggestion_demo_morning_review">9:00 AM Review yesterday\'s work</string>
    <string name="ai_suggestion_demo_afternoon_meeting">3:00 PM Meeting</string>
    <string name="ai_suggestion_demo_conflict_message">Time conflict with task \'Project Discussion\'</string>
    <string name="ai_suggestion_demo_presentation">Prepare tomorrow\'s presentation</string>

    <!-- Search Related -->
    <string name="search_suggestions">Search Suggestions</string>
    <string name="you_might_be_looking_for">You might be looking for</string>

    <!-- Settings Page Titles and Descriptions -->
    <string name="theme_settings_desc">Customize app theme and colors</string>
    <string name="language_settings_desc">Choose app display language</string>
    <string name="notification_settings_desc">Manage app notifications and reminders</string>
    <string name="privacy_settings_desc">Manage privacy and security options</string>

    <!-- User Interface Related -->
    <string name="change_background">Change Background</string>
    <string name="remove_background">Remove Background</string>
    <string name="card_background">Card Background</string>
    <string name="user_avatar">User Avatar</string>
    <string name="avatar">Avatar</string>
    <string name="long_press_card_set_background">Long press card to set background image</string>
    <string name="click_avatar_to_change">Click avatar to change avatar</string>

    <!-- Membership Types -->
    <string name="lifetime_member">Lifetime Member</string>
    <string name="monthly_member">Monthly Member</string>
    <string name="regular_user">Regular User</string>

    <!-- Profile Page Related -->
    <string name="my_space">My Space</string>
    <string name="emotion_type_joy">Happy</string>
    <string name="emotion_type_calm">Calm</string>
    <string name="emotion_type_sad">Sad</string>
    <string name="emotion_type_angry">Angry</string>
    <string name="emotion_type_anxious">Anxious</string>

    <!-- Settings Page Related -->
    <string name="sync_settings">Sync Settings</string>
    <string name="sync_settings_desc">Manage data sync and cloud storage</string>
    <string name="data_management">Data Management</string>
    <string name="data_management_desc">Backup, restore, import/export and storage cleanup</string>
    <string name="ai_settings">AI Settings</string>
    <string name="ai_settings_desc">Customize AI service providers</string>
    <string name="about_desc">App version and information</string>

    <!-- Data Management Tabs -->
    <string name="backup">Backup</string>
    <string name="restore">Restore</string>
    <string name="import_export">Import/Export</string>

    <!-- Notification Settings Related -->
    <string name="reminder_time">Reminder Time</string>
    <string name="reminder_time_desc">Set general reminder advance time</string>
    <string name="default_reminder_time">Default Reminder Time</string>
    <string name="not_set">Not Set</string>

    <!-- Home Card Related -->
    <string name="wish_cloud">Wish Cloud</string>
    <string name="view_wish_pool">View Wish Pool</string>
    <string name="no_wishes_yet">No wishes yet, click to add your first wish!</string>
    <string name="add_wish">Add Wish</string>

    <!-- About Page Related -->
    <string name="core_features">Core Features</string>
    <string name="feature_ai_split">AI Smart Split</string>
    <string name="feature_ai_split_desc">Automatically break down complex tasks into executable subtasks</string>
    <string name="feature_time_tracking">Automatic Time Tracking</string>
    <string name="feature_time_tracking_desc">Intelligently record focus time and deeply analyze work habits</string>
    <string name="feature_personalized_theme">Personalized Theme</string>
    <string name="feature_personalized_theme_desc">Multiple theme colors to create your exclusive visual experience</string>
    <string name="feature_data_analysis">Data Statistics Analysis</string>
    <string name="feature_data_analysis_desc">Visual efficiency reports to understand time usage patterns</string>
    <string name="feature_calendar_sync">Calendar Sync</string>
    <string name="feature_calendar_sync_desc">Seamlessly integrate system calendar for unified time management</string>
    <string name="feature_emotion_record">Emotion Record</string>
    <string name="feature_emotion_record_desc">Record daily mood changes and focus on mental health</string>
    <string name="feature_goal_tracking">Goal Tracking</string>
    <string name="feature_goal_tracking_desc">Set goals and track progress for continuous self-improvement</string>
    <string name="feature_kanban_management">Kanban Management</string>
    <string name="feature_kanban_management_desc">Trello-like task board for intuitive workflow management</string>

    <!-- Notification Settings Page -->
    <string name="enable_notifications">Enable Notifications</string>
    <string name="enable_notifications_desc">Master switch for all app notifications</string>
    <string name="notifications_disabled_hint">No reminder notifications will be received when disabled</string>
    <string name="task_reminders">Task Reminders</string>
    <string name="task_reminders_desc">Manage task-related reminder notifications</string>
    <string name="task_reminders_enable">Task Reminders</string>
    <string name="task_reminders_enable_desc">Enable reminders before tasks are due</string>
    <string name="deadline_reminders">Deadline Reminders</string>
    <string name="deadline_reminders_desc">Notify when tasks are about to expire</string>
    <string name="overdue_reminders">Overdue Task Reminders</string>
    <string name="overdue_reminders_desc">Remind about overdue important tasks</string>
    <string name="notification_sound">Notification Sound</string>
    <string name="notification_sound_desc">Choose notification alert tone</string>
    <string name="notification_vibration">Vibration Alert</string>
    <string name="notification_vibration_desc">Vibrate when notifications arrive</string>
    <string name="quiet_hours">Quiet Hours</string>
    <string name="quiet_hours_desc">Set do not disturb time period</string>
    <string name="daily_review_notification">Daily Review Notification</string>
    <string name="daily_review_notification_desc">Daily scheduled review reminders</string>

    <!-- Sync Settings Page -->
    <string name="sync_settings_title">Sync Settings</string>
    <string name="cloud_storage_config">Cloud Storage Configuration</string>
    <string name="sync_status">Sync Status</string>
    <string name="last_sync_time">Last Sync Time</string>
    <string name="manual_sync">Manual Sync</string>
    <string name="auto_sync">Auto Sync</string>
    <string name="sync_frequency">Sync Frequency</string>
    <string name="sync_on_wifi_only">Sync on WiFi Only</string>
    <string name="sync_enabled">Sync Enabled</string>
    <string name="sync_disabled">Sync Disabled</string>
    <string name="sync_in_progress">Syncing...</string>
    <string name="sync_success">Sync Successful</string>
    <string name="sync_failed">Sync Failed</string>
    <string name="never_synced">Never Synced</string>
    <string name="cloud_provider">Cloud Storage Provider</string>
    <string name="configure_cloud_storage">Configure Cloud Storage</string>

    <!-- Data Management Page -->
    <string name="data_management_title">Data Management</string>
    <string name="backup_tab">Backup</string>
    <string name="restore_tab">Restore</string>
    <string name="import_export_tab">Import/Export</string>
    <string name="manual_backup">Manual Backup</string>
    <string name="auto_backup">Auto Backup</string>
    <string name="backup_frequency">Backup Frequency</string>
    <string name="backup_now">Backup Now</string>
    <string name="restore_backup">Restore Backup</string>
    <string name="delete_backup">Delete Backup</string>
    <string name="quick_restore">Quick Restore</string>
    <string name="export_data">Export Data</string>
    <string name="import_data">Import Data</string>
    <string name="backup_created">Backup Created</string>
    <string name="backup_restored">Backup Restored</string>
    <string name="backup_deleted">Backup Deleted</string>
    <string name="no_backups_available">No Backups Available</string>
    <string name="storage_usage">Storage Usage</string>

    <!-- AI Settings Page -->
    <string name="ai_settings_title">AI Settings</string>
    <string name="ai_provider">AI Service Provider</string>
    <string name="api_key">API Key</string>
    <string name="api_endpoint">API Endpoint</string>
    <string name="model_selection">Model Selection</string>
    <string name="test_connection">Test Connection</string>
    <string name="connection_successful">Connection Successful</string>
    <string name="connection_failed">Connection Failed</string>
    <string name="ai_features">AI Features</string>
    <string name="smart_suggestions">Smart Suggestions</string>
    <string name="task_breakdown">Task Breakdown</string>
    <string name="auto_categorization">Auto Categorization</string>
    <string name="ai_chat">AI Chat</string>
    <string name="response_language">Response Language</string>
    <string name="creativity_level">Creativity Level</string>
    <string name="conservative">Conservative</string>
    <string name="balanced">Balanced</string>
    <string name="creative">Creative</string>

    <!-- About Page -->
    <string name="about_title">About</string>
    <string name="app_version">App Version</string>
    <string name="build_number">Build Number</string>
    <string name="developer">Developer</string>
    <string name="contact_us">Contact Us</string>
    <string name="feedback">Feedback</string>
    <string name="rate_app">Rate App</string>
    <string name="share_app">Share App</string>
    <string name="privacy_policy">Privacy Policy</string>
    <string name="terms_of_service">Terms of Service</string>
    <string name="open_source_licenses">Open Source Licenses</string>
    <string name="check_for_updates">Check for Updates</string>
    <string name="latest_version">Latest Version</string>
    <string name="update_available">Update Available</string>

    <!-- Menstrual Cycle Page -->
    <string name="cycle_tracking">Cycle Tracking</string>
    <string name="period_start">Period Start</string>
    <string name="period_end">Period End</string>
    <string name="cycle_length">Cycle Length</string>
    <string name="period_length">Period Length</string>
    <string name="ovulation_day">Ovulation Day</string>
    <string name="fertile_window">Fertile Window</string>
    <string name="next_period">Next Period</string>
    <string name="cycle_history">Cycle History</string>
    <string name="symptoms">Symptoms</string>
    <string name="mood">Mood</string>
    <string name="flow_intensity">Flow Intensity</string>
    <string name="light_flow">Light</string>
    <string name="medium_flow">Medium</string>
    <string name="heavy_flow">Heavy</string>
    <string name="cycle_insights">Cycle Insights</string>
    <string name="average_cycle">Average Cycle</string>
    <string name="cycle_regularity">Cycle Regularity</string>
    <string name="predictions">Predictions</string>

    <!-- Medication Record Page -->
    <string name="medication_record">Medication Record</string>
    <string name="add_medication">Add Medication</string>
    <string name="medication_name">Medication Name</string>
    <string name="dosage">Dosage</string>
    <string name="frequency">Frequency</string>
    <string name="start_date">Start Date</string>
    <string name="end_date">End Date</string>
    <string name="medication_time">Medication Time</string>
    <string name="before_meal">Before Meal</string>
    <string name="after_meal">After Meal</string>
    <string name="with_meal">With Meal</string>
    <string name="medication_notes">Medication Notes</string>
    <string name="side_effects">Side Effects</string>
    <string name="medication_reminder">Medication Reminder</string>
    <string name="daily_medication">Daily Medication</string>
    <string name="weekly_medication">Weekly Medication</string>
    <string name="as_needed">As Needed</string>
    <string name="medication_history">Medication History</string>
    <string name="missed_dose">Missed Dose</string>
    <string name="taken">Taken</string>
    <string name="medication_adherence">Medication Adherence</string>

    <!-- Goals Page -->
    <string name="goals">Goals</string>
    <string name="add_goal">Add Goal</string>
    <string name="goal_title">Goal Title</string>
    <string name="goal_description">Goal Description</string>
    <string name="goal_category">Goal Category</string>
    <string name="annual_goals">Annual Goals</string>
    <string name="monthly_goals">Monthly Goals</string>
    <string name="weekly_goals">Weekly Goals</string>
    <string name="daily_goals">Daily Goals</string>
    <string name="project_goals">Project Goals</string>
    <string name="personal_goals">Personal Goals</string>
    <string name="work_goals">Work Goals</string>
    <string name="health_goals">Health Goals</string>
    <string name="learning_goals">Learning Goals</string>
    <string name="goal_progress">Goal Progress</string>
    <string name="goal_deadline">Goal Deadline</string>
    <string name="goal_priority">Goal Priority</string>
    <string name="goal_status">Goal Status</string>
    <string name="not_started">Not Started</string>
    <string name="in_progress">In Progress</string>
    <string name="completed">Completed</string>
    <string name="paused">Paused</string>
    <string name="cancelled">Cancelled</string>
    <string name="goal_milestones">Goal Milestones</string>
    <string name="add_milestone">Add Milestone</string>
    <string name="milestone_achieved">Milestone Achieved</string>
    <string name="goal_insights">Goal Insights</string>
    <string name="completion_rate">Completion Rate</string>
    <string name="time_to_completion">Time to Completion</string>

    <!-- New Theme Settings -->
    <string name="hex_color_value">Hex Color Value</string>
    <string name="invalid_hex_color_value">Please enter a valid hex color value (e.g., #A8B8B8)</string>

    <!-- AI Settings -->
    <string name="select_ai_assistant_avatar">Select AI Assistant Avatar</string>
    <string name="select_prompt_emoji">Select Prompt Emoji</string>
    <string name="ai_assistant_personalization_settings">AI Assistant Personalization Settings</string>

    <!-- Task List -->
    <string name="completed_list">Completed</string>
    <string name="all_tasks">All</string>
    <string name="no_time_set">No time set</string>
    <string name="recognition_failed_retry">Recognition failed, please try again</string>

    <!-- Notification Settings -->
    <string name="review_time">Review Time</string>
    <string name="task_persistent_notification">Task Persistent Notification</string>
    <string name="task_persistent_notification_desc">Show today\'s main tasks in notification bar, excluding subtasks</string>
    <string name="habit_reminders">Habit Reminders</string>
    <string name="habit_reminders_desc">Manage habit-related reminders</string>
    <string name="habit_reminders_enable">Habit Reminders</string>
    <string name="habit_reminders_enable_desc">Timely reminders to develop good habits</string>
    <string name="focus_reminders">Focus Reminders</string>
    <string name="focus_reminders_desc">Manage focus time related reminders</string>
    <string name="focus_reminders_enable">Focus Reminders</string>
    <string name="focus_reminders_enable_desc">Notifications when focus time starts and ends</string>
    <string name="set_daily_review_time">Set Daily Review Time</string>
    <string name="set_dnd_start_time">Set Do Not Disturb Start Time</string>
    <string name="set_dnd_end_time">Set Do Not Disturb End Time</string>
    <string name="select_time">Select Time</string>

    <!-- Sync Settings -->
    <string name="data_sync">Data Sync</string>
    <string name="connected">Connected</string>
    <string name="connecting">Connecting...</string>
    <string name="disconnected">Disconnected</string>
    <string name="last_sync_time_format">Last sync: %s</string>
    <string name="sync_now">Sync Now</string>

    <!-- Data Management -->
    <string name="delete_backup_confirm">Are you sure you want to delete this backup file? This action cannot be undone.</string>
    <string name="app_data">App Data</string>
    <string name="backup_files">Backup Files</string>
    <string name="cache">Cache</string>
    <string name="one_click_restore">One-Click Restore</string>
    <string name="restore_to_latest_backup">Restore to latest backup version</string>
    <string name="create_backup_first">Please create a backup first</string>
    <string name="data_format_info">• JSON format: Complete data structure\n• CSV format: Tabular data (tasks, habits, etc.)\n• Backup files: .db format database files</string>

    <!-- Goal Analysis -->
    <string name="completed_goals">Completed Goals</string>
    <string name="in_progress_goals">In Progress Goals</string>
    <string name="average_completion_rate">Average Completion Rate</string>
    <string name="fastest_completion">Fastest Completion</string>
    <string name="fastest_completion_example">30-day Reading Challenge (6 days early)</string>
    <string name="longest_goal">Longest Goal</string>
    <string name="longest_goal_example">Language Learning (8 months)</string>
    <string name="best_persistence">Best Persistence</string>
    <string name="best_persistence_example">Morning Run Plan (96%)</string>
    <string name="achievement_perseverance">Perseverance Star</string>
    <string name="achievement_efficiency">Efficiency Expert</string>
    <string name="achievement_breakthrough">Self Breakthrough</string>
    <string name="achievement_focus">Focus Master</string>
    <string name="achievement_balance">Balanced Development</string>
    <string name="time_dimension">Time Dimension</string>
    <string name="domain_dimension">Domain Dimension</string>
    <string name="difficulty_dimension">Difficulty Dimension</string>
    <string name="year">Year</string>
    <string name="quarter">Quarter</string>
    <string name="month">Month</string>
    <string name="yearly">Annual</string>
    <string name="quarterly">Quarterly</string>
    <string name="analysis">Analysis</string>
    <string name="select_time_dimension">Select Time Dimension:</string>
    <string name="no_domain_data">No domain data</string>
    <string name="no_difficulty_data">No difficulty data</string>
    <string name="goal_difficulty_distribution_completion_rate">Goal Difficulty Distribution &amp; Completion Rate</string>

    <!-- Data Analysis -->
    <string name="daily_efficiency_trend">Daily Efficiency Trend</string>

    <!-- Reflection -->

    <!-- Habit Tracking -->
    <string name="habit_tracking">Habit Tracking</string>
    <string name="add_habit">Add Habit</string>
    <string name="no_habits_in_category">No habits in this category</string>
    <string name="no_habits">No habits</string>
    <string name="add_habit_hint">Tap the + button in the top right to add a new habit</string>
    <string name="habit_archived_message">Habit \"%1$s\" has been archived</string>
    <string name="habit_deleted_message">Habit \"%1$s\" has been deleted</string>
    <string name="undo">Undo</string>

    <!-- Wish Pool -->
    <string name="my_wish_pool">My Wish Pool</string>
    <string name="dreams_come_true">Dreams come true ✨</string>
    <string name="about_to_delete_wish">About to delete wish:</string>
    <string name="delete_warning_message">Cannot be recovered after deletion, please operate with caution</string>
    <string name="wish_universe">Wish Universe</string>
    <string name="manifest_your_wishes">Manifest your beautiful wishes ✨</string>

    <!-- Time Tracking -->
    <string name="break_time">Break Time</string>
    <string name="pomodoro_count">Pomodoro %1$d</string>

    <!-- Language Settings -->
    <string name="current_system_language">Current system language: %1$s</string>

    <!-- Notification Settings -->
    <string name="send_test_notification">Send Test Notification</string>
    <string name="focus_session_notifications">Focus Session Notifications</string>
    <string name="focus_session_notifications_desc">Notify when focus session status changes</string>
    <string name="health_management">Health Management</string>
    <string name="health_management_desc">Manage health-related reminder notifications</string>
    <string name="medication_reminders">Medication Reminders</string>
    <string name="medication_reminders_desc">Remind to take medication on time, stay healthy</string>
    <string name="medication_reminder_sound">Medication Reminder Sound</string>
    <string name="medication_reminder_sound_desc">Play sound for medication reminders</string>
    <string name="medication_reminder_vibration">Medication Reminder Vibration</string>
    <string name="medication_reminder_vibration_desc">Device vibration for medication reminders</string>
    <string name="medication_reminder_advance_time">Medication Reminder Advance Time</string>
    <string name="notification_methods">Notification Methods</string>
    <string name="notification_methods_desc">Choose notification presentation forms</string>
    <string name="sound_reminders">Sound Reminders</string>
    <string name="sound_reminders_desc">Play notification sounds</string>
    <string name="vibration_reminders">Vibration Reminders</string>
    <string name="vibration_reminders_desc">Device vibration feedback</string>
    <string name="do_not_disturb">Do Not Disturb</string>
    <string name="do_not_disturb_desc">Set quiet periods to avoid notification interruptions</string>
    <string name="do_not_disturb_mode">Do Not Disturb Mode</string>

    <!-- Data Management -->
    <string name="restore_success">Restore Successful</string>
    <string name="restore_success_message">Data has been restored, please restart the app to load the restored data</string>
    <string name="available_backups">Available Backups (%1$d)</string>
    <string name="last_backup_time">Last backup: %1$s</string>
    <string name="no_backup_yet">No backup has been performed yet</string>
    <string name="backing_up">Backing up...</string>
    <string name="start_backup">Start Backup</string>
    <string name="restoring">Restoring...</string>
    <string name="restore_now">Restore Now</string>
    <string name="restore">Restore</string>
    <string name="no_backup_files">No backup files</string>
    <string name="create_first_backup">Create your first backup to protect your data</string>
    <string name="export_data">Export Data</string>
    <string name="export_data_desc">Export data in readable format</string>
    <string name="exporting">Exporting...</string>
    <string name="start_export">Start Export</string>
    <string name="import_data">Import Data</string>
    <string name="import_data_desc">Import data from file</string>
    <string name="importing">Importing...</string>
    <string name="select_file">Select File</string>
    <string name="backup_history">Backup History</string>
    <string name="no_backup_records">No backup records</string>
</resources>
