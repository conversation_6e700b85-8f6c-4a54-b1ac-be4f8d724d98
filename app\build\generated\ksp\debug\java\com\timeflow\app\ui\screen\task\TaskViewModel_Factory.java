package com.timeflow.app.ui.screen.task;

import com.timeflow.app.data.repository.TaskRepository;
import com.timeflow.app.ui.screen.reflection.ReflectionRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class TaskViewModel_Factory implements Factory<TaskViewModel> {
  private final Provider<TaskRepository> taskRepositoryProvider;

  private final Provider<ReflectionRepository> reflectionRepositoryProvider;

  public TaskViewModel_Factory(Provider<TaskRepository> taskRepositoryProvider,
      Provider<ReflectionRepository> reflectionRepositoryProvider) {
    this.taskRepositoryProvider = taskRepositoryProvider;
    this.reflectionRepositoryProvider = reflectionRepositoryProvider;
  }

  @Override
  public TaskViewModel get() {
    return newInstance(taskRepositoryProvider.get(), reflectionRepositoryProvider.get());
  }

  public static TaskViewModel_Factory create(Provider<TaskRepository> taskRepositoryProvider,
      Provider<ReflectionRepository> reflectionRepositoryProvider) {
    return new TaskViewModel_Factory(taskRepositoryProvider, reflectionRepositoryProvider);
  }

  public static TaskViewModel newInstance(TaskRepository taskRepository,
      ReflectionRepository reflectionRepository) {
    return new TaskViewModel(taskRepository, reflectionRepository);
  }
}
