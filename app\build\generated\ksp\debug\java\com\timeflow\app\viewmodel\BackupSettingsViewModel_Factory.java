package com.timeflow.app.viewmodel;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class BackupSettingsViewModel_Factory implements Factory<BackupSettingsViewModel> {
  private final Provider<Context> contextProvider;

  public BackupSettingsViewModel_Factory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public BackupSettingsViewModel get() {
    return newInstance(contextProvider.get());
  }

  public static BackupSettingsViewModel_Factory create(Provider<Context> contextProvider) {
    return new BackupSettingsViewModel_Factory(contextProvider);
  }

  public static BackupSettingsViewModel newInstance(Context context) {
    return new BackupSettingsViewModel(context);
  }
}
