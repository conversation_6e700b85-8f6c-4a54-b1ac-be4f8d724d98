package com.timeflow.app.data.repository;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SharedPendingDeletionState_Factory implements Factory<SharedPendingDeletionState> {
  private final Provider<Context> contextProvider;

  private final Provider<TaskRepository> taskRepositoryProvider;

  public SharedPendingDeletionState_Factory(Provider<Context> contextProvider,
      Provider<TaskRepository> taskRepositoryProvider) {
    this.contextProvider = contextProvider;
    this.taskRepositoryProvider = taskRepositoryProvider;
  }

  @Override
  public SharedPendingDeletionState get() {
    return newInstance(contextProvider.get(), taskRepositoryProvider.get());
  }

  public static SharedPendingDeletionState_Factory create(Provider<Context> contextProvider,
      Provider<TaskRepository> taskRepositoryProvider) {
    return new SharedPendingDeletionState_Factory(contextProvider, taskRepositoryProvider);
  }

  public static SharedPendingDeletionState newInstance(Context context,
      TaskRepository taskRepository) {
    return new SharedPendingDeletionState(context, taskRepository);
  }
}
