package com.timeflow.app.di;

import com.timeflow.app.data.dao.WishDao;
import com.timeflow.app.data.db.AppDatabase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class WishModule_ProvideWishDaoFactory implements Factory<WishDao> {
  private final Provider<AppDatabase> databaseProvider;

  public WishModule_ProvideWishDaoFactory(Provider<AppDatabase> databaseProvider) {
    this.databaseProvider = databaseProvider;
  }

  @Override
  public WishDao get() {
    return provideWishDao(databaseProvider.get());
  }

  public static WishModule_ProvideWishDaoFactory create(Provider<AppDatabase> databaseProvider) {
    return new WishModule_ProvideWishDaoFactory(databaseProvider);
  }

  public static WishDao provideWishDao(AppDatabase database) {
    return Preconditions.checkNotNullFromProvides(WishModule.INSTANCE.provideWishDao(database));
  }
}
