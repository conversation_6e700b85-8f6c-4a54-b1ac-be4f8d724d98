package com.timeflow.app.ui.screen.profile

import android.app.Activity
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material.icons.outlined.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavController
import com.timeflow.app.R
import com.timeflow.app.utils.SystemBarManager
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.TextStyle
import kotlin.math.roundToInt
import com.timeflow.app.ui.navigation.AppDestinations
import android.util.Log
import androidx.navigation.compose.rememberNavController
import androidx.compose.runtime.compositionLocalOf
import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import kotlinx.parcelize.RawValue
import androidx.hilt.navigation.compose.hiltViewModel
import com.timeflow.app.ui.viewmodel.HabitViewModel
import com.timeflow.app.ui.screen.health.HabitData
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.foundation.border
import androidx.compose.runtime.collectAsState

// LocalNavController定义
val LocalNavController = compositionLocalOf<NavController> { error("NavController not provided") }

/**
 * 情绪类型
 */
@Parcelize
enum class EmotionType(
    val title: String,
    val icon: @RawValue ImageVector,
    val color: @RawValue Color,
    val emoji: String,
    val displayName: String
) : Parcelable {
    JOY("joy", Icons.Outlined.SentimentVerySatisfied, Color(0xFFa8d8a8), "😊", "joy"),
    CALM("calm", Icons.Outlined.SentimentSatisfied, Color(0xFF9ed0e6), "😐", "calm"),
    SAD("sad", Icons.Outlined.SentimentDissatisfied, Color(0xFFf5e1a4), "😢", "sad"),
    ANGRY("angry", Icons.Outlined.SentimentVeryDissatisfied, Color(0xFFf5c4b8), "😡", "angry"),
    ANXIOUS("anxious", Icons.Outlined.Face, Color(0xFFd7b8e0), "😰", "anxious")
}

/**
 * 情绪记录
 */
@Parcelize
data class EmotionRecord(
    val date: LocalDate,
    val emotion: EmotionType,
    val triggers: List<String> = emptyList(),
    val mindfulnessNote: String = "",
    val imageUri: String? = null,
    val audioUri: String? = null,
    val isDetailed: Boolean = false
) : Parcelable



/**
 * 我的页面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ProfileScreen(
    navController: NavController,
    modifier: Modifier = Modifier,
    habitViewModel: HabitViewModel = hiltViewModel(),
    profileViewModel: ProfileViewModel = hiltViewModel() // 🔧 添加ProfileViewModel
) {
    val context = LocalContext.current
    val activity = remember { context as? Activity }
    val today = remember { LocalDate.now() }

    // 🔧 使用ViewModel的状态
    val emotionRecords by profileViewModel.emotionRecords.collectAsState()
    val isLoading by profileViewModel.isLoading.collectAsState()
    val error by profileViewModel.error.collectAsState()

    // 本地UI状态
    var selectedEmotion by remember { mutableStateOf<EmotionType?>(null) }
    
    // 🔧 数据迁移：如果数据库为空且需要示例数据，可以通过ViewModel迁移
    LaunchedEffect(Unit) {
        // 检查是否需要添加示例数据（仅在开发测试时）
        if (emotionRecords.isEmpty()) {
            val sampleData = listOf(
                EmotionRecord(today.minusDays(1), EmotionType.JOY),
                EmotionRecord(today.minusDays(2), EmotionType.CALM),
                EmotionRecord(today.minusDays(3), EmotionType.SAD),
                EmotionRecord(today.minusDays(5), EmotionType.ANGRY),
                EmotionRecord(today.minusDays(7), EmotionType.ANXIOUS)
            )
            // 🔧 使用ViewModel的迁移方法添加示例数据
            profileViewModel.migrateExistingEmotionRecords(sampleData)
        }
    }
    
    // 🔥 新增：监听习惯数据变化，确保ProfileScreen中的习惯卡片与最新数据同步
    LaunchedEffect(Unit) {
        Log.d("ProfileScreen", "ProfileScreen初始化，刷新习惯数据")
        habitViewModel.refreshHabits()
    }
    
    // 接收从详细记录页面返回的结果
    val savedStateHandle = navController.currentBackStackEntry?.savedStateHandle
    LaunchedEffect(savedStateHandle) {
        try {
            // 尝试获取完整EmotionRecord对象
            savedStateHandle?.get<EmotionRecord>("emotionRecord")?.let { newRecord ->
                // 🔧 使用ViewModel保存新的情绪记录
                profileViewModel.saveEmotionRecord(newRecord)
                // 消费掉这个结果，避免重复处理
                savedStateHandle.remove<EmotionRecord>("emotionRecord")
                // 清空选中的情绪
                selectedEmotion = null
            }
        } catch (e: Exception) {
            // 如果获取完整对象失败，尝试使用简单数据
            if (savedStateHandle?.get<Boolean>("emotion_recorded") == true) {
                // 从简单数据创建记录
                val emotionTypeName = savedStateHandle.get<String>("emotion_type") ?: ""
                val dateString = savedStateHandle.get<String>("emotion_date") ?: ""
                
                try {
                    // 尝试解析情绪类型和日期
                    val emotionType = EmotionType.valueOf(emotionTypeName)
                    val date = if (dateString.isNotEmpty())
                        LocalDate.parse(dateString) else LocalDate.now()

                    // 🔧 创建简化的记录对象并保存到数据库
                    val simpleRecord = EmotionRecord(date, emotionType, isDetailed = true)
                    profileViewModel.saveEmotionRecord(simpleRecord)

                    // 清理状态
                    savedStateHandle.apply {
                        remove<Boolean>("emotion_recorded")
                        remove<String>("emotion_type")
                        remove<String>("emotion_date")
                    }
                    selectedEmotion = null
                } catch (e: Exception) {
                    // 记录解析错误
                    android.util.Log.e("TimeFlow", "解析情绪记录失败", e)
                }
            }
        }
    }
    
    // 使用更安全的状态栏实现
    SideEffect {
        activity?.let { 
            SystemBarManager.forceOpaqueStatusBar(it)  // 使用不透明黑色状态栏
        }
    }
    
    activity?.let { act ->
        DisposableEffect(key1 = Unit) {
            val window = act.window
            
            // 保存原始值以在dispose时恢复
            val originalStatusBarColor = window.statusBarColor
            
            // 应用不透明状态栏设置
            SystemBarManager.forceOpaqueStatusBar(act)
            
            onDispose {
                // 恢复原始状态栏颜色
                window.statusBarColor = originalStatusBarColor
                Log.d("ProfileScreen", "ProfileScreen disposed")
            }
        }
    }
    
    // 主界面
    Column(
        modifier = modifier
            .fillMaxSize()
            .background(Color(0xFFF8F6F8))
            .padding(top = SystemBarManager.getFixedStatusBarHeight()) // 添加固定高度顶部内边距
    ) {
        // 顶部标题栏
        TopAppBar(
            title = {
                Text(
                    stringResource(R.string.my_space),
                    fontSize = 20.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = MaterialTheme.colorScheme.primary
                )
            },
            actions = {
                IconButton(onClick = { 
                    navController.navigate(AppDestinations.SETTINGS_ROUTE)
                }) {
                    Icon(
                        imageVector = Icons.Filled.Settings,
                        contentDescription = stringResource(R.string.settings),
                        tint = MaterialTheme.colorScheme.primary
                    )
                }
            },
            colors = TopAppBarDefaults.smallTopAppBarColors(
                containerColor = Color(0xFFF8F6F8),
                titleContentColor = MaterialTheme.colorScheme.primary
            )
        )
        
        // 内容区域 - 使用LazyVerticalGrid实现响应式网格布局
        LazyVerticalGrid(
            columns = GridCells.Fixed(1),
            contentPadding = PaddingValues(start = 16.dp, end = 16.dp, top = 16.dp, bottom = 72.dp), // 增加底部内边距，避免内容被底部导航栏遮挡
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 习惯追踪卡片 - 放到第一个位置
            item {
                HabitTrackerCard(
                    habitViewModel = habitViewModel,
                    onClick = {
                        navController.navigate(AppDestinations.HABIT_TRACKER_ROUTE)
                    }
                )
            }
            
            // 情绪记录卡片
            item {
                EmotionTrackerCard(
                    selectedEmotion = selectedEmotion,
                    onEmotionSelected = { selectedEmotion = it },
                    emotionRecords = emotionRecords,
                    onRecordUpdate = { updatedRecord ->
                        // 🔧 使用ViewModel保存情绪记录
                        profileViewModel.saveEmotionRecord(updatedRecord)
                    },
                    modifier = Modifier,
                    navController = navController
                )
            }
            
            // 生理期记录卡片 - 占位符
            item {
                FeatureCard(
                    title = "生理期记录",
                    icon = Icons.Outlined.Favorite,
                    contentColor = Color(0xFFE91E63),
                    onClick = { 
                        navController.navigate(AppDestinations.MENSTRUAL_CYCLE_ROUTE)
                    }
                ) {
                    Column {
                        Text(
                            "记录和跟踪您的生理期和健康状况",
                            color = Color(0xFF666666),
                            fontSize = 14.sp,
                            modifier = Modifier.padding(top = 8.dp, bottom = 8.dp)
                        )
                        
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.End
                        ) {
                            Button(
                                onClick = {
                                    navController.navigate(AppDestinations.MENSTRUAL_CYCLE_STATS_ROUTE)
                                },
                                shape = RoundedCornerShape(16.dp),
                                colors = ButtonDefaults.buttonColors(
                                    containerColor = Color(0xFFF9E6EE),
                                    contentColor = Color(0xFFE291B7)
                                ),
                                modifier = Modifier.size(width = 100.dp, height = 36.dp)
                            ) {
                                Row(
                                    verticalAlignment = Alignment.CenterVertically,
                                    horizontalArrangement = Arrangement.spacedBy(4.dp)
                                ) {
                                    Icon(
                                        imageVector = Icons.Filled.BarChart,
                                        contentDescription = "统计",
                                        modifier = Modifier.size(18.dp)
                                    )
                                    Text(
                                        "统计",
                                        fontSize = 12.sp,
                                        fontWeight = FontWeight.Medium
                                    )
                                }
                            }
                        }
                    }
                }
            }
            
            // 专业用药管理卡片
            item {
                FeatureCard(
                    title = "健康管家",
                    icon = Icons.Outlined.LocalPharmacy,
                    contentColor = Color(0xFF2E7D32),
                    onClick = { navController.navigate(AppDestinations.MEDICATION_MANAGEMENT_ROUTE) }
                ) {
                    Column {
                    Text(
                            text = "专业用药管理与健康监护",
                        color = Color(0xFF666666),
                        fontSize = 14.sp,
                        modifier = Modifier.padding(top = 8.dp)
                    )
                        Spacer(modifier = Modifier.height(4.dp))
                        Row(
                            horizontalArrangement = Arrangement.spacedBy(12.dp),
                            modifier = Modifier.padding(top = 4.dp)
                        ) {
                            Surface(
                                shape = RoundedCornerShape(12.dp),
                                color = Color(0xFF2E7D32).copy(alpha = 0.1f)
                            ) {
                                Text(
                                    text = "智能提醒",
                                    fontSize = 10.sp,
                                    color = Color(0xFF2E7D32),
                                    modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
                                )
                            }
                            Surface(
                                shape = RoundedCornerShape(12.dp),
                                color = Color(0xFF2E7D32).copy(alpha = 0.1f)
                            ) {
                                Text(
                                    text = "安全监控",
                                    fontSize = 10.sp,
                                    color = Color(0xFF2E7D32),
                                    modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
                                )
                            }
                            Surface(
                                shape = RoundedCornerShape(12.dp),
                                color = Color(0xFF2E7D32).copy(alpha = 0.1f)
                            ) {
                                Text(
                                    text = "依从分析",
                                    fontSize = 10.sp,
                                    color = Color(0xFF2E7D32),
                                    modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
                                )
                            }
                        }
                    }
                }
            }
            
            // 添加用药管理卡片下方的间距
            item {
                Spacer(modifier = Modifier.height(16.dp))
            }
        }
    }
}

/**
 * 情绪记录卡片
 */
@Composable
fun EmotionTrackerCard(
    selectedEmotion: EmotionType?,
    onEmotionSelected: (EmotionType) -> Unit,
    emotionRecords: List<EmotionRecord>,
    onRecordUpdate: (EmotionRecord) -> Unit = {}, // 🔧 修改为单个记录回调
    modifier: Modifier = Modifier,
    navController: NavController = rememberNavController()
) {
    // 记录模式状态：简洁模式（默认）或详细模式
    var isDetailedMode by remember { mutableStateOf(false) }
    
    Card(
        modifier = modifier
            .fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = 0.dp
        )
    ) {
        Column(
            modifier = Modifier
                .padding(12.dp) // 从16.dp缩小到12.dp
                .fillMaxWidth()
        ) {
            // 卡片标题行
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                // 左侧标题
                Text(
                    text = "情绪记录",
                    fontSize = 16.sp, // 从18.sp缩小到16.sp
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.primary
                )
                
                // 🔧 右侧开关组（删除统计按钮）
                Row(
                    horizontalArrangement = Arrangement.spacedBy(6.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 详细模式开关
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "详细模式",
                            fontSize = 12.sp, // 从14.sp缩小到12.sp
                            color = Color(0xFF666666),
                            modifier = Modifier.padding(end = 6.dp) // 从8.dp缩小到6.dp
                        )
                        
                        Switch(
                            checked = isDetailedMode,
                            onCheckedChange = { isDetailedMode = it },
                            colors = SwitchDefaults.colors(
                                checkedThumbColor = Color(0xFFe8d9df),
                                checkedTrackColor = Color(0xFFe8d9df).copy(alpha = 0.5f),
                                uncheckedThumbColor = Color.White,
                                uncheckedTrackColor = Color.LightGray.copy(alpha = 0.5f),
                                checkedBorderColor = Color.Transparent,
                                uncheckedBorderColor = Color.Transparent
                            ),
                            modifier = Modifier.size(width = 40.dp, height = 24.dp) // 缩小开关尺寸
                        )
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 情绪选择器
            Text(
                text = "今天感觉如何？",
                fontSize = 14.sp, // 从16.sp缩小到14.sp
                color = MaterialTheme.colorScheme.primary,
                modifier = Modifier.padding(bottom = 6.dp) // 从8.dp缩小到6.dp
            )
            
            LazyRow(
                horizontalArrangement = Arrangement.spacedBy(8.dp), // 从12.dp缩小到8.dp
                modifier = Modifier.fillMaxWidth()
            ) {
                items(EmotionType.values()) { emotion ->
                    EmotionItem(
                        emotion = emotion,
                        isSelected = selectedEmotion == emotion,
                        onClick = { onEmotionSelected(emotion) }
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp)) // 从20.dp缩小到16.dp
            
            // 30天情绪日历
            Text(
                text = "30天情绪回顾",
                fontSize = 14.sp, // 从16.sp缩小到14.sp
                color = MaterialTheme.colorScheme.primary,
                modifier = Modifier.padding(bottom = 8.dp) // 从12.dp缩小到8.dp
            )
            
            EmotionCalendarView(
                records = emotionRecords,
                modifier = Modifier.fillMaxWidth(),
                onDateClick = { date, existingEmotion ->
                    // 根据模式处理日期点击事件
                    if (isDetailedMode) {
                        // 详细模式：导航到详细情绪记录页面
                        if (existingEmotion != null) {
                            // 🔧 已有记录，删除功能暂时保留原有逻辑
                            // TODO: 实现删除单个记录的功能
                        } else if (selectedEmotion != null) {
                            // 导航到详细记录页面，并传递选中的情绪和日期
                            navController.currentBackStackEntry?.savedStateHandle?.set("selectedEmotion", selectedEmotion)
                            navController.currentBackStackEntry?.savedStateHandle?.set("selectedDate", date)
                            navController.navigate(AppDestinations.DETAILED_EMOTION_RECORD_ROUTE)
                        }
                    } else {
                        // 简洁模式：直接添加/删除记录
                        if (existingEmotion != null) {
                            // 🔧 如果点击了已有记录，删除功能暂时保留原有逻辑
                            // TODO: 实现删除单个记录的功能
                        } else if (selectedEmotion != null) {
                            // 🔧 如果选择了情绪且点击了没有记录的日期，则添加简洁记录
                            val newRecord = EmotionRecord(
                                date = date,
                                emotion = selectedEmotion,
                                isDetailed = false
                            )
                            onRecordUpdate(newRecord)
                        }
                    }
                }
            )

            Spacer(modifier = Modifier.height(16.dp))

            // 🔧 添加回顾按钮
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                // 回顾记录按钮
                OutlinedButton(
                    onClick = {
                        navController.navigate(AppDestinations.EMOTION_RECORD_REVIEW_ROUTE)
                    },
                    modifier = Modifier.weight(1f),
                    colors = ButtonDefaults.outlinedButtonColors(
                        contentColor = MaterialTheme.colorScheme.primary
                    )
                ) {
                    Icon(
                        Icons.Default.History,
                        contentDescription = null,
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("回顾记录", fontSize = 12.sp)
                }

                // 统计分析按钮
                OutlinedButton(
                    onClick = {
                        // 传递情绪记录数据到统计页面
                        navController.currentBackStackEntry?.savedStateHandle?.set("emotion_records", emotionRecords)
                        navController.navigate(AppDestinations.EMOTION_STATS_ROUTE)
                    },
                    modifier = Modifier.weight(1f),
                    colors = ButtonDefaults.outlinedButtonColors(
                        contentColor = MaterialTheme.colorScheme.primary
                    )
                ) {
                    Icon(
                        Icons.Default.Analytics,
                        contentDescription = null,
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("统计分析", fontSize = 12.sp)
                }
            }
        }
    }
}

/**
 * 情绪项
 */
@Composable
fun EmotionItem(
    emotion: EmotionType,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier
            .width(50.dp) // 从60.dp缩小到50.dp
            .clickable(onClick = onClick)
    ) {
        // 情绪图标
        Box(
            contentAlignment = Alignment.Center,
            modifier = Modifier
                .size(40.dp) // 从48.dp缩小到40.dp
                .clip(CircleShape)
                .background(
                    if (isSelected) emotion.color.copy(alpha = 0.2f)
                    else Color(0xFFF5F5F5)
                )
                .padding(6.dp) // 从8.dp缩小到6.dp
        ) {
            Icon(
                imageVector = emotion.icon,
                contentDescription = emotion.title,
                tint = if (isSelected) emotion.color else Color.Gray,
                modifier = Modifier.size(20.dp) // 从24.dp缩小到20.dp
            )
        }
        
        // 情绪名称
        Text(
            text = emotion.title,
            fontSize = 10.sp, // 从12.sp缩小到10.sp
            color = if (isSelected) emotion.color else Color(0xFF666666),
            modifier = Modifier.padding(top = 3.dp) // 从4.dp缩小到3.dp
        )
    }
}

/**
 * 情绪日历视图
 */
@Composable
fun EmotionCalendarView(
    records: List<EmotionRecord>,
    modifier: Modifier = Modifier,
    onDateClick: ((LocalDate, EmotionType?) -> Unit)? = null
) {
    val today = LocalDate.now()
    val monthFormatter = DateTimeFormatter.ofPattern("M月")
    
    // 计算当前月份的第一天以及上个月需要显示的天数
    val currentMonth = today.withDayOfMonth(1)
    val previousMonth = currentMonth.minusMonths(1)
    
    // 生成日历网格所需的日期
    // 1. 确定当前月第一天是星期几
    val firstDayOfWeek = currentMonth.dayOfWeek.value // 1=周一, 7=周日
    // 2. 如果不是周一，则需要显示上个月的最后几天
    val daysFromPreviousMonth = if (firstDayOfWeek > 1) firstDayOfWeek - 1 else 0
    
    // 生成完整的日期列表：上个月末尾几天 + 当前月 + 下个月开始几天(确保总数为42，即6周)
    val dates = mutableListOf<LocalDate>()
    
    // 添加上个月的日期
    if (daysFromPreviousMonth > 0) {
        val lastDayOfPreviousMonth = previousMonth.withDayOfMonth(previousMonth.lengthOfMonth())
        for (i in daysFromPreviousMonth downTo 1) {
            dates.add(lastDayOfPreviousMonth.minusDays(i.toLong() - 1))
        }
    }
    
    // 添加当前月的日期
    val daysInCurrentMonth = currentMonth.lengthOfMonth()
    for (i in 1..daysInCurrentMonth) {
        dates.add(currentMonth.withDayOfMonth(i))
    }
    
    // 添加下个月的日期，确保总数为42（6行*7列）或者至少4行*7列
    val totalDays = 42 // 6周
    val daysFromNextMonth = totalDays - dates.size
    if (daysFromNextMonth > 0) {
        val nextMonth = currentMonth.plusMonths(1)
        for (i in 1..daysFromNextMonth) {
            dates.add(nextMonth.withDayOfMonth(i))
        }
    }
    
    // 将日期分组为每行7天
    val weeks = dates.chunked(7)
    
    Column(modifier = modifier) {
        // 月份标签
        // 判断是否跨月显示
        val monthSet = dates.map { it.month }.toSet()
        if (monthSet.size == 1) {
            // 只有一个月
            Text(
                text = dates.first().format(monthFormatter),
                fontSize = 12.sp, // 从14.sp缩小到12.sp
                fontWeight = FontWeight.Medium,
                color = MaterialTheme.colorScheme.primary,
                modifier = Modifier.padding(bottom = 6.dp) // 从8.dp缩小到6.dp
            )
        } else {
            // 跨月显示
            val firstMonth = dates.first().format(monthFormatter)
            val lastMonth = dates.last().format(monthFormatter)
            Text(
                text = "$firstMonth - $lastMonth",
                fontSize = 12.sp, // 从14.sp缩小到12.sp
                fontWeight = FontWeight.Medium,
                color = MaterialTheme.colorScheme.primary,
                modifier = Modifier.padding(bottom = 6.dp) // 从8.dp缩小到6.dp
            )
        }
        
        // 星期标签
        Row(modifier = Modifier.fillMaxWidth()) {
            listOf("一", "二", "三", "四", "五", "六", "日").forEach { day ->
                Text(
                    text = day,
                    fontSize = 10.sp, // 从12.sp缩小到10.sp
                    color = Color(0xFF999999),
                    textAlign = TextAlign.Center,
                    modifier = Modifier.weight(1f)
                )
            }
        }
        
        Spacer(modifier = Modifier.height(6.dp)) // 从8.dp缩小到6.dp
        
                        // 日历网格
        Column(
            verticalArrangement = Arrangement.spacedBy(6.dp) // 从8.dp缩小到6.dp
        ) {
            weeks.take(5).forEach { week ->  // 最多显示5周，保持紧凑
                Row(
                    horizontalArrangement = Arrangement.spacedBy(3.dp), // 从4.dp缩小到3.dp
                    modifier = Modifier.fillMaxWidth()
                ) {
                    // 显示一周的日期
                    week.forEach { date ->
                        val record = records.find { it.date == date }
                        val isCurrentMonth = date.month == today.month
                        
                        EmotionCalendarDay(
                            date = date,
                            day = date.dayOfMonth.toString(), // 只显示日期数字
                            emotion = record?.emotion,
                            isToday = date == today,
                            isCurrentMonth = isCurrentMonth,
                            onClick = { 
                                // 如果是当前月才允许点击记录
                                if (isCurrentMonth) {
                                    onDateClick?.invoke(date, record?.emotion)
                                }
                            },
                            modifier = Modifier.weight(1f)
                        )
                    }
                }
            }
        }
    }
}

/**
 * 情绪日历单日
 */
@Composable
fun EmotionCalendarDay(
    date: LocalDate,
    day: String,
    emotion: EmotionType?,
    isToday: Boolean,
    isCurrentMonth: Boolean = true,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    // 获取主题色，因为不能在Canvas的drawScope中调用@Composable函数
    val primaryColor = MaterialTheme.colorScheme.primary
    
    Box(
        contentAlignment = Alignment.Center,
        modifier = modifier
            .aspectRatio(1f)
            .padding(1.dp) // 从2.dp缩小到1.dp
            .clickable(onClick = onClick) // 添加点击事件
    ) {
        // 日期圆圈
        Canvas(
        modifier = Modifier
            .fillMaxSize()
            .padding(1.dp) // 从2.dp缩小到1.dp
        ) {
            // 绘制日期背景圆圈
            val radius = size.minDimension / 2
            
            if (isToday) {
                // 今天的圆圈轮廓
                drawCircle(
                    color = primaryColor,
                    radius = radius * 0.9f,
                    style = Stroke(width = 1.5.dp.toPx())
                )
            }
            
            // 如果有情绪记录，绘制情绪颜色圆圈
            if (emotion != null) {
                drawCircle(
                    color = emotion.color.copy(alpha = 0.3f),
                    radius = radius * 0.85f
                )
                
                // 绘制情绪图标
                drawCircle(
                    color = emotion.color.copy(alpha = 0.4f),
                    radius = radius * 0.4f,
                    center = Offset(center.x, center.y)
                )
            }
        }
        
        // 日期文本
        Text(
            text = day,
            fontSize = 10.sp, // 从12.sp缩小到10.sp
            fontWeight = if (isToday) FontWeight.Bold else FontWeight.Normal,
            // 根据是否为当前月份和是否有情绪记录调整颜色
            color = when {
                !isCurrentMonth -> Color(0xFFCCCCCC) // 非当前月份的日期显示为浅灰色
                emotion != null -> emotion.color.copy(alpha = 0.8f) // 有情绪记录的日期
                else -> Color(0xFF666666) // 普通日期
            },
            textAlign = TextAlign.Center
        )
    }
}

/**
 * 通用功能卡片
 */
@Composable
fun FeatureCard(
    title: String,
    icon: ImageVector,
    contentColor: Color,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    content: @Composable () -> Unit
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .clickable(onClick = onClick),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = 0.dp
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 卡片标题和图标
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = icon,
                    contentDescription = title,
                    tint = contentColor,
                    modifier = Modifier.size(24.dp)
                )
                
                Spacer(modifier = Modifier.width(12.dp))
                
                Text(
                    text = title,
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.primary
                )
            }
            
            // 卡片内容
            content()
        }
    }
}

/**
 * 习惯追踪卡片 - 美化版
 */
@Composable
fun HabitTrackerCard(
    habitViewModel: HabitViewModel,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    // 从ViewModel获取真实的习惯数据，并显示前3个习惯
    val allHabits by habitViewModel.habits.collectAsState()
    val displayHabits = remember(allHabits) { allHabits.take(3) }
    
    // 计算今日完成数和总数
    val today = LocalDate.now()
    val todayCompletedCount = remember(allHabits) {
        allHabits.count { habit -> habit.completedDates.contains(today) }
    }
    val totalHabitsCount = allHabits.size

    Card(
        modifier = modifier
            .fillMaxWidth()
            .clickable(onClick = onClick),
        shape = RoundedCornerShape(20.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = 2.dp
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp) // 从20.dp缩小到16.dp
        ) {
            // 卡片标题和统计信息
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween,
                modifier = Modifier.fillMaxWidth()
            ) {
                Row(verticalAlignment = Alignment.CenterVertically) {
                    // 精美的渐变图标背景 - 调整为用户指定的颜色
                    Box(
                        contentAlignment = Alignment.Center,
                        modifier = Modifier
                            .size(36.dp) // 从44.dp缩小到36.dp
                            .background(
                                brush = Brush.radialGradient(
                                    colors = listOf(
                                        Color(0xFFd9ddd7), // 用户指定的起始颜色
                                        Color(0xFFc6ccc3)  // 用户指定的结束颜色
                                    )
                                ),
                                shape = RoundedCornerShape(12.dp) // 从14.dp缩小到12.dp
                            )
                    ) {
                        Icon(
                            imageVector = Icons.Outlined.EmojiEvents,
                            contentDescription = "习惯追踪",
                            tint = Color.White,
                            modifier = Modifier.size(20.dp) // 从24.dp缩小到20.dp
                        )
                    }
                    
                    Spacer(modifier = Modifier.width(10.dp)) // 从12.dp缩小到10.dp
                    
                    Column {
                        Text(
                            text = "习惯追踪",
                            fontSize = 16.sp, // 从18.sp缩小到16.sp
                            fontWeight = FontWeight.Bold,
                            color = MaterialTheme.colorScheme.primary
                        )
                        
                        // 今日完成进度
                        if (totalHabitsCount > 0) {
                            Spacer(modifier = Modifier.height(2.dp))
                            Text(
                                text = "今日进度 $todayCompletedCount/$totalHabitsCount",
                                fontSize = 11.sp, // 从12.sp缩小到11.sp
                                color = Color(0xFF718096),
                                fontWeight = FontWeight.Medium
                            )
                        }
                    }
                }
                
                // 右侧统计数字和箭头
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    // 完成百分比圆环
                    if (totalHabitsCount > 0) {
                        val completionPercentage = (todayCompletedCount.toFloat() / totalHabitsCount.toFloat())
                        Box(
                            contentAlignment = Alignment.Center,
                            modifier = Modifier.size(32.dp)
                        ) {
                            Canvas(modifier = Modifier.size(32.dp)) {
                                val strokeWidth = 3.dp.toPx()
                                drawCircle(
                                    color = Color(0xFFE2E8F0),
                                    radius = size.minDimension / 2 - strokeWidth / 2,
                                    style = Stroke(strokeWidth)
                                )
                                drawArc(
                                    color = Color(0xFF48BB78),
                                    startAngle = -90f,
                                    sweepAngle = 360f * completionPercentage,
                                    useCenter = false,
                                    style = Stroke(strokeWidth, cap = androidx.compose.ui.graphics.StrokeCap.Round)
                                )
                            }
                            Text(
                                text = "${(completionPercentage * 100).roundToInt()}%",
                                fontSize = 8.sp,
                                fontWeight = FontWeight.Bold,
                                color = Color(0xFF48BB78)
                            )
                        }
                    }
                    
                    Icon(
                        imageVector = Icons.Default.KeyboardArrowRight,
                        contentDescription = "查看更多",
                        tint = Color(0xFFA0AEC0),
                        modifier = Modifier.size(20.dp)
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(12.dp)) // 从16.dp缩小到12.dp
            
            // 分隔线
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(1.dp)
                    .background(Color(0xFFF1F5F9))
            )
            
            Spacer(modifier = Modifier.height(12.dp)) // 从16.dp缩小到12.dp
            
            // 习惯预览区域
            if (displayHabits.isNotEmpty()) {
                LazyRow(
                    horizontalArrangement = Arrangement.spacedBy(8.dp), // 从12.dp缩小到8.dp
                    modifier = Modifier.fillMaxWidth()
                ) {
                    items(displayHabits) { habit ->
                        EnhancedHabitItem(habit = habit)
                    }
                }
            } else {
                // 空状态
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 8.dp) // 从12.dp缩小到8.dp
                ) {
                    Icon(
                        imageVector = Icons.Outlined.Add,
                        contentDescription = "添加习惯",
                        tint = Color(0xFFA0AEC0),
                        modifier = Modifier.size(24.dp)
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "还没有习惯，点击添加第一个",
                        fontSize = 14.sp,
                        color = Color(0xFF718096),
                        textAlign = TextAlign.Center
                    )
                }
            }
        }
    }
}

/**
 * 单个习惯项 - 美化版
 */
@Composable
fun EnhancedHabitItem(
    habit: HabitData,
    modifier: Modifier = Modifier
) {
    val today = LocalDate.now()
    val isCompletedToday = habit.completedDates.contains(today)
    val iconEmoji = habit.icon
    
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = modifier
            .width(60.dp) // 从70.dp进一步缩小到60.dp
            .clip(RoundedCornerShape(12.dp)) // 从14.dp缩小到12.dp
            .background(
                if (isCompletedToday) {
                    Brush.verticalGradient(
                        colors = listOf(
                            habit.color.copy(alpha = 0.1f),
                            habit.color.copy(alpha = 0.05f)
                        )
                    )
                } else {
                    Brush.verticalGradient(
                        colors = listOf(
                            Color(0xFFF8FAFC),
                            Color(0xFFF1F5F9)
                        )
                    )
                }
            )
            // 取消外边框
            .padding(8.dp) // 从10.dp缩小到8.dp
    ) {
        // 完成状态指示器
        Box(
            contentAlignment = Alignment.Center,
            modifier = Modifier
                .size(40.dp) // 从48.dp缩小到40.dp
                .background(
                    brush = if (isCompletedToday) {
                        Brush.radialGradient(
                            colors = listOf(
                                habit.color,
                                habit.color.copy(alpha = 0.8f)
                            )
                        )
                    } else {
                        Brush.radialGradient(
                            colors = listOf(
                                Color(0xFFF1F5F9),
                                Color(0xFFE2E8F0)
                            )
                        )
                    },
                    shape = CircleShape
                )
        ) {
            if (isCompletedToday) {
                // 显示完成图标
                Icon(
                    imageVector = Icons.Default.Check,
                    contentDescription = "已完成",
                    tint = Color.White,
                    modifier = Modifier.size(20.dp) // 从24.dp缩小到20.dp
                )
            } else {
                // 显示习惯图标（emoji）
                Text(
                    text = iconEmoji,
                    fontSize = 18.sp, // 从20.sp缩小到18.sp
                    textAlign = TextAlign.Center
                )
            }
        }
        
        Spacer(modifier = Modifier.height(6.dp)) // 从8.dp缩小到6.dp
        
        // 习惯名称
        Text(
            text = habit.name,
            fontSize = 11.sp, // 从12.sp缩小到11.sp
            fontWeight = FontWeight.Medium,
            color = if (isCompletedToday) habit.color else Color(0xFF4A5568),
            textAlign = TextAlign.Center,
            maxLines = 1,
            overflow = TextOverflow.Ellipsis
        )
        
        Spacer(modifier = Modifier.height(3.dp)) // 从4.dp缩小到3.dp
        
        // 连续天数 
        Surface(
            shape = RoundedCornerShape(10.dp), // 从12.dp缩小到10.dp
            color = if (isCompletedToday) {
                habit.color.copy(alpha = 0.1f)
            } else {
                Color(0xFFF7FAFC)
            }
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.Center,
                modifier = Modifier.padding(horizontal = 6.dp, vertical = 1.dp) // 从8.dp/2.dp缩小到6.dp/1.dp
            ) {
                Icon(
                    imageVector = Icons.Outlined.LocalFireDepartment,
                    contentDescription = "连续",
                    tint = if (habit.streak > 0) Color(0xFFED8936) else Color(0xFFA0AEC0),
                    modifier = Modifier.size(8.dp) // 从10.dp缩小到8.dp
                )
                
                Spacer(modifier = Modifier.width(1.dp)) // 从2.dp缩小到1.dp
                
                Text(
                    text = "${habit.streak}",
                    fontSize = 9.sp, // 从10.sp缩小到9.sp
                    color = if (habit.streak > 0) Color(0xFFED8936) else Color(0xFFA0AEC0),
                    fontWeight = FontWeight.Bold
                )
            }
        }
    }
}

/**
 * 旧版习惯项 - 保持兼容性
 */
data class Habit(
    val id: String,
    val name: String,
    val icon: ImageVector,
    val color: Color,
    val completedDays: Int,
    val streak: Int,
    val bgColor: Color = Color.Transparent
)

@Composable
fun HabitItem(
    habit: Habit,
    modifier: Modifier = Modifier
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = modifier
            .width(70.dp)
            .clip(RoundedCornerShape(12.dp))
            .background(habit.bgColor)
            .padding(6.dp)
    ) {
        Box(
            contentAlignment = Alignment.Center,
            modifier = Modifier
                .size(36.dp)
                .clip(RoundedCornerShape(10.dp))
                .background(habit.color.copy(alpha = 0.15f))
                .padding(6.dp)
        ) {
            Icon(
                imageVector = habit.icon,
                contentDescription = habit.name,
                tint = habit.color,
                modifier = Modifier.size(22.dp)
            )
        }
        
        Spacer(modifier = Modifier.height(4.dp))
        
        Text(
            text = habit.name,
            fontSize = 12.sp,
            fontWeight = FontWeight.Medium,
            color = Color(0xFF555555),
            textAlign = TextAlign.Center
        )
        
        Spacer(modifier = Modifier.height(2.dp))
        
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Center,
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 1.dp)
        ) {
            Icon(
                imageVector = Icons.Outlined.LocalFireDepartment,
                contentDescription = "连续",
                tint = Color(0xFFFF7043),
                modifier = Modifier.size(12.dp)
            )
            
            Spacer(modifier = Modifier.width(1.dp))
            
            Text(
                text = "${habit.streak}天",
                fontSize = 10.sp,
                color = Color(0xFFFF7043),
                fontWeight = FontWeight.Bold
            )
        }
    }
} 