package com.timeflow.app.viewmodel;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class BackupRestoreViewModel_Factory implements Factory<BackupRestoreViewModel> {
  private final Provider<Context> contextProvider;

  public BackupRestoreViewModel_Factory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public BackupRestoreViewModel get() {
    return newInstance(contextProvider.get());
  }

  public static BackupRestoreViewModel_Factory create(Provider<Context> contextProvider) {
    return new BackupRestoreViewModel_Factory(contextProvider);
  }

  public static BackupRestoreViewModel newInstance(Context context) {
    return new BackupRestoreViewModel(context);
  }
}
