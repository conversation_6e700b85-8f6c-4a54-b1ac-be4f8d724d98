package com.timeflow.app.ui.viewmodel;

import com.timeflow.app.domain.usecase.TaskTimeUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class TaskTimeViewModel_Factory implements Factory<TaskTimeViewModel> {
  private final Provider<TaskTimeUseCase> taskTimeUseCaseProvider;

  public TaskTimeViewModel_Factory(Provider<TaskTimeUseCase> taskTimeUseCaseProvider) {
    this.taskTimeUseCaseProvider = taskTimeUseCaseProvider;
  }

  @Override
  public TaskTimeViewModel get() {
    return newInstance(taskTimeUseCaseProvider.get());
  }

  public static TaskTimeViewModel_Factory create(
      Provider<TaskTimeUseCase> taskTimeUseCaseProvider) {
    return new TaskTimeViewModel_Factory(taskTimeUseCaseProvider);
  }

  public static TaskTimeViewModel newInstance(TaskTimeUseCase taskTimeUseCase) {
    return new TaskTimeViewModel(taskTimeUseCase);
  }
}
