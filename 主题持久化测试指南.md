# 主题持久化测试指南

## 🧪 **测试步骤**

### 准备工作
1. 确保应用已构建成功
2. 安装应用到测试设备
3. 准备记录测试结果

### 测试场景1：自定义主题颜色持久化测试

#### 步骤：
1. **启动应用**
   - 打开TimeFlow应用
   - 进入设置页面

2. **设置自定义主题颜色**
   - 点击"主题设置"
   - 选择"主色调"设置
   - 设置为红色 (#FF0000) 或其他明显颜色
   - 确认保存

3. **验证即时效果**
   - 检查应用界面是否立即应用了新颜色
   - 返回主页面，确认颜色变化生效

4. **重启应用测试**
   - 完全关闭应用（从后台清除）
   - 重新启动应用
   - **预期结果**：主题颜色应该保持为设置的红色

5. **记录结果**
   - ✅ 成功：颜色保持不变
   - ❌ 失败：颜色恢复为默认值

### 测试场景2：预设主题持久化测试

#### 步骤：
1. **应用预设主题**
   - 进入设置 → 预设主题
   - 选择"雾霾蓝"主题
   - 确认应用

2. **验证预设主题状态**
   - 检查"雾霾蓝"主题是否显示为已选中
   - 确认应用界面颜色变为雾霾蓝色调

3. **重启应用测试**
   - 完全关闭应用
   - 重新启动应用
   - 进入预设主题页面
   - **预期结果**：
     - 应用颜色保持为雾霾蓝
     - 雾霾蓝主题显示为已选中状态

4. **记录结果**
   - ✅ 成功：预设主题状态和颜色都保持正确
   - ❌ 失败：预设主题状态丢失或颜色恢复默认

### 测试场景3：自定义与预设主题切换测试

#### 步骤：
1. **先应用预设主题**
   - 选择"莫兰迪·雾绿"主题
   - 确认应用成功

2. **自定义主题颜色**
   - 返回主题设置
   - 修改主色调为蓝色 (#0000FF)
   - 保存设置

3. **检查预设主题状态**
   - 进入预设主题页面
   - **预期结果**：所有预设主题都应显示为未选中状态

4. **重启验证**
   - 重启应用
   - 检查主题颜色是否为蓝色
   - 检查预设主题页面是否所有主题都未选中

5. **记录结果**
   - ✅ 成功：自定义颜色保持，预设主题状态正确清除
   - ❌ 失败：颜色丢失或预设主题状态错误

### 测试场景4：多次修改测试

#### 步骤：
1. **连续修改主题颜色**
   - 设置主色调为红色，保存
   - 立即修改为绿色，保存
   - 再修改为紫色，保存

2. **验证最终状态**
   - 检查当前颜色是否为紫色
   - 重启应用
   - **预期结果**：颜色应该保持为最后设置的紫色

3. **记录结果**
   - ✅ 成功：最终颜色正确保存
   - ❌ 失败：颜色不是最后设置的值

## 🔍 **问题排查**

### 如果测试失败，检查以下内容：

#### 1. 查看日志输出
```bash
adb logcat | grep -E "(ThemeManager|PresetThemeManager|ThemeSettingsViewModel)"
```

关键日志标识：
- `✅ 成功获取颜色值` - 颜色读取成功
- `⚠️ 检测到无效颜色值` - 发现无效颜色
- `❌ 颜色类型转换失败` - 转换错误
- `✅ 主题偏好设置已保存到DataStore` - 保存成功

#### 2. 检查DataStore数据
- 确认颜色值是否正确保存到DataStore
- 检查是否有类型转换错误

#### 3. 验证颜色值格式
- 确认保存的颜色值不是 0x00000000（无效值）
- 检查颜色值是否在有效范围内

## 📊 **测试结果记录表**

| 测试场景 | 预期结果 | 实际结果 | 状态 | 备注 |
|---------|---------|---------|------|------|
| 自定义颜色持久化 | 重启后颜色保持 | | ⏳ | |
| 预设主题持久化 | 重启后状态正确 | | ⏳ | |
| 主题切换状态 | 状态正确更新 | | ⏳ | |
| 多次修改 | 最终值正确保存 | | ⏳ | |

## 🎯 **成功标准**

测试通过的标准：
- ✅ 所有4个测试场景都通过
- ✅ 没有颜色值丢失或恢复默认的情况
- ✅ 预设主题状态管理正确
- ✅ 日志中没有严重错误信息

## 🚀 **下一步**

如果测试通过：
1. 标记问题为已解决
2. 更新版本发布说明
3. 通知用户问题已修复

如果测试失败：
1. 分析失败原因
2. 根据日志进行进一步修复
3. 重新测试验证
