package com.timeflow.app.ui.screen.wishlist

import android.app.Activity
import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.BorderStroke
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.foundation.*
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material.icons.outlined.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.*
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.navigation.NavController
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import com.timeflow.app.R
import com.timeflow.app.data.model.*
import com.timeflow.app.ui.viewmodel.WishStatisticsViewModel
import com.timeflow.app.ui.theme.*
import com.timeflow.app.utils.SystemBarManager
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import kotlin.math.*

// 🌸 显化理念配色方案 - 温暖可爱风格
private val ManifestPrimary = Color(0xFFFF9A9E) // 温暖粉色
private val ManifestSecondary = Color(0xFFFECFEF) // 浅粉色
private val ManifestAccent = Color(0xFFFFD700) // 金色能量
private val ManifestBackground = Color(0xFFFFFBF8) // 温暖奶白
private val ManifestCard = Color(0xFFFFFFFF) // 纯白卡片
private val ManifestText = Color(0xFF2C2C2C) // 深灰文字
private val ManifestTextSecondary = Color(0xFF6B6B6B) // 中性灰
private val ManifestSuccess = Color(0xFF4CAF50) // 成功绿
private val ManifestProgress = Color(0xFFFF9800) // 进度橙
private val ManifestEnergy = Color(0xFF9C27B0) // 能量紫
private val ManifestLove = Color(0xFFE91E63) // 爱心红

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun WishStatisticsScreen(
    navController: NavController,
    viewModel: WishStatisticsViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val context = LocalContext.current
    val activity = context as? Activity
    
    // 筛选状态管理
    var showFilterDialog by remember { mutableStateOf(false) }
    var showFilterResults by remember { mutableStateOf(false) }
    var currentFilters by remember { mutableStateOf(WishFilters()) }
    
    // 动画状态
    var isLoaded by remember { mutableStateOf(false) }
    val contentAlpha by animateFloatAsState(
        targetValue = if (isLoaded) 1f else 0f,
        animationSpec = tween(400, easing = EaseOutCubic), // 减少动画时间 600->400
        label = "content_alpha"
    )
    
    // 性能优化：缓存筛选结果
    val filteredStatistics = remember(uiState.statistics, currentFilters) {
        applyWishFilters(uiState.statistics, currentFilters)
    }
    
    // 筛选的愿望列表
    val filteredWishes = remember(uiState.allWishes, currentFilters) {
        filterWishes(uiState.allWishes, currentFilters)
    }
    
    // 是否有活跃的筛选条件
    val hasActiveFilters = remember(currentFilters) {
        currentFilters.categories.isNotEmpty() || 
        currentFilters.statuses.isNotEmpty() ||
        currentFilters.difficultyLevels.isNotEmpty()
    }
    
    // 性能优化：缓存背景渐变
    val backgroundGradient = remember {
        Brush.verticalGradient(
            colors = listOf(
                ManifestBackground,
                Color(0xFFFFF5F5),
                ManifestBackground
            )
        )
    }

    // 系统栏处理
    LaunchedEffect(Unit) {
        activity?.let { 
            SystemBarManager.setupLightModeSystemBars(it)
        }
        delay(150) // 减少延迟 300->150
        isLoaded = true
        viewModel.loadStatistics()
    }
    
    // 性能监控：避免过度重组
    DisposableEffect(key1 = uiState.statistics.totalWishes) {
        onDispose {
            // 组件销毁时清理资源
        }
    }

    // 温暖渐变背景 - 使用缓存的渐变
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(brush = backgroundGradient)
    ) {
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .alpha(contentAlpha)
                .padding(top = SystemBarManager.getFixedStatusBarHeight()),
            contentPadding = PaddingValues(bottom = 24.dp), // 减小底部间距 32->24
            verticalArrangement = Arrangement.spacedBy(12.dp) // 减小项目间距 16->12
        ) {
            // 🎨 温暖顶部导航栏
            item(key = "top_navigation") { // 添加stable key
                WarmTopNavigationBar(
                    onBackClick = { navController.popBackStack() }
                )
            }

            // 🌟 愿望宇宙置顶 - 核心数据面板优先展示
            item(key = "core_metrics") { // 添加stable key
                AnimatedVisibility(
                    visible = isLoaded,
                    enter = slideInVertically(
                        animationSpec = tween(400, delayMillis = 50) // 减少动画时间
                    ) + fadeIn(animationSpec = tween(400, delayMillis = 50))
                ) {
                    CoreMetricsPanel(
                        statistics = filteredStatistics,
                        modifier = Modifier.padding(horizontal = 16.dp)
                    )
                }
            }

            // 📊 信息觅食优化：愿望能量流动图 - 重要数据前置
            item(key = "energy_visualization") { // 添加stable key
                AnimatedVisibility(
                    visible = isLoaded,
                    enter = slideInVertically(
                        animationSpec = tween(400, delayMillis = 100) // 减少动画时间和延迟
                    ) + fadeIn(animationSpec = tween(400, delayMillis = 100))
                ) {
                    WishEnergyVisualization(
                        heatMapData = uiState.heatMapData,
                        modifier = Modifier.padding(horizontal = 16.dp)
                    )
                }
            }

            // 🎯 愿望孵化率 - 合并到核心面板中以减少滚动
            // item 已移除，合并到CoreMetricsPanel中

            // ✨ 愿望分析师 - 降低优先级，减少认知负荷
            item(key = "wish_analyst") { // 添加stable key
                AnimatedVisibility(
                    visible = isLoaded,
                    enter = slideInVertically(
                        animationSpec = tween(400, delayMillis = 150) // 减少动画时间和延迟
                    ) + fadeIn(animationSpec = tween(400, delayMillis = 150))
                ) {
                    WishAnalyst(
                        insights = uiState.insights,
                        modifier = Modifier.padding(horizontal = 16.dp)
                    )
                }
            }

            // 📈 愿望时间轴
            item(key = "wish_timeline") { // 添加stable key
                AnimatedVisibility(
                    visible = isLoaded,
                    enter = slideInVertically(
                        animationSpec = tween(400, delayMillis = 200) // 减少动画时间和延迟
                    ) + fadeIn(animationSpec = tween(400, delayMillis = 200))
                ) {
                    WishTimeline(
                        recentWishes = filteredStatistics.recentWishList,
                        modifier = Modifier.padding(horizontal = 16.dp)
                    )
                }
            }

            // 🏆 愿望成就墙 - 优化设计，放在最下方作为正向鼓励的结尾
            item(key = "achievement_wall") { // 添加stable key
                AnimatedVisibility(
                    visible = isLoaded,
                    enter = slideInVertically(
                        animationSpec = tween(400, delayMillis = 250) // 减少动画时间和延迟
                    ) + fadeIn(animationSpec = tween(400, delayMillis = 250))
                ) {
                    EnhancedAchievementWall(
                        achievements = uiState.achievements,
                        modifier = Modifier.padding(horizontal = 16.dp)
                    )
                }
            }
        }

        // 🌸 智能筛选按钮 - 参照抖音/小红书设计
        ExtendedFloatingActionButton(
            onClick = { 
                if (hasActiveFilters && !showFilterResults) {
                    showFilterResults = true
                } else {
                    showFilterDialog = true
                }
            },
            modifier = Modifier
                .align(Alignment.BottomEnd)
                .padding(16.dp),
            containerColor = if (hasActiveFilters) ManifestAccent else ManifestPrimary,
            contentColor = Color.White
        ) {
            Icon(
                imageVector = if (hasActiveFilters) Icons.Default.FilterList else Icons.Default.Tune,
                contentDescription = if (hasActiveFilters) "查看筛选结果" else "筛选设置"
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = if (hasActiveFilters) "筛选结果(${filteredWishes.size})" else "筛选",
                fontSize = 14.sp
            )
        }
        
        // 筛选对话框
        if (showFilterDialog) {
            WishFilterDialog(
                currentFilters = currentFilters,
                onFiltersChanged = { newFilters ->
                    currentFilters = newFilters
                    viewModel.applyFilters(newFilters)
                    showFilterDialog = false
                    if (hasActiveFilters) {
                        showFilterResults = true
                    }
                },
                onDismiss = { showFilterDialog = false }
            )
        }
        
        // 🎯 筛选结果页面 - 参照小红书/抖音设计
        if (showFilterResults) {
            WishFilterResultsSheet(
                filteredWishes = filteredWishes,
                currentFilters = currentFilters,
                onDismiss = { showFilterResults = false },
                onEditFilters = { 
                    showFilterResults = false
                    showFilterDialog = true
                },
                onClearFilters = {
                    currentFilters = WishFilters()
                    showFilterResults = false
                    viewModel.applyFilters(WishFilters())
                }
            )
        }
    }
}

/**
 * 🎨 温暖顶部导航栏
 */
@Composable
private fun WarmTopNavigationBar(
    onBackClick: () -> Unit
) {
    Surface(
        modifier = Modifier.fillMaxWidth(),
        color = Color.Transparent
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp), // 减小内边距 16->12
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 返回按钮 - 纯白背景，无阴影
            IconButton(
                onClick = onBackClick,
                modifier = Modifier
                    .size(36.dp) // 减小按钮 40->36
                    .background(
                        Color.White, // 改为纯白色
                        RoundedCornerShape(18.dp) // 相应减小圆角
                    )
                    // 移除阴影
            ) {
                Icon(
                    imageVector = Icons.Default.ArrowBack,
                    contentDescription = "返回",
                    tint = ManifestText
                )
            }
            
            Spacer(modifier = Modifier.width(12.dp)) // 减小间距 16->12
            
            // 标题区域
            Column {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "🌟",
                        fontSize = 16.sp // 减小emoji 20->16
                    )
                    Spacer(modifier = Modifier.width(6.dp)) // 减小间距 8->6
                    Text(
                        text = "愿望宇宙",
                        fontSize = 15.sp, // 减小字体 18->15
                        fontWeight = FontWeight.Bold,
                        color = ManifestText,
                        letterSpacing = (-0.3).sp // 紧凑字间距
                    )
                }
                Text(
                    text = "显化你的美好愿望 ✨",
                    fontSize = 9.sp, // 减小字体 11->9
                    color = ManifestTextSecondary,
                    fontStyle = androidx.compose.ui.text.font.FontStyle.Italic,
                    letterSpacing = (-0.2).sp // 紧凑字间距
                )
            }
        }
    }
}

/**
 * 🎯 核心数据面板 - 基于心理学原理优化的信息展示
 * 应用峰终定律和认知负荷理论，将最重要的信息前置并集中展示
 */
@Composable
private fun CoreMetricsPanel(
    statistics: WishStatistics,
    modifier: Modifier = Modifier
) {
    Surface(
        modifier = modifier
            .fillMaxWidth()
            .shadow(
                elevation = 8.dp,
                shape = RoundedCornerShape(24.dp),
                spotColor = ManifestPrimary.copy(alpha = 0.3f)
            ),
        shape = RoundedCornerShape(24.dp),
        color = ManifestCard
    ) {
        Column(
            modifier = Modifier.padding(16.dp) // 减小内边距 24->16
        ) {
            // 💫 即时反馈区域 - 最新成就
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column {
                    Text(
                        text = "🌟 愿望宇宙",
                        fontSize = 16.sp, // 减小字体 18->16
                        fontWeight = FontWeight.Bold,
                        color = ManifestText,
                        letterSpacing = (-0.5).sp // 紧凑字间距
                    )
                    Text(
                        text = "你的显化能量中心",
                        fontSize = 10.sp, // 减小字体 12->10
                        color = ManifestTextSecondary,
                        fontStyle = androidx.compose.ui.text.font.FontStyle.Italic,
                        letterSpacing = (-0.2).sp // 紧凑字间距
                    )
                }
                
                // 即时成就反馈
                Surface(
                    color = ManifestSuccess.copy(alpha = 0.15f),
                    shape = RoundedCornerShape(12.dp) // 减小圆角 16->12
                ) {
                    Text(
                        text = "最近实现：${statistics.recentAchievement}",
                        modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp), // 减小内边距
                        fontSize = 9.sp, // 减小字体 10->9
                        color = ManifestSuccess,
                        fontWeight = FontWeight.Medium,
                        letterSpacing = (-0.1).sp // 紧凑字间距
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(12.dp)) // 减小间距 20->12
            
            // 🔥 核心指标 - 减少认知负荷的三核心展示
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                // 总愿望数
                CoreMetricItem(
                    value = statistics.totalWishes.toString(),
                    label = "愿望总数",
                    icon = "💫",
                    color = ManifestPrimary,
                    modifier = Modifier.weight(1f)
                )
                
                // 实现率
                CoreMetricItem(
                    value = "${(statistics.realizationRate * 100).toInt()}%",
                    label = "实现率",
                    icon = "🎯",
                    color = ManifestSuccess,
                    modifier = Modifier.weight(1f)
                )
                
                // 活跃愿望
                CoreMetricItem(
                    value = statistics.activeWishes.toString(),
                    label = "孵化中",
                    icon = "🌱",
                    color = ManifestProgress,
                    modifier = Modifier.weight(1f)
                )
            }
            
            Spacer(modifier = Modifier.height(12.dp)) // 减小间距 20->12
            
            // 📊 快速愿望分布 - 信息觅食优化
            Text(
                text = "愿望分布",
                fontSize = 12.sp, // 减小字体 14->12
                fontWeight = FontWeight.SemiBold,
                color = ManifestText,
                letterSpacing = (-0.3).sp // 紧凑字间距
            )
            
            Spacer(modifier = Modifier.height(8.dp)) // 减小间距 12->8
            
            LazyRow(
                horizontalArrangement = Arrangement.spacedBy(8.dp) // 减小间距 12->8
            ) {
                items(
                    items = statistics.categoryDistribution.entries.toList(),
                    key = { (category, _) -> category.name } // 添加stable key
                ) { (category, count) ->
                    CategoryChip(
                        category = category,
                        count = count
                    )
                }
            }
        }
    }
}

/**
 * 核心指标项 - 紧凑版
 */
@Composable
private fun CoreMetricItem(
    value: String,
    label: String,
    icon: String,
    color: Color,
    modifier: Modifier = Modifier
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = modifier
    ) {
        Text(
            text = icon,
            fontSize = 18.sp // 减小emoji 24->18
        )
        Spacer(modifier = Modifier.height(4.dp)) // 减小间距 8->4
        Text(
            text = value,
            fontSize = 16.sp, // 减小字体 20->16
            fontWeight = FontWeight.Bold,
            color = color,
            letterSpacing = (-0.5).sp // 紧凑字间距
        )
        Text(
            text = label,
            fontSize = 9.sp, // 减小字体 11->9
            color = ManifestTextSecondary,
            textAlign = TextAlign.Center,
            letterSpacing = (-0.2).sp // 紧凑字间距
        )
    }
}

/**
 * 类别标签芯片
 */
@Composable
private fun CategoryChip(
    category: WishCategory,
    count: Int
) {
    // 为不同类别定义颜色映射
    val categoryColor = when (category) {
        WishCategory.TRAVEL -> Color(0xFF4CAF50) // 绿色 - 旅行
        WishCategory.SHOPPING -> Color(0xFFFF9800) // 橙色 - 购物
        WishCategory.LEARNING -> Color(0xFF2196F3) // 蓝色 - 学习
        WishCategory.CAREER -> Color(0xFF9C27B0) // 紫色 - 事业
        WishCategory.LIFESTYLE -> Color(0xFF795548) // 棕色 - 生活
        WishCategory.HEALTH -> Color(0xFFE91E63) // 粉色 - 健康
        WishCategory.HOBBY -> Color(0xFFFF5722) // 深橙 - 爱好
        WishCategory.RELATIONSHIP -> Color(0xFF607D8B) // 蓝灰 - 关系
        WishCategory.OTHER -> Color(0xFF9E9E9E) // 灰色 - 其他
    }
    
    Surface(
        color = categoryColor.copy(alpha = 0.15f),
        shape = RoundedCornerShape(8.dp) // 减小圆角 12->8
    ) {
        Row(
            modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp), // 减小内边距
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = category.emoji,
                fontSize = 11.sp // 减小emoji 14->11
            )
            Spacer(modifier = Modifier.width(3.dp)) // 减小间距 4->3
            Text(
                text = "$count",
                fontSize = 10.sp, // 减小字体 12->10
                fontWeight = FontWeight.Medium,
                color = categoryColor,
                letterSpacing = (-0.2).sp // 紧凑字间距
            )
        }
    }
}

/**
 * 🌟 情感化数据看板（已废弃，功能合并到CoreMetricsPanel）
 */
@Composable
private fun EmotionalDashboard(
    statistics: WishStatistics,
    modifier: Modifier = Modifier
) {
    Surface(
        modifier = modifier
            .fillMaxWidth()
            .shadow(
                elevation = 8.dp,
                shape = RoundedCornerShape(24.dp),
                spotColor = ManifestPrimary.copy(alpha = 0.3f)
            ),
        shape = RoundedCornerShape(24.dp),
        color = ManifestCard
    ) {
        Column(
            modifier = Modifier.padding(24.dp)
        ) {
            // 头部标题
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.fillMaxWidth()
            ) {
                Text(
                    text = "🌟",
                    fontSize = 20.sp // 减小emoji字体
                )
                Spacer(modifier = Modifier.width(12.dp))
                Text(
                    text = "愿望宇宙",
                    fontSize = 16.sp, // 减小字体
                    fontWeight = FontWeight.Bold,
                    color = ManifestText
                )
            }
            
            Spacer(modifier = Modifier.height(20.dp))
            
            // 统计数据
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                StatCard(
                    value = "${statistics.totalWishes}个",
                    label = "已记录愿望",
                    icon = "💫",
                    color = ManifestPrimary
                )
                
                StatCard(
                    value = "${(statistics.achievementRate * 100).toInt()}%",
                    label = "实现进度",
                    icon = "🎯",
                    color = ManifestSuccess
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 进度条
            Column {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "实现进度",
                        fontSize = 13.sp, // 减小字体
                        color = ManifestTextSecondary
                    )
                    Text(
                        text = "${(statistics.achievementRate * 100).toInt()}%",
                        fontSize = 13.sp, // 减小字体
                        fontWeight = FontWeight.Bold,
                        color = ManifestSuccess
                    )
                }
                
                Spacer(modifier = Modifier.height(8.dp))
                
                LinearProgressIndicator(
                    progress = statistics.achievementRate,
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(8.dp)
                        .clip(RoundedCornerShape(4.dp)),
                    color = ManifestSuccess,
                    trackColor = ManifestSecondary
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 最近成就
            if (statistics.recentAchievement.isNotEmpty()) {
                Surface(
                    modifier = Modifier.fillMaxWidth(),
                    color = ManifestSecondary.copy(alpha = 0.3f),
                    shape = RoundedCornerShape(16.dp)
                ) {
                    Row(
                        modifier = Modifier.padding(16.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "🎉",
                            fontSize = 18.sp // 减小emoji字体
                        )
                        Spacer(modifier = Modifier.width(12.dp))
                        Column {
                            Text(
                                text = "最近成就",
                                fontSize = 11.sp, // 减小字体
                                color = ManifestTextSecondary
                            )
                            Text(
                                text = statistics.recentAchievement,
                                fontSize = 13.sp, // 减小字体
                                fontWeight = FontWeight.Medium,
                                color = ManifestText
                            )
                        }
                    }
                }
            }
        }
    }
}

/**
 * 📊 愿望能量流动图 - 可视化显示愿望活动的时间分布模式
 * 
 * 设计目的：
 * 1. 帮助用户了解自己的愿望活动规律（哪些日期更活跃）
 * 2. 类似GitHub贡献图，展示持续性和活跃度
 * 3. 通过颜色深浅直观反映愿望关注度
 * 4. 发现行为模式，比如周末更活跃或某些时期更专注
 */
@Composable
private fun WishEnergyVisualization(
    heatMapData: List<WishHeatMapData>,
    modifier: Modifier = Modifier
) {
    Surface(
        modifier = modifier
            .fillMaxWidth()
            .shadow(
                elevation = 6.dp,
                shape = RoundedCornerShape(20.dp),
                spotColor = ManifestEnergy.copy(alpha = 0.2f)
            ),
        shape = RoundedCornerShape(20.dp),
        color = ManifestCard
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            // 增强标题区域
            Column {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "⚡",
                        fontSize = 18.sp
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = "愿望能量流动图",
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Bold,
                        color = ManifestText
                    )
                }
                
                Spacer(modifier = Modifier.height(4.dp))
                
                Text(
                    text = "追踪你的愿望活跃度，发现行为模式 ✨",
                    fontSize = 10.sp,
                    color = ManifestTextSecondary.copy(alpha = 0.8f),
                    fontStyle = androidx.compose.ui.text.font.FontStyle.Italic
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 智能时间轴标签 - 基于认知心理学的时间导向
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "💫 最近活跃",
                    fontSize = 9.sp,
                    color = ManifestSuccess,
                    fontWeight = FontWeight.Medium
                )
                
                Text(
                    text = "→ 时间流动 →",
                    fontSize = 8.sp,
                    color = ManifestTextSecondary.copy(alpha = 0.6f),
                    fontStyle = androidx.compose.ui.text.font.FontStyle.Italic
                )
                
                Text(
                    text = "🕒 历史记录",
                    fontSize = 9.sp,
                    color = ManifestTextSecondary.copy(alpha = 0.8f),
                    fontWeight = FontWeight.Medium
                )
            }
            
            Spacer(modifier = Modifier.height(6.dp))
            
            // 优化热力图 - GitHub风格的贡献图（最新数据在前）
            LazyVerticalGrid(
                columns = GridCells.Fixed(14), // 14列，类似日历布局
                modifier = Modifier.height(120.dp), // 调整高度
                verticalArrangement = Arrangement.spacedBy(2.dp), // 稍微增加间距便于观察
                horizontalArrangement = Arrangement.spacedBy(2.dp)
            ) {
                items(
                    items = heatMapData.reversed(), // 🔥 关键修复：反转数据，最新的在前面
                    key = { it.date.toString() }
                ) { data ->
                    WishEnergyCell(
                        intensity = data.intensity,
                        date = data.date,
                        onClick = { /* 可以添加点击查看详情功能 */ }
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 增强图例区域
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column {
                    Text(
                        text = "愿望活跃度",
                        fontSize = 10.sp,
                        fontWeight = FontWeight.Medium,
                        color = ManifestTextSecondary
                    )
                    Text(
                        text = "颜色越深表示该日愿望关注度越高",
                        fontSize = 8.sp,
                        color = ManifestTextSecondary.copy(alpha = 0.7f)
                    )
                }
                
                // 渐变图例
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "低",
                        fontSize = 8.sp,
                        color = ManifestTextSecondary
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    
                    // 显示5个渐变色块
                    Row(
                        horizontalArrangement = Arrangement.spacedBy(1.dp)
                    ) {
                        listOf(0.0, 0.25, 0.5, 0.75, 1.0).forEach { intensity ->
                            Box(
                                modifier = Modifier
                                    .size(8.dp)
                                    .background(
                                        color = getIntensityColor(intensity),
                                        shape = RoundedCornerShape(1.dp)
                                    )
                            )
                        }
                    }
                    
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(
                        text = "高",
                        fontSize = 8.sp,
                        color = ManifestTextSecondary
                    )
                }
            }
        }
    }
}

/**
 * 单个愿望能量格子 - 可交互的热力图单元
 */
@Composable
private fun WishEnergyCell(
    intensity: Double,
    date: LocalDate,
    onClick: () -> Unit = {}
) {
    val animatedColor by animateColorAsState(
        targetValue = getIntensityColor(intensity),
        animationSpec = tween(300),
        label = "cell_color"
    )
    
    // 添加悬停效果的缩放动画
    var isPressed by remember { mutableStateOf(false) }
    val scale by animateFloatAsState(
        targetValue = if (isPressed) 0.9f else 1f,
        animationSpec = spring(dampingRatio = 0.6f),
        label = "cell_scale"
    )
    
    Box(
        modifier = Modifier
            .aspectRatio(1f)
            .graphicsLayer {
                scaleX = scale
                scaleY = scale
            }
            .clip(RoundedCornerShape(2.dp))
            .background(animatedColor)
            .clickable { 
                // 可以显示该日期的愿望详情
                onClick() 
            }
            .indication(
                interactionSource = remember { MutableInteractionSource() },
                indication = null // 自定义交互效果
            )
    ) {
        // 高强度时添加微光效果
        if (intensity > 0.7) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(
                        brush = Brush.radialGradient(
                            colors = listOf(
                                Color.White.copy(alpha = 0.3f),
                                Color.Transparent
                            ),
                            radius = 8f
                        )
                    )
            )
        }
    }
}

/**
 * 根据强度获取对应颜色
 */
private fun getIntensityColor(intensity: Double): Color {
    return when {
        intensity <= 0.1 -> Color(0xFFEBEDF0) // 无活动 - 浅灰
        intensity <= 0.3 -> Color(0xFFC6E48B) // 轻度活动 - 浅绿
        intensity <= 0.5 -> Color(0xFF7BC96F) // 中度活动 - 中绿  
        intensity <= 0.7 -> Color(0xFF239A3B) // 高度活动 - 深绿
        else -> Color(0xFF196127) // 超高活动 - 最深绿
    }
}

/**
 * 🎯 愿望孵化率
 */
@Composable
private fun WishIncubationRate(
    statistics: WishStatistics,
    modifier: Modifier = Modifier
) {
    Surface(
        modifier = modifier
            .fillMaxWidth()
            .shadow(
                elevation = 6.dp,
                shape = RoundedCornerShape(20.dp),
                spotColor = ManifestProgress.copy(alpha = 0.2f)
            ),
        shape = RoundedCornerShape(20.dp),
        color = ManifestCard
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            // 标题
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "🥚",
                    fontSize = 18.sp // 减小emoji字体
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "愿望孵化率",
                    fontSize = 14.sp, // 减小字体
                    fontWeight = FontWeight.Bold,
                    color = ManifestText
                )
            }
            
            Spacer(modifier = Modifier.height(20.dp))
            
            // 孵化状态
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                IncubationCard(
                    count = statistics.achievedWishes,
                    total = statistics.totalWishes,
                    label = "已实现愿望",
                    icon = "🎉",
                    color = ManifestSuccess,
                    modifier = Modifier.weight(1f)
                )
                
                Spacer(modifier = Modifier.width(16.dp))
                
                IncubationCard(
                    count = statistics.activeWishes,
                    total = statistics.totalWishes,
                    label = "孵化中愿望",
                    icon = "🌱",
                    color = ManifestProgress,
                    modifier = Modifier.weight(1f)
                )
            }
        }
    }
}

/**
 * ✨ 愿望分析师
 */
@Composable
private fun WishAnalyst(
    insights: List<WishInsight>,
    modifier: Modifier = Modifier
) {
    Surface(
        modifier = modifier
            .fillMaxWidth()
            .shadow(
                elevation = 6.dp,
                shape = RoundedCornerShape(20.dp),
                spotColor = ManifestEnergy.copy(alpha = 0.2f)
            ),
        shape = RoundedCornerShape(20.dp),
        color = ManifestCard
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            // 标题
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "✨",
                    fontSize = 18.sp // 减小emoji字体
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "愿望分析师",
                    fontSize = 14.sp, // 减小字体
                    fontWeight = FontWeight.Bold,
                    color = ManifestText
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 高频关键词
            if (insights.isNotEmpty()) {
                Column {
                    Text(
                        text = "🔍 高频关键词：",
                        fontSize = 13.sp, // 减小字体
                        fontWeight = FontWeight.Medium,
                        color = ManifestText
                    )
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                                                         LazyRow(
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    items(
                        items = insights.take(5),
                        key = { it.keyword } // 已有stable key，保持不变
                    ) { insight ->
                             Surface(
                                 color = ManifestSecondary.copy(alpha = 0.3f),
                                 shape = RoundedCornerShape(12.dp)
                             ) {
                                 Text(
                                     text = "「${insight.keyword}」",
                                     modifier = Modifier.padding(horizontal = 12.dp, vertical = 6.dp),
                                     fontSize = 11.sp, // 减小字体
                                     color = ManifestText
                                 )
                             }
                         }
                     }
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 本月建议
            Surface(
                modifier = Modifier.fillMaxWidth(),
                color = Color(0xFFF0F8FF),
                shape = RoundedCornerShape(12.dp)
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "💡",
                            fontSize = 16.sp
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = "本月建议：",
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Medium,
                            color = ManifestText
                        )
                    }
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    Text(
                        text = "您有3个旅行类愿望临近最佳实现季节",
                        fontSize = 13.sp,
                        color = ManifestTextSecondary
                    )
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    Column(
                        verticalArrangement = Arrangement.spacedBy(4.dp)
                    ) {
                        SuggestionItem("北海道滑雪（11月-2月）→ 开始规划")
                        SuggestionItem("樱花季签证（需提前3月办理）")
                    }
                }
            }
        }
    }
}

/**
 * 🏆 增强版愿望成就墙 - 终极正向激励体验
 * 
 * 设计理念：
 * 1. 庆祝用户的每一步成长
 * 2. 将成就可视化，提供强烈的满足感
 * 3. 用积极的语言和视觉元素激励用户
 * 4. 即将解锁的成就提供明确的目标导向
 */
@Composable
private fun EnhancedAchievementWall(
    achievements: List<WishAchievement>,
    modifier: Modifier = Modifier
) {
    val unlockedAchievements = achievements.filter { it.isUnlocked }
    val lockedAchievements = achievements.filter { !it.isUnlocked }
    val totalProgress = if (achievements.isNotEmpty()) unlockedAchievements.size.toFloat() / achievements.size else 0f

    Surface(
        modifier = modifier
            .fillMaxWidth()
            .shadow(
                elevation = 12.dp,
                shape = RoundedCornerShape(24.dp),
                spotColor = ManifestAccent.copy(alpha = 0.3f)
            ),
        shape = RoundedCornerShape(24.dp),
        color = ManifestCard
    ) {
        Column(
            modifier = Modifier.padding(24.dp)
        ) {
            // 🎉 庆祝式头部区域
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(
                        brush = Brush.horizontalGradient(
                            colors = listOf(
                                ManifestAccent.copy(alpha = 0.1f),
                                ManifestSuccess.copy(alpha = 0.1f)
                            )
                        ),
                        shape = RoundedCornerShape(16.dp)
                    )
                    .clip(RoundedCornerShape(16.dp))
            ) {
                Column(
                    modifier = Modifier.padding(20.dp)
                ) {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Column {
                            Row(
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text(
                                    text = if (unlockedAchievements.isNotEmpty()) "🎉" else "🏆",
                                    fontSize = 24.sp
                                )
                                Spacer(modifier = Modifier.width(12.dp))
                                Text(
                                    text = if (unlockedAchievements.isNotEmpty()) "愿望成就殿堂" else "成就等你解锁",
                                    fontSize = 18.sp,
                                    fontWeight = FontWeight.Bold,
                                    color = ManifestText
                                )
                            }
                            
                            Spacer(modifier = Modifier.height(8.dp))
                            
                            Text(
                                text = when {
                                    totalProgress >= 0.8f -> "哇！你真是愿望实现大师！🌟"
                                    totalProgress >= 0.5f -> "棒极了！你正在成为愿望达人！💫"
                                    totalProgress >= 0.2f -> "很棒的开始！继续加油！🚀"
                                    else -> "每一个愿望都是成长的种子！🌱"
                                },
                                fontSize = 13.sp,
                                color = ManifestSuccess,
                                fontWeight = FontWeight.Medium,
                                fontStyle = androidx.compose.ui.text.font.FontStyle.Italic
                            )
                        }
                        
                        // 进度环形展示
                        Box(
                            contentAlignment = Alignment.Center
                        ) {
                            CircularProgressIndicator(
                                progress = totalProgress,
                                modifier = Modifier.size(60.dp),
                                color = ManifestAccent,
                                trackColor = ManifestSecondary.copy(alpha = 0.3f),
                                strokeWidth = 6.dp
                            )
                            Text(
                                text = "${(totalProgress * 100).toInt()}%",
                                fontSize = 14.sp,
                                fontWeight = FontWeight.Bold,
                                color = ManifestAccent
                            )
                        }
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(24.dp))
            
            // 🌟 已解锁成就展示 - 庆祝用户的成功
            if (unlockedAchievements.isNotEmpty()) {
                Text(
                    text = "🎖️ 你的荣誉徽章",
                    fontSize = 15.sp, // 减小字体
                    fontWeight = FontWeight.Bold,
                    color = ManifestText
                )
                
                Spacer(modifier = Modifier.height(10.dp)) // 减小间距
                
                LazyRow(
                    horizontalArrangement = Arrangement.spacedBy(8.dp) // 减小间距
                ) {
                    items(
                        items = unlockedAchievements,
                        key = { it.title } // 添加stable key
                    ) { achievement ->
                        UnlockedAchievementCard(achievement = achievement)
                    }
                }
                
                Spacer(modifier = Modifier.height(18.dp)) // 减小间距
            }
            
            // 🚀 即将解锁的成就 - 提供明确的目标导向
            if (lockedAchievements.isNotEmpty()) {
                Text(
                    text = "🎯 即将解锁",
                    fontSize = 15.sp, // 减小字体
                    fontWeight = FontWeight.Bold,
                    color = ManifestText
                )
                
                Spacer(modifier = Modifier.height(10.dp)) // 减小间距
                
                Column(
                    verticalArrangement = Arrangement.spacedBy(8.dp) // 减小间距
                ) {
                    lockedAchievements.take(3).forEach { achievement ->
                        UpcomingAchievementCard(achievement = achievement)
                    }
                }
            }
            
            // 💫 鼓励语句
            Spacer(modifier = Modifier.height(16.dp)) // 减小间距
            
            Surface(
                modifier = Modifier.fillMaxWidth(),
                color = ManifestPrimary.copy(alpha = 0.08f),
                shape = RoundedCornerShape(10.dp) // 减小圆角
            ) {
                Text(
                    text = getEncouragementMessage(totalProgress),
                    modifier = Modifier.padding(12.dp), // 减小内边距
                    fontSize = 13.sp, // 减小字体
                    color = ManifestText,
                    textAlign = TextAlign.Center,
                    fontWeight = FontWeight.Medium,
                    lineHeight = 18.sp // 减小行高
                )
            }
        }
    }
}

/**
 * 已解锁成就卡片 - 紧凑庆祝式设计
 */
@Composable
private fun UnlockedAchievementCard(
    achievement: WishAchievement
) {
    // 一次性进入动画 - 优化性能
    var isVisible by remember { mutableStateOf(false) }
    val animationSpec = remember { spring<Float>(
        dampingRatio = Spring.DampingRatioMediumBouncy,
        stiffness = Spring.StiffnessMedium // 提高刚性，减少动画时间
    ) }
    
    val scale by animateFloatAsState(
        targetValue = if (isVisible) 1f else 0.8f, // 减小初始缩放差异
        animationSpec = animationSpec,
        label = "enter_scale"
    )
    
    val alpha by animateFloatAsState(
        targetValue = if (isVisible) 1f else 0f,
        animationSpec = tween(300, delayMillis = 50), // 减少动画时间和延迟
        label = "enter_alpha"
    )
    
    LaunchedEffect(Unit) {
        delay(100) // 减少延迟
        isVisible = true
    }
    
    Surface(
        modifier = Modifier
            .width(110.dp) // 缩小宽度
            .graphicsLayer {
                scaleX = scale
                scaleY = scale
                this.alpha = alpha
            },
        color = ManifestAccent.copy(alpha = 0.1f),
        shape = RoundedCornerShape(12.dp), // 减小圆角
        border = BorderStroke(1.5.dp, ManifestAccent.copy(alpha = 0.3f)) // 减小边框
    ) {
        Column(
            modifier = Modifier.padding(12.dp), // 减小内边距
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = "🏆",
                fontSize = 24.sp // 减小emoji尺寸
            )
            
            Spacer(modifier = Modifier.height(6.dp)) // 减小间距
            
            Text(
                text = achievement.title,
                fontSize = 11.sp, // 减小字体
                fontWeight = FontWeight.Bold,
                color = ManifestAccent,
                textAlign = TextAlign.Center,
                maxLines = 2,
                overflow = TextOverflow.Ellipsis,
                lineHeight = 14.sp // 紧凑行高
            )
            
            Spacer(modifier = Modifier.height(4.dp))
            
            Surface(
                color = ManifestSuccess.copy(alpha = 0.2f),
                shape = RoundedCornerShape(6.dp) // 减小圆角
            ) {
                Text(
                    text = "✨ 已解锁",
                    modifier = Modifier.padding(horizontal = 6.dp, vertical = 2.dp), // 减小内边距
                    fontSize = 9.sp, // 减小字体
                    color = ManifestSuccess,
                    fontWeight = FontWeight.Medium
                )
            }
        }
    }
}

/**
 * 即将解锁成就卡片 - 紧凑目标导向设计
 */
@Composable
private fun UpcomingAchievementCard(
    achievement: WishAchievement
) {
    val progress = if (achievement.requiredCount > 0) {
        achievement.currentCount.toFloat() / achievement.requiredCount
    } else 0f
    
    Surface(
        modifier = Modifier.fillMaxWidth(),
        color = Color.Transparent,
        shape = RoundedCornerShape(10.dp), // 减小圆角
        border = BorderStroke(1.dp, ManifestTextSecondary.copy(alpha = 0.2f))
    ) {
        Row(
            modifier = Modifier.padding(12.dp), // 减小内边距
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 成就图标 - 缩小
            Box(
                modifier = Modifier
                    .size(36.dp) // 从48dp减小到36dp
                    .background(
                        ManifestTextSecondary.copy(alpha = 0.1f),
                        CircleShape
                    ),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "🎯",
                    fontSize = 16.sp // 减小emoji尺寸
                )
            }
            
            Spacer(modifier = Modifier.width(12.dp)) // 减小间距
            
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = achievement.title,
                    fontSize = 13.sp, // 减小字体
                    fontWeight = FontWeight.Medium,
                    color = ManifestText,
                    maxLines = 1, // 限制行数
                    overflow = TextOverflow.Ellipsis
                )
                
                Spacer(modifier = Modifier.height(3.dp)) // 减小间距
                
                Text(
                    text = achievement.description,
                    fontSize = 11.sp, // 减小字体
                    color = ManifestTextSecondary,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
                
                if (achievement.requiredCount > 0) {
                    Spacer(modifier = Modifier.height(4.dp)) // 减小间距
                    
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        LinearProgressIndicator(
                            progress = progress,
                            modifier = Modifier
                                .weight(1f)
                                .height(3.dp) // 减小进度条高度
                                .clip(RoundedCornerShape(1.5.dp)),
                            color = ManifestProgress,
                            trackColor = ManifestSecondary.copy(alpha = 0.3f)
                        )
                        
                        Spacer(modifier = Modifier.width(6.dp)) // 减小间距
                        
                        Text(
                            text = "${achievement.currentCount}/${achievement.requiredCount}",
                            fontSize = 9.sp, // 减小字体
                            color = ManifestProgress,
                            fontWeight = FontWeight.Medium
                        )
                    }
                }
            }
        }
    }
}

/**
 * 根据进度获取鼓励话语
 */
@Composable
private fun getEncouragementMessage(progress: Float): String {
    return when {
        progress >= 0.9f -> stringResource(R.string.encouragement_master)
        progress >= 0.7f -> stringResource(R.string.encouragement_amazing)
        progress >= 0.5f -> stringResource(R.string.encouragement_great)
        progress >= 0.3f -> stringResource(R.string.encouragement_good_start)
        progress > 0f -> stringResource(R.string.encouragement_progress)
        else -> stringResource(R.string.encouragement_ready)
    }
}

/**
 * 🏆 成就里程碑 - 已废弃，被EnhancedAchievementWall替代
 */
@Composable
private fun AchievementMilestones(
    achievements: List<WishAchievement>,
    modifier: Modifier = Modifier
) {
    Surface(
        modifier = modifier
            .fillMaxWidth()
            .shadow(
                elevation = 6.dp,
                shape = RoundedCornerShape(20.dp),
                spotColor = ManifestAccent.copy(alpha = 0.2f)
            ),
        shape = RoundedCornerShape(20.dp),
        color = ManifestCard
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            // 标题
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "🏆",
                    fontSize = 20.sp
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "愿望成就墙",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Bold,
                    color = ManifestText
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 成就列表
            Column(
                verticalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                achievements.forEach { achievement ->
                    AchievementItem(achievement = achievement)
                }
            }
        }
    }
}

/**
 * 📈 愿望时间轴 - 紧凑版
 */
@Composable
private fun WishTimeline(
    recentWishes: List<WishModel>,
    modifier: Modifier = Modifier
) {
    Surface(
        modifier = modifier
            .fillMaxWidth()
            .shadow(
                elevation = 4.dp, // 减小阴影 6->4
                shape = RoundedCornerShape(16.dp), // 减小圆角 20->16
                spotColor = ManifestLove.copy(alpha = 0.2f)
            ),
        shape = RoundedCornerShape(16.dp), // 减小圆角 20->16
        color = ManifestCard
    ) {
        Column(
            modifier = Modifier.padding(14.dp) // 减小内边距 20->14
        ) {
            // 标题
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "📈",
                    fontSize = 16.sp // 减小emoji 20->16
                )
                Spacer(modifier = Modifier.width(6.dp)) // 减小间距 8->6
                Text(
                    text = "愿望生命周期",
                    fontSize = 14.sp, // 减小字体 16->14
                    fontWeight = FontWeight.Bold,
                    color = ManifestText,
                    letterSpacing = (-0.3).sp // 紧凑字间距
                )
            }
            
            Spacer(modifier = Modifier.height(10.dp)) // 减小间距 16->10
            
            // 时间轴
            Column(
                verticalArrangement = Arrangement.spacedBy(10.dp) // 减小间距 16->10
            ) {
                recentWishes.take(5).forEach { wish ->
                    TimelineItem(wish = wish)
                }
            }
        }
    }
}

// 辅助组件

@Composable
private fun StatCard(
    value: String,
    label: String,
    icon: String,
    color: Color
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = icon,
                fontSize = 14.sp // 减小emoji字体
            )
            Spacer(modifier = Modifier.width(4.dp))
            Text(
                text = value,
                fontSize = 18.sp, // 减小字体
                fontWeight = FontWeight.Bold,
                color = color
            )
        }
        Text(
            text = label,
            fontSize = 11.sp, // 减小字体
            color = ManifestTextSecondary
        )
    }
}

@Composable
private fun IncubationCard(
    count: Int,
    total: Int,
    label: String,
    icon: String,
    color: Color,
    modifier: Modifier = Modifier
) {
    Surface(
        modifier = modifier,
        color = color.copy(alpha = 0.1f),
        shape = RoundedCornerShape(16.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = icon,
                fontSize = 20.sp // 减小emoji字体
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = "$count",
                fontSize = 22.sp, // 减小字体
                fontWeight = FontWeight.Bold,
                color = color
            )
            
            Text(
                text = if (total > 0) "(${(count * 100 / total)}%)" else "(0%)",
                fontSize = 11.sp, // 减小字体
                color = ManifestTextSecondary
            )
            
            Spacer(modifier = Modifier.height(4.dp))
            
            Text(
                text = label,
                fontSize = 11.sp, // 减小字体
                color = ManifestText,
                textAlign = TextAlign.Center
            )
        }
    }
}

@Composable
private fun SuggestionItem(text: String) {
    Row(
        verticalAlignment = Alignment.Top
    ) {
        Text(
            text = "•",
            fontSize = 11.sp, // 减小字体
            color = ManifestTextSecondary
        )
        Spacer(modifier = Modifier.width(8.dp))
        Text(
            text = text,
            fontSize = 11.sp, // 减小字体
            color = ManifestTextSecondary
        )
    }
}

@Composable
private fun AchievementItem(achievement: WishAchievement) {
    // 解锁动效状态
    val animatedScale by animateFloatAsState(
        targetValue = if (achievement.isUnlocked) 1f else 0.9f,
        animationSpec = spring(
            dampingRatio = Spring.DampingRatioMediumBouncy,
            stiffness = Spring.StiffnessLow
        ),
        label = "achievement_scale"
    )
    
    val animatedGlow by animateFloatAsState(
        targetValue = if (achievement.isUnlocked) 1f else 0.3f,
        animationSpec = tween(800, easing = EaseOutCubic),
        label = "achievement_glow"
    )
    
    // 解锁成就的闪光动效
    val infiniteTransition = rememberInfiniteTransition(label = "achievement_sparkle")
    val sparkleAlpha by infiniteTransition.animateFloat(
        initialValue = 0.3f,
        targetValue = 1f,
        animationSpec = infiniteRepeatable(
            animation = tween(1500, easing = EaseInOutSine),
            repeatMode = RepeatMode.Reverse
        ),
        label = "sparkle_alpha"
    )
    
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier.fillMaxWidth()
    ) {
        Surface(
            modifier = Modifier
                .size(40.dp)
                .graphicsLayer(
                    scaleX = animatedScale,
                    scaleY = animatedScale
                ),
            color = if (achievement.isUnlocked) 
                ManifestAccent.copy(alpha = 0.2f * animatedGlow) 
            else 
                Color(0xFFEEEEEE),
            shape = CircleShape,
            shadowElevation = 0.dp // 取消所有阴影效果
        ) {
            Box(
                contentAlignment = Alignment.Center
            ) {
                // 解锁成就的背景光晕
                if (achievement.isUnlocked) {
                    Box(
                        modifier = Modifier
                            .fillMaxSize()
                            .background(
                                brush = Brush.radialGradient(
                                    colors = listOf(
                                        ManifestAccent.copy(alpha = 0.3f * sparkleAlpha),
                                        Color.Transparent
                                    )
                                ),
                                shape = CircleShape
                            )
                    )
                }
                
                Text(
                    text = if (achievement.isUnlocked) "🏆" else "🔒",
                    fontSize = (16 * animatedScale).sp
                )
            }
        }
        
        Spacer(modifier = Modifier.width(12.dp))
        
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = achievement.title,
                fontSize = 13.sp, // 缩小字体
                fontWeight = FontWeight.Medium,
                color = if (achievement.isUnlocked) ManifestText else ManifestTextSecondary
            )
            Text(
                text = achievement.description,
                fontSize = 11.sp, // 缩小字体
                color = ManifestTextSecondary
            )
        }
        
        if (achievement.isUnlocked) {
            Surface(
                color = ManifestAccent.copy(alpha = 0.2f * animatedGlow),
                shape = RoundedCornerShape(8.dp),
                shadowElevation = 0.dp // 取消状态标签阴影
            ) {
                Text(
                    text = "已解锁",
                    modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp),
                    fontSize = 9.sp, // 缩小字体
                    color = ManifestAccent,
                    fontWeight = FontWeight.Medium
                )
            }
        }
    }
}

@Composable
private fun TimelineItem(wish: WishModel) {
    Row(
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 状态点 - 缩小
        Box(
            modifier = Modifier
                .size(8.dp) // 减小状态点 12->8
                .background(
                    color = when (wish.status) {
                        WishStatus.ACTIVE -> ManifestProgress
                        WishStatus.ACHIEVED -> ManifestSuccess
                        else -> ManifestTextSecondary
                    },
                    shape = CircleShape
                )
        )
        
        Spacer(modifier = Modifier.width(8.dp)) // 减小间距 12->8
        
        // 愿望信息 - 标题和时间同排显示
        Row(
            modifier = Modifier.weight(1f),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = wish.title,
                fontSize = 11.sp, // 减小字体 13->11
                fontWeight = FontWeight.Medium,
                color = ManifestText,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                letterSpacing = (-0.1).sp, // 紧凑字间距
                modifier = Modifier.weight(1f, fill = false) // 不填充剩余空间，让时间更靠近
            )
            
            Spacer(modifier = Modifier.width(4.dp)) // 减少标题和时间之间的间距 8->4
            
            Text(
                text = wish.createdAt.format(DateTimeFormatter.ofPattern("MM-dd")), // 简化日期格式
                fontSize = 9.sp, // 减小字体 11->9
                color = ManifestTextSecondary,
                letterSpacing = (-0.1).sp // 紧凑字间距
            )
        }
        
        // 状态标签 - 缩小
        Surface(
            color = when (wish.status) {
                WishStatus.ACTIVE -> ManifestProgress.copy(alpha = 0.2f)
                WishStatus.ACHIEVED -> ManifestSuccess.copy(alpha = 0.2f)
                else -> ManifestTextSecondary.copy(alpha = 0.2f)
            },
            shape = RoundedCornerShape(6.dp) // 减小圆角 8->6
        ) {
            Text(
                text = when (wish.status) {
                    WishStatus.ACTIVE -> "孵化中"
                    WishStatus.ACHIEVED -> "已实现"
                    else -> "已归档"
                },
                modifier = Modifier.padding(horizontal = 6.dp, vertical = 2.dp), // 减小内边距
                fontSize = 8.sp, // 减小字体 10->8
                color = when (wish.status) {
                    WishStatus.ACTIVE -> ManifestProgress
                    WishStatus.ACHIEVED -> ManifestSuccess
                    else -> ManifestTextSecondary
                },
                fontWeight = FontWeight.Medium,
                letterSpacing = (1).sp // 紧凑字间距
            )
        }
    }
}

// ========================================================================================
// 数据模型定义
// ========================================================================================

/**
 * 愿望统计数据
 */
data class WishStatistics(
    val totalWishes: Int = 0,
    val achievementRate: Float = 0f,
    val recentAchievement: String = "暂无",
    val categoryDistribution: Map<WishCategory, Int> = emptyMap(),
    val achievedWishes: Int = 0,
    val activeWishes: Int = 0,
    val incubatingWishes: Int = 0,
    val realizationRate: Float = 0f,
    val recentWishList: List<WishModel> = emptyList()
)

/**
 * 愿望热力图数据
 */
data class WishHeatMapData(
    val date: LocalDate,
    val intensity: Double = 0.0
)

/**
 * 愿望洞察分析
 */
data class WishInsight(
    val keyword: String,
    val frequency: Int = 0,
    val category: WishCategory? = null,
    val suggestion: String = ""
)

/**
 * 愿望成就项
 */
data class WishAchievement(
    val id: String,
    val title: String,
    val description: String = "",
    val isUnlocked: Boolean = false,
    val unlockedAt: LocalDateTime? = null,
    val requiredCount: Int = 0,
    val currentCount: Int = 0,
    val category: String = ""
)

/**
 * 热力图日期数据
 */
data class HeatMapDay(
    val date: LocalDate,
    val intensity: Int = 0
)

/**
 * 愿望洞察集合
 */
data class WishInsights(
    val frequentKeywords: List<String> = emptyList(),
    val monthlyRecommendation: String = "暂无建议",
    val insights: List<WishInsight> = emptyList()
)

/**
 * 成就项
 */
data class Achievement(
    val id: String,
    val title: String,
    val description: String = "",
    val isUnlocked: Boolean = false,
    val unlockedAt: LocalDateTime? = null,
    val category: String = "",
    val requiredCount: Int = 0,
    val currentCount: Int = 0
)

/**
 * 愿望筛选条件
 */
data class WishFilters(
    val categories: Set<WishCategory> = emptySet(),
    val statuses: Set<WishStatus> = emptySet(),
    val priorityRange: IntRange = 1..5,
    val yearRange: IntRange = 2024..2025,
    val difficultyLevels: Set<WishDifficulty> = emptySet()
)

/**
 * 🔍 愿望筛选对话框
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun WishFilterDialog(
    currentFilters: WishFilters,
    onFiltersChanged: (WishFilters) -> Unit,
    onDismiss: () -> Unit
) {
    var filters by remember { mutableStateOf(currentFilters) }
    
    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            usePlatformDefaultWidth = false,
            dismissOnBackPress = true,
            dismissOnClickOutside = true
        )
    ) {
        Surface(
            modifier = Modifier
                .fillMaxWidth(0.92f)
                .wrapContentHeight(),
            shape = RoundedCornerShape(20.dp),
            color = ManifestCard
        ) {
            Column(
                modifier = Modifier.padding(20.dp)
            ) {
                // 标题
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "🔍 筛选设置",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold,
                        color = ManifestText
                    )
                    
                    IconButton(
                        onClick = onDismiss,
                        modifier = Modifier.size(24.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Close,
                            contentDescription = "关闭",
                            tint = ManifestTextSecondary
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 愿望状态筛选
                Text(
                    text = "愿望状态",
                    fontSize = 14.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = ManifestText
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                LazyRow(
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    items(
                        items = WishStatus.values().toList(),
                        key = { it.name } // 添加stable key
                    ) { status ->
                        FilterChip(
                            selected = status in filters.statuses,
                            onClick = {
                                filters = if (status in filters.statuses) {
                                    filters.copy(statuses = filters.statuses - status)
                                } else {
                                    filters.copy(statuses = filters.statuses + status)
                                }
                            },
                            label = {
                                Text(
                                    text = when (status) {
                                        WishStatus.ACTIVE -> "孵化中"
                                        WishStatus.ACHIEVED -> "已实现"
                                        WishStatus.ARCHIVED -> "已归档"
                                        WishStatus.CONVERTED_TO_GOAL -> "已转目标"
                                    },
                                    fontSize = 12.sp
                                )
                            }
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 愿望类别筛选
                Text(
                    text = "愿望类别",
                    fontSize = 14.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = ManifestText
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                LazyRow(
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    items(
                        items = WishCategory.values().toList(),
                        key = { it.name } // 添加stable key
                    ) { category ->
                        FilterChip(
                            selected = category in filters.categories,
                            onClick = {
                                filters = if (category in filters.categories) {
                                    filters.copy(categories = filters.categories - category)
                                } else {
                                    filters.copy(categories = filters.categories + category)
                                }
                            },
                            label = {
                                Row(
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    Text(
                                        text = category.emoji,
                                        fontSize = 12.sp
                                    )
                                    Spacer(modifier = Modifier.width(4.dp))
                                    Text(
                                        text = category.displayName,
                                        fontSize = 11.sp
                                    )
                                }
                            }
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(24.dp))
                
                // 操作按钮
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    // 重置按钮
                    OutlinedButton(
                        onClick = {
                            filters = WishFilters()
                        },
                        modifier = Modifier.weight(1f),
                        colors = ButtonDefaults.outlinedButtonColors(
                            contentColor = ManifestTextSecondary
                        )
                    ) {
                        Text("重置", fontSize = 14.sp)
                    }
                    
                    // 应用按钮
                    Button(
                        onClick = {
                            onFiltersChanged(filters)
                        },
                        modifier = Modifier.weight(1f),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = ManifestPrimary
                        )
                    ) {
                        Text("应用", fontSize = 14.sp, color = Color.White)
                    }
                }
            }
        }
    }
}

/**
 * 🎯 筛选结果页面 - 参照小红书/抖音的设计风格
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun WishFilterResultsSheet(
    filteredWishes: List<WishModel>,
    currentFilters: WishFilters,
    onDismiss: () -> Unit,
    onEditFilters: () -> Unit,
    onClearFilters: () -> Unit
) {
    ModalBottomSheet(
        onDismissRequest = onDismiss,
        modifier = Modifier.fillMaxHeight(0.9f),
        containerColor = ManifestBackground
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 16.dp)
        ) {
            // 🎨 顶部操作栏 - 参照抖音设计
            FilterResultsTopBar(
                resultsCount = filteredWishes.size,
                currentFilters = currentFilters,
                onEditFilters = onEditFilters,
                onClearFilters = onClearFilters,
                onDismiss = onDismiss
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 📊 筛选结果内容
            if (filteredWishes.isEmpty()) {
                // 空状态 - 参照小红书的空状态设计
                FilterEmptyState(
                    onEditFilters = onEditFilters,
                    currentFilters = currentFilters
                )
            } else {
                // 愿望结果列表
                LazyColumn(
                    verticalArrangement = Arrangement.spacedBy(12.dp),
                    contentPadding = PaddingValues(bottom = 80.dp)
                ) {
                    items(
                        items = filteredWishes,
                        key = { it.id }
                    ) { wish ->
                        FilteredWishCard(wish = wish)
                    }
                }
            }
        }
    }
}

/**
 * 🎨 筛选结果顶部操作栏
 */
@Composable
private fun FilterResultsTopBar(
    resultsCount: Int,
    currentFilters: WishFilters,
    onEditFilters: () -> Unit,
    onClearFilters: () -> Unit,
    onDismiss: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 左侧：结果标题
        Column {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "🎯",
                    fontSize = 18.sp
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "筛选结果",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = ManifestText
                )
            }
            
            Spacer(modifier = Modifier.height(4.dp))
            
            Text(
                text = "找到 $resultsCount 个愿望",
                fontSize = 12.sp,
                color = ManifestTextSecondary
            )
        }
        
        // 右侧：操作按钮
        Row(
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // 编辑筛选条件
            Surface(
                onClick = onEditFilters,
                color = ManifestPrimary.copy(alpha = 0.1f),
                shape = RoundedCornerShape(20.dp)
            ) {
                Row(
                    modifier = Modifier.padding(horizontal = 12.dp, vertical = 6.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.Edit,
                        contentDescription = "编辑筛选",
                        tint = ManifestPrimary,
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(
                        text = "编辑",
                        fontSize = 12.sp,
                        color = ManifestPrimary
                    )
                }
            }
            
            // 清除筛选
            Surface(
                onClick = onClearFilters,
                color = ManifestTextSecondary.copy(alpha = 0.1f),
                shape = RoundedCornerShape(20.dp)
            ) {
                Row(
                    modifier = Modifier.padding(horizontal = 12.dp, vertical = 6.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.Clear,
                        contentDescription = "清除筛选",
                        tint = ManifestTextSecondary,
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(
                        text = "清除",
                        fontSize = 12.sp,
                        color = ManifestTextSecondary
                    )
                }
            }
            
            // 关闭按钮
            IconButton(
                onClick = onDismiss,
                modifier = Modifier.size(32.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.Close,
                    contentDescription = "关闭",
                    tint = ManifestTextSecondary
                )
            }
        }
    }
    
    // 当前筛选条件展示
    if (currentFilters.categories.isNotEmpty() || currentFilters.statuses.isNotEmpty()) {
        Spacer(modifier = Modifier.height(8.dp))
        
        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // 状态筛选标签
            items(currentFilters.statuses.toList()) { status ->
                FilterTag(
                    text = when (status) {
                        WishStatus.ACTIVE -> "🌱 孵化中"
                        WishStatus.ACHIEVED -> "✨ 已实现"
                        WishStatus.ARCHIVED -> "📦 已归档"
                        WishStatus.CONVERTED_TO_GOAL -> "🎯 已转目标"
                    },
                    color = when (status) {
                        WishStatus.ACTIVE -> ManifestProgress
                        WishStatus.ACHIEVED -> ManifestSuccess
                        WishStatus.ARCHIVED -> ManifestTextSecondary
                        WishStatus.CONVERTED_TO_GOAL -> ManifestAccent
                    }
                )
            }
            
            // 类别筛选标签
            items(currentFilters.categories.toList()) { category ->
                FilterTag(
                    text = "${category.emoji} ${category.displayName}",
                    color = ManifestPrimary
                )
            }
        }
    }
}

/**
 * 筛选标签组件
 */
@Composable
private fun FilterTag(
    text: String,
    color: Color
) {
    Surface(
        color = color.copy(alpha = 0.1f),
        shape = RoundedCornerShape(16.dp)
    ) {
        Text(
            text = text,
            modifier = Modifier.padding(horizontal = 12.dp, vertical = 6.dp),
            fontSize = 12.sp,
            color = color,
            fontWeight = FontWeight.Medium
        )
    }
}

/**
 * 🌸 筛选结果愿望卡片 - 参照小红书的卡片设计
 */
@Composable
private fun FilteredWishCard(
    wish: WishModel
) {
    Surface(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { /* 点击跳转到愿望详情 */ },
        color = ManifestCard,
        shape = RoundedCornerShape(16.dp),
        shadowElevation = 2.dp
    ) {
        Row(
            modifier = Modifier.padding(16.dp),
            verticalAlignment = Alignment.Top
        ) {
            // 左侧类别图标
            Box(
                modifier = Modifier
                    .size(48.dp)
                    .background(
                        wish.category.getThemeColor().copy(alpha = 0.1f),
                        CircleShape
                    ),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = wish.category.emoji,
                    fontSize = 20.sp
                )
            }
            
            Spacer(modifier = Modifier.width(12.dp))
            
            // 中间内容
            Column(
                modifier = Modifier.weight(1f)
            ) {
                // 愿望标题
                Text(
                    text = wish.title,
                    fontSize = 15.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = ManifestText,
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis
                )
                
                if (wish.description.isNotEmpty()) {
                    Spacer(modifier = Modifier.height(4.dp))
                    Text(
                        text = wish.description,
                        fontSize = 13.sp,
                        color = ManifestTextSecondary,
                        maxLines = 2,
                        overflow = TextOverflow.Ellipsis
                    )
                }
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // 底部信息
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 创建时间
                    Text(
                        text = wish.createdAt.format(DateTimeFormatter.ofPattern("MM-dd")),
                        fontSize = 11.sp,
                        color = ManifestTextSecondary
                    )
                    
                    // 优先级星星
                    Row {
                        repeat(wish.priority) {
                            Icon(
                                imageVector = Icons.Default.Star,
                                contentDescription = null,
                                tint = ManifestAccent,
                                modifier = Modifier.size(12.dp)
                            )
                        }
                    }
                }
            }
            
            // 右侧状态
            Surface(
                color = when (wish.status) {
                    WishStatus.ACTIVE -> ManifestProgress.copy(alpha = 0.1f)
                    WishStatus.ACHIEVED -> ManifestSuccess.copy(alpha = 0.1f)
                    WishStatus.ARCHIVED -> ManifestTextSecondary.copy(alpha = 0.1f)
                    WishStatus.CONVERTED_TO_GOAL -> ManifestAccent.copy(alpha = 0.1f)
                },
                shape = RoundedCornerShape(12.dp)
            ) {
                Text(
                    text = when (wish.status) {
                        WishStatus.ACTIVE -> "孵化"
                        WishStatus.ACHIEVED -> "实现"
                        WishStatus.ARCHIVED -> "归档"
                        WishStatus.CONVERTED_TO_GOAL -> "转目标"
                    },
                    modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp),
                    fontSize = 10.sp,
                    color = when (wish.status) {
                        WishStatus.ACTIVE -> ManifestProgress
                        WishStatus.ACHIEVED -> ManifestSuccess
                        WishStatus.ARCHIVED -> ManifestTextSecondary
                        WishStatus.CONVERTED_TO_GOAL -> ManifestAccent
                    },
                    fontWeight = FontWeight.Medium
                )
            }
        }
    }
}

/**
 * 📭 筛选结果为空的状态页面
 */
@Composable
private fun FilterEmptyState(
    onEditFilters: () -> Unit,
    currentFilters: WishFilters
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 60.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // 空状态图标
        Box(
            modifier = Modifier
                .size(120.dp)
                .background(
                    ManifestTextSecondary.copy(alpha = 0.05f),
                    CircleShape
                ),
            contentAlignment = Alignment.Center
        ) {
            Icon(
                imageVector = Icons.Default.SearchOff,
                contentDescription = null,
                tint = ManifestTextSecondary.copy(alpha = 0.6f),
                modifier = Modifier.size(48.dp)
            )
        }
        
        Spacer(modifier = Modifier.height(24.dp))
        
        Text(
            text = "没有找到匹配的愿望",
            fontSize = 18.sp,
            fontWeight = FontWeight.SemiBold,
            color = ManifestText,
            textAlign = TextAlign.Center
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Text(
            text = "尝试调整筛选条件\n或添加新的愿望",
            fontSize = 14.sp,
            color = ManifestTextSecondary,
            textAlign = TextAlign.Center,
            lineHeight = 20.sp
        )
        
        Spacer(modifier = Modifier.height(32.dp))
        
        // 操作按钮
        Button(
            onClick = onEditFilters,
            colors = ButtonDefaults.buttonColors(
                containerColor = ManifestPrimary
            ),
            shape = RoundedCornerShape(24.dp)
        ) {
            Icon(
                imageVector = Icons.Default.Tune,
                contentDescription = null,
                modifier = Modifier.size(18.dp)
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text("调整筛选条件")
        }
    }
}

/**
 * 应用愿望筛选逻辑
 */
private fun applyWishFilters(statistics: WishStatistics, filters: WishFilters): WishStatistics {
    if (filters.categories.isEmpty() && filters.statuses.isEmpty() && filters.difficultyLevels.isEmpty()) {
        return statistics
    }
    
    // 重新计算统计数据（这里简化处理，实际应该根据筛选条件重新计算）
    return statistics
}

/**
 * 筛选愿望列表
 */
private fun filterWishes(allWishes: List<WishModel>, filters: WishFilters): List<WishModel> {
    return allWishes.filter { wish ->
        // 🔧 状态筛选 - 特殊处理已归档状态
        val statusMatch = if (filters.statuses.isEmpty()) {
            true
        } else {
            filters.statuses.any { filterStatus ->
                when (filterStatus) {
                    WishStatus.ARCHIVED -> {
                        // 已归档筛选：显示所有已归档的愿望，无论其状态如何
                        wish.isArchived
                    }
                    else -> {
                        // 其他状态：按状态精确匹配
                        wish.status == filterStatus
                    }
                }
            }
        }
        
        // 类别筛选
        val categoryMatch = if (filters.categories.isEmpty()) {
            true
        } else {
            wish.category in filters.categories
        }
        
        // 难度筛选
        val difficultyMatch = if (filters.difficultyLevels.isEmpty()) {
            true
        } else {
            wish.difficulty in filters.difficultyLevels
        }
        
        statusMatch && categoryMatch && difficultyMatch
    }
}

/**
 * 获取愿望类别的主题色
 */
private fun WishCategory.getThemeColor(): Color {
    return when (this) {
        WishCategory.CAREER -> ManifestProgress
        WishCategory.HEALTH -> ManifestSuccess
        WishCategory.RELATIONSHIP -> ManifestLove
        WishCategory.LEARNING -> ManifestEnergy
        WishCategory.TRAVEL -> Color(0xFF2196F3)
        WishCategory.SHOPPING -> ManifestAccent
        else -> ManifestPrimary
    }
}