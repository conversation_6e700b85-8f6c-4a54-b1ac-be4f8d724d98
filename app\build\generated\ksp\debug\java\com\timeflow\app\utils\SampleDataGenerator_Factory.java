package com.timeflow.app.utils;

import com.timeflow.app.data.repository.KanbanBoardRepository;
import com.timeflow.app.data.repository.KanbanColumnRepository;
import com.timeflow.app.data.repository.TaskRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SampleDataGenerator_Factory implements Factory<SampleDataGenerator> {
  private final Provider<KanbanBoardRepository> boardRepositoryProvider;

  private final Provider<KanbanColumnRepository> columnRepositoryProvider;

  private final Provider<TaskRepository> taskRepositoryProvider;

  public SampleDataGenerator_Factory(Provider<KanbanBoardRepository> boardRepositoryProvider,
      Provider<KanbanColumnRepository> columnRepositoryProvider,
      Provider<TaskRepository> taskRepositoryProvider) {
    this.boardRepositoryProvider = boardRepositoryProvider;
    this.columnRepositoryProvider = columnRepositoryProvider;
    this.taskRepositoryProvider = taskRepositoryProvider;
  }

  @Override
  public SampleDataGenerator get() {
    return newInstance(boardRepositoryProvider.get(), columnRepositoryProvider.get(), taskRepositoryProvider);
  }

  public static SampleDataGenerator_Factory create(
      Provider<KanbanBoardRepository> boardRepositoryProvider,
      Provider<KanbanColumnRepository> columnRepositoryProvider,
      Provider<TaskRepository> taskRepositoryProvider) {
    return new SampleDataGenerator_Factory(boardRepositoryProvider, columnRepositoryProvider, taskRepositoryProvider);
  }

  public static SampleDataGenerator newInstance(KanbanBoardRepository boardRepository,
      KanbanColumnRepository columnRepository, Provider<TaskRepository> taskRepositoryProvider) {
    return new SampleDataGenerator(boardRepository, columnRepository, taskRepositoryProvider);
  }
}
