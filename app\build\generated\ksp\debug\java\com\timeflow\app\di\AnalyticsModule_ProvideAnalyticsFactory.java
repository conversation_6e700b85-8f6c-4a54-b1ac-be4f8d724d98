package com.timeflow.app.di;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AnalyticsModule_ProvideAnalyticsFactory implements Factory<AnalyticsTracker> {
  private final Provider<Context> contextProvider;

  public AnalyticsModule_ProvideAnalyticsFactory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public AnalyticsTracker get() {
    return provideAnalytics(contextProvider.get());
  }

  public static AnalyticsModule_ProvideAnalyticsFactory create(Provider<Context> contextProvider) {
    return new AnalyticsModule_ProvideAnalyticsFactory(contextProvider);
  }

  public static AnalyticsTracker provideAnalytics(Context context) {
    return Preconditions.checkNotNullFromProvides(AnalyticsModule.INSTANCE.provideAnalytics(context));
  }
}
