package com.timeflow.app.ui.screen.settings;

import com.timeflow.app.data.repository.TaskRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SyncSettingsViewModel_Factory implements Factory<SyncSettingsViewModel> {
  private final Provider<SyncRepository> syncRepositoryProvider;

  private final Provider<TaskRepository> taskRepositoryProvider;

  public SyncSettingsViewModel_Factory(Provider<SyncRepository> syncRepositoryProvider,
      Provider<TaskRepository> taskRepositoryProvider) {
    this.syncRepositoryProvider = syncRepositoryProvider;
    this.taskRepositoryProvider = taskRepositoryProvider;
  }

  @Override
  public SyncSettingsViewModel get() {
    return newInstance(syncRepositoryProvider.get(), taskRepositoryProvider.get());
  }

  public static SyncSettingsViewModel_Factory create(
      Provider<SyncRepository> syncRepositoryProvider,
      Provider<TaskRepository> taskRepositoryProvider) {
    return new SyncSettingsViewModel_Factory(syncRepositoryProvider, taskRepositoryProvider);
  }

  public static SyncSettingsViewModel newInstance(SyncRepository syncRepository,
      TaskRepository taskRepository) {
    return new SyncSettingsViewModel(syncRepository, taskRepository);
  }
}
