package com.timeflow.app.ui.language;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class LanguageSettingsViewModel_Factory implements Factory<LanguageSettingsViewModel> {
  @Override
  public LanguageSettingsViewModel get() {
    return newInstance();
  }

  public static LanguageSettingsViewModel_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static LanguageSettingsViewModel newInstance() {
    return new LanguageSettingsViewModel();
  }

  private static final class InstanceHolder {
    private static final LanguageSettingsViewModel_Factory INSTANCE = new LanguageSettingsViewModel_Factory();
  }
}
