package com.timeflow.app.data.dao;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.timeflow.app.data.converter.DateTimeConverter;
import com.timeflow.app.data.entity.GoalSubTaskTemplate;
import com.timeflow.app.data.entity.GoalTemplate;
import java.lang.Class;
import java.lang.Double;
import java.lang.Exception;
import java.lang.IllegalStateException;
import java.lang.Integer;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class GoalTemplateDao_Impl implements GoalTemplateDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<GoalTemplate> __insertionAdapterOfGoalTemplate;

  private final DateTimeConverter __dateTimeConverter = new DateTimeConverter();

  private final EntityInsertionAdapter<GoalSubTaskTemplate> __insertionAdapterOfGoalSubTaskTemplate;

  private final EntityDeletionOrUpdateAdapter<GoalTemplate> __deletionAdapterOfGoalTemplate;

  private final EntityDeletionOrUpdateAdapter<GoalTemplate> __updateAdapterOfGoalTemplate;

  private final EntityDeletionOrUpdateAdapter<GoalSubTaskTemplate> __updateAdapterOfGoalSubTaskTemplate;

  private final SharedSQLiteStatement __preparedStmtOfDeleteTemplateById;

  private final SharedSQLiteStatement __preparedStmtOfDeleteAllSubTaskTemplatesForTemplate;

  private final SharedSQLiteStatement __preparedStmtOfIncrementTemplateUsage;

  public GoalTemplateDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfGoalTemplate = new EntityInsertionAdapter<GoalTemplate>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `goal_templates` (`id`,`name`,`description`,`category`,`categoryId`,`iconName`,`colorHex`,`usageCount`,`lastUsed`,`createdAt`,`defaultTitle`,`defaultDescription`,`defaultPriority`,`defaultTags`,`defaultDurationDays`,`goalType`,`defaultTargetValue`,`defaultUnit`,`isRecurring`,`recurringSettingsJson`,`reminderSettingsJson`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final GoalTemplate entity) {
        statement.bindString(1, entity.getId());
        statement.bindString(2, entity.getName());
        statement.bindString(3, entity.getDescription());
        statement.bindString(4, entity.getCategory());
        statement.bindString(5, entity.getCategoryId());
        statement.bindString(6, entity.getIconName());
        statement.bindString(7, entity.getColorHex());
        statement.bindLong(8, entity.getUsageCount());
        final Long _tmp = __dateTimeConverter.fromLocalDateTime(entity.getLastUsed());
        if (_tmp == null) {
          statement.bindNull(9);
        } else {
          statement.bindLong(9, _tmp);
        }
        final Long _tmp_1 = __dateTimeConverter.fromLocalDateTime(entity.getCreatedAt());
        if (_tmp_1 == null) {
          statement.bindNull(10);
        } else {
          statement.bindLong(10, _tmp_1);
        }
        statement.bindString(11, entity.getDefaultTitle());
        statement.bindString(12, entity.getDefaultDescription());
        statement.bindString(13, entity.getDefaultPriority());
        statement.bindString(14, entity.getDefaultTags());
        if (entity.getDefaultDurationDays() == null) {
          statement.bindNull(15);
        } else {
          statement.bindLong(15, entity.getDefaultDurationDays());
        }
        statement.bindString(16, entity.getGoalType());
        if (entity.getDefaultTargetValue() == null) {
          statement.bindNull(17);
        } else {
          statement.bindDouble(17, entity.getDefaultTargetValue());
        }
        if (entity.getDefaultUnit() == null) {
          statement.bindNull(18);
        } else {
          statement.bindString(18, entity.getDefaultUnit());
        }
        final int _tmp_2 = entity.isRecurring() ? 1 : 0;
        statement.bindLong(19, _tmp_2);
        statement.bindString(20, entity.getRecurringSettingsJson());
        statement.bindString(21, entity.getReminderSettingsJson());
      }
    };
    this.__insertionAdapterOfGoalSubTaskTemplate = new EntityInsertionAdapter<GoalSubTaskTemplate>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `goal_subtask_templates` (`id`,`templateId`,`title`,`description`,`estimatedDurationDays`,`orderIndex`) VALUES (?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final GoalSubTaskTemplate entity) {
        statement.bindString(1, entity.getId());
        statement.bindString(2, entity.getTemplateId());
        statement.bindString(3, entity.getTitle());
        statement.bindString(4, entity.getDescription());
        statement.bindLong(5, entity.getEstimatedDurationDays());
        statement.bindLong(6, entity.getOrderIndex());
      }
    };
    this.__deletionAdapterOfGoalTemplate = new EntityDeletionOrUpdateAdapter<GoalTemplate>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `goal_templates` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final GoalTemplate entity) {
        statement.bindString(1, entity.getId());
      }
    };
    this.__updateAdapterOfGoalTemplate = new EntityDeletionOrUpdateAdapter<GoalTemplate>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `goal_templates` SET `id` = ?,`name` = ?,`description` = ?,`category` = ?,`categoryId` = ?,`iconName` = ?,`colorHex` = ?,`usageCount` = ?,`lastUsed` = ?,`createdAt` = ?,`defaultTitle` = ?,`defaultDescription` = ?,`defaultPriority` = ?,`defaultTags` = ?,`defaultDurationDays` = ?,`goalType` = ?,`defaultTargetValue` = ?,`defaultUnit` = ?,`isRecurring` = ?,`recurringSettingsJson` = ?,`reminderSettingsJson` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final GoalTemplate entity) {
        statement.bindString(1, entity.getId());
        statement.bindString(2, entity.getName());
        statement.bindString(3, entity.getDescription());
        statement.bindString(4, entity.getCategory());
        statement.bindString(5, entity.getCategoryId());
        statement.bindString(6, entity.getIconName());
        statement.bindString(7, entity.getColorHex());
        statement.bindLong(8, entity.getUsageCount());
        final Long _tmp = __dateTimeConverter.fromLocalDateTime(entity.getLastUsed());
        if (_tmp == null) {
          statement.bindNull(9);
        } else {
          statement.bindLong(9, _tmp);
        }
        final Long _tmp_1 = __dateTimeConverter.fromLocalDateTime(entity.getCreatedAt());
        if (_tmp_1 == null) {
          statement.bindNull(10);
        } else {
          statement.bindLong(10, _tmp_1);
        }
        statement.bindString(11, entity.getDefaultTitle());
        statement.bindString(12, entity.getDefaultDescription());
        statement.bindString(13, entity.getDefaultPriority());
        statement.bindString(14, entity.getDefaultTags());
        if (entity.getDefaultDurationDays() == null) {
          statement.bindNull(15);
        } else {
          statement.bindLong(15, entity.getDefaultDurationDays());
        }
        statement.bindString(16, entity.getGoalType());
        if (entity.getDefaultTargetValue() == null) {
          statement.bindNull(17);
        } else {
          statement.bindDouble(17, entity.getDefaultTargetValue());
        }
        if (entity.getDefaultUnit() == null) {
          statement.bindNull(18);
        } else {
          statement.bindString(18, entity.getDefaultUnit());
        }
        final int _tmp_2 = entity.isRecurring() ? 1 : 0;
        statement.bindLong(19, _tmp_2);
        statement.bindString(20, entity.getRecurringSettingsJson());
        statement.bindString(21, entity.getReminderSettingsJson());
        statement.bindString(22, entity.getId());
      }
    };
    this.__updateAdapterOfGoalSubTaskTemplate = new EntityDeletionOrUpdateAdapter<GoalSubTaskTemplate>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `goal_subtask_templates` SET `id` = ?,`templateId` = ?,`title` = ?,`description` = ?,`estimatedDurationDays` = ?,`orderIndex` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final GoalSubTaskTemplate entity) {
        statement.bindString(1, entity.getId());
        statement.bindString(2, entity.getTemplateId());
        statement.bindString(3, entity.getTitle());
        statement.bindString(4, entity.getDescription());
        statement.bindLong(5, entity.getEstimatedDurationDays());
        statement.bindLong(6, entity.getOrderIndex());
        statement.bindString(7, entity.getId());
      }
    };
    this.__preparedStmtOfDeleteTemplateById = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM goal_templates WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteAllSubTaskTemplatesForTemplate = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM goal_subtask_templates WHERE templateId = ?";
        return _query;
      }
    };
    this.__preparedStmtOfIncrementTemplateUsage = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE goal_templates SET usageCount = usageCount + 1, lastUsed = ? WHERE id = ?";
        return _query;
      }
    };
  }

  @Override
  public Object insertTemplate(final GoalTemplate template,
      final Continuation<? super Long> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Long>() {
      @Override
      @NonNull
      public Long call() throws Exception {
        __db.beginTransaction();
        try {
          final Long _result = __insertionAdapterOfGoalTemplate.insertAndReturnId(template);
          __db.setTransactionSuccessful();
          return _result;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object insertSubTaskTemplate(final GoalSubTaskTemplate subTaskTemplate,
      final Continuation<? super Long> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Long>() {
      @Override
      @NonNull
      public Long call() throws Exception {
        __db.beginTransaction();
        try {
          final Long _result = __insertionAdapterOfGoalSubTaskTemplate.insertAndReturnId(subTaskTemplate);
          __db.setTransactionSuccessful();
          return _result;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object insertSubTaskTemplates(final List<GoalSubTaskTemplate> subTaskTemplates,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfGoalSubTaskTemplate.insert(subTaskTemplates);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteTemplate(final GoalTemplate template,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfGoalTemplate.handle(template);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateTemplate(final GoalTemplate template,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfGoalTemplate.handle(template);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateSubTaskTemplate(final GoalSubTaskTemplate subTaskTemplate,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfGoalSubTaskTemplate.handle(subTaskTemplate);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteTemplateById(final String templateId,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteTemplateById.acquire();
        int _argIndex = 1;
        _stmt.bindString(_argIndex, templateId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteTemplateById.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteAllSubTaskTemplatesForTemplate(final String templateId,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteAllSubTaskTemplatesForTemplate.acquire();
        int _argIndex = 1;
        _stmt.bindString(_argIndex, templateId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteAllSubTaskTemplatesForTemplate.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object incrementTemplateUsage(final String templateId, final long timestamp,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfIncrementTemplateUsage.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, timestamp);
        _argIndex = 2;
        _stmt.bindString(_argIndex, templateId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfIncrementTemplateUsage.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<GoalTemplate>> getAllTemplates() {
    final String _sql = "SELECT * FROM goal_templates ORDER BY name";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"goal_templates"}, new Callable<List<GoalTemplate>>() {
      @Override
      @NonNull
      public List<GoalTemplate> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfCategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "categoryId");
          final int _cursorIndexOfIconName = CursorUtil.getColumnIndexOrThrow(_cursor, "iconName");
          final int _cursorIndexOfColorHex = CursorUtil.getColumnIndexOrThrow(_cursor, "colorHex");
          final int _cursorIndexOfUsageCount = CursorUtil.getColumnIndexOrThrow(_cursor, "usageCount");
          final int _cursorIndexOfLastUsed = CursorUtil.getColumnIndexOrThrow(_cursor, "lastUsed");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfDefaultTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultTitle");
          final int _cursorIndexOfDefaultDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultDescription");
          final int _cursorIndexOfDefaultPriority = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultPriority");
          final int _cursorIndexOfDefaultTags = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultTags");
          final int _cursorIndexOfDefaultDurationDays = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultDurationDays");
          final int _cursorIndexOfGoalType = CursorUtil.getColumnIndexOrThrow(_cursor, "goalType");
          final int _cursorIndexOfDefaultTargetValue = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultTargetValue");
          final int _cursorIndexOfDefaultUnit = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultUnit");
          final int _cursorIndexOfIsRecurring = CursorUtil.getColumnIndexOrThrow(_cursor, "isRecurring");
          final int _cursorIndexOfRecurringSettingsJson = CursorUtil.getColumnIndexOrThrow(_cursor, "recurringSettingsJson");
          final int _cursorIndexOfReminderSettingsJson = CursorUtil.getColumnIndexOrThrow(_cursor, "reminderSettingsJson");
          final List<GoalTemplate> _result = new ArrayList<GoalTemplate>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final GoalTemplate _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpDescription;
            _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            final String _tmpCategory;
            _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            final String _tmpCategoryId;
            _tmpCategoryId = _cursor.getString(_cursorIndexOfCategoryId);
            final String _tmpIconName;
            _tmpIconName = _cursor.getString(_cursorIndexOfIconName);
            final String _tmpColorHex;
            _tmpColorHex = _cursor.getString(_cursorIndexOfColorHex);
            final int _tmpUsageCount;
            _tmpUsageCount = _cursor.getInt(_cursorIndexOfUsageCount);
            final LocalDateTime _tmpLastUsed;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfLastUsed)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfLastUsed);
            }
            _tmpLastUsed = __dateTimeConverter.toLocalDateTime(_tmp);
            final LocalDateTime _tmpCreatedAt;
            final Long _tmp_1;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            final LocalDateTime _tmp_2 = __dateTimeConverter.toLocalDateTime(_tmp_1);
            if (_tmp_2 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpCreatedAt = _tmp_2;
            }
            final String _tmpDefaultTitle;
            _tmpDefaultTitle = _cursor.getString(_cursorIndexOfDefaultTitle);
            final String _tmpDefaultDescription;
            _tmpDefaultDescription = _cursor.getString(_cursorIndexOfDefaultDescription);
            final String _tmpDefaultPriority;
            _tmpDefaultPriority = _cursor.getString(_cursorIndexOfDefaultPriority);
            final String _tmpDefaultTags;
            _tmpDefaultTags = _cursor.getString(_cursorIndexOfDefaultTags);
            final Integer _tmpDefaultDurationDays;
            if (_cursor.isNull(_cursorIndexOfDefaultDurationDays)) {
              _tmpDefaultDurationDays = null;
            } else {
              _tmpDefaultDurationDays = _cursor.getInt(_cursorIndexOfDefaultDurationDays);
            }
            final String _tmpGoalType;
            _tmpGoalType = _cursor.getString(_cursorIndexOfGoalType);
            final Double _tmpDefaultTargetValue;
            if (_cursor.isNull(_cursorIndexOfDefaultTargetValue)) {
              _tmpDefaultTargetValue = null;
            } else {
              _tmpDefaultTargetValue = _cursor.getDouble(_cursorIndexOfDefaultTargetValue);
            }
            final String _tmpDefaultUnit;
            if (_cursor.isNull(_cursorIndexOfDefaultUnit)) {
              _tmpDefaultUnit = null;
            } else {
              _tmpDefaultUnit = _cursor.getString(_cursorIndexOfDefaultUnit);
            }
            final boolean _tmpIsRecurring;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfIsRecurring);
            _tmpIsRecurring = _tmp_3 != 0;
            final String _tmpRecurringSettingsJson;
            _tmpRecurringSettingsJson = _cursor.getString(_cursorIndexOfRecurringSettingsJson);
            final String _tmpReminderSettingsJson;
            _tmpReminderSettingsJson = _cursor.getString(_cursorIndexOfReminderSettingsJson);
            _item = new GoalTemplate(_tmpId,_tmpName,_tmpDescription,_tmpCategory,_tmpCategoryId,_tmpIconName,_tmpColorHex,_tmpUsageCount,_tmpLastUsed,_tmpCreatedAt,_tmpDefaultTitle,_tmpDefaultDescription,_tmpDefaultPriority,_tmpDefaultTags,_tmpDefaultDurationDays,_tmpGoalType,_tmpDefaultTargetValue,_tmpDefaultUnit,_tmpIsRecurring,_tmpRecurringSettingsJson,_tmpReminderSettingsJson);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getAllTemplatesSync(final Continuation<? super List<GoalTemplate>> $completion) {
    final String _sql = "SELECT * FROM goal_templates ORDER BY name";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<GoalTemplate>>() {
      @Override
      @NonNull
      public List<GoalTemplate> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfCategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "categoryId");
          final int _cursorIndexOfIconName = CursorUtil.getColumnIndexOrThrow(_cursor, "iconName");
          final int _cursorIndexOfColorHex = CursorUtil.getColumnIndexOrThrow(_cursor, "colorHex");
          final int _cursorIndexOfUsageCount = CursorUtil.getColumnIndexOrThrow(_cursor, "usageCount");
          final int _cursorIndexOfLastUsed = CursorUtil.getColumnIndexOrThrow(_cursor, "lastUsed");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfDefaultTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultTitle");
          final int _cursorIndexOfDefaultDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultDescription");
          final int _cursorIndexOfDefaultPriority = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultPriority");
          final int _cursorIndexOfDefaultTags = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultTags");
          final int _cursorIndexOfDefaultDurationDays = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultDurationDays");
          final int _cursorIndexOfGoalType = CursorUtil.getColumnIndexOrThrow(_cursor, "goalType");
          final int _cursorIndexOfDefaultTargetValue = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultTargetValue");
          final int _cursorIndexOfDefaultUnit = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultUnit");
          final int _cursorIndexOfIsRecurring = CursorUtil.getColumnIndexOrThrow(_cursor, "isRecurring");
          final int _cursorIndexOfRecurringSettingsJson = CursorUtil.getColumnIndexOrThrow(_cursor, "recurringSettingsJson");
          final int _cursorIndexOfReminderSettingsJson = CursorUtil.getColumnIndexOrThrow(_cursor, "reminderSettingsJson");
          final List<GoalTemplate> _result = new ArrayList<GoalTemplate>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final GoalTemplate _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpDescription;
            _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            final String _tmpCategory;
            _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            final String _tmpCategoryId;
            _tmpCategoryId = _cursor.getString(_cursorIndexOfCategoryId);
            final String _tmpIconName;
            _tmpIconName = _cursor.getString(_cursorIndexOfIconName);
            final String _tmpColorHex;
            _tmpColorHex = _cursor.getString(_cursorIndexOfColorHex);
            final int _tmpUsageCount;
            _tmpUsageCount = _cursor.getInt(_cursorIndexOfUsageCount);
            final LocalDateTime _tmpLastUsed;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfLastUsed)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfLastUsed);
            }
            _tmpLastUsed = __dateTimeConverter.toLocalDateTime(_tmp);
            final LocalDateTime _tmpCreatedAt;
            final Long _tmp_1;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            final LocalDateTime _tmp_2 = __dateTimeConverter.toLocalDateTime(_tmp_1);
            if (_tmp_2 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpCreatedAt = _tmp_2;
            }
            final String _tmpDefaultTitle;
            _tmpDefaultTitle = _cursor.getString(_cursorIndexOfDefaultTitle);
            final String _tmpDefaultDescription;
            _tmpDefaultDescription = _cursor.getString(_cursorIndexOfDefaultDescription);
            final String _tmpDefaultPriority;
            _tmpDefaultPriority = _cursor.getString(_cursorIndexOfDefaultPriority);
            final String _tmpDefaultTags;
            _tmpDefaultTags = _cursor.getString(_cursorIndexOfDefaultTags);
            final Integer _tmpDefaultDurationDays;
            if (_cursor.isNull(_cursorIndexOfDefaultDurationDays)) {
              _tmpDefaultDurationDays = null;
            } else {
              _tmpDefaultDurationDays = _cursor.getInt(_cursorIndexOfDefaultDurationDays);
            }
            final String _tmpGoalType;
            _tmpGoalType = _cursor.getString(_cursorIndexOfGoalType);
            final Double _tmpDefaultTargetValue;
            if (_cursor.isNull(_cursorIndexOfDefaultTargetValue)) {
              _tmpDefaultTargetValue = null;
            } else {
              _tmpDefaultTargetValue = _cursor.getDouble(_cursorIndexOfDefaultTargetValue);
            }
            final String _tmpDefaultUnit;
            if (_cursor.isNull(_cursorIndexOfDefaultUnit)) {
              _tmpDefaultUnit = null;
            } else {
              _tmpDefaultUnit = _cursor.getString(_cursorIndexOfDefaultUnit);
            }
            final boolean _tmpIsRecurring;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfIsRecurring);
            _tmpIsRecurring = _tmp_3 != 0;
            final String _tmpRecurringSettingsJson;
            _tmpRecurringSettingsJson = _cursor.getString(_cursorIndexOfRecurringSettingsJson);
            final String _tmpReminderSettingsJson;
            _tmpReminderSettingsJson = _cursor.getString(_cursorIndexOfReminderSettingsJson);
            _item = new GoalTemplate(_tmpId,_tmpName,_tmpDescription,_tmpCategory,_tmpCategoryId,_tmpIconName,_tmpColorHex,_tmpUsageCount,_tmpLastUsed,_tmpCreatedAt,_tmpDefaultTitle,_tmpDefaultDescription,_tmpDefaultPriority,_tmpDefaultTags,_tmpDefaultDurationDays,_tmpGoalType,_tmpDefaultTargetValue,_tmpDefaultUnit,_tmpIsRecurring,_tmpRecurringSettingsJson,_tmpReminderSettingsJson);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getTemplateById(final String templateId,
      final Continuation<? super GoalTemplate> $completion) {
    final String _sql = "SELECT * FROM goal_templates WHERE id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, templateId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<GoalTemplate>() {
      @Override
      @Nullable
      public GoalTemplate call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfCategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "categoryId");
          final int _cursorIndexOfIconName = CursorUtil.getColumnIndexOrThrow(_cursor, "iconName");
          final int _cursorIndexOfColorHex = CursorUtil.getColumnIndexOrThrow(_cursor, "colorHex");
          final int _cursorIndexOfUsageCount = CursorUtil.getColumnIndexOrThrow(_cursor, "usageCount");
          final int _cursorIndexOfLastUsed = CursorUtil.getColumnIndexOrThrow(_cursor, "lastUsed");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfDefaultTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultTitle");
          final int _cursorIndexOfDefaultDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultDescription");
          final int _cursorIndexOfDefaultPriority = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultPriority");
          final int _cursorIndexOfDefaultTags = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultTags");
          final int _cursorIndexOfDefaultDurationDays = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultDurationDays");
          final int _cursorIndexOfGoalType = CursorUtil.getColumnIndexOrThrow(_cursor, "goalType");
          final int _cursorIndexOfDefaultTargetValue = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultTargetValue");
          final int _cursorIndexOfDefaultUnit = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultUnit");
          final int _cursorIndexOfIsRecurring = CursorUtil.getColumnIndexOrThrow(_cursor, "isRecurring");
          final int _cursorIndexOfRecurringSettingsJson = CursorUtil.getColumnIndexOrThrow(_cursor, "recurringSettingsJson");
          final int _cursorIndexOfReminderSettingsJson = CursorUtil.getColumnIndexOrThrow(_cursor, "reminderSettingsJson");
          final GoalTemplate _result;
          if (_cursor.moveToFirst()) {
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpDescription;
            _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            final String _tmpCategory;
            _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            final String _tmpCategoryId;
            _tmpCategoryId = _cursor.getString(_cursorIndexOfCategoryId);
            final String _tmpIconName;
            _tmpIconName = _cursor.getString(_cursorIndexOfIconName);
            final String _tmpColorHex;
            _tmpColorHex = _cursor.getString(_cursorIndexOfColorHex);
            final int _tmpUsageCount;
            _tmpUsageCount = _cursor.getInt(_cursorIndexOfUsageCount);
            final LocalDateTime _tmpLastUsed;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfLastUsed)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfLastUsed);
            }
            _tmpLastUsed = __dateTimeConverter.toLocalDateTime(_tmp);
            final LocalDateTime _tmpCreatedAt;
            final Long _tmp_1;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            final LocalDateTime _tmp_2 = __dateTimeConverter.toLocalDateTime(_tmp_1);
            if (_tmp_2 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpCreatedAt = _tmp_2;
            }
            final String _tmpDefaultTitle;
            _tmpDefaultTitle = _cursor.getString(_cursorIndexOfDefaultTitle);
            final String _tmpDefaultDescription;
            _tmpDefaultDescription = _cursor.getString(_cursorIndexOfDefaultDescription);
            final String _tmpDefaultPriority;
            _tmpDefaultPriority = _cursor.getString(_cursorIndexOfDefaultPriority);
            final String _tmpDefaultTags;
            _tmpDefaultTags = _cursor.getString(_cursorIndexOfDefaultTags);
            final Integer _tmpDefaultDurationDays;
            if (_cursor.isNull(_cursorIndexOfDefaultDurationDays)) {
              _tmpDefaultDurationDays = null;
            } else {
              _tmpDefaultDurationDays = _cursor.getInt(_cursorIndexOfDefaultDurationDays);
            }
            final String _tmpGoalType;
            _tmpGoalType = _cursor.getString(_cursorIndexOfGoalType);
            final Double _tmpDefaultTargetValue;
            if (_cursor.isNull(_cursorIndexOfDefaultTargetValue)) {
              _tmpDefaultTargetValue = null;
            } else {
              _tmpDefaultTargetValue = _cursor.getDouble(_cursorIndexOfDefaultTargetValue);
            }
            final String _tmpDefaultUnit;
            if (_cursor.isNull(_cursorIndexOfDefaultUnit)) {
              _tmpDefaultUnit = null;
            } else {
              _tmpDefaultUnit = _cursor.getString(_cursorIndexOfDefaultUnit);
            }
            final boolean _tmpIsRecurring;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfIsRecurring);
            _tmpIsRecurring = _tmp_3 != 0;
            final String _tmpRecurringSettingsJson;
            _tmpRecurringSettingsJson = _cursor.getString(_cursorIndexOfRecurringSettingsJson);
            final String _tmpReminderSettingsJson;
            _tmpReminderSettingsJson = _cursor.getString(_cursorIndexOfReminderSettingsJson);
            _result = new GoalTemplate(_tmpId,_tmpName,_tmpDescription,_tmpCategory,_tmpCategoryId,_tmpIconName,_tmpColorHex,_tmpUsageCount,_tmpLastUsed,_tmpCreatedAt,_tmpDefaultTitle,_tmpDefaultDescription,_tmpDefaultPriority,_tmpDefaultTags,_tmpDefaultDurationDays,_tmpGoalType,_tmpDefaultTargetValue,_tmpDefaultUnit,_tmpIsRecurring,_tmpRecurringSettingsJson,_tmpReminderSettingsJson);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<GoalTemplate>> getTemplatesByCategory(final String category) {
    final String _sql = "SELECT * FROM goal_templates WHERE category = ? ORDER BY name";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, category);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"goal_templates"}, new Callable<List<GoalTemplate>>() {
      @Override
      @NonNull
      public List<GoalTemplate> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfCategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "categoryId");
          final int _cursorIndexOfIconName = CursorUtil.getColumnIndexOrThrow(_cursor, "iconName");
          final int _cursorIndexOfColorHex = CursorUtil.getColumnIndexOrThrow(_cursor, "colorHex");
          final int _cursorIndexOfUsageCount = CursorUtil.getColumnIndexOrThrow(_cursor, "usageCount");
          final int _cursorIndexOfLastUsed = CursorUtil.getColumnIndexOrThrow(_cursor, "lastUsed");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfDefaultTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultTitle");
          final int _cursorIndexOfDefaultDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultDescription");
          final int _cursorIndexOfDefaultPriority = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultPriority");
          final int _cursorIndexOfDefaultTags = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultTags");
          final int _cursorIndexOfDefaultDurationDays = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultDurationDays");
          final int _cursorIndexOfGoalType = CursorUtil.getColumnIndexOrThrow(_cursor, "goalType");
          final int _cursorIndexOfDefaultTargetValue = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultTargetValue");
          final int _cursorIndexOfDefaultUnit = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultUnit");
          final int _cursorIndexOfIsRecurring = CursorUtil.getColumnIndexOrThrow(_cursor, "isRecurring");
          final int _cursorIndexOfRecurringSettingsJson = CursorUtil.getColumnIndexOrThrow(_cursor, "recurringSettingsJson");
          final int _cursorIndexOfReminderSettingsJson = CursorUtil.getColumnIndexOrThrow(_cursor, "reminderSettingsJson");
          final List<GoalTemplate> _result = new ArrayList<GoalTemplate>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final GoalTemplate _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpDescription;
            _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            final String _tmpCategory;
            _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            final String _tmpCategoryId;
            _tmpCategoryId = _cursor.getString(_cursorIndexOfCategoryId);
            final String _tmpIconName;
            _tmpIconName = _cursor.getString(_cursorIndexOfIconName);
            final String _tmpColorHex;
            _tmpColorHex = _cursor.getString(_cursorIndexOfColorHex);
            final int _tmpUsageCount;
            _tmpUsageCount = _cursor.getInt(_cursorIndexOfUsageCount);
            final LocalDateTime _tmpLastUsed;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfLastUsed)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfLastUsed);
            }
            _tmpLastUsed = __dateTimeConverter.toLocalDateTime(_tmp);
            final LocalDateTime _tmpCreatedAt;
            final Long _tmp_1;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            final LocalDateTime _tmp_2 = __dateTimeConverter.toLocalDateTime(_tmp_1);
            if (_tmp_2 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpCreatedAt = _tmp_2;
            }
            final String _tmpDefaultTitle;
            _tmpDefaultTitle = _cursor.getString(_cursorIndexOfDefaultTitle);
            final String _tmpDefaultDescription;
            _tmpDefaultDescription = _cursor.getString(_cursorIndexOfDefaultDescription);
            final String _tmpDefaultPriority;
            _tmpDefaultPriority = _cursor.getString(_cursorIndexOfDefaultPriority);
            final String _tmpDefaultTags;
            _tmpDefaultTags = _cursor.getString(_cursorIndexOfDefaultTags);
            final Integer _tmpDefaultDurationDays;
            if (_cursor.isNull(_cursorIndexOfDefaultDurationDays)) {
              _tmpDefaultDurationDays = null;
            } else {
              _tmpDefaultDurationDays = _cursor.getInt(_cursorIndexOfDefaultDurationDays);
            }
            final String _tmpGoalType;
            _tmpGoalType = _cursor.getString(_cursorIndexOfGoalType);
            final Double _tmpDefaultTargetValue;
            if (_cursor.isNull(_cursorIndexOfDefaultTargetValue)) {
              _tmpDefaultTargetValue = null;
            } else {
              _tmpDefaultTargetValue = _cursor.getDouble(_cursorIndexOfDefaultTargetValue);
            }
            final String _tmpDefaultUnit;
            if (_cursor.isNull(_cursorIndexOfDefaultUnit)) {
              _tmpDefaultUnit = null;
            } else {
              _tmpDefaultUnit = _cursor.getString(_cursorIndexOfDefaultUnit);
            }
            final boolean _tmpIsRecurring;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfIsRecurring);
            _tmpIsRecurring = _tmp_3 != 0;
            final String _tmpRecurringSettingsJson;
            _tmpRecurringSettingsJson = _cursor.getString(_cursorIndexOfRecurringSettingsJson);
            final String _tmpReminderSettingsJson;
            _tmpReminderSettingsJson = _cursor.getString(_cursorIndexOfReminderSettingsJson);
            _item = new GoalTemplate(_tmpId,_tmpName,_tmpDescription,_tmpCategory,_tmpCategoryId,_tmpIconName,_tmpColorHex,_tmpUsageCount,_tmpLastUsed,_tmpCreatedAt,_tmpDefaultTitle,_tmpDefaultDescription,_tmpDefaultPriority,_tmpDefaultTags,_tmpDefaultDurationDays,_tmpGoalType,_tmpDefaultTargetValue,_tmpDefaultUnit,_tmpIsRecurring,_tmpRecurringSettingsJson,_tmpReminderSettingsJson);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<GoalTemplate>> getTemplatesByCategoryId(final String categoryId) {
    final String _sql = "SELECT * FROM goal_templates WHERE categoryId = ? ORDER BY usageCount DESC, lastUsed DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, categoryId);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"goal_templates"}, new Callable<List<GoalTemplate>>() {
      @Override
      @NonNull
      public List<GoalTemplate> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfCategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "categoryId");
          final int _cursorIndexOfIconName = CursorUtil.getColumnIndexOrThrow(_cursor, "iconName");
          final int _cursorIndexOfColorHex = CursorUtil.getColumnIndexOrThrow(_cursor, "colorHex");
          final int _cursorIndexOfUsageCount = CursorUtil.getColumnIndexOrThrow(_cursor, "usageCount");
          final int _cursorIndexOfLastUsed = CursorUtil.getColumnIndexOrThrow(_cursor, "lastUsed");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfDefaultTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultTitle");
          final int _cursorIndexOfDefaultDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultDescription");
          final int _cursorIndexOfDefaultPriority = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultPriority");
          final int _cursorIndexOfDefaultTags = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultTags");
          final int _cursorIndexOfDefaultDurationDays = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultDurationDays");
          final int _cursorIndexOfGoalType = CursorUtil.getColumnIndexOrThrow(_cursor, "goalType");
          final int _cursorIndexOfDefaultTargetValue = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultTargetValue");
          final int _cursorIndexOfDefaultUnit = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultUnit");
          final int _cursorIndexOfIsRecurring = CursorUtil.getColumnIndexOrThrow(_cursor, "isRecurring");
          final int _cursorIndexOfRecurringSettingsJson = CursorUtil.getColumnIndexOrThrow(_cursor, "recurringSettingsJson");
          final int _cursorIndexOfReminderSettingsJson = CursorUtil.getColumnIndexOrThrow(_cursor, "reminderSettingsJson");
          final List<GoalTemplate> _result = new ArrayList<GoalTemplate>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final GoalTemplate _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpDescription;
            _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            final String _tmpCategory;
            _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            final String _tmpCategoryId;
            _tmpCategoryId = _cursor.getString(_cursorIndexOfCategoryId);
            final String _tmpIconName;
            _tmpIconName = _cursor.getString(_cursorIndexOfIconName);
            final String _tmpColorHex;
            _tmpColorHex = _cursor.getString(_cursorIndexOfColorHex);
            final int _tmpUsageCount;
            _tmpUsageCount = _cursor.getInt(_cursorIndexOfUsageCount);
            final LocalDateTime _tmpLastUsed;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfLastUsed)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfLastUsed);
            }
            _tmpLastUsed = __dateTimeConverter.toLocalDateTime(_tmp);
            final LocalDateTime _tmpCreatedAt;
            final Long _tmp_1;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            final LocalDateTime _tmp_2 = __dateTimeConverter.toLocalDateTime(_tmp_1);
            if (_tmp_2 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpCreatedAt = _tmp_2;
            }
            final String _tmpDefaultTitle;
            _tmpDefaultTitle = _cursor.getString(_cursorIndexOfDefaultTitle);
            final String _tmpDefaultDescription;
            _tmpDefaultDescription = _cursor.getString(_cursorIndexOfDefaultDescription);
            final String _tmpDefaultPriority;
            _tmpDefaultPriority = _cursor.getString(_cursorIndexOfDefaultPriority);
            final String _tmpDefaultTags;
            _tmpDefaultTags = _cursor.getString(_cursorIndexOfDefaultTags);
            final Integer _tmpDefaultDurationDays;
            if (_cursor.isNull(_cursorIndexOfDefaultDurationDays)) {
              _tmpDefaultDurationDays = null;
            } else {
              _tmpDefaultDurationDays = _cursor.getInt(_cursorIndexOfDefaultDurationDays);
            }
            final String _tmpGoalType;
            _tmpGoalType = _cursor.getString(_cursorIndexOfGoalType);
            final Double _tmpDefaultTargetValue;
            if (_cursor.isNull(_cursorIndexOfDefaultTargetValue)) {
              _tmpDefaultTargetValue = null;
            } else {
              _tmpDefaultTargetValue = _cursor.getDouble(_cursorIndexOfDefaultTargetValue);
            }
            final String _tmpDefaultUnit;
            if (_cursor.isNull(_cursorIndexOfDefaultUnit)) {
              _tmpDefaultUnit = null;
            } else {
              _tmpDefaultUnit = _cursor.getString(_cursorIndexOfDefaultUnit);
            }
            final boolean _tmpIsRecurring;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfIsRecurring);
            _tmpIsRecurring = _tmp_3 != 0;
            final String _tmpRecurringSettingsJson;
            _tmpRecurringSettingsJson = _cursor.getString(_cursorIndexOfRecurringSettingsJson);
            final String _tmpReminderSettingsJson;
            _tmpReminderSettingsJson = _cursor.getString(_cursorIndexOfReminderSettingsJson);
            _item = new GoalTemplate(_tmpId,_tmpName,_tmpDescription,_tmpCategory,_tmpCategoryId,_tmpIconName,_tmpColorHex,_tmpUsageCount,_tmpLastUsed,_tmpCreatedAt,_tmpDefaultTitle,_tmpDefaultDescription,_tmpDefaultPriority,_tmpDefaultTags,_tmpDefaultDurationDays,_tmpGoalType,_tmpDefaultTargetValue,_tmpDefaultUnit,_tmpIsRecurring,_tmpRecurringSettingsJson,_tmpReminderSettingsJson);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<GoalTemplate>> getMostUsedTemplates(final int limit) {
    final String _sql = "SELECT * FROM goal_templates ORDER BY usageCount DESC LIMIT ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, limit);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"goal_templates"}, new Callable<List<GoalTemplate>>() {
      @Override
      @NonNull
      public List<GoalTemplate> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfCategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "categoryId");
          final int _cursorIndexOfIconName = CursorUtil.getColumnIndexOrThrow(_cursor, "iconName");
          final int _cursorIndexOfColorHex = CursorUtil.getColumnIndexOrThrow(_cursor, "colorHex");
          final int _cursorIndexOfUsageCount = CursorUtil.getColumnIndexOrThrow(_cursor, "usageCount");
          final int _cursorIndexOfLastUsed = CursorUtil.getColumnIndexOrThrow(_cursor, "lastUsed");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfDefaultTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultTitle");
          final int _cursorIndexOfDefaultDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultDescription");
          final int _cursorIndexOfDefaultPriority = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultPriority");
          final int _cursorIndexOfDefaultTags = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultTags");
          final int _cursorIndexOfDefaultDurationDays = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultDurationDays");
          final int _cursorIndexOfGoalType = CursorUtil.getColumnIndexOrThrow(_cursor, "goalType");
          final int _cursorIndexOfDefaultTargetValue = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultTargetValue");
          final int _cursorIndexOfDefaultUnit = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultUnit");
          final int _cursorIndexOfIsRecurring = CursorUtil.getColumnIndexOrThrow(_cursor, "isRecurring");
          final int _cursorIndexOfRecurringSettingsJson = CursorUtil.getColumnIndexOrThrow(_cursor, "recurringSettingsJson");
          final int _cursorIndexOfReminderSettingsJson = CursorUtil.getColumnIndexOrThrow(_cursor, "reminderSettingsJson");
          final List<GoalTemplate> _result = new ArrayList<GoalTemplate>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final GoalTemplate _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpDescription;
            _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            final String _tmpCategory;
            _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            final String _tmpCategoryId;
            _tmpCategoryId = _cursor.getString(_cursorIndexOfCategoryId);
            final String _tmpIconName;
            _tmpIconName = _cursor.getString(_cursorIndexOfIconName);
            final String _tmpColorHex;
            _tmpColorHex = _cursor.getString(_cursorIndexOfColorHex);
            final int _tmpUsageCount;
            _tmpUsageCount = _cursor.getInt(_cursorIndexOfUsageCount);
            final LocalDateTime _tmpLastUsed;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfLastUsed)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfLastUsed);
            }
            _tmpLastUsed = __dateTimeConverter.toLocalDateTime(_tmp);
            final LocalDateTime _tmpCreatedAt;
            final Long _tmp_1;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            final LocalDateTime _tmp_2 = __dateTimeConverter.toLocalDateTime(_tmp_1);
            if (_tmp_2 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpCreatedAt = _tmp_2;
            }
            final String _tmpDefaultTitle;
            _tmpDefaultTitle = _cursor.getString(_cursorIndexOfDefaultTitle);
            final String _tmpDefaultDescription;
            _tmpDefaultDescription = _cursor.getString(_cursorIndexOfDefaultDescription);
            final String _tmpDefaultPriority;
            _tmpDefaultPriority = _cursor.getString(_cursorIndexOfDefaultPriority);
            final String _tmpDefaultTags;
            _tmpDefaultTags = _cursor.getString(_cursorIndexOfDefaultTags);
            final Integer _tmpDefaultDurationDays;
            if (_cursor.isNull(_cursorIndexOfDefaultDurationDays)) {
              _tmpDefaultDurationDays = null;
            } else {
              _tmpDefaultDurationDays = _cursor.getInt(_cursorIndexOfDefaultDurationDays);
            }
            final String _tmpGoalType;
            _tmpGoalType = _cursor.getString(_cursorIndexOfGoalType);
            final Double _tmpDefaultTargetValue;
            if (_cursor.isNull(_cursorIndexOfDefaultTargetValue)) {
              _tmpDefaultTargetValue = null;
            } else {
              _tmpDefaultTargetValue = _cursor.getDouble(_cursorIndexOfDefaultTargetValue);
            }
            final String _tmpDefaultUnit;
            if (_cursor.isNull(_cursorIndexOfDefaultUnit)) {
              _tmpDefaultUnit = null;
            } else {
              _tmpDefaultUnit = _cursor.getString(_cursorIndexOfDefaultUnit);
            }
            final boolean _tmpIsRecurring;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfIsRecurring);
            _tmpIsRecurring = _tmp_3 != 0;
            final String _tmpRecurringSettingsJson;
            _tmpRecurringSettingsJson = _cursor.getString(_cursorIndexOfRecurringSettingsJson);
            final String _tmpReminderSettingsJson;
            _tmpReminderSettingsJson = _cursor.getString(_cursorIndexOfReminderSettingsJson);
            _item = new GoalTemplate(_tmpId,_tmpName,_tmpDescription,_tmpCategory,_tmpCategoryId,_tmpIconName,_tmpColorHex,_tmpUsageCount,_tmpLastUsed,_tmpCreatedAt,_tmpDefaultTitle,_tmpDefaultDescription,_tmpDefaultPriority,_tmpDefaultTags,_tmpDefaultDurationDays,_tmpGoalType,_tmpDefaultTargetValue,_tmpDefaultUnit,_tmpIsRecurring,_tmpRecurringSettingsJson,_tmpReminderSettingsJson);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<GoalTemplate>> getRecentlyUsedTemplates(final int limit) {
    final String _sql = "SELECT * FROM goal_templates ORDER BY lastUsed DESC LIMIT ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, limit);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"goal_templates"}, new Callable<List<GoalTemplate>>() {
      @Override
      @NonNull
      public List<GoalTemplate> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfCategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "categoryId");
          final int _cursorIndexOfIconName = CursorUtil.getColumnIndexOrThrow(_cursor, "iconName");
          final int _cursorIndexOfColorHex = CursorUtil.getColumnIndexOrThrow(_cursor, "colorHex");
          final int _cursorIndexOfUsageCount = CursorUtil.getColumnIndexOrThrow(_cursor, "usageCount");
          final int _cursorIndexOfLastUsed = CursorUtil.getColumnIndexOrThrow(_cursor, "lastUsed");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfDefaultTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultTitle");
          final int _cursorIndexOfDefaultDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultDescription");
          final int _cursorIndexOfDefaultPriority = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultPriority");
          final int _cursorIndexOfDefaultTags = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultTags");
          final int _cursorIndexOfDefaultDurationDays = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultDurationDays");
          final int _cursorIndexOfGoalType = CursorUtil.getColumnIndexOrThrow(_cursor, "goalType");
          final int _cursorIndexOfDefaultTargetValue = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultTargetValue");
          final int _cursorIndexOfDefaultUnit = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultUnit");
          final int _cursorIndexOfIsRecurring = CursorUtil.getColumnIndexOrThrow(_cursor, "isRecurring");
          final int _cursorIndexOfRecurringSettingsJson = CursorUtil.getColumnIndexOrThrow(_cursor, "recurringSettingsJson");
          final int _cursorIndexOfReminderSettingsJson = CursorUtil.getColumnIndexOrThrow(_cursor, "reminderSettingsJson");
          final List<GoalTemplate> _result = new ArrayList<GoalTemplate>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final GoalTemplate _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpDescription;
            _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            final String _tmpCategory;
            _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            final String _tmpCategoryId;
            _tmpCategoryId = _cursor.getString(_cursorIndexOfCategoryId);
            final String _tmpIconName;
            _tmpIconName = _cursor.getString(_cursorIndexOfIconName);
            final String _tmpColorHex;
            _tmpColorHex = _cursor.getString(_cursorIndexOfColorHex);
            final int _tmpUsageCount;
            _tmpUsageCount = _cursor.getInt(_cursorIndexOfUsageCount);
            final LocalDateTime _tmpLastUsed;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfLastUsed)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfLastUsed);
            }
            _tmpLastUsed = __dateTimeConverter.toLocalDateTime(_tmp);
            final LocalDateTime _tmpCreatedAt;
            final Long _tmp_1;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            final LocalDateTime _tmp_2 = __dateTimeConverter.toLocalDateTime(_tmp_1);
            if (_tmp_2 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpCreatedAt = _tmp_2;
            }
            final String _tmpDefaultTitle;
            _tmpDefaultTitle = _cursor.getString(_cursorIndexOfDefaultTitle);
            final String _tmpDefaultDescription;
            _tmpDefaultDescription = _cursor.getString(_cursorIndexOfDefaultDescription);
            final String _tmpDefaultPriority;
            _tmpDefaultPriority = _cursor.getString(_cursorIndexOfDefaultPriority);
            final String _tmpDefaultTags;
            _tmpDefaultTags = _cursor.getString(_cursorIndexOfDefaultTags);
            final Integer _tmpDefaultDurationDays;
            if (_cursor.isNull(_cursorIndexOfDefaultDurationDays)) {
              _tmpDefaultDurationDays = null;
            } else {
              _tmpDefaultDurationDays = _cursor.getInt(_cursorIndexOfDefaultDurationDays);
            }
            final String _tmpGoalType;
            _tmpGoalType = _cursor.getString(_cursorIndexOfGoalType);
            final Double _tmpDefaultTargetValue;
            if (_cursor.isNull(_cursorIndexOfDefaultTargetValue)) {
              _tmpDefaultTargetValue = null;
            } else {
              _tmpDefaultTargetValue = _cursor.getDouble(_cursorIndexOfDefaultTargetValue);
            }
            final String _tmpDefaultUnit;
            if (_cursor.isNull(_cursorIndexOfDefaultUnit)) {
              _tmpDefaultUnit = null;
            } else {
              _tmpDefaultUnit = _cursor.getString(_cursorIndexOfDefaultUnit);
            }
            final boolean _tmpIsRecurring;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfIsRecurring);
            _tmpIsRecurring = _tmp_3 != 0;
            final String _tmpRecurringSettingsJson;
            _tmpRecurringSettingsJson = _cursor.getString(_cursorIndexOfRecurringSettingsJson);
            final String _tmpReminderSettingsJson;
            _tmpReminderSettingsJson = _cursor.getString(_cursorIndexOfReminderSettingsJson);
            _item = new GoalTemplate(_tmpId,_tmpName,_tmpDescription,_tmpCategory,_tmpCategoryId,_tmpIconName,_tmpColorHex,_tmpUsageCount,_tmpLastUsed,_tmpCreatedAt,_tmpDefaultTitle,_tmpDefaultDescription,_tmpDefaultPriority,_tmpDefaultTags,_tmpDefaultDurationDays,_tmpGoalType,_tmpDefaultTargetValue,_tmpDefaultUnit,_tmpIsRecurring,_tmpRecurringSettingsJson,_tmpReminderSettingsJson);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getSubTaskTemplatesForTemplate(final String templateId,
      final Continuation<? super List<GoalSubTaskTemplate>> $completion) {
    final String _sql = "SELECT * FROM goal_subtask_templates WHERE templateId = ? ORDER BY orderIndex";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, templateId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<GoalSubTaskTemplate>>() {
      @Override
      @NonNull
      public List<GoalSubTaskTemplate> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfTemplateId = CursorUtil.getColumnIndexOrThrow(_cursor, "templateId");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfEstimatedDurationDays = CursorUtil.getColumnIndexOrThrow(_cursor, "estimatedDurationDays");
          final int _cursorIndexOfOrderIndex = CursorUtil.getColumnIndexOrThrow(_cursor, "orderIndex");
          final List<GoalSubTaskTemplate> _result = new ArrayList<GoalSubTaskTemplate>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final GoalSubTaskTemplate _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpTemplateId;
            _tmpTemplateId = _cursor.getString(_cursorIndexOfTemplateId);
            final String _tmpTitle;
            _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            final String _tmpDescription;
            _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            final int _tmpEstimatedDurationDays;
            _tmpEstimatedDurationDays = _cursor.getInt(_cursorIndexOfEstimatedDurationDays);
            final int _tmpOrderIndex;
            _tmpOrderIndex = _cursor.getInt(_cursorIndexOfOrderIndex);
            _item = new GoalSubTaskTemplate(_tmpId,_tmpTemplateId,_tmpTitle,_tmpDescription,_tmpEstimatedDurationDays,_tmpOrderIndex);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<GoalTemplate>> searchTemplates(final String searchText) {
    final String _sql = "SELECT * FROM goal_templates WHERE name LIKE '%' || ? || '%' OR description LIKE '%' || ? || '%'";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindString(_argIndex, searchText);
    _argIndex = 2;
    _statement.bindString(_argIndex, searchText);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"goal_templates"}, new Callable<List<GoalTemplate>>() {
      @Override
      @NonNull
      public List<GoalTemplate> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfCategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "categoryId");
          final int _cursorIndexOfIconName = CursorUtil.getColumnIndexOrThrow(_cursor, "iconName");
          final int _cursorIndexOfColorHex = CursorUtil.getColumnIndexOrThrow(_cursor, "colorHex");
          final int _cursorIndexOfUsageCount = CursorUtil.getColumnIndexOrThrow(_cursor, "usageCount");
          final int _cursorIndexOfLastUsed = CursorUtil.getColumnIndexOrThrow(_cursor, "lastUsed");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfDefaultTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultTitle");
          final int _cursorIndexOfDefaultDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultDescription");
          final int _cursorIndexOfDefaultPriority = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultPriority");
          final int _cursorIndexOfDefaultTags = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultTags");
          final int _cursorIndexOfDefaultDurationDays = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultDurationDays");
          final int _cursorIndexOfGoalType = CursorUtil.getColumnIndexOrThrow(_cursor, "goalType");
          final int _cursorIndexOfDefaultTargetValue = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultTargetValue");
          final int _cursorIndexOfDefaultUnit = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultUnit");
          final int _cursorIndexOfIsRecurring = CursorUtil.getColumnIndexOrThrow(_cursor, "isRecurring");
          final int _cursorIndexOfRecurringSettingsJson = CursorUtil.getColumnIndexOrThrow(_cursor, "recurringSettingsJson");
          final int _cursorIndexOfReminderSettingsJson = CursorUtil.getColumnIndexOrThrow(_cursor, "reminderSettingsJson");
          final List<GoalTemplate> _result = new ArrayList<GoalTemplate>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final GoalTemplate _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpDescription;
            _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            final String _tmpCategory;
            _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            final String _tmpCategoryId;
            _tmpCategoryId = _cursor.getString(_cursorIndexOfCategoryId);
            final String _tmpIconName;
            _tmpIconName = _cursor.getString(_cursorIndexOfIconName);
            final String _tmpColorHex;
            _tmpColorHex = _cursor.getString(_cursorIndexOfColorHex);
            final int _tmpUsageCount;
            _tmpUsageCount = _cursor.getInt(_cursorIndexOfUsageCount);
            final LocalDateTime _tmpLastUsed;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfLastUsed)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfLastUsed);
            }
            _tmpLastUsed = __dateTimeConverter.toLocalDateTime(_tmp);
            final LocalDateTime _tmpCreatedAt;
            final Long _tmp_1;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            final LocalDateTime _tmp_2 = __dateTimeConverter.toLocalDateTime(_tmp_1);
            if (_tmp_2 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpCreatedAt = _tmp_2;
            }
            final String _tmpDefaultTitle;
            _tmpDefaultTitle = _cursor.getString(_cursorIndexOfDefaultTitle);
            final String _tmpDefaultDescription;
            _tmpDefaultDescription = _cursor.getString(_cursorIndexOfDefaultDescription);
            final String _tmpDefaultPriority;
            _tmpDefaultPriority = _cursor.getString(_cursorIndexOfDefaultPriority);
            final String _tmpDefaultTags;
            _tmpDefaultTags = _cursor.getString(_cursorIndexOfDefaultTags);
            final Integer _tmpDefaultDurationDays;
            if (_cursor.isNull(_cursorIndexOfDefaultDurationDays)) {
              _tmpDefaultDurationDays = null;
            } else {
              _tmpDefaultDurationDays = _cursor.getInt(_cursorIndexOfDefaultDurationDays);
            }
            final String _tmpGoalType;
            _tmpGoalType = _cursor.getString(_cursorIndexOfGoalType);
            final Double _tmpDefaultTargetValue;
            if (_cursor.isNull(_cursorIndexOfDefaultTargetValue)) {
              _tmpDefaultTargetValue = null;
            } else {
              _tmpDefaultTargetValue = _cursor.getDouble(_cursorIndexOfDefaultTargetValue);
            }
            final String _tmpDefaultUnit;
            if (_cursor.isNull(_cursorIndexOfDefaultUnit)) {
              _tmpDefaultUnit = null;
            } else {
              _tmpDefaultUnit = _cursor.getString(_cursorIndexOfDefaultUnit);
            }
            final boolean _tmpIsRecurring;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfIsRecurring);
            _tmpIsRecurring = _tmp_3 != 0;
            final String _tmpRecurringSettingsJson;
            _tmpRecurringSettingsJson = _cursor.getString(_cursorIndexOfRecurringSettingsJson);
            final String _tmpReminderSettingsJson;
            _tmpReminderSettingsJson = _cursor.getString(_cursorIndexOfReminderSettingsJson);
            _item = new GoalTemplate(_tmpId,_tmpName,_tmpDescription,_tmpCategory,_tmpCategoryId,_tmpIconName,_tmpColorHex,_tmpUsageCount,_tmpLastUsed,_tmpCreatedAt,_tmpDefaultTitle,_tmpDefaultDescription,_tmpDefaultPriority,_tmpDefaultTags,_tmpDefaultDurationDays,_tmpGoalType,_tmpDefaultTargetValue,_tmpDefaultUnit,_tmpIsRecurring,_tmpRecurringSettingsJson,_tmpReminderSettingsJson);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getTemplatesPaged(final int pageSize, final int offset,
      final Continuation<? super List<GoalTemplate>> $completion) {
    final String _sql = "SELECT * FROM goal_templates ORDER BY name LIMIT ? OFFSET ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, pageSize);
    _argIndex = 2;
    _statement.bindLong(_argIndex, offset);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<GoalTemplate>>() {
      @Override
      @NonNull
      public List<GoalTemplate> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfCategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "categoryId");
          final int _cursorIndexOfIconName = CursorUtil.getColumnIndexOrThrow(_cursor, "iconName");
          final int _cursorIndexOfColorHex = CursorUtil.getColumnIndexOrThrow(_cursor, "colorHex");
          final int _cursorIndexOfUsageCount = CursorUtil.getColumnIndexOrThrow(_cursor, "usageCount");
          final int _cursorIndexOfLastUsed = CursorUtil.getColumnIndexOrThrow(_cursor, "lastUsed");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfDefaultTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultTitle");
          final int _cursorIndexOfDefaultDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultDescription");
          final int _cursorIndexOfDefaultPriority = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultPriority");
          final int _cursorIndexOfDefaultTags = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultTags");
          final int _cursorIndexOfDefaultDurationDays = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultDurationDays");
          final int _cursorIndexOfGoalType = CursorUtil.getColumnIndexOrThrow(_cursor, "goalType");
          final int _cursorIndexOfDefaultTargetValue = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultTargetValue");
          final int _cursorIndexOfDefaultUnit = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultUnit");
          final int _cursorIndexOfIsRecurring = CursorUtil.getColumnIndexOrThrow(_cursor, "isRecurring");
          final int _cursorIndexOfRecurringSettingsJson = CursorUtil.getColumnIndexOrThrow(_cursor, "recurringSettingsJson");
          final int _cursorIndexOfReminderSettingsJson = CursorUtil.getColumnIndexOrThrow(_cursor, "reminderSettingsJson");
          final List<GoalTemplate> _result = new ArrayList<GoalTemplate>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final GoalTemplate _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpDescription;
            _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            final String _tmpCategory;
            _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            final String _tmpCategoryId;
            _tmpCategoryId = _cursor.getString(_cursorIndexOfCategoryId);
            final String _tmpIconName;
            _tmpIconName = _cursor.getString(_cursorIndexOfIconName);
            final String _tmpColorHex;
            _tmpColorHex = _cursor.getString(_cursorIndexOfColorHex);
            final int _tmpUsageCount;
            _tmpUsageCount = _cursor.getInt(_cursorIndexOfUsageCount);
            final LocalDateTime _tmpLastUsed;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfLastUsed)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfLastUsed);
            }
            _tmpLastUsed = __dateTimeConverter.toLocalDateTime(_tmp);
            final LocalDateTime _tmpCreatedAt;
            final Long _tmp_1;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            final LocalDateTime _tmp_2 = __dateTimeConverter.toLocalDateTime(_tmp_1);
            if (_tmp_2 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpCreatedAt = _tmp_2;
            }
            final String _tmpDefaultTitle;
            _tmpDefaultTitle = _cursor.getString(_cursorIndexOfDefaultTitle);
            final String _tmpDefaultDescription;
            _tmpDefaultDescription = _cursor.getString(_cursorIndexOfDefaultDescription);
            final String _tmpDefaultPriority;
            _tmpDefaultPriority = _cursor.getString(_cursorIndexOfDefaultPriority);
            final String _tmpDefaultTags;
            _tmpDefaultTags = _cursor.getString(_cursorIndexOfDefaultTags);
            final Integer _tmpDefaultDurationDays;
            if (_cursor.isNull(_cursorIndexOfDefaultDurationDays)) {
              _tmpDefaultDurationDays = null;
            } else {
              _tmpDefaultDurationDays = _cursor.getInt(_cursorIndexOfDefaultDurationDays);
            }
            final String _tmpGoalType;
            _tmpGoalType = _cursor.getString(_cursorIndexOfGoalType);
            final Double _tmpDefaultTargetValue;
            if (_cursor.isNull(_cursorIndexOfDefaultTargetValue)) {
              _tmpDefaultTargetValue = null;
            } else {
              _tmpDefaultTargetValue = _cursor.getDouble(_cursorIndexOfDefaultTargetValue);
            }
            final String _tmpDefaultUnit;
            if (_cursor.isNull(_cursorIndexOfDefaultUnit)) {
              _tmpDefaultUnit = null;
            } else {
              _tmpDefaultUnit = _cursor.getString(_cursorIndexOfDefaultUnit);
            }
            final boolean _tmpIsRecurring;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfIsRecurring);
            _tmpIsRecurring = _tmp_3 != 0;
            final String _tmpRecurringSettingsJson;
            _tmpRecurringSettingsJson = _cursor.getString(_cursorIndexOfRecurringSettingsJson);
            final String _tmpReminderSettingsJson;
            _tmpReminderSettingsJson = _cursor.getString(_cursorIndexOfReminderSettingsJson);
            _item = new GoalTemplate(_tmpId,_tmpName,_tmpDescription,_tmpCategory,_tmpCategoryId,_tmpIconName,_tmpColorHex,_tmpUsageCount,_tmpLastUsed,_tmpCreatedAt,_tmpDefaultTitle,_tmpDefaultDescription,_tmpDefaultPriority,_tmpDefaultTags,_tmpDefaultDurationDays,_tmpGoalType,_tmpDefaultTargetValue,_tmpDefaultUnit,_tmpIsRecurring,_tmpRecurringSettingsJson,_tmpReminderSettingsJson);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getTemplatesCount(final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT COUNT(*) FROM goal_templates";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final int _tmp;
            _tmp = _cursor.getInt(0);
            _result = _tmp;
          } else {
            _result = 0;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getTemplatesByCategoryPaged(final String category, final int pageSize,
      final int offset, final Continuation<? super List<GoalTemplate>> $completion) {
    final String _sql = "SELECT * FROM goal_templates WHERE category = ? ORDER BY name LIMIT ? OFFSET ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 3);
    int _argIndex = 1;
    _statement.bindString(_argIndex, category);
    _argIndex = 2;
    _statement.bindLong(_argIndex, pageSize);
    _argIndex = 3;
    _statement.bindLong(_argIndex, offset);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<GoalTemplate>>() {
      @Override
      @NonNull
      public List<GoalTemplate> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfCategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "categoryId");
          final int _cursorIndexOfIconName = CursorUtil.getColumnIndexOrThrow(_cursor, "iconName");
          final int _cursorIndexOfColorHex = CursorUtil.getColumnIndexOrThrow(_cursor, "colorHex");
          final int _cursorIndexOfUsageCount = CursorUtil.getColumnIndexOrThrow(_cursor, "usageCount");
          final int _cursorIndexOfLastUsed = CursorUtil.getColumnIndexOrThrow(_cursor, "lastUsed");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfDefaultTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultTitle");
          final int _cursorIndexOfDefaultDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultDescription");
          final int _cursorIndexOfDefaultPriority = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultPriority");
          final int _cursorIndexOfDefaultTags = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultTags");
          final int _cursorIndexOfDefaultDurationDays = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultDurationDays");
          final int _cursorIndexOfGoalType = CursorUtil.getColumnIndexOrThrow(_cursor, "goalType");
          final int _cursorIndexOfDefaultTargetValue = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultTargetValue");
          final int _cursorIndexOfDefaultUnit = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultUnit");
          final int _cursorIndexOfIsRecurring = CursorUtil.getColumnIndexOrThrow(_cursor, "isRecurring");
          final int _cursorIndexOfRecurringSettingsJson = CursorUtil.getColumnIndexOrThrow(_cursor, "recurringSettingsJson");
          final int _cursorIndexOfReminderSettingsJson = CursorUtil.getColumnIndexOrThrow(_cursor, "reminderSettingsJson");
          final List<GoalTemplate> _result = new ArrayList<GoalTemplate>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final GoalTemplate _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpDescription;
            _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            final String _tmpCategory;
            _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            final String _tmpCategoryId;
            _tmpCategoryId = _cursor.getString(_cursorIndexOfCategoryId);
            final String _tmpIconName;
            _tmpIconName = _cursor.getString(_cursorIndexOfIconName);
            final String _tmpColorHex;
            _tmpColorHex = _cursor.getString(_cursorIndexOfColorHex);
            final int _tmpUsageCount;
            _tmpUsageCount = _cursor.getInt(_cursorIndexOfUsageCount);
            final LocalDateTime _tmpLastUsed;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfLastUsed)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfLastUsed);
            }
            _tmpLastUsed = __dateTimeConverter.toLocalDateTime(_tmp);
            final LocalDateTime _tmpCreatedAt;
            final Long _tmp_1;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            final LocalDateTime _tmp_2 = __dateTimeConverter.toLocalDateTime(_tmp_1);
            if (_tmp_2 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpCreatedAt = _tmp_2;
            }
            final String _tmpDefaultTitle;
            _tmpDefaultTitle = _cursor.getString(_cursorIndexOfDefaultTitle);
            final String _tmpDefaultDescription;
            _tmpDefaultDescription = _cursor.getString(_cursorIndexOfDefaultDescription);
            final String _tmpDefaultPriority;
            _tmpDefaultPriority = _cursor.getString(_cursorIndexOfDefaultPriority);
            final String _tmpDefaultTags;
            _tmpDefaultTags = _cursor.getString(_cursorIndexOfDefaultTags);
            final Integer _tmpDefaultDurationDays;
            if (_cursor.isNull(_cursorIndexOfDefaultDurationDays)) {
              _tmpDefaultDurationDays = null;
            } else {
              _tmpDefaultDurationDays = _cursor.getInt(_cursorIndexOfDefaultDurationDays);
            }
            final String _tmpGoalType;
            _tmpGoalType = _cursor.getString(_cursorIndexOfGoalType);
            final Double _tmpDefaultTargetValue;
            if (_cursor.isNull(_cursorIndexOfDefaultTargetValue)) {
              _tmpDefaultTargetValue = null;
            } else {
              _tmpDefaultTargetValue = _cursor.getDouble(_cursorIndexOfDefaultTargetValue);
            }
            final String _tmpDefaultUnit;
            if (_cursor.isNull(_cursorIndexOfDefaultUnit)) {
              _tmpDefaultUnit = null;
            } else {
              _tmpDefaultUnit = _cursor.getString(_cursorIndexOfDefaultUnit);
            }
            final boolean _tmpIsRecurring;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfIsRecurring);
            _tmpIsRecurring = _tmp_3 != 0;
            final String _tmpRecurringSettingsJson;
            _tmpRecurringSettingsJson = _cursor.getString(_cursorIndexOfRecurringSettingsJson);
            final String _tmpReminderSettingsJson;
            _tmpReminderSettingsJson = _cursor.getString(_cursorIndexOfReminderSettingsJson);
            _item = new GoalTemplate(_tmpId,_tmpName,_tmpDescription,_tmpCategory,_tmpCategoryId,_tmpIconName,_tmpColorHex,_tmpUsageCount,_tmpLastUsed,_tmpCreatedAt,_tmpDefaultTitle,_tmpDefaultDescription,_tmpDefaultPriority,_tmpDefaultTags,_tmpDefaultDurationDays,_tmpGoalType,_tmpDefaultTargetValue,_tmpDefaultUnit,_tmpIsRecurring,_tmpRecurringSettingsJson,_tmpReminderSettingsJson);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getTemplatesCountByCategory(final String category,
      final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT COUNT(*) FROM goal_templates WHERE category = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, category);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final int _tmp;
            _tmp = _cursor.getInt(0);
            _result = _tmp;
          } else {
            _result = 0;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object searchTemplatesPaged(final String query, final int pageSize, final int offset,
      final Continuation<? super List<GoalTemplate>> $completion) {
    final String _sql = "SELECT * FROM goal_templates WHERE name LIKE '%' || ? || '%' OR description LIKE '%' || ? || '%' ORDER BY name LIMIT ? OFFSET ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 4);
    int _argIndex = 1;
    _statement.bindString(_argIndex, query);
    _argIndex = 2;
    _statement.bindString(_argIndex, query);
    _argIndex = 3;
    _statement.bindLong(_argIndex, pageSize);
    _argIndex = 4;
    _statement.bindLong(_argIndex, offset);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<GoalTemplate>>() {
      @Override
      @NonNull
      public List<GoalTemplate> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfCategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "categoryId");
          final int _cursorIndexOfIconName = CursorUtil.getColumnIndexOrThrow(_cursor, "iconName");
          final int _cursorIndexOfColorHex = CursorUtil.getColumnIndexOrThrow(_cursor, "colorHex");
          final int _cursorIndexOfUsageCount = CursorUtil.getColumnIndexOrThrow(_cursor, "usageCount");
          final int _cursorIndexOfLastUsed = CursorUtil.getColumnIndexOrThrow(_cursor, "lastUsed");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfDefaultTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultTitle");
          final int _cursorIndexOfDefaultDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultDescription");
          final int _cursorIndexOfDefaultPriority = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultPriority");
          final int _cursorIndexOfDefaultTags = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultTags");
          final int _cursorIndexOfDefaultDurationDays = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultDurationDays");
          final int _cursorIndexOfGoalType = CursorUtil.getColumnIndexOrThrow(_cursor, "goalType");
          final int _cursorIndexOfDefaultTargetValue = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultTargetValue");
          final int _cursorIndexOfDefaultUnit = CursorUtil.getColumnIndexOrThrow(_cursor, "defaultUnit");
          final int _cursorIndexOfIsRecurring = CursorUtil.getColumnIndexOrThrow(_cursor, "isRecurring");
          final int _cursorIndexOfRecurringSettingsJson = CursorUtil.getColumnIndexOrThrow(_cursor, "recurringSettingsJson");
          final int _cursorIndexOfReminderSettingsJson = CursorUtil.getColumnIndexOrThrow(_cursor, "reminderSettingsJson");
          final List<GoalTemplate> _result = new ArrayList<GoalTemplate>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final GoalTemplate _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpDescription;
            _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            final String _tmpCategory;
            _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            final String _tmpCategoryId;
            _tmpCategoryId = _cursor.getString(_cursorIndexOfCategoryId);
            final String _tmpIconName;
            _tmpIconName = _cursor.getString(_cursorIndexOfIconName);
            final String _tmpColorHex;
            _tmpColorHex = _cursor.getString(_cursorIndexOfColorHex);
            final int _tmpUsageCount;
            _tmpUsageCount = _cursor.getInt(_cursorIndexOfUsageCount);
            final LocalDateTime _tmpLastUsed;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfLastUsed)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfLastUsed);
            }
            _tmpLastUsed = __dateTimeConverter.toLocalDateTime(_tmp);
            final LocalDateTime _tmpCreatedAt;
            final Long _tmp_1;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            final LocalDateTime _tmp_2 = __dateTimeConverter.toLocalDateTime(_tmp_1);
            if (_tmp_2 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.time.LocalDateTime', but it was NULL.");
            } else {
              _tmpCreatedAt = _tmp_2;
            }
            final String _tmpDefaultTitle;
            _tmpDefaultTitle = _cursor.getString(_cursorIndexOfDefaultTitle);
            final String _tmpDefaultDescription;
            _tmpDefaultDescription = _cursor.getString(_cursorIndexOfDefaultDescription);
            final String _tmpDefaultPriority;
            _tmpDefaultPriority = _cursor.getString(_cursorIndexOfDefaultPriority);
            final String _tmpDefaultTags;
            _tmpDefaultTags = _cursor.getString(_cursorIndexOfDefaultTags);
            final Integer _tmpDefaultDurationDays;
            if (_cursor.isNull(_cursorIndexOfDefaultDurationDays)) {
              _tmpDefaultDurationDays = null;
            } else {
              _tmpDefaultDurationDays = _cursor.getInt(_cursorIndexOfDefaultDurationDays);
            }
            final String _tmpGoalType;
            _tmpGoalType = _cursor.getString(_cursorIndexOfGoalType);
            final Double _tmpDefaultTargetValue;
            if (_cursor.isNull(_cursorIndexOfDefaultTargetValue)) {
              _tmpDefaultTargetValue = null;
            } else {
              _tmpDefaultTargetValue = _cursor.getDouble(_cursorIndexOfDefaultTargetValue);
            }
            final String _tmpDefaultUnit;
            if (_cursor.isNull(_cursorIndexOfDefaultUnit)) {
              _tmpDefaultUnit = null;
            } else {
              _tmpDefaultUnit = _cursor.getString(_cursorIndexOfDefaultUnit);
            }
            final boolean _tmpIsRecurring;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfIsRecurring);
            _tmpIsRecurring = _tmp_3 != 0;
            final String _tmpRecurringSettingsJson;
            _tmpRecurringSettingsJson = _cursor.getString(_cursorIndexOfRecurringSettingsJson);
            final String _tmpReminderSettingsJson;
            _tmpReminderSettingsJson = _cursor.getString(_cursorIndexOfReminderSettingsJson);
            _item = new GoalTemplate(_tmpId,_tmpName,_tmpDescription,_tmpCategory,_tmpCategoryId,_tmpIconName,_tmpColorHex,_tmpUsageCount,_tmpLastUsed,_tmpCreatedAt,_tmpDefaultTitle,_tmpDefaultDescription,_tmpDefaultPriority,_tmpDefaultTags,_tmpDefaultDurationDays,_tmpGoalType,_tmpDefaultTargetValue,_tmpDefaultUnit,_tmpIsRecurring,_tmpRecurringSettingsJson,_tmpReminderSettingsJson);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object searchTemplatesCount(final String query,
      final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT COUNT(*) FROM goal_templates WHERE name LIKE '%' || ? || '%' OR description LIKE '%' || ? || '%'";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindString(_argIndex, query);
    _argIndex = 2;
    _statement.bindString(_argIndex, query);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final int _tmp;
            _tmp = _cursor.getInt(0);
            _result = _tmp;
          } else {
            _result = 0;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
