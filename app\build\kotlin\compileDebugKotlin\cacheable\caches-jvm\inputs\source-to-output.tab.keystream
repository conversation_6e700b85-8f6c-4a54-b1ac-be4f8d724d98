Aapp/src/main/kotlin/com/timeflow/app/data/dao/EmotionRecordDao.ktGapp/src/main/kotlin/com/timeflow/app/service/NotificationTestService.ktAapp/src/main/kotlin/com/timeflow/app/utils/SampleDataGenerator.ktDapp/src/main/kotlin/com/timeflow/app/ui/screen/milestone/GridView.ktRapp/src/main/kotlin/com/timeflow/app/data/repository/SharedPendingDeletionState.ktGapp/src/main/kotlin/com/timeflow/app/ui/screen/goal/GoalReviewScreen.ktRapp/src/main/kotlin/com/timeflow/app/ui/timetracking/screens/TimeTrackingScreen.ktIapp/src/main/kotlin/com/timeflow/app/ui/screen/goal/GoalTemplateScreen.ktOapp/src/main/kotlin/com/timeflow/app/worker/OverdueTaskCheckWorkerEntryPoint.ktJapp/src/main/kotlin/com/timeflow/app/data/repository/TaskRepositoryImpl.kt=app/src/main/kotlin/com/timeflow/app/data/model/Converters.ktSapp/src/main/kotlin/com/timeflow/app/ui/screen/health/HabitDetailScreenOptimized.ktMapp/src/main/kotlin/com/timeflow/app/ui/language/LanguageSettingsViewModel.ktFapp/src/main/kotlin/com/timeflow/app/ui/screen/health/SymptomWidget.ktOapp/src/main/kotlin/com/timeflow/app/data/repository/EmotionRecordRepository.ktBapp/src/main/kotlin/com/timeflow/app/ui/viewmodel/GoalViewModel.kt;app/src/main/kotlin/com/timeflow/app/di/RepositoryModule.ktDapp/src/main/kotlin/com/timeflow/app/ui/screen/goal/AddGoalScreen.kt6app/src/main/kotlin/com/timeflow/app/di/ImageModule.ktMapp/src/main/kotlin/com/timeflow/app/data/repository/KanbanBoardRepository.kt9app/src/main/kotlin/com/timeflow/app/utils/RenderUtils.kt@app/src/main/kotlin/com/timeflow/app/data/entity/KanbanColumn.kt<app/src/main/kotlin/com/timeflow/app/utils/StatusBarUtils.ktRapp/src/main/kotlin/com/timeflow/app/ui/component/goal/ModernProgressComponents.kt?app/src/main/kotlin/com/timeflow/app/data/model/AppUsageData.kt>app/src/main/kotlin/com/timeflow/app/ui/splash/SplashScreen.ktIapp/src/main/kotlin/com/timeflow/app/ui/timetracking/TimerUIComponents.ktJapp/src/main/kotlin/com/timeflow/app/ui/task/components/TaskFilterChips.kt:app/src/main/kotlin/com/timeflow/app/ui/TimeFlowNavHost.ktJapp/src/main/kotlin/com/timeflow/app/ui/task/components/TaskGroupHeader.kt7app/src/main/kotlin/com/timeflow/app/di/ViewModelKey.kt\app/src/main/kotlin/com/timeflow/app/ui/timetracking/components/TaskTimeStatisticsSection.kt=app/src/main/kotlin/com/timeflow/app/ui/theme/ThemeManager.ktGapp/src/main/kotlin/com/timeflow/app/ui/screen/health/AddHabitScreen.ktDapp/src/main/kotlin/com/timeflow/app/utils/ComposeScreenshotUtils.ktGapp/src/main/kotlin/com/timeflow/app/ui/screen/account/AccountScreen.ktSapp/src/main/kotlin/com/timeflow/app/ui/task/components/common/PriorityIndicator.kt;app/src/main/kotlin/com/timeflow/app/di/ReflectionModule.kt8app/src/main/kotlin/com/timeflow/app/data/dao/GoalDao.ktDapp/src/main/kotlin/com/timeflow/app/service/RecurringTaskManager.ktLapp/src/main/kotlin/com/timeflow/app/ui/viewmodel/MenstrualCycleViewModel.ktMapp/src/main/kotlin/com/timeflow/app/ui/screen/settings/SyncSettingsScreen.kt@app/src/main/kotlin/com/timeflow/app/utils/PerformanceMonitor.ktIapp/src/main/kotlin/com/timeflow/app/ui/viewmodel/GlobalTimerViewModel.ktPapp/src/main/kotlin/com/timeflow/app/ui/screen/wishlist/EnhancedAddWishDialog.ktLapp/src/main/kotlin/com/timeflow/app/ui/screen/goal/GoalTemplateViewModel.kt?app/src/main/kotlin/com/timeflow/app/ui/task/KanbanViewModel.kt:app/src/main/kotlin/com/timeflow/app/di/ViewModelModule.ktOapp/src/main/kotlin/com/timeflow/app/ui/components/goal/GoalAssistantButtons.ktIapp/src/main/kotlin/com/timeflow/app/viewmodel/BackupSettingsViewModel.ktJapp/src/main/kotlin/com/timeflow/app/ui/statistics/TimeStatisticsScreen.ktOapp/src/main/kotlin/com/timeflow/app/data/repository/TimeAnalyticsRepository.ktFapp/src/main/kotlin/com/timeflow/app/ui/screen/ai/AiAssistantScreen.ktEapp/src/main/kotlin/com/timeflow/app/data/model/NotificationConfig.ktBapp/src/main/kotlin/com/timeflow/app/widget/WidgetUpdateManager.kt:app/src/main/kotlin/com/timeflow/app/di/AnalyticsModule.ktEapp/src/main/kotlin/com/timeflow/app/ui/navigation/AppDestinations.kt;app/src/main/kotlin/com/timeflow/app/di/MedicationModule.ktLapp/src/main/kotlin/com/timeflow/app/data/repository/AiTaskRepositoryImpl.ktBapp/src/main/kotlin/com/timeflow/app/data/entity/AppUsageEntity.ktGapp/src/main/kotlin/com/timeflow/app/ui/screen/profile/ProfileScreen.ktEapp/src/main/kotlin/com/timeflow/app/service/TaskReminderScheduler.ktIapp/src/main/kotlin/com/timeflow/app/utils/TimeFlowNotificationManager.ktPapp/src/main/kotlin/com/timeflow/app/ui/task/components/common/event/EventBus.kt8app/src/main/kotlin/com/timeflow/app/utils/ImageUtils.ktNapp/src/main/kotlin/com/timeflow/app/ui/screen/milestone/MilestoneViewModel.ktAapp/src/main/kotlin/com/timeflow/app/service/FocusTimerManager.ktHapp/src/main/kotlin/com/timeflow/app/ui/screen/home/<USER>/src/main/kotlin/com/timeflow/app/ui/task/model/SortOption.ktCapp/src/main/kotlin/com/timeflow/app/ui/navigation/NavAnimations.kt<app/src/main/kotlin/com/timeflow/app/data/entity/Priority.kt6app/src/main/kotlin/com/timeflow/app/di/UtilsModule.ktLapp/src/main/kotlin/com/timeflow/app/ui/screen/discover/DiscoverViewModel.ktSapp/src/main/kotlin/com/timeflow/app/ui/screen/profile/EmotionRecordReviewScreen.kt>app/src/main/kotlin/com/timeflow/app/ui/screen/task/SubTask.ktYapp/src/main/kotlin/com/timeflow/app/ui/timetracking/screens/TimeAnalyticsDetailScreen.ktMapp/src/main/kotlin/com/timeflow/app/ui/screen/health/SymptomsDetailScreen.ktIapp/src/main/kotlin/com/timeflow/app/ui/screen/wishlist/WishListScreen.ktMapp/src/main/kotlin/com/timeflow/app/ui/screen/goal/GoalTemplateEditScreen.kt?app/src/main/kotlin/com/timeflow/app/utils/TaskReminderUtils.ktKapp/src/main/kotlin/com/timeflow/app/ui/task/components/AiSuggestionArea.ktEapp/src/main/kotlin/com/timeflow/app/ui/navigation/NavigationGraph.ktDapp/src/main/kotlin/com/timeflow/app/ui/screen/task/TaskViewModel.ktOapp/src/main/kotlin/com/timeflow/app/ui/screen/goal/SaveGoalAsTemplateScreen.ktLapp/src/main/kotlin/com/timeflow/app/ui/components/goal/SmartTemplateList.ktEapp/src/main/kotlin/com/timeflow/app/utils/ActivityContextProvider.ktIapp/src/main/kotlin/com/timeflow/app/ui/components/WishToGoalAnimation.ktGapp/src/main/kotlin/com/timeflow/app/ui/screen/goal/GoalDetailScreen.ktTapp/src/main/kotlin/com/timeflow/app/domain/usecase/goal/QuickGoalCreationUseCase.ktIapp/src/main/kotlin/com/timeflow/app/ui/screen/task/model/FeedbackData.kt@app/src/main/kotlin/com/timeflow/app/data/ai/model/AiSettings.kt7app/src/main/kotlin/com/timeflow/app/ui/MainActivity.kt8app/src/main/kotlin/com/timeflow/app/data/entity/Wish.ktLapp/src/main/kotlin/com/timeflow/app/ui/viewmodel/WishStatisticsViewModel.ktNapp/src/main/kotlin/com/timeflow/app/ui/screen/goal/SmartCategoryTestScreen.ktAapp/src/main/kotlin/com/timeflow/app/ai/GoalCategoryClassifier.kt;app/src/main/kotlin/com/timeflow/app/data/model/GoalType.kt8app/src/main/kotlin/com/timeflow/app/data/dao/WishDao.kt?app/src/main/kotlin/com/timeflow/app/widget/QuickTimerWidget.ktAapp/src/main/kotlin/com/timeflow/app/data/converter/Converters.kt=app/src/main/kotlin/com/timeflow/app/utils/RippleOptimizer.ktIapp/src/main/kotlin/com/timeflow/app/ui/screen/milestone/MilestoneType.kt?app/src/main/kotlin/com/timeflow/app/data/model/GoalTemplate.ktAapp/src/main/kotlin/com/timeflow/app/data/entity/SymptomRecord.kt7app/src/main/kotlin/com/timeflow/app/data/model/Goal.ktIapp/src/main/kotlin/com/timeflow/app/data/converter/LocalDateConverter.ktAapp/src/main/kotlin/com/timeflow/app/di/WorkManagerInitializer.kt>app/src/main/kotlin/com/timeflow/app/data/dao/ReflectionDao.kt@app/src/main/kotlin/com/timeflow/app/widget/WeeklyStatsWidget.ktJapp/src/main/kotlin/com/timeflow/app/ui/viewmodel/GoalCreationViewModel.kt7app/src/main/kotlin/com/timeflow/app/data/model/Task.kt?app/src/main/kotlin/com/timeflow/app/data/dao/KanbanBoardDao.ktRapp/src/main/kotlin/com/timeflow/app/utils/TaskPersistentNotificationTestHelper.kt>app/src/main/kotlin/com/timeflow/app/util/MarkdownFormatter.ktOapp/src/main/kotlin/com/timeflow/app/ui/task/components/common/TaskCardTheme.ktDapp/src/main/kotlin/com/timeflow/app/ui/navigation/ScreenWrappers.ktOapp/src/main/kotlin/com/timeflow/app/data/preferences/UserPreferencesManager.ktTapp/src/main/kotlin/com/timeflow/app/ui/task/components/common/state/ManagedState.ktMapp/src/main/kotlin/com/timeflow/app/ui/screen/settings/SyncRepositoryImpl.ktFapp/src/main/kotlin/com/timeflow/app/domain/usecase/TaskTimeUseCase.ktFapp/src/main/kotlin/com/timeflow/app/data/repository/WishRepository.ktGapp/src/main/kotlin/com/timeflow/app/data/repository/HabitRepository.ktQapp/src/main/kotlin/com/timeflow/app/ui/screen/milestone/MilestoneDetailDialog.ktIapp/src/main/kotlin/com/timeflow/app/service/MedicationReminderManager.ktHapp/src/main/kotlin/com/timeflow/app/viewmodel/BackupRestoreViewModel.ktOapp/src/main/kotlin/com/timeflow/app/ui/screen/milestone/EditMilestoneScreen.kt?app/src/main/kotlin/com/timeflow/app/ui/theme/AnalyticsTheme.ktCapp/src/main/kotlin/com/timeflow/app/ui/navigation/BottomNavItem.ktIapp/src/main/kotlin/com/timeflow/app/ui/screen/calendar/CalendarScreen.ktAapp/src/main/kotlin/com/timeflow/app/service/FocusTimerService.ktJapp/src/main/kotlin/com/timeflow/app/data/converter/StringListConverter.ktDapp/src/main/kotlin/com/timeflow/app/ui/screen/goal/GoalViewModel.ktQapp/src/main/kotlin/com/timeflow/app/service/TaskPersistentNotificationService.kt:app/src/main/kotlin/com/timeflow/app/util/EventListener.ktJapp/src/main/kotlin/com/timeflow/app/ui/screen/health/AddHabitViewModel.kt=app/src/main/kotlin/com/timeflow/app/ui/settings/ColorType.ktFapp/src/main/kotlin/com/timeflow/app/data/repository/GoalRepository.kt4app/src/main/kotlin/com/timeflow/app/MainActivity.ktEapp/src/main/kotlin/com/timeflow/app/ui/task/model/TasksStatistics.ktOapp/src/main/kotlin/com/timeflow/app/ui/screen/goal/GoalTemplateImportScreen.kt>app/src/main/kotlin/com/timeflow/app/data/dao/MedicationDao.ktSapp/src/main/kotlin/com/timeflow/app/ui/optimization/OptimizedTaskListComponents.ktDapp/src/main/kotlin/com/timeflow/app/data/ai/model/AiConversation.ktOapp/src/main/kotlin/com/timeflow/app/ui/screen/settings/DataManagementScreen.kt:app/src/main/kotlin/com/timeflow/app/di/DataStoreModule.ktMapp/src/main/kotlin/com/timeflow/app/data/repository/TimeSessionRepository.kt=app/src/main/kotlin/com/timeflow/app/data/model/HabitModel.ktUapp/src/main/kotlin/com/timeflow/app/ui/screen/profile/DetailedEmotionRecordScreen.kt;app/src/main/kotlin/com/timeflow/app/data/model/AiConfig.kt;app/src/main/kotlin/com/timeflow/app/TimeFlowApplication.kt9app/src/main/kotlin/com/timeflow/app/di/AppInitializer.ktJapp/src/main/kotlin/com/timeflow/app/ui/screen/goal/GoalBreakdownScreen.kt4app/src/main/kotlin/com/timeflow/app/di/AppModule.ktDapp/src/main/kotlin/com/timeflow/app/ui/components/LoadingContent.ktOapp/src/main/kotlin/com/timeflow/app/ui/screen/wishlist/WishStatisticsScreen.ktNapp/src/main/kotlin/com/timeflow/app/ui/screen/health/PeriodAnalyticsScreen.ktIapp/src/main/kotlin/com/timeflow/app/viewmodel/DataManagementViewModel.ktMapp/src/main/kotlin/com/timeflow/app/ui/screen/reflection/ReflectionScreen.ktNapp/src/main/kotlin/com/timeflow/app/ui/screen/analytics/AnalyticsViewModel.kt@app/src/main/kotlin/com/timeflow/app/debug/PriorityUpdateTest.ktMapp/src/main/kotlin/com/timeflow/app/ui/screen/reflection/ReflectionModels.kt<app/src/main/kotlin/com/timeflow/app/data/dao/AppUsageDao.ktXapp/src/main/kotlin/com/timeflow/app/ui/screen/goal/components/RecurringSettingsPanel.ktCapp/src/main/kotlin/com/timeflow/app/ui/viewmodel/HabitViewModel.ktDapp/src/main/kotlin/com/timeflow/app/data/model/RecurringSettings.kt?app/src/main/kotlin/com/timeflow/app/data/model/TimeSlotInfo.kt9app/src/main/kotlin/com/timeflow/app/di/DatabaseModule.ktIapp/src/main/kotlin/com/timeflow/app/ui/screen/settings/SettingsScreen.kt@app/src/main/kotlin/com/timeflow/app/data/ai/model/SentStatus.ktIapp/src/main/kotlin/com/timeflow/app/data/ai/model/AiTaskDecomposition.ktBapp/src/main/kotlin/com/timeflow/app/navigation/TimeFlowNavHost.ktSapp/src/main/kotlin/com/timeflow/app/data/notification/GoalNotificationScheduler.kt>app/src/main/kotlin/com/timeflow/app/utils/SafeParcelHelper.ktMapp/src/main/kotlin/com/timeflow/app/ui/screen/milestone/MilestoneInsights.ktZapp/src/main/kotlin/com/timeflow/app/ui/screen/reflection/data/ReflectionRepositoryImpl.ktHapp/src/main/kotlin/com/timeflow/app/ui/task/components/TaskGroupList.ktDapp/src/main/kotlin/com/timeflow/app/ui/screen/task/AddTaskScreen.ktHapp/src/main/kotlin/com/timeflow/app/data/repository/AiTaskRepository.ktCapp/src/main/kotlin/com/timeflow/app/ui/language/LanguageManager.ktEapp/src/main/kotlin/com/timeflow/app/service/AiSuggestionScheduler.ktFapp/src/main/kotlin/com/timeflow/app/data/analytics/PeriodAnalytics.ktIapp/src/main/kotlin/com/timeflow/app/service/NotificationConfigManager.kt9app/src/main/kotlin/com/timeflow/app/data/dao/CycleDao.ktAapp/src/main/kotlin/com/timeflow/app/util/DataConsistencyFixer.kt9app/src/main/kotlin/com/timeflow/app/di/PreferenceKeys.kt>app/src/main/kotlin/com/timeflow/app/data/model/WorkManager.ktPapp/src/main/kotlin/com/timeflow/app/ui/screen/settings/SyncSettingsViewModel.ktFapp/src/main/kotlin/com/timeflow/app/ui/screen/ai/AIReviewViewModel.kt<app/src/main/kotlin/com/timeflow/app/data/model/TaskGroup.ktBapp/src/main/kotlin/com/timeflow/app/ui/components/MiniTimerBar.kt?app/src/main/kotlin/com/timeflow/app/data/model/ViewTimeSlot.kt@app/src/main/kotlin/com/timeflow/app/worker/DailyReviewWorker.ktKapp/src/main/kotlin/com/timeflow/app/ui/task/components/VoiceInputDialog.ktKapp/src/main/kotlin/com/timeflow/app/ui/screen/milestone/MilestoneScreen.kt7app/src/main/kotlin/com/timeflow/app/utils/LogConfig.ktJapp/src/main/kotlin/com/timeflow/app/ui/screen/profile/ProfileViewModel.kt;app/src/main/kotlin/com/timeflow/app/data/entity/TaskTag.ktKapp/src/main/kotlin/com/timeflow/app/ui/task/components/AiSuggestionCard.ktEapp/src/main/kotlin/com/timeflow/app/ui/screen/milestone/Milestone.kt=app/src/main/kotlin/com/timeflow/app/data/model/Reflection.ktBapp/src/main/kotlin/com/timeflow/app/receiver/TaskAlarmReceiver.ktBapp/src/main/kotlin/com/timeflow/app/utils/PerformanceOptimizer.ktDapp/src/main/kotlin/com/timeflow/app/data/dao/MedicationRecordDao.ktQapp/src/main/kotlin/com/timeflow/app/service/TaskPersistentNotificationManager.kt_app/src/main/kotlin/com/timeflow/app/ui/screen/reflection/components/ShareableReflectionCard.ktSapp/src/main/kotlin/com/timeflow/app/ui/screen/profile/EmotionRecordDetailScreen.ktKapp/src/main/kotlin/com/timeflow/app/data/repository/HabitRepositoryImpl.ktMapp/src/main/kotlin/com/timeflow/app/ui/screen/settings/DataRecoveryScreen.ktOapp/src/main/kotlin/com/timeflow/app/ui/screen/settings/BackupSettingsScreen.ktMapp/src/main/kotlin/com/timeflow/app/ui/statistics/TimeStatisticsViewModel.kt6app/src/main/kotlin/com/timeflow/app/ui/theme/Color.kt;app/src/main/kotlin/com/timeflow/app/di/SingletonModules.ktEapp/src/main/kotlin/com/timeflow/app/ui/navigation/NavigationRoute.ktCapp/src/main/kotlin/com/timeflow/app/data/model/GoalWizardModels.kt6app/src/main/kotlin/com/timeflow/app/ui/theme/Theme.ktPapp/src/main/kotlin/com/timeflow/app/ui/screen/reflection/ReflectionViewModel.ktCapp/src/main/kotlin/com/timeflow/app/ui/screen/task/TaskTimeSync.ktOapp/src/main/kotlin/com/timeflow/app/ui/screen/analytics/AnalyticsComponents.ktFapp/src/main/kotlin/com/timeflow/app/ui/screen/health/FrequencyType.ktJapp/src/main/kotlin/com/timeflow/app/data/repository/TaskTimeRepository.kt@app/src/main/kotlin/com/timeflow/app/data/dao/GoalTemplateDao.kt@app/src/main/kotlin/com/timeflow/app/ui/utils/DateFormatUtils.kt>app/src/main/kotlin/com/timeflow/app/data/model/AiApiResult.ktSapp/src/main/kotlin/com/timeflow/app/ui/timetracking/TaskTimeStatisticsViewModel.kt>app/src/main/kotlin/com/timeflow/app/ui/task/model/ViewMode.ktFapp/src/main/kotlin/com/timeflow/app/data/repository/TaskRepository.kt?app/src/main/kotlin/com/timeflow/app/data/model/GoalCategory.kt@app/src/main/kotlin/com/timeflow/app/data/entity/GoalTemplate.ktFapp/src/main/kotlin/com/timeflow/app/service/DailyReviewDataService.ktGapp/src/main/kotlin/com/timeflow/app/data/entity/EmotionRecordEntity.ktHapp/src/main/kotlin/com/timeflow/app/data/converter/DateTimeConverter.ktAapp/src/main/kotlin/com/timeflow/app/utils/AppExceptionHandler.ktZapp/src/main/kotlin/com/timeflow/app/ui/screen/calendar/components/FloatingTasksSection.ktBapp/src/main/kotlin/com/timeflow/app/worker/RecurringTaskWorker.ktMapp/src/main/kotlin/com/timeflow/app/ui/screen/goal/GoalTemplateListScreen.ktCapp/src/main/kotlin/com/timeflow/app/service/TimeTrackingService.ktDapp/src/main/kotlin/com/timeflow/app/service/RecurrenceCalculator.ktIapp/src/main/kotlin/com/timeflow/app/ui/screen/task/TaskFeedbackDialog.ktUapp/src/main/kotlin/com/timeflow/app/data/repository/impl/MedicationRepositoryImpl.ktHapp/src/main/kotlin/com/timeflow/app/ui/navigation/LocalNavController.ktFapp/src/main/kotlin/com/timeflow/app/ui/components/WishCloudSection.ktEapp/src/main/kotlin/com/timeflow/app/ui/screen/goal/EditGoalScreen.ktSapp/src/main/kotlin/com/timeflow/app/data/repository/TimeAnalyticsRepositoryImpl.ktLapp/src/main/kotlin/com/timeflow/app/ui/screen/settings/SettingsViewModel.ktPapp/src/main/kotlin/com/timeflow/app/domain/usecase/goal/SmartTemplateUseCase.ktJapp/src/main/kotlin/com/timeflow/app/ui/screen/task/TaskDetailViewModel.ktDapp/src/main/kotlin/com/timeflow/app/ui/screen/milestone/ViewMode.ktBapp/src/main/kotlin/com/timeflow/app/util/DataConsistencyHelper.kt?app/src/main/kotlin/com/timeflow/app/ui/components/StatsCard.kt]app/src/main/kotlin/com/timeflow/app/ui/screen/reflection/data/SearchSuggestionServiceImpl.ktJapp/src/main/kotlin/com/timeflow/app/ui/language/LanguageSettingsScreen.ktKapp/src/main/kotlin/com/timeflow/app/ui/screen/task/TaskQuickDetailSheet.ktHapp/src/main/kotlin/com/timeflow/app/data/repository/KanbanRepository.ktHapp/src/main/kotlin/com/timeflow/app/ui/screen/milestone/TimelineView.kt>app/src/main/kotlin/com/timeflow/app/utils/SystemBarManager.ktCapp/src/main/kotlin/com/timeflow/app/ui/screen/SharedFilterState.ktBapp/src/main/kotlin/com/timeflow/app/utils/RestrictedReflection.kt5app/src/main/kotlin/com/timeflow/app/ui/theme/Type.ktPapp/src/main/kotlin/com/timeflow/app/ui/timetracking/TaskTimeStatisticsScreen.ktIapp/src/main/kotlin/com/timeflow/app/ui/screen/calendar/DimensionUtils.kt5app/src/main/kotlin/com/timeflow/app/di/WishModule.ktWapp/src/main/kotlin/com/timeflow/app/ui/screen/goal/components/ReminderSettingsPanel.ktJapp/src/main/kotlin/com/timeflow/app/data/repository/WishRepositoryImpl.kt?app/src/main/kotlin/com/timeflow/app/widget/TodayTasksWidget.kt;app/src/main/kotlin/com/timeflow/app/utils/DateTimeUtils.ktIapp/src/main/kotlin/com/timeflow/app/receiver/FocusTimerActionReceiver.ktKapp/src/main/kotlin/com/timeflow/app/ui/screen/settings/AiSettingsScreen.ktFapp/src/main/kotlin/com/timeflow/app/ui/screen/milestone/YearlyView.kt8app/src/main/kotlin/com/timeflow/app/data/dao/TaskDao.ktMapp/src/main/kotlin/com/timeflow/app/ui/timetracking/TimeTrackingViewModel.ktXapp/src/main/kotlin/com/timeflow/app/ui/screen/task/components/GoalSelectionComponent.kt?app/src/main/kotlin/com/timeflow/app/widget/FocusTimerWidget.ktQapp/src/main/kotlin/com/timeflow/app/ui/screen/profile/EmotionStatisticsScreen.ktFapp/src/main/kotlin/com/timeflow/app/data/ai/model/AiTimeEstimation.ktOapp/src/main/kotlin/com/timeflow/app/ui/screen/task/model/TaskDataExtensions.ktYapp/src/main/kotlin/com/timeflow/app/ui/task/components/common/cache/SmartCacheManager.ktZapp/src/main/kotlin/com/timeflow/app/ui/screen/reflection/components/ShareOptionsDialog.ktKapp/src/main/kotlin/com/timeflow/app/ui/components/payment/PaymentDialog.ktGapp/src/main/kotlin/com/timeflow/app/ui/screen/task/TaskDetailScreen.ktUapp/src/main/kotlin/com/timeflow/app/ui/screen/health/ProfessionalMedicationScreen.kt@app/src/main/kotlin/com/timeflow/app/ui/task/model/TaskStatus.ktBapp/src/main/kotlin/com/timeflow/app/worker/HabitReminderWorker.ktBapp/src/main/kotlin/com/timeflow/app/data/model/RecurrenceModel.kt=app/src/main/kotlin/com/timeflow/app/utils/SafeImageLoader.ktLapp/src/main/kotlin/com/timeflow/app/data/repository/MedicationRepository.kt>app/src/main/kotlin/com/timeflow/app/data/model/GoalSubTask.ktQapp/src/main/kotlin/com/timeflow/app/ui/screen/reflection/model/TimeViewModels.ktBapp/src/main/kotlin/com/timeflow/app/ui/components/BottomNavBar.ktBapp/src/main/kotlin/com/timeflow/app/ui/screen/task/TaskContent.ktKapp/src/main/kotlin/com/timeflow/app/ui/screen/milestone/MilestoneTopBar.kt6app/src/main/kotlin/com/timeflow/app/ui/TimeFlowApp.ktFapp/src/main/kotlin/com/timeflow/app/receiver/BootCompletedReceiver.ktDapp/src/main/kotlin/com/timeflow/app/data/entity/MedicationRecord.ktIapp/src/main/kotlin/com/timeflow/app/ui/task/components/PriorityPicker.ktGapp/src/main/kotlin/com/timeflow/app/ui/screen/task/model/TaskModels.ktYapp/src/main/kotlin/com/timeflow/app/ui/timetracking/components/TimeTrackingComponents.kt<app/src/main/kotlin/com/timeflow/app/utils/ViewExtensions.ktPapp/src/main/kotlin/com/timeflow/app/data/algorithm/PeriodPredictionAlgorithm.ktDapp/src/main/kotlin/com/timeflow/app/data/entity/ReflectionEntity.kt;app/src/main/kotlin/com/timeflow/app/ui/theme/GlassPanel.ktCapp/src/main/kotlin/com/timeflow/app/utils/DatabaseBackupManager.ktEapp/src/main/kotlin/com/timeflow/app/data/model/HistoryPeriodModel.kt>app/src/main/kotlin/com/timeflow/app/data/model/TimeSession.kt9app/src/main/kotlin/com/timeflow/app/utils/ImageLoader.ktGapp/src/main/kotlin/com/timeflow/app/ui/screen/task/TaskTimeSyncTest.kt@app/src/main/kotlin/com/timeflow/app/widget/TimeInsightWidget.ktLapp/src/main/kotlin/com/timeflow/app/ui/screen/task/TaskDetailBottomSheet.kt=app/src/main/kotlin/com/timeflow/app/util/HabitGoalManager.ktSapp/src/main/kotlin/com/timeflow/app/ui/screen/settings/CloudStorageConfigScreen.ktGapp/src/main/kotlin/com/timeflow/app/ui/navigation/ScreenWrappersFix.ktNapp/src/main/kotlin/com/timeflow/app/data/repository/GoalTemplateRepository.ktPapp/src/main/kotlin/com/timeflow/app/data/repository/UserPreferenceRepository.kt=app/src/main/kotlin/com/timeflow/app/data/model/Medication.kt?app/src/main/kotlin/com/timeflow/app/utils/HwcLutsErrorFixer.ktHapp/src/main/kotlin/com/timeflow/app/ui/components/WishLinkedGoalCard.ktKapp/src/main/kotlin/com/timeflow/app/ui/screen/goal/GoalManagementScreen.ktLapp/src/main/kotlin/com/timeflow/app/ui/screen/calendar/CalendarViewModel.kt;app/src/main/kotlin/com/timeflow/app/data/model/TaskTime.ktFapp/src/main/kotlin/com/timeflow/app/ui/components/GlassmorphicCard.kt<app/src/main/kotlin/com/timeflow/app/utils/PreferenceKeys.ktOapp/src/main/kotlin/com/timeflow/app/ui/navigation/BottomNavAnimationManager.ktYapp/src/main/kotlin/com/timeflow/app/receiver/TaskPersistentNotificationActionReceiver.kt9app/src/main/kotlin/com/timeflow/app/data/dao/HabitDao.ktQapp/src/main/kotlin/com/timeflow/app/data/repository/KanbanBoardRepositoryImpl.kt@app/src/main/kotlin/com/timeflow/app/utils/ActivityExtensions.ktDapp/src/main/kotlin/com/timeflow/app/data/converter/TaskConverter.ktQapp/src/main/kotlin/com/timeflow/app/ui/screen/home/<USER>/src/main/kotlin/com/timeflow/app/ui/viewmodel/WishListViewModel.kt=app/src/main/kotlin/com/timeflow/app/ui/base/SafeViewModel.ktFapp/src/main/kotlin/com/timeflow/app/ui/components/ai/AiPromptInput.kt<app/src/main/kotlin/com/timeflow/app/utils/SafeNavigation.ktAapp/src/main/kotlin/com/timeflow/app/widget/TimerWidgetUpdater.ktKapp/src/main/kotlin/com/timeflow/app/ui/navigation/AnimationConfigurator.ktBapp/src/main/kotlin/com/timeflow/app/data/model/RecurringPeriod.ktIapp/src/main/kotlin/com/timeflow/app/ui/screen/discover/DiscoverScreen.ktAapp/src/main/kotlin/com/timeflow/app/worker/AiSuggestionWorker.ktEapp/src/main/kotlin/com/timeflow/app/utils/BinderTransactionHelper.kt;app/src/main/kotlin/com/timeflow/app/utils/TemplateCache.kt;app/src/main/kotlin/com/timeflow/app/data/model/Priority.ktWapp/src/main/kotlin/com/timeflow/app/ui/screen/goal/components/AiTaskBreakdownScreen.ktQapp/src/main/kotlin/com/timeflow/app/ui/screen/health/EnhancedHealthComponents.ktLapp/src/main/kotlin/com/timeflow/app/ui/screen/milestone/YearTimelineView.ktJapp/src/main/kotlin/com/timeflow/app/ui/screen/goal/GoalDashboardScreen.ktPapp/src/main/kotlin/com/timeflow/app/service/DailyReviewNotificationGenerator.ktNapp/src/main/kotlin/com/timeflow/app/ui/task/components/common/BaseTaskCard.ktLapp/src/main/kotlin/com/timeflow/app/ui/screen/home/<USER>/src/main/kotlin/com/timeflow/app/ui/screen/goal/GoalSetupWizardScreen.ktIapp/src/main/kotlin/com/timeflow/app/ui/screen/task/TaskListFullScreen.ktBapp/src/main/kotlin/com/timeflow/app/navigation/AppDestinations.kt8app/src/main/kotlin/com/timeflow/app/utils/ColorUtils.ktJapp/src/main/kotlin/com/timeflow/app/ui/screen/health/PeriodEditDialogs.ktCapp/src/main/kotlin/com/timeflow/app/data/model/DataLoadingState.ktXapp/src/main/kotlin/com/timeflow/app/ui/screen/settings/NotificationSettingsViewModel.ktVapp/src/main/kotlin/com/timeflow/app/ui/screen/reflection/model/ReflectionListState.kt;app/src/main/kotlin/com/timeflow/app/data/model/MoodType.ktGapp/src/main/kotlin/com/timeflow/app/ui/screen/goal/GoalAnalysisTest.ktFapp/src/main/kotlin/com/timeflow/app/ui/components/LoadingIndicator.ktQapp/src/main/kotlin/com/timeflow/app/ui/screen/reflection/ReflectionComponents.kt<app/src/main/kotlin/com/timeflow/app/ui/navigation/Screen.kt;app/src/main/kotlin/com/timeflow/app/navigation/NavGraph.kt8app/src/main/kotlin/com/timeflow/app/utils/CycleUtils.ktOapp/src/main/kotlin/com/timeflow/app/data/repository/HistoryPeriodRepository.ktCapp/src/main/kotlin/com/timeflow/app/ui/screen/ai/AIReviewScreen.ktPapp/src/main/kotlin/com/timeflow/app/ui/task/components/common/util/RetryUtil.ktLapp/src/main/kotlin/com/timeflow/app/ui/task/components/SwipeableTaskCard.ktEapp/src/main/kotlin/com/timeflow/app/test/NotificationSettingsTest.kt@app/src/main/kotlin/com/timeflow/app/ui/components/ErrorState.ktDapp/src/main/kotlin/com/timeflow/app/utils/ReflectionShareManager.ktMapp/src/main/kotlin/com/timeflow/app/ui/screen/milestone/MilestoneCategory.ktCapp/src/main/kotlin/com/timeflow/app/data/ai/model/AiTaskInsight.ktZapp/src/main/kotlin/com/timeflow/app/ui/screen/goal/components/AiBreakdownProcessScreen.ktPapp/src/main/kotlin/com/timeflow/app/ui/task/components/OptimizedTaskListView.ktIapp/src/main/kotlin/com/timeflow/app/receiver/DailyReviewAlarmReceiver.kt9app/src/main/kotlin/com/timeflow/app/utils/SafetyGuard.kt8app/src/main/kotlin/com/timeflow/app/data/model/Event.kt<app/src/main/kotlin/com/timeflow/app/data/model/WishModel.ktAapp/src/main/kotlin/com/timeflow/app/worker/TaskReminderWorker.kt6app/src/main/kotlin/com/timeflow/app/util/TaskUtils.ktBapp/src/main/kotlin/com/timeflow/app/ui/components/CalendarGrid.ktYapp/src/main/kotlin/com/timeflow/app/ui/screen/goal/components/AiBreakdownResultScreen.ktAapp/src/main/kotlin/com/timeflow/app/widget/GoalProgressWidget.kt@app/src/main/kotlin/com/timeflow/app/data/model/AiConfigModel.ktOapp/src/main/kotlin/com/timeflow/app/ui/task/components/DraggableKanbanBoard.ktOapp/src/main/kotlin/com/timeflow/app/ui/timetracking/components/CalendarInfo.ktDapp/src/main/kotlin/com/timeflow/app/data/mapper/ReflectionMapper.ktJapp/src/main/kotlin/com/timeflow/app/ui/settings/ThemeSettingsViewModel.kt:app/src/main/kotlin/com/timeflow/app/data/model/TaskTag.kt?app/src/main/kotlin/com/timeflow/app/data/dao/TimeSessionDao.ktAapp/src/main/kotlin/com/timeflow/app/service/AutoBackupService.kt8app/src/main/kotlin/com/timeflow/app/data/entity/Task.kt?app/src/main/kotlin/com/timeflow/app/data/entity/CycleRecord.ktRapp/src/main/kotlin/com/timeflow/app/ui/task/components/common/EnhancedTaskCard.kt@app/src/main/kotlin/com/timeflow/app/ui/components/EmptyState.kt@app/src/main/kotlin/com/timeflow/app/utils/NotificationHelper.ktEapp/src/main/kotlin/com/timeflow/app/ui/settings/PresetThemeScreen.kt>app/src/main/kotlin/com/timeflow/app/data/model/PaymentInfo.ktFapp/src/main/kotlin/com/timeflow/app/ui/screen/settings/AboutScreen.ktVapp/src/main/kotlin/com/timeflow/app/ui/components/goal/SmartCategoryRecommendation.ktSapp/src/main/kotlin/com/timeflow/app/ui/screen/analytics/AnalyticsInsightService.kt<app/src/main/kotlin/com/timeflow/app/ui/theme/ThemeModels.ktKapp/src/main/kotlin/com/timeflow/app/ui/screen/analytics/AnalyticsScreen.ktPapp/src/main/kotlin/com/timeflow/app/ui/screen/analytics/AnalyticsDataService.ktFapp/src/main/kotlin/com/timeflow/app/data/repository/BaseRepository.ktKapp/src/main/kotlin/com/timeflow/app/ui/screen/health/HabitTrackerScreen.ktSapp/src/main/kotlin/com/timeflow/app/ui/screen/reflection/ReflectionDetailScreen.ktDapp/src/main/kotlin/com/timeflow/app/service/DailyReviewScheduler.kt;app/src/main/kotlin/com/timeflow/app/data/db/AppDatabase.ktUapp/src/main/kotlin/com/timeflow/app/ui/task/components/common/cache/ChangeTracker.ktLapp/src/main/kotlin/com/timeflow/app/ui/screen/health/PeriodHistoryScreen.ktRapp/src/main/kotlin/com/timeflow/app/data/repository/KanbanColumnRepositoryImpl.ktCapp/src/main/kotlin/com/timeflow/app/ui/screen/task/TaskAdapters.kt9app/src/main/kotlin/com/timeflow/app/di/TaskTimeModule.ktBapp/src/main/kotlin/com/timeflow/app/ui/components/LoadingState.ktQapp/src/main/kotlin/com/timeflow/app/data/notification/GoalNotificationManager.kt?app/src/main/kotlin/com/timeflow/app/worker/AutoBackupWorker.ktFapp/src/main/kotlin/com/timeflow/app/ui/viewmodel/AiConfigViewModel.ktEapp/src/main/kotlin/com/timeflow/app/ui/navigation/TimeFlowNavHost.ktHapp/src/main/kotlin/com/timeflow/app/ui/viewmodel/AiSettingsViewModel.ktGapp/src/main/kotlin/com/timeflow/app/data/repository/CycleRepository.ktFapp/src/main/kotlin/com/timeflow/app/ui/task/components/TaskFilters.ktAapp/src/main/kotlin/com/timeflow/app/ui/task/SharedFilterState.ktKapp/src/main/kotlin/com/timeflow/app/ui/screen/milestone/MilestoneRoutes.ktLapp/src/main/kotlin/com/timeflow/app/ui/screen/analytics/AnalyticsContent.ktSapp/src/main/kotlin/com/timeflow/app/ui/screen/calendar/components/ModernDayView.kt6app/src/main/kotlin/com/timeflow/app/utils/EventBus.kt?app/src/main/kotlin/com/timeflow/app/ui/components/ChartCard.ktFapp/src/main/kotlin/com/timeflow/app/ui/viewmodel/TaskTimeViewModel.ktFapp/src/main/kotlin/com/timeflow/app/ui/screen/task/TaskTimeManager.ktRapp/src/main/kotlin/com/timeflow/app/data/repository/DefaultTemplateInitializer.kt;app/src/main/kotlin/com/timeflow/app/utils/TimeZoneUtils.ktEapp/src/main/kotlin/com/timeflow/app/worker/OverdueTaskCheckWorker.ktIapp/src/main/kotlin/com/timeflow/app/ui/task/components/TaskListTopBar.ktGapp/src/main/kotlin/com/timeflow/app/ui/settings/ThemeSettingsScreen.ktIapp/src/main/kotlin/com/timeflow/app/ui/task/components/SmartTaskInput.ktYapp/src/main/kotlin/com/timeflow/app/ui/screen/wishlist/WishRealizationAnimationEffect.ktMapp/src/main/kotlin/com/timeflow/app/ui/screen/health/MenstrualCycleScreen.ktEapp/src/main/kotlin/com/timeflow/app/widget/TodayTasksDataProvider.ktLapp/src/main/kotlin/com/timeflow/app/initializer/RecurringTaskInitializer.kt=app/src/main/kotlin/com/timeflow/app/utils/SettingsManager.kt=app/src/main/kotlin/com/timeflow/app/utils/RenderOptimizer.ktOapp/src/main/kotlin/com/timeflow/app/ui/task/components/HierarchicalTaskList.ktGapp/src/main/kotlin/com/timeflow/app/data/repository/EventRepository.ktEapp/src/main/kotlin/com/timeflow/app/ui/screen/task/model/TaskData.ktFapp/src/main/kotlin/com/timeflow/app/ui/task/model/TaskStatusChange.kt>app/src/main/kotlin/com/timeflow/app/service/PaymentManager.ktIapp/src/main/kotlin/com/timeflow/app/data/repository/SharedFilterState.ktPapp/src/main/kotlin/com/timeflow/app/ui/screen/settings/AiModelSettingsScreen.kt?app/src/main/kotlin/com/timeflow/app/data/entity/TaskClosure.ktKapp/src/main/kotlin/com/timeflow/app/ui/task/components/SegmentedControl.ktUapp/src/main/kotlin/com/timeflow/app/ui/optimization/ComposeRecompositionOptimizer.ktGapp/src/main/kotlin/com/timeflow/app/ui/navigation/TimeFlowNavigator.ktPapp/src/main/kotlin/com/timeflow/app/ui/navigation/LocalNavigationDestination.ktJapp/src/main/kotlin/com/timeflow/app/ui/screen/milestone/RichTextEditor.kt;app/src/main/kotlin/com/timeflow/app/ui/screen/task/temp.kt>app/src/main/kotlin/com/timeflow/app/data/ai/model/AiConfig.kt@app/src/main/kotlin/com/timeflow/app/ui/screen/task/TaskModel.kt8app/src/main/kotlin/com/timeflow/app/data/entity/Goal.ktCapp/src/main/kotlin/com/timeflow/app/receiver/HabitAlarmReceiver.ktAapp/src/main/kotlin/com/timeflow/app/utils/NavigationOptimizer.ktUapp/src/main/kotlin/com/timeflow/app/ui/screen/settings/NotificationSettingsScreen.kt[app/src/main/kotlin/com/timeflow/app/ui/task/components/common/cache/TaskRepositoryCache.ktJapp/src/main/kotlin/com/timeflow/app/ui/screen/health/HabitDetailScreen.ktCapp/src/main/kotlin/com/timeflow/app/ui/theme/DynamicThemeHelper.ktJapp/src/main/kotlin/com/timeflow/app/data/converter/ListStringConverter.ktIapp/src/main/kotlin/com/timeflow/app/ui/viewmodel/AiAssistantViewModel.ktXapp/src/main/kotlin/com/timeflow/app/ui/screen/health/ProfessionalMedicationViewModel.ktFapp/src/main/kotlin/com/timeflow/app/ui/viewmodel/TimeFlowViewModel.kt?app/src/main/kotlin/com/timeflow/app/util/NotificationCenter.ktEapp/src/main/kotlin/com/timeflow/app/ui/screen/task/TaskEditScreen.ktAapp/src/main/kotlin/com/timeflow/app/ui/task/TaskListOptimizer.kt=app/src/main/kotlin/com/timeflow/app/ui/task/TaskViewModel.kt6app/src/main/kotlin/com/timeflow/app/ui/theme/Shape.ktIapp/src/main/kotlin/com/timeflow/app/data/repository/TransactionHelper.kt?app/src/main/kotlin/com/timeflow/app/data/entity/KanbanBoard.kt?app/src/main/kotlin/com/timeflow/app/data/model/ReminderType.kt5app/src/main/kotlin/com/timeflow/app/di/SyncModule.ktOapp/src/main/kotlin/com/timeflow/app/ui/components/goal/GoalCategorySelector.ktSapp/src/main/kotlin/com/timeflow/app/ui/screen/goal/GoalCategoryManagementScreen.kt?app/src/main/kotlin/com/timeflow/app/data/model/KanbanColumn.kt9app/src/main/kotlin/com/timeflow/app/data/entity/Habit.ktJapp/src/main/kotlin/com/timeflow/app/utils/NotificationPermissionHelper.kt^app/src/main/kotlin/com/timeflow/app/ui/screen/calendar/components/CreateFloatingTaskDialog.ktNapp/src/main/kotlin/com/timeflow/app/data/repository/KanbanColumnRepository.kt@app/src/main/kotlin/com/timeflow/app/data/dao/KanbanColumnDao.ktSapp/src/main/kotlin/com/timeflow/app/ui/screen/goal/GoalCompletionAnalysisScreen.kt                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                