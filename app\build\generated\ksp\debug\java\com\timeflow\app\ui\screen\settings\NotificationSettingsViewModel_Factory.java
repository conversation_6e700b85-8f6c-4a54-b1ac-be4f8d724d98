package com.timeflow.app.ui.screen.settings;

import android.content.Context;
import androidx.datastore.core.DataStore;
import androidx.datastore.preferences.core.Preferences;
import com.timeflow.app.service.DailyReviewScheduler;
import com.timeflow.app.service.NotificationConfigManager;
import com.timeflow.app.service.TaskPersistentNotificationManager;
import com.timeflow.app.utils.TimeFlowNotificationManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata({
    "dagger.hilt.android.qualifiers.ApplicationContext",
    "com.timeflow.app.di.DataStoreModule.NotificationDataStore"
})
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class NotificationSettingsViewModel_Factory implements Factory<NotificationSettingsViewModel> {
  private final Provider<Context> contextProvider;

  private final Provider<TimeFlowNotificationManager> notificationManagerProvider;

  private final Provider<DailyReviewScheduler> dailyReviewSchedulerProvider;

  private final Provider<NotificationConfigManager> configManagerProvider;

  private final Provider<TaskPersistentNotificationManager> taskPersistentNotificationManagerProvider;

  private final Provider<DataStore<Preferences>> notificationDataStoreProvider;

  public NotificationSettingsViewModel_Factory(Provider<Context> contextProvider,
      Provider<TimeFlowNotificationManager> notificationManagerProvider,
      Provider<DailyReviewScheduler> dailyReviewSchedulerProvider,
      Provider<NotificationConfigManager> configManagerProvider,
      Provider<TaskPersistentNotificationManager> taskPersistentNotificationManagerProvider,
      Provider<DataStore<Preferences>> notificationDataStoreProvider) {
    this.contextProvider = contextProvider;
    this.notificationManagerProvider = notificationManagerProvider;
    this.dailyReviewSchedulerProvider = dailyReviewSchedulerProvider;
    this.configManagerProvider = configManagerProvider;
    this.taskPersistentNotificationManagerProvider = taskPersistentNotificationManagerProvider;
    this.notificationDataStoreProvider = notificationDataStoreProvider;
  }

  @Override
  public NotificationSettingsViewModel get() {
    return newInstance(contextProvider.get(), notificationManagerProvider.get(), dailyReviewSchedulerProvider.get(), configManagerProvider.get(), taskPersistentNotificationManagerProvider.get(), notificationDataStoreProvider.get());
  }

  public static NotificationSettingsViewModel_Factory create(Provider<Context> contextProvider,
      Provider<TimeFlowNotificationManager> notificationManagerProvider,
      Provider<DailyReviewScheduler> dailyReviewSchedulerProvider,
      Provider<NotificationConfigManager> configManagerProvider,
      Provider<TaskPersistentNotificationManager> taskPersistentNotificationManagerProvider,
      Provider<DataStore<Preferences>> notificationDataStoreProvider) {
    return new NotificationSettingsViewModel_Factory(contextProvider, notificationManagerProvider, dailyReviewSchedulerProvider, configManagerProvider, taskPersistentNotificationManagerProvider, notificationDataStoreProvider);
  }

  public static NotificationSettingsViewModel newInstance(Context context,
      TimeFlowNotificationManager notificationManager, DailyReviewScheduler dailyReviewScheduler,
      NotificationConfigManager configManager,
      TaskPersistentNotificationManager taskPersistentNotificationManager,
      DataStore<Preferences> notificationDataStore) {
    return new NotificationSettingsViewModel(context, notificationManager, dailyReviewScheduler, configManager, taskPersistentNotificationManager, notificationDataStore);
  }
}
