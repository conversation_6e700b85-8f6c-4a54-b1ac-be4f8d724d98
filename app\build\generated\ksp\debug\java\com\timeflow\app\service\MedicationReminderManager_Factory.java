package com.timeflow.app.service;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class MedicationReminderManager_Factory implements Factory<MedicationReminderManager> {
  private final Provider<Context> contextProvider;

  public MedicationReminderManager_Factory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public MedicationReminderManager get() {
    return newInstance(contextProvider.get());
  }

  public static MedicationReminderManager_Factory create(Provider<Context> contextProvider) {
    return new MedicationReminderManager_Factory(contextProvider);
  }

  public static MedicationReminderManager newInstance(Context context) {
    return new MedicationReminderManager(context);
  }
}
