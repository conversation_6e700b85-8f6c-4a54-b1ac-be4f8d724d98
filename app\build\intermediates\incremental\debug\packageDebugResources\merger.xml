<?xml version="1.0" encoding="utf-8"?>
<merger version="3" xmlns:ns1="http://schemas.android.com/tools"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\development\Codes\MyApplication\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\development\Codes\MyApplication\app\src\main\res"><file name="fade_in_fast" path="D:\development\Codes\MyApplication\app\src\main\res\anim\fade_in_fast.xml" qualifiers="" type="anim"/><file name="fade_out_fast" path="D:\development\Codes\MyApplication\app\src\main\res\anim\fade_out_fast.xml" qualifiers="" type="anim"/><file name="fast_fade_in" path="D:\development\Codes\MyApplication\app\src\main\res\anim\fast_fade_in.xml" qualifiers="" type="anim"/><file name="fast_fade_out" path="D:\development\Codes\MyApplication\app\src\main\res\anim\fast_fade_out.xml" qualifiers="" type="anim"/><file name="slide_in_left_fast" path="D:\development\Codes\MyApplication\app\src\main\res\anim\slide_in_left_fast.xml" qualifiers="" type="anim"/><file name="slide_in_right_fast" path="D:\development\Codes\MyApplication\app\src\main\res\anim\slide_in_right_fast.xml" qualifiers="" type="anim"/><file name="slide_out_left_fast" path="D:\development\Codes\MyApplication\app\src\main\res\anim\slide_out_left_fast.xml" qualifiers="" type="anim"/><file name="slide_out_right_fast" path="D:\development\Codes\MyApplication\app\src\main\res\anim\slide_out_right_fast.xml" qualifiers="" type="anim"/><file name="cat" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\cat.png" qualifiers="" type="drawable"/><file name="compat_ripple" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\compat_ripple.xml" qualifiers="" type="drawable"/><file name="flower" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\flower.png" qualifiers="" type="drawable"/><file name="flower2" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\flower2.png" qualifiers="" type="drawable"/><file name="flowers" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\flowers.png" qualifiers="" type="drawable"/><file name="ic_ai" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\ic_ai.xml" qualifiers="" type="drawable"/><file name="ic_career" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\ic_career.xml" qualifiers="" type="drawable"/><file name="ic_check" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\ic_check.xml" qualifiers="" type="drawable"/><file name="ic_check_circle" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\ic_check_circle.xml" qualifiers="" type="drawable"/><file name="ic_education" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\ic_education.xml" qualifiers="" type="drawable"/><file name="ic_family" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\ic_family.xml" qualifiers="" type="drawable"/><file name="ic_finance" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\ic_finance.xml" qualifiers="" type="drawable"/><file name="ic_health" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\ic_health.xml" qualifiers="" type="drawable"/><file name="ic_health_check" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\ic_health_check.xml" qualifiers="" type="drawable"/><file name="ic_history" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\ic_history.xml" qualifiers="" type="drawable"/><file name="ic_image_placeholder" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\ic_image_placeholder.xml" qualifiers="" type="drawable"/><file name="ic_impact" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\ic_impact.xml" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="ic_launcher_monochrome" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\ic_launcher_monochrome.xml" qualifiers="" type="drawable"/><file name="ic_life" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\ic_life.xml" qualifiers="" type="drawable"/><file name="ic_list" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\ic_list.xml" qualifiers="" type="drawable"/><file name="ic_love" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\ic_love.xml" qualifiers="" type="drawable"/><file name="ic_milestone" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\ic_milestone.xml" qualifiers="" type="drawable"/><file name="ic_nebula" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\ic_nebula.xml" qualifiers="" type="drawable"/><file name="ic_other" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\ic_other.xml" qualifiers="" type="drawable"/><file name="ic_parallel_universe" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\ic_parallel_universe.xml" qualifiers="" type="drawable"/><file name="ic_pause" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\ic_pause.xml" qualifiers="" type="drawable"/><file name="ic_play" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\ic_play.xml" qualifiers="" type="drawable"/><file name="ic_play_arrow" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\ic_play_arrow.xml" qualifiers="" type="drawable"/><file name="ic_skill" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\ic_skill.xml" qualifiers="" type="drawable"/><file name="ic_star_filled" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\ic_star_filled.xml" qualifiers="" type="drawable"/><file name="ic_star_half" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\ic_star_half.xml" qualifiers="" type="drawable"/><file name="ic_star_outline" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\ic_star_outline.xml" qualifiers="" type="drawable"/><file name="ic_stop" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\ic_stop.xml" qualifiers="" type="drawable"/><file name="ic_timeflow_app_icon" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\ic_timeflow_app_icon.xml" qualifiers="" type="drawable"/><file name="ic_timeflow_logo" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\ic_timeflow_logo.xml" qualifiers="" type="drawable"/><file name="ic_timer_pause" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\ic_timer_pause.xml" qualifiers="" type="drawable"/><file name="ic_timer_play" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\ic_timer_play.xml" qualifiers="" type="drawable"/><file name="ic_timer_stop" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\ic_timer_stop.xml" qualifiers="" type="drawable"/><file name="ic_time_capsule" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\ic_time_capsule.xml" qualifiers="" type="drawable"/><file name="ic_time_machine" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\ic_time_machine.xml" qualifiers="" type="drawable"/><file name="ic_travel" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\ic_travel.xml" qualifiers="" type="drawable"/><file name="ic_turning_point" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\ic_turning_point.xml" qualifiers="" type="drawable"/><file name="jh" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\jh.png" qualifiers="" type="drawable"/><file name="nomessage" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\nomessage.png" qualifiers="" type="drawable"/><file name="pig" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\pig.png" qualifiers="" type="drawable"/><file name="placeholder_image" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\placeholder_image.xml" qualifiers="" type="drawable"/><file name="widget_background" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\widget_background.xml" qualifiers="" type="drawable"/><file name="widget_background_blue" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\widget_background_blue.xml" qualifiers="" type="drawable"/><file name="widget_background_green" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\widget_background_green.xml" qualifiers="" type="drawable"/><file name="widget_button_background" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\widget_button_background.xml" qualifiers="" type="drawable"/><file name="widget_button_primary" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\widget_button_primary.xml" qualifiers="" type="drawable"/><file name="widget_button_secondary" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\widget_button_secondary.xml" qualifiers="" type="drawable"/><file name="widget_card_background" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\widget_card_background.xml" qualifiers="" type="drawable"/><file name="widget_chart_background" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\widget_chart_background.xml" qualifiers="" type="drawable"/><file name="widget_checkbox_checked" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\widget_checkbox_checked.xml" qualifiers="" type="drawable"/><file name="widget_checkbox_selector" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\widget_checkbox_selector.xml" qualifiers="" type="drawable"/><file name="widget_checkbox_unchecked" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\widget_checkbox_unchecked.xml" qualifiers="" type="drawable"/><file name="widget_circle_blue" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\widget_circle_blue.xml" qualifiers="" type="drawable"/><file name="widget_circle_button_accent" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\widget_circle_button_accent.xml" qualifiers="" type="drawable"/><file name="widget_circle_button_secondary" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\widget_circle_button_secondary.xml" qualifiers="" type="drawable"/><file name="widget_circle_button_white" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\widget_circle_button_white.xml" qualifiers="" type="drawable"/><file name="widget_circle_gray" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\widget_circle_gray.xml" qualifiers="" type="drawable"/><file name="widget_circle_green" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\widget_circle_green.xml" qualifiers="" type="drawable"/><file name="widget_circle_orange" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\widget_circle_orange.xml" qualifiers="" type="drawable"/><file name="widget_circle_red" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\widget_circle_red.xml" qualifiers="" type="drawable"/><file name="widget_emoji_background" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\widget_emoji_background.xml" qualifiers="" type="drawable"/><file name="widget_focus_background" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\widget_focus_background.xml" qualifiers="" type="drawable"/><file name="widget_focus_timer_preview" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\widget_focus_timer_preview.xml" qualifiers="" type="drawable"/><file name="widget_gradient_blue" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\widget_gradient_blue.xml" qualifiers="" type="drawable"/><file name="widget_gradient_timetracking" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\widget_gradient_timetracking.xml" qualifiers="" type="drawable"/><file name="widget_pill_background" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\widget_pill_background.xml" qualifiers="" type="drawable"/><file name="widget_pill_background_white" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\widget_pill_background_white.xml" qualifiers="" type="drawable"/><file name="widget_stats_background" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\widget_stats_background.xml" qualifiers="" type="drawable"/><file name="widget_status_idle" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\widget_status_idle.xml" qualifiers="" type="drawable"/><file name="widget_status_paused" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\widget_status_paused.xml" qualifiers="" type="drawable"/><file name="widget_status_running" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\widget_status_running.xml" qualifiers="" type="drawable"/><file name="widget_task_background" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\widget_task_background.xml" qualifiers="" type="drawable"/><file name="widget_task_item_background" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\widget_task_item_background.xml" qualifiers="" type="drawable"/><file name="widget_timer_button_background" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\widget_timer_button_background.xml" qualifiers="" type="drawable"/><file name="widget_timer_custom_background" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\widget_timer_custom_background.xml" qualifiers="" type="drawable"/><file name="widget_today_tasks_preview" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\widget_today_tasks_preview.xml" qualifiers="" type="drawable"/><file name="xx" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\xx.png" qualifiers="" type="drawable"/><file name="yinhua" path="D:\development\Codes\MyApplication\app\src\main\res\drawable\yinhua.png" qualifiers="" type="drawable"/><file name="activity_main" path="D:\development\Codes\MyApplication\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="widget_focus_timer" path="D:\development\Codes\MyApplication\app\src\main\res\layout\widget_focus_timer.xml" qualifiers="" type="layout"/><file name="widget_focus_timer_large" path="D:\development\Codes\MyApplication\app\src\main\res\layout\widget_focus_timer_large.xml" qualifiers="" type="layout"/><file name="widget_focus_timer_small" path="D:\development\Codes\MyApplication\app\src\main\res\layout\widget_focus_timer_small.xml" qualifiers="" type="layout"/><file name="widget_goal_progress" path="D:\development\Codes\MyApplication\app\src\main\res\layout\widget_goal_progress.xml" qualifiers="" type="layout"/><file name="widget_quick_timer" path="D:\development\Codes\MyApplication\app\src\main\res\layout\widget_quick_timer.xml" qualifiers="" type="layout"/><file name="widget_quick_timer_small" path="D:\development\Codes\MyApplication\app\src\main\res\layout\widget_quick_timer_small.xml" qualifiers="" type="layout"/><file name="widget_time_insight" path="D:\development\Codes\MyApplication\app\src\main\res\layout\widget_time_insight.xml" qualifiers="" type="layout"/><file name="widget_today_tasks" path="D:\development\Codes\MyApplication\app\src\main\res\layout\widget_today_tasks.xml" qualifiers="" type="layout"/><file name="widget_today_tasks_large" path="D:\development\Codes\MyApplication\app\src\main\res\layout\widget_today_tasks_large.xml" qualifiers="" type="layout"/><file name="widget_today_tasks_medium" path="D:\development\Codes\MyApplication\app\src\main\res\layout\widget_today_tasks_medium.xml" qualifiers="" type="layout"/><file name="widget_today_tasks_small" path="D:\development\Codes\MyApplication\app\src\main\res\layout\widget_today_tasks_small.xml" qualifiers="" type="layout"/><file name="widget_weekly_stats" path="D:\development\Codes\MyApplication\app\src\main\res\layout\widget_weekly_stats.xml" qualifiers="" type="layout"/><file name="ic_launcher" path="D:\development\Codes\MyApplication\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="D:\development\Codes\MyApplication\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="D:\development\Codes\MyApplication\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\development\Codes\MyApplication\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\development\Codes\MyApplication\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\development\Codes\MyApplication\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\development\Codes\MyApplication\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\development\Codes\MyApplication\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\development\Codes\MyApplication\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\development\Codes\MyApplication\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\development\Codes\MyApplication\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\development\Codes\MyApplication\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="D:\development\Codes\MyApplication\app\src\main\res\values\bools.xml" qualifiers=""><bool name="enable_hardware_acceleration">true</bool><bool name="is_low_memory_device">false</bool></file><file path="D:\development\Codes\MyApplication\app\src\main\res\values\colors.xml" qualifiers=""><color name="purple_200">#FFBB86FC</color><color name="purple_500">#FFf5f4f6</color><color name="purple_700">#FF3700B3</color><color name="teal_200">#FF03DAC5</color><color name="teal_700">#FF018786</color><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color><color name="primary">#6650a4</color><color name="primary_color">#6650a4</color><color name="primary_variant">#3700B3</color><color name="primary_light">#E8D1F2</color><color name="secondary">#625b71</color><color name="secondary_variant">#4A2F8F</color><color name="secondary_light">#CCC2DC</color><color name="background">#FFFFFF</color><color name="surface">#FFFFFF</color><color name="error">#B00020</color><color name="on_primary">#FFFFFF</color><color name="on_secondary">#FFFFFF</color><color name="on_background">#000000</color><color name="on_surface">#000000</color><color name="on_error">#FFFFFF</color><color name="success">#FF36B37E</color><color name="warning">#FFFFC400</color><color name="info">#FF2684FF</color><color name="time_picker_background">#F0F0F0</color><color name="splash_background">#FAF8F5</color><color name="splash_primary">#B49EC9</color><color name="splash_secondary">#D9C2D4</color><color name="splash_accent">#E8DFF1</color><color name="splash_text">#2A2A2A</color><color name="splash_gold">#FFD700</color><color name="widget_divider_light">#E0E0E0</color><color name="widget_today_text_tertiary_light">#BDBDBD</color></file><file path="D:\development\Codes\MyApplication\app\src\main\res\values\ids.xml" qualifiers=""><item name="ripple_fixed_tag" type="id"/></file><file path="D:\development\Codes\MyApplication\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">时光流</string><string name="home">主页</string><string name="tasks">首页</string><string name="tasks_3d">3D任务</string><string name="time">时间</string><string name="analytics">统计</string><string name="discover">发现</string><string name="profile">我的</string><string name="calendar">日历</string><string name="wish_pool">愿望池</string><string name="ai_assistant_header">AI 助手</string><string name="ai_review">AI复盘</string><string name="menstrual_cycle">生理周期</string><string name="menstrual_period">经期</string><string name="predicted_period">预测经期</string><string name="fertile_period">易孕期</string><string name="period_started">经期开始了吗?</string><string name="sexual_activity">性行为</string><string name="physical_symptoms">身体症状</string><string name="cycle_view">周期视图</string><string name="calendar_view">日历视图</string><string name="day_x">第 %1$d 天</string><string name="cycle_days">共 %1$d 天</string><string name="prompt_input_hint">输入您的问题...</string><string name="theme_settings">主题设置</string><string name="material_you">Material You</string><string name="material_you_description">启用动态配色</string><string name="material_you_desc">使用动态配色方案，根据壁纸自动生成主题色</string><string name="color_scheme">配色方案</string><string name="color_theme_settings">颜色主题设置</string><string name="custom_theme_colors">自定义主题颜色</string><string name="primary_color">主题色</string><string name="primary_color_desc">应用的主要强调色</string><string name="secondary_color">次要颜色</string><string name="secondary_color_desc">主题的次要强调色</string><string name="tertiary_color">第三颜色</string><string name="tertiary_color_desc">主题的第三强调色</string><string name="background_color">背景色</string><string name="background_color_desc">应用的默认背景色</string><string name="background_colors">背景颜色设置</string><string name="unified_background">统一背景</string><string name="unified_background_desc">为所有页面使用相同的背景颜色</string><string name="unified_background_color">统一背景色</string><string name="unified_background_color_desc">应用于所有页面的背景色</string><string name="home_page_color">主页背景色</string><string name="home_page_color_desc">主页的背景颜色</string><string name="calendar_page_color">日历背景色</string><string name="calendar_page_color_desc">日历页面的背景颜色</string><string name="statistics_page_color">统计背景色</string><string name="statistics_page_color_desc">统计页面的背景颜色</string><string name="profile_page_color">我的背景色</string><string name="profile_page_color_desc">个人资料页面的背景颜色</string><string name="settings_page_color">设置背景色</string><string name="settings_page_color_desc">设置页面的背景颜色</string><string name="dark_mode_settings">深色模式设置</string><string name="dark_mode">深色模式</string><string name="dark_mode_desc">启用应用的深色主题</string><string name="home_color">主页背景色</string><string name="calendar_color">日历背景色</string><string name="statistics_color">统计背景色</string><string name="profile_color">我的背景色</string><string name="reset_colors">重置颜色</string><string name="apply_colors">应用颜色</string><string name="time_insight_widget_description">显示今日时间分布和专注统计</string><string name="today_tasks_widget_description">显示今日重要待办事项</string><string name="quick_timer_widget_description">快速开始专注计时</string><string name="focus_timer_widget_description">显示正在专注的任务和计时状态</string><string name="weekly_stats_widget_description">显示一周专注时间统计图表</string><string name="goal_progress_widget_description">显示习惯和目标完成进度</string><string name="language_settings">语言设置</string><string name="app_language">应用语言</string><string name="app_language_desc">选择您偏好的语言</string><string name="follow_system">跟随系统</string><string name="simplified_chinese">简体中文</string><string name="traditional_chinese">繁体中文</string><string name="english">English</string><string name="language_changed">语言已更改</string><string name="language_change_desc">应用将重新启动以应用新语言</string><string name="restart_now">立即重启</string><string name="restart_later">稍后重启</string><string name="settings">设置</string><string name="general_settings">通用设置</string><string name="appearance_settings">外观设置</string><string name="notification_settings">通知设置</string><string name="privacy_settings">隐私设置</string><string name="about">关于</string><string name="version">版本</string><string name="save">保存</string><string name="cancel">取消</string><string name="confirm">确认</string><string name="ok">确定</string><string name="yes">是</string><string name="no">否</string><string name="done">完成</string><string name="edit">编辑</string><string name="delete">删除</string><string name="add">添加</string><string name="remove">移除</string><string name="clear">清除</string><string name="reset">重置</string><string name="apply">应用</string><string name="close">关闭</string><string name="back">返回</string><string name="next">下一步</string><string name="previous">上一步</string><string name="finish">完成</string><string name="loading">加载中...</string><string name="error">错误</string><string name="success">成功</string><string name="warning">警告</string><string name="info">信息</string><string name="feedback_feeling_tag">#感觉</string><string name="feedback_followup_tag">#后续</string><string name="feedback_difficulty_tag">#难度</string><string name="feedback_time_tag">#时间</string><string name="feeling_accomplished">充满成就感</string><string name="feeling_struggling">有点吃力</string><string name="feeling_need_help">需要帮助</string><string name="feeling_very_accomplished">很有成就感</string><string name="feeling_smooth">完成得很顺利</string><string name="followup_need_tracking">需要跟进</string><string name="followup_completed_archived">已完成归档</string><string name="followup_to_share">待分享</string><string name="followup_need_improvement">需要改进</string><string name="followup_worth_recording">值得记录</string><string name="difficulty_easier_than_expected">比预期简单</string><string name="difficulty_as_expected">符合预期</string><string name="difficulty_harder_than_expected">比预期困难</string><string name="difficulty_need_more_learning">需要学习更多</string><string name="time_completed_early">提前完成</string><string name="time_completed_on_time">按时完成</string><string name="time_slightly_delayed">稍有延迟</string><string name="time_significantly_delayed">大幅延迟</string><string name="set_ai_assistant_name">设置AI助手名称</string><string name="ai_assistant_name">AI助手名称</string><string name="ai_assistant_name_example">例如：小助手</string><string name="enter_ai_assistant_name">输入AI助手名称</string><string name="feedback_input_hint">输入感想或使用 #标签 获取智能建议...</string><string name="monday">周一</string><string name="tuesday">周二</string><string name="wednesday">周三</string><string name="thursday">周四</string><string name="friday">周五</string><string name="saturday">周六</string><string name="sunday">周日</string><string name="month_day_format">%1$d月%2$d日</string><string name="week_month_day_format">%1$s · %2$s</string><string name="content_desc_reflection">感想</string><string name="content_desc_settings">设置</string><string name="reflection">感想</string><string name="encouragement_master">🌟 你已经成为真正的愿望大师！继续保持这份热情，你的能量会感染更多人！</string><string name="encouragement_amazing">🚀 哇！你的愿望实现能力令人惊叹！还有几步就能达到大师级别了！</string><string name="encouragement_great">💫 太棒了！你已经走在愿望实现的快车道上！每一个成就都在为下一个奠定基础！</string><string name="encouragement_good_start">🌱 很棒的开始！你的愿望种子正在茁壮成长，保持这份坚持和热情！</string><string name="encouragement_progress">✨ 每一步都是进步！你已经开始了愿望实现的旅程，相信自己的力量！</string><string name="encouragement_ready">🎯 愿望成就系统已为你准备好！开始你的第一个愿望，解锁专属于你的成就徽章！</string><string name="notification_category_task_management">任务管理</string><string name="notification_category_task_management_desc">管理任务相关的提醒通知</string><string name="notification_item_task_reminders">任务提醒</string><string name="notification_item_task_reminders_desc">开启任务到期前的提醒</string><string name="notification_item_deadline_reminders">截止日期提醒</string><string name="notification_item_deadline_reminders_desc">任务即将到期时通知</string><string name="notification_item_overdue_reminders">逾期任务提醒</string><string name="notification_item_overdue_reminders_desc">提醒已过期的重要任务</string><string name="search_suggestion_study_method">学习方法</string><string name="search_suggestion_course">课程</string><string name="search_suggestion_running">跑步</string><string name="ai_suggestion_demo_morning_review">上午9:00 复盘昨日工作</string><string name="search_suggestion_exercise_effect">运动效果</string><string name="search_suggestion_exam">考试</string><string name="ai_suggestion_demo_conflict_message">与任务\'项目讨论\'存在时间冲突</string><string name="search_suggestion_master">掌握</string><string name="search_suggestion_meditation">冥想</string><string name="search_suggestion_study_progress">学习进度</string><string name="focus_mode_exit">退出专注模式</string><string name="ai_suggestion_smart_reminder_content">根据您的使用模式，AI助手为您推荐了一些优化建议，点击查看详情。</string><string name="search_suggestion_understand">理解</string><string name="search_suggestion_history">历史</string><string name="ai_suggestion_smart_reminder_title">智能提醒</string><string name="search_suggestion_study">学习</string><string name="search_suggestion_morning_run">晨跑</string><string name="search_suggestion_work">工作</string><string name="search_suggestion_reading">读书</string><string name="time_melody">时间旋律</string><string name="search_suggestion_work_result">工作成果</string><string name="search_suggestion_meditation_method">冥想方法</string><string name="search_suggestion_task">任务</string><string name="search_suggestion_psychology">心理</string><string name="search_suggestion_exercise_plan">运动计划</string><string name="search_suggestion_novel">小说</string><string name="search_suggestion_meditation_experience">冥想体验</string><string name="search_suggestion_fitness">健身</string><string name="search_suggestion_notes">笔记</string><string name="ai_suggestion_monthly_content">基于您近一个月的数据分析，为您提供以下建议：\n\n1. 📈 效率趋势：您的工作效率在周三和周四最高\n2. 🎯 目标达成：建议将大目标拆分为更小的里程碑\n3. 💪 习惯养成：您在坚持习惯方面表现优秀，可以尝试增加新的挑战\n\n%s，继续加油！</string><string name="search_suggestion_introspection">内观</string><string name="search_suggestion_meditation_effect">冥想效果</string><string name="search_suggestion_reading_thoughts">读书感悟</string><string name="search_suggestion_clarity">清晰</string><string name="ai_suggestion_monthly_title">月度工作模式分析</string><string name="search_suggestion_self_help">自助</string><string name="ai_suggestion_weekly_title">本周效率提升建议</string><string name="search_suggestion_yoga">瑜伽</string><string name="search_suggestion_breathing">呼吸</string><string name="ai_suggestion_weekly_content">根据您本周的任务完成情况，AI助手建议：\n\n1. 🎯 专注时段优化：建议在上午9-11点安排重要任务\n2. ⏰ 任务时间预估：可以适当增加15%的缓冲时间\n3. 🔄 工作节奏调整：每工作50分钟休息10分钟效果更佳\n\n继续保持良好的工作习惯！</string><string name="search_suggestion_relax">放松</string><string name="ai_suggestion_demo_afternoon_meeting">下午3:00 会议</string><string name="search_suggestion_project">项目</string><string name="search_suggestion_work_progress">工作进展</string><string name="search_suggestion_exercise_reflection">运动反思</string><string name="search_suggestion_philosophy">哲学</string><string name="search_suggestion_work_pressure">工作压力</string><string name="search_suggestion_meeting">会议</string><string name="search_suggestion_pressure">压力</string><string name="search_suggestion_basketball">篮球</string><string name="search_suggestion_study_notes">学习笔记</string><string name="ai_suggestion_demo_presentation">准备明天的演示文稿</string><string name="search_suggestion_reading_plan">读书计划</string><string name="search_suggestion_study_plan">学习计划</string><string name="focus_mode_enter">进入专注模式</string><string name="search_suggestion_work_reflection">工作反思</string><string name="search_suggestion_exercise">运动</string><string name="search_suggestion_review">复习</string><string name="search_suggestion_swimming">游泳</string><string name="search_suggestion_team">团队</string><string name="task_management">任务管理</string><string name="search_suggestion_focus">专注</string><string name="search_suggestion_efficiency">效率</string><string name="search_suggestion_reading_notes">读书笔记</string><string name="search_suggestion_science">科学</string><string name="you_might_be_looking_for">您可能想找</string><string name="search_suggestions">搜索建议</string><string name="feature_emotion_record_desc">记录每日心情变化，关注心理健康</string><string name="not_set">未设置</string><string name="emotion_type_anxious">焦虑</string><string name="import_export">导入导出</string><string name="feature_data_analysis_desc">可视化效率报告，洞察时间使用规律</string><string name="feature_calendar_sync_desc">无缝集成系统日历，统一管理时间安排</string><string name="view_wish_pool">查看愿望池</string><string name="feature_kanban_management_desc">类似Trello的任务看板，直观管理工作流</string><string name="feature_goal_tracking">目标追踪</string><string name="my_space">我的空间</string><string name="restore">恢复</string><string name="core_features">核心功能</string><string name="feature_data_analysis">数据统计分析</string><string name="emotion_type_sad">伤心</string><string name="sync_settings_desc">管理数据同步和云存储</string><string name="about_desc">应用版本和信息</string><string name="emotion_type_joy">开心</string><string name="no_wishes_yet">还没有愿望，点击添加你的第一个愿望吧！</string><string name="sync_settings">同步设置</string><string name="backup">备份</string><string name="default_reminder_time">默认提醒时间</string><string name="data_management_desc">备份、恢复、导入导出和存储清理</string><string name="ai_settings">AI设置</string><string name="data_management">数据管理</string><string name="feature_personalized_theme_desc">多种主题色彩，打造专属的视觉体验</string><string name="emotion_type_angry">生气</string><string name="wish_cloud">愿望星云</string><string name="feature_calendar_sync">日历同步</string><string name="feature_time_tracking">时间自动追踪</string><string name="feature_emotion_record">情绪记录</string><string name="feature_personalized_theme">个性化主题</string><string name="ai_settings_desc">自定义AI服务提供商</string><string name="reminder_time_desc">设置通用的提醒提前时间</string><string name="reminder_time">提醒时间</string><string name="feature_ai_split_desc">自动将复杂任务拆解为可执行的子任务</string><string name="feature_ai_split">AI智能拆分</string><string name="emotion_type_calm">平静</string><string name="feature_goal_tracking_desc">设定目标并跟踪进度，持续自我提升</string><string name="feature_kanban_management">看板管理</string><string name="add_wish">添加愿望</string><string name="feature_time_tracking_desc">智能记录专注时间，深度分析工作习惯</string></file><file path="D:\development\Codes\MyApplication\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.TimeFlow" parent="Theme.Material3.DayNight.NoActionBar">
        
        <item name="android:windowLightStatusBar">true</item>
        <item name="android:windowLightNavigationBar" ns1:targetApi="27">true</item>
        <item name="android:statusBarColor">@android:color/background_light</item>
        <item name="android:navigationBarColor">@android:color/background_light</item>
        
        
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowLayoutInDisplayCutoutMode" ns1:targetApi="o_mr1">shortEdges</item>
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:enforceNavigationBarContrast" ns1:targetApi="q">false</item>
        <item name="android:enforceStatusBarContrast" ns1:targetApi="q">false</item>
    </style><style name="Theme.TimeFlow.StaticColors">
        
        <item name="colorPrimary">@color/primary</item>
        <item name="colorPrimaryContainer">@color/primary_light</item>
        <item name="colorOnPrimary">@color/on_primary</item>
        
        <item name="colorSecondary">@color/secondary</item>
        <item name="colorSecondaryContainer">@color/secondary_light</item>
        <item name="colorOnSecondary">@color/on_secondary</item>
        
        <item name="colorTertiary">@color/primary_variant</item>
        <item name="colorOnTertiary">@color/white</item>
        
        <item name="android:windowLightStatusBar">true</item>
        <item name="android:windowLightNavigationBar" ns1:targetApi="27">true</item>
        <item name="android:statusBarColor">@android:color/background_light</item>
        <item name="android:navigationBarColor">@android:color/background_light</item>
        
        
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowLayoutInDisplayCutoutMode" ns1:targetApi="o_mr1">shortEdges</item>
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:enforceNavigationBarContrast" ns1:targetApi="q">false</item>
        <item name="android:enforceStatusBarContrast" ns1:targetApi="q">false</item>
    </style><style name="Theme.MyApplication" parent="Theme.TimeFlow"/><style name="Theme.TimeFlowApp" parent="Theme.TimeFlow"/><style name="Theme.TimeFlow.OptimizedAnimation" parent="Theme.TimeFlowApp">
        
        <item name="android:windowBackground">@color/splash_background</item>
        <item name="android:windowDisablePreview">false</item>
        
        
        <item name="android:windowAnimationStyle">@style/OptimizedWindowAnimation</item>
        
        
        <item name="android:hardwareAccelerated">true</item>
        
        
        <item name="android:windowContentTransitions">true</item>
        <item name="android:windowAllowEnterTransitionOverlap">false</item>
        <item name="android:windowAllowReturnTransitionOverlap">false</item>
    </style><style name="Theme.TimeFlow.NoAnimation" parent="Theme.TimeFlowApp">
        
        <item name="android:windowAnimationStyle">@null</item>
        <item name="android:windowDisablePreview">true</item>
        <item name="android:windowNoDisplay">false</item>
        <item name="android:windowBackground">@android:color/white</item>
        
        
        <item name="android:windowSplashScreenAnimatedIcon" ns1:targetApi="s">@null</item>
        <item name="android:windowSplashScreenAnimationDuration" ns1:targetApi="s">0</item>
        <item name="android:windowSplashScreenBrandingImage" ns1:targetApi="s">@null</item>
        
        
        <item name="android:activityOpenEnterAnimation">@null</item>
        <item name="android:activityOpenExitAnimation">@null</item>
        <item name="android:activityCloseEnterAnimation">@null</item>
        <item name="android:activityCloseExitAnimation">@null</item>
        <item name="android:taskOpenEnterAnimation">@null</item>
        <item name="android:taskOpenExitAnimation">@null</item>
        <item name="android:taskCloseEnterAnimation">@null</item>
        <item name="android:taskCloseExitAnimation">@null</item>
        <item name="android:taskToFrontEnterAnimation">@null</item>
        <item name="android:taskToFrontExitAnimation">@null</item>
        <item name="android:taskToBackEnterAnimation">@null</item>
        <item name="android:taskToBackExitAnimation">@null</item>
    </style><style name="OptimizedWindowAnimation" parent="@android:style/Animation.Activity">
        <item name="android:activityOpenEnterAnimation">@anim/slide_in_right_fast</item>
        <item name="android:activityOpenExitAnimation">@anim/slide_out_left_fast</item>
        <item name="android:activityCloseEnterAnimation">@anim/slide_in_left_fast</item>
        <item name="android:activityCloseExitAnimation">@anim/slide_out_right_fast</item>
    </style></file><file path="D:\development\Codes\MyApplication\app\src\main\res\values\widget_colors.xml" qualifiers=""><color name="widget_gradient_start">#F8FAFF</color><color name="widget_gradient_end">#E8F4FD</color><color name="widget_background_white">#FFFFFF</color><color name="widget_background_overlay">#F5F7FA</color><color name="widget_text_primary">#2C3E50</color><color name="widget_text_secondary">#7F8C8D</color><color name="widget_text_tertiary">#BDC3C7</color><color name="widget_text_light">#ECF0F1</color><color name="widget_accent_blue">#3498DB</color><color name="widget_accent_green">#2ECC71</color><color name="widget_accent_orange">#F39C12</color><color name="widget_accent_purple">#9B59B6</color><color name="widget_accent_red">#E74C3C</color><color name="widget_accent_teal">#1ABC9C</color><color name="widget_accent_pink">#E91E63</color><color name="widget_priority_high">#FF6B6B</color><color name="widget_priority_medium">#FFD93D</color><color name="widget_priority_low">#6BCF7F</color><color name="widget_gradient_blue_start">#667eea</color><color name="widget_gradient_blue_end">#764ba2</color><color name="widget_gradient_green_start">#11998e</color><color name="widget_gradient_green_end">#38ef7d</color><color name="widget_gradient_orange_start">#fc4a1a</color><color name="widget_gradient_orange_end">#f7b733</color><color name="widget_timetracking_primary">#CCAEC5</color><color name="widget_timetracking_secondary">#D9C2D4</color><color name="widget_timetracking_accent">#FF8FB3</color><color name="widget_timetracking_background_start">#F2EBF0</color><color name="widget_timetracking_background_end">#D4BBC6</color><color name="widget_gradient_purple_start">#667eea</color><color name="widget_gradient_purple_end">#764ba2</color><color name="widget_timer_background">#FFFFFF</color><color name="widget_timer_text">#2C3E50</color><color name="widget_timer_text_secondary">#7F8C8D</color><color name="widget_timer_shadow">#E8E8E8</color><color name="widget_chart_sleep">#9B59B6</color><color name="widget_chart_work">#3498DB</color><color name="widget_chart_entertainment">#F39C12</color><color name="widget_chart_focus">#E74C3C</color><color name="widget_chart_health">#2ECC71</color><color name="widget_chart_reading">#E91E63</color><color name="widget_task_completed">#2ECC71</color><color name="widget_task_pending">#F39C12</color><color name="widget_task_overdue">#E74C3C</color><color name="widget_overlay_light">#80FFFFFF</color><color name="widget_overlay_dark">#40000000</color><color name="widget_shadow">#20000000</color><color name="widget_chart_background">#F8F9FA</color><color name="widget_chart_border">#E5E5EA</color><color name="widget_task_background">#F8F9FA</color><color name="widget_task_border">#E5E5EA</color><color name="widget_timer_custom_background">#007AFF</color><color name="widget_timer_custom_text">#FFFFFF</color><color name="widget_today_background_light">#FFFFFF</color><color name="widget_today_surface_light">#F8F9FA</color><color name="widget_today_text_primary_light">#1C1C1E</color><color name="widget_today_text_secondary_light">#6C6C70</color><color name="widget_today_accent_light">#007AFF</color><color name="widget_today_border_light">#E5E5EA</color><color name="widget_today_shadow_light">#10000000</color><color name="widget_today_background_dark">#1C1C1E</color><color name="widget_today_surface_dark">#2C2C2E</color><color name="widget_today_text_primary_dark">#FFFFFF</color><color name="widget_today_text_secondary_dark">#AEAEB2</color><color name="widget_today_accent_dark">#0A84FF</color><color name="widget_today_border_dark">#38383A</color><color name="widget_today_shadow_dark">#20000000</color><color name="widget_focus_text_primary_light">#2C3E50</color><color name="widget_focus_text_secondary_light">#7F8C8D</color><color name="widget_focus_text_primary_dark">#FFFFFF</color><color name="widget_focus_text_secondary_dark">#AEAEB2</color></file><file path="D:\development\Codes\MyApplication\app\src\main\res\values-en\strings.xml" qualifiers="en"><string name="app_name">TimeFlow</string><string name="home">Home</string><string name="tasks">Tasks</string><string name="tasks_3d">3D Tasks</string><string name="time">Time</string><string name="analytics">Analytics</string><string name="discover">Discover</string><string name="profile">Profile</string><string name="calendar">Calendar</string><string name="wish_pool">Wish Pool</string><string name="ai_assistant_header">AI Assistant</string><string name="ai_review">AI Review</string><string name="menstrual_cycle">Menstrual Cycle</string><string name="menstrual_period">Period</string><string name="predicted_period">Predicted Period</string><string name="fertile_period">Fertile Period</string><string name="period_started">Did your period start?</string><string name="sexual_activity">Sexual Activity</string><string name="physical_symptoms">Physical Symptoms</string><string name="cycle_view">Cycle View</string><string name="calendar_view">Calendar View</string><string name="day_x">Day %1$d</string><string name="cycle_days">%1$d days total</string><string name="prompt_input_hint">Enter your question...</string><string name="theme_settings">Theme Settings</string><string name="material_you">Material You</string><string name="material_you_description">Enable dynamic colors</string><string name="material_you_desc">Use dynamic color scheme, automatically generate theme colors based on wallpaper</string><string name="color_scheme">Color Scheme</string><string name="color_theme_settings">Color Theme Settings</string><string name="custom_theme_colors">Custom Theme Colors</string><string name="primary_color">Primary Color</string><string name="primary_color_desc">Main accent color of the app</string><string name="secondary_color">Secondary Color</string><string name="secondary_color_desc">Secondary accent color of the theme</string><string name="tertiary_color">Tertiary Color</string><string name="tertiary_color_desc">Tertiary accent color of the theme</string><string name="background_color">Background Color</string><string name="background_color_desc">Default background color of the app</string><string name="background_colors">Background Color Settings</string><string name="unified_background">Unified Background</string><string name="unified_background_desc">Use the same background color for all pages</string><string name="language_settings">Language Settings</string><string name="app_language">App Language</string><string name="app_language_desc">Choose your preferred language</string><string name="follow_system">Follow System</string><string name="simplified_chinese">Simplified Chinese</string><string name="traditional_chinese">Traditional Chinese</string><string name="english">English</string><string name="language_changed">Language changed</string><string name="language_change_desc">The app will restart to apply the new language</string><string name="restart_now">Restart Now</string><string name="restart_later">Restart Later</string><string name="settings">Settings</string><string name="general_settings">General Settings</string><string name="appearance_settings">Appearance Settings</string><string name="notification_settings">Notification Settings</string><string name="privacy_settings">Privacy Settings</string><string name="about">About</string><string name="version">Version</string><string name="save">Save</string><string name="cancel">Cancel</string><string name="confirm">Confirm</string><string name="ok">OK</string><string name="yes">Yes</string><string name="no">No</string><string name="done">Done</string><string name="edit">Edit</string><string name="delete">Delete</string><string name="add">Add</string><string name="remove">Remove</string><string name="clear">Clear</string><string name="reset">Reset</string><string name="apply">Apply</string><string name="close">Close</string><string name="back">Back</string><string name="next">Next</string><string name="previous">Previous</string><string name="finish">Finish</string><string name="loading">Loading...</string><string name="error">Error</string><string name="success">Success</string><string name="warning">Warning</string><string name="info">Information</string><string name="feedback_feeling_tag">#Feeling</string><string name="feedback_followup_tag">#Followup</string><string name="feedback_difficulty_tag">#Difficulty</string><string name="feedback_time_tag">#Time</string><string name="feeling_accomplished">Feeling accomplished</string><string name="feeling_struggling">A bit challenging</string><string name="feeling_need_help">Need help</string><string name="feeling_very_accomplished">Very accomplished</string><string name="feeling_smooth">Completed smoothly</string><string name="followup_need_tracking">Need follow-up</string><string name="followup_completed_archived">Completed and archived</string><string name="followup_to_share">To share</string><string name="followup_need_improvement">Need improvement</string><string name="followup_worth_recording">Worth recording</string><string name="difficulty_easier_than_expected">Easier than expected</string><string name="difficulty_as_expected">As expected</string><string name="difficulty_harder_than_expected">Harder than expected</string><string name="difficulty_need_more_learning">Need more learning</string><string name="time_completed_early">Completed early</string><string name="time_completed_on_time">Completed on time</string><string name="time_slightly_delayed">Slightly delayed</string><string name="time_significantly_delayed">Significantly delayed</string><string name="set_ai_assistant_name">Set AI Assistant Name</string><string name="ai_assistant_name">AI Assistant Name</string><string name="ai_assistant_name_example">e.g.: Assistant</string><string name="enter_ai_assistant_name">Enter AI assistant name</string><string name="feedback_input_hint">Enter thoughts or use #tags for smart suggestions...</string><string name="monday">Mon</string><string name="tuesday">Tue</string><string name="wednesday">Wed</string><string name="thursday">Thu</string><string name="friday">Fri</string><string name="saturday">Sat</string><string name="sunday">Sun</string><string name="month_day_format">%1$s %2$d</string><string name="week_month_day_format">%1$s · %2$s</string><string name="reflection">Reflection</string><string name="content_desc_reflection">Reflection</string><string name="content_desc_settings">Settings</string><string name="encouragement_master">🌟 You\'ve become a true wish master! Keep this passion, your energy will inspire others!</string><string name="encouragement_amazing">🚀 Wow! Your wish fulfillment ability is amazing! Just a few more steps to master level!</string><string name="encouragement_great">💫 Fantastic! You\'re on the fast track to wish fulfillment! Each achievement builds the foundation for the next!</string><string name="encouragement_good_start">🌱 Great start! Your wish seeds are growing strong, keep this persistence and passion!</string><string name="encouragement_progress">✨ Every step is progress! You\'ve started your wish fulfillment journey, believe in your power!</string><string name="encouragement_ready">🎯 The wish achievement system is ready for you! Start your first wish and unlock your exclusive achievement badges!</string><string name="notification_category_task_management">Task Management</string><string name="notification_category_task_management_desc">Manage task-related reminder notifications</string><string name="notification_item_task_reminders">Task Reminders</string><string name="notification_item_task_reminders_desc">Enable reminders before tasks are due</string><string name="notification_item_deadline_reminders">Deadline Reminders</string><string name="notification_item_deadline_reminders_desc">Notify when tasks are about to expire</string><string name="notification_item_overdue_reminders">Overdue Task Reminders</string><string name="notification_item_overdue_reminders_desc">Remind about overdue important tasks</string><string name="search_suggestion_study_progress">Study Progress</string><string name="search_suggestion_notes">Notes</string><string name="search_suggestion_understand">Understand</string><string name="search_suggestion_psychology">Psychology</string><string name="search_suggestion_clarity">Clarity</string><string name="search_suggestion_efficiency">Efficiency</string><string name="task_management">Task Management</string><string name="search_suggestion_meditation_method">Meditation Method</string><string name="search_suggestion_focus">Focus</string><string name="search_suggestion_reading_plan">Reading Plan</string><string name="search_suggestion_task">Task</string><string name="search_suggestion_introspection">Introspection</string><string name="search_suggestion_meditation_experience">Meditation Experience</string><string name="search_suggestion_exercise_plan">Exercise Plan</string><string name="search_suggestion_novel">Novel</string><string name="search_suggestion_study_plan">Study Plan</string><string name="search_suggestion_work">Work</string><string name="search_suggestion_reading_notes">Reading Notes</string><string name="ai_suggestion_monthly_title">Monthly Work Pattern Analysis</string><string name="search_suggestion_history">History</string><string name="ai_suggestion_smart_reminder_content">Based on your usage patterns, AI assistant has recommended some optimization suggestions for you. Click to view details.</string><string name="search_suggestion_team">Team</string><string name="focus_mode_enter">Enter Focus Mode</string><string name="search_suggestion_meeting">Meeting</string><string name="search_suggestion_relax">Relax</string><string name="ai_suggestion_demo_conflict_message">Time conflict with task \'Project Discussion\'</string><string name="search_suggestion_reading_thoughts">Reading Thoughts</string><string name="search_suggestion_philosophy">Philosophy</string><string name="ai_suggestion_smart_reminder_title">Smart Reminder</string><string name="time_melody">Time Melody</string><string name="search_suggestion_exercise_effect">Exercise Effect</string><string name="ai_suggestion_monthly_content">Based on your data analysis over the past month, here are some suggestions:\n\n1. 📈 Efficiency Trends: Your work efficiency is highest on Wednesdays and Thursdays\n2. 🎯 Goal Achievement: Consider breaking large goals into smaller milestones\n3. 💪 Habit Formation: You excel at maintaining habits, try adding new challenges\n\n%s, keep going!</string><string name="search_suggestion_work_result">Work Result</string><string name="search_suggestion_review">Review</string><string name="focus_mode_exit">Exit Focus Mode</string><string name="search_suggestion_yoga">Yoga</string><string name="search_suggestion_work_progress">Work Progress</string><string name="search_suggestion_reading">Reading</string><string name="search_suggestion_morning_run">Morning Run</string><string name="search_suggestion_pressure">Pressure</string><string name="search_suggestion_fitness">Fitness</string><string name="ai_suggestion_demo_afternoon_meeting">3:00 PM Meeting</string><string name="search_suggestion_study">Study</string><string name="ai_suggestion_weekly_title">Weekly Efficiency Improvement Suggestions</string><string name="search_suggestion_work_reflection">Work Reflection</string><string name="search_suggestion_exercise">Exercise</string><string name="search_suggestion_exercise_reflection">Exercise Reflection</string><string name="search_suggestion_exam">Exam</string><string name="search_suggestion_basketball">Basketball</string><string name="search_suggestion_swimming">Swimming</string><string name="ai_suggestion_demo_morning_review">9:00 AM Review yesterday\'s work</string><string name="search_suggestion_breathing">Breathing</string><string name="search_suggestion_course">Course</string><string name="search_suggestion_master">Master</string><string name="search_suggestion_science">Science</string><string name="search_suggestion_work_pressure">Work Pressure</string><string name="ai_suggestion_weekly_content">Based on your task completion this week, AI assistant suggests:\n\n1. 🎯 Focus Time Optimization: Schedule important tasks between 9-11 AM\n2. ⏰ Task Time Estimation: Consider adding 15% buffer time\n3. 🔄 Work Rhythm Adjustment: Work for 50 minutes, rest for 10 minutes for better results\n\nKeep up the good work habits!</string><string name="search_suggestion_meditation">Meditation</string><string name="ai_suggestion_demo_presentation">Prepare tomorrow\'s presentation</string><string name="search_suggestion_self_help">Self Help</string><string name="search_suggestion_project">Project</string><string name="search_suggestion_study_method">Study Method</string><string name="search_suggestion_study_notes">Study Notes</string><string name="search_suggestion_running">Running</string><string name="search_suggestion_meditation_effect">Meditation Effect</string><string name="search_suggestions">Search Suggestions</string><string name="you_might_be_looking_for">You might be looking for</string><string name="feature_data_analysis_desc">Visual efficiency reports to understand time usage patterns</string><string name="feature_calendar_sync">Calendar Sync</string><string name="emotion_type_angry">Angry</string><string name="sync_settings">Sync Settings</string><string name="feature_time_tracking">Automatic Time Tracking</string><string name="add_wish">Add Wish</string><string name="reminder_time_desc">Set general reminder advance time</string><string name="view_wish_pool">View Wish Pool</string><string name="feature_data_analysis">Data Statistics Analysis</string><string name="sync_settings_desc">Manage data sync and cloud storage</string><string name="about_desc">App version and information</string><string name="backup">Backup</string><string name="feature_goal_tracking_desc">Set goals and track progress for continuous self-improvement</string><string name="emotion_type_joy">Happy</string><string name="restore">Restore</string><string name="emotion_type_sad">Sad</string><string name="feature_personalized_theme_desc">Multiple theme colors to create your exclusive visual experience</string><string name="reminder_time">Reminder Time</string><string name="data_management_desc">Backup, restore, import/export and storage cleanup</string><string name="ai_settings_desc">Customize AI service providers</string><string name="not_set">Not Set</string><string name="feature_personalized_theme">Personalized Theme</string><string name="wish_cloud">Wish Cloud</string><string name="feature_kanban_management_desc">Trello-like task board for intuitive workflow management</string><string name="feature_emotion_record">Emotion Record</string><string name="ai_settings">AI Settings</string><string name="feature_ai_split">AI Smart Split</string><string name="emotion_type_calm">Calm</string><string name="feature_kanban_management">Kanban Management</string><string name="emotion_type_anxious">Anxious</string><string name="feature_time_tracking_desc">Intelligently record focus time and deeply analyze work habits</string><string name="no_wishes_yet">No wishes yet, click to add your first wish!</string><string name="default_reminder_time">Default Reminder Time</string><string name="feature_calendar_sync_desc">Seamlessly integrate system calendar for unified time management</string><string name="import_export">Import/Export</string><string name="core_features">Core Features</string><string name="data_management">Data Management</string><string name="feature_goal_tracking">Goal Tracking</string><string name="feature_ai_split_desc">Automatically break down complex tasks into executable subtasks</string><string name="feature_emotion_record_desc">Record daily mood changes and focus on mental health</string><string name="my_space">My Space</string></file><file path="D:\development\Codes\MyApplication\app\src\main\res\values-ldpi\bools.xml" qualifiers="ldpi-v4"><bool name="enable_hardware_acceleration">false</bool><bool name="is_low_memory_device">true</bool></file><file path="D:\development\Codes\MyApplication\app\src\main\res\values-ldrtl\bools.xml" qualifiers="ldrtl-v17"><bool name="enable_hardware_acceleration">false</bool><bool name="is_low_memory_device">true</bool></file><file path="D:\development\Codes\MyApplication\app\src\main\res\values-night\themes.xml" qualifiers="night-v8"><style name="Theme.TimeFlow" parent="Theme.Material3.DayNight.NoActionBar">
        <item name="android:windowLightStatusBar">false</item>
        <item name="android:windowLightNavigationBar" ns1:targetApi="27">false</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
    </style><style name="Theme.TimeFlow.StaticColors">
        
        <item name="colorPrimary">@color/primary_light</item>
        <item name="colorPrimaryContainer">@color/primary_variant</item>
        <item name="colorOnPrimary">@color/black</item>
        
        <item name="colorSecondary">@color/secondary_light</item>
        <item name="colorSecondaryContainer">@color/secondary_variant</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="colorTertiary">@color/primary_light</item>
        <item name="colorOnTertiary">@color/black</item>
        
        <item name="android:windowLightStatusBar">false</item>
        <item name="android:windowLightNavigationBar" ns1:targetApi="27">false</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
    </style></file><file path="D:\development\Codes\MyApplication\app\src\main\res\values-night\widget_colors.xml" qualifiers="night-v8"><color name="widget_today_background_light">#1C1C1E</color><color name="widget_today_surface_light">#2C2C2E</color><color name="widget_today_text_primary_light">#FFFFFF</color><color name="widget_today_text_secondary_light">#AEAEB2</color><color name="widget_today_accent_light">#0A84FF</color><color name="widget_today_border_light">#38383A</color><color name="widget_today_shadow_light">#20000000</color><color name="widget_text_primary">#FFFFFF</color><color name="widget_text_secondary">#AEAEB2</color><color name="widget_text_tertiary">#6C6C70</color><color name="widget_background_white">#1C1C1E</color><color name="widget_background_overlay">#2C2C2E</color><color name="widget_gradient_start">#1C1C1E</color><color name="widget_gradient_end">#2C2C2E</color><color name="widget_task_completed">#30D158</color><color name="widget_task_pending">#FF9F0A</color><color name="widget_task_overdue">#FF453A</color><color name="widget_accent_blue">#0A84FF</color><color name="widget_accent_green">#30D158</color><color name="widget_accent_orange">#FF9F0A</color><color name="widget_accent_red">#FF453A</color></file><file path="D:\development\Codes\MyApplication\app\src\main\res\values-v23\bools.xml" qualifiers="v23"><bool name="enable_hardware_acceleration">true</bool></file><file path="D:\development\Codes\MyApplication\app\src\main\res\values-zh-rTW\strings.xml" qualifiers="zh-rTW"><string name="app_name">時光流</string><string name="home">主頁</string><string name="tasks">首頁</string><string name="tasks_3d">3D任務</string><string name="time">時間</string><string name="analytics">統計</string><string name="discover">發現</string><string name="profile">我的</string><string name="calendar">日曆</string><string name="wish_pool">願望池</string><string name="ai_assistant_header">AI 助手</string><string name="ai_review">AI復盤</string><string name="menstrual_cycle">生理週期</string><string name="menstrual_period">經期</string><string name="predicted_period">預測經期</string><string name="fertile_period">易孕期</string><string name="period_started">經期開始了嗎?</string><string name="sexual_activity">性行為</string><string name="physical_symptoms">身體症狀</string><string name="cycle_view">週期視圖</string><string name="calendar_view">日曆視圖</string><string name="day_x">第 %1$d 天</string><string name="cycle_days">共 %1$d 天</string><string name="prompt_input_hint">輸入您的問題...</string><string name="theme_settings">主題設置</string><string name="material_you">Material You</string><string name="material_you_description">啟用動態配色</string><string name="material_you_desc">使用動態配色方案，根據壁紙自動生成主題色</string><string name="color_scheme">配色方案</string><string name="color_theme_settings">顏色主題設置</string><string name="custom_theme_colors">自定義主題顏色</string><string name="primary_color">主題色</string><string name="primary_color_desc">應用的主要強調色</string><string name="secondary_color">次要顏色</string><string name="secondary_color_desc">主題的次要強調色</string><string name="tertiary_color">第三顏色</string><string name="tertiary_color_desc">主題的第三強調色</string><string name="background_color">背景色</string><string name="background_color_desc">應用的默認背景色</string><string name="background_colors">背景顏色設置</string><string name="unified_background">統一背景</string><string name="unified_background_desc">為所有頁面使用相同的背景顏色</string><string name="language_settings">語言設置</string><string name="app_language">應用語言</string><string name="app_language_desc">選擇您偏好的語言</string><string name="follow_system">跟隨系統</string><string name="simplified_chinese">簡體中文</string><string name="traditional_chinese">繁體中文</string><string name="english">English</string><string name="language_changed">語言已更改</string><string name="language_change_desc">應用將重新啟動以應用新語言</string><string name="restart_now">立即重啟</string><string name="restart_later">稍後重啟</string><string name="settings">設置</string><string name="general_settings">一般設置</string><string name="appearance_settings">外觀設置</string><string name="notification_settings">通知設置</string><string name="privacy_settings">隱私設置</string><string name="about">關於</string><string name="version">版本</string><string name="save">保存</string><string name="cancel">取消</string><string name="confirm">確認</string><string name="ok">確定</string><string name="yes">是</string><string name="no">否</string><string name="done">完成</string><string name="edit">編輯</string><string name="delete">刪除</string><string name="add">添加</string><string name="remove">移除</string><string name="clear">清除</string><string name="reset">重置</string><string name="apply">應用</string><string name="close">關閉</string><string name="back">返回</string><string name="next">下一步</string><string name="previous">上一步</string><string name="finish">完成</string><string name="loading">載入中...</string><string name="error">錯誤</string><string name="success">成功</string><string name="warning">警告</string><string name="info">信息</string><string name="feedback_feeling_tag">#感覺</string><string name="feedback_followup_tag">#後續</string><string name="feedback_difficulty_tag">#難度</string><string name="feedback_time_tag">#時間</string><string name="feeling_accomplished">充滿成就感</string><string name="feeling_struggling">有點吃力</string><string name="feeling_need_help">需要幫助</string><string name="feeling_very_accomplished">很有成就感</string><string name="feeling_smooth">完成得很順利</string><string name="followup_need_tracking">需要跟進</string><string name="followup_completed_archived">已完成歸檔</string><string name="followup_to_share">待分享</string><string name="followup_need_improvement">需要改進</string><string name="followup_worth_recording">值得記錄</string><string name="difficulty_easier_than_expected">比預期簡單</string><string name="difficulty_as_expected">符合預期</string><string name="difficulty_harder_than_expected">比預期困難</string><string name="difficulty_need_more_learning">需要學習更多</string><string name="time_completed_early">提前完成</string><string name="time_completed_on_time">按時完成</string><string name="time_slightly_delayed">稍有延遲</string><string name="time_significantly_delayed">大幅延遲</string><string name="set_ai_assistant_name">設置AI助手名稱</string><string name="ai_assistant_name">AI助手名稱</string><string name="ai_assistant_name_example">例如：小助手</string><string name="enter_ai_assistant_name">輸入AI助手名稱</string><string name="feedback_input_hint">輸入感想或使用 #標籤 獲取智能建議...</string><string name="monday">週一</string><string name="tuesday">週二</string><string name="wednesday">週三</string><string name="thursday">週四</string><string name="friday">週五</string><string name="saturday">週六</string><string name="sunday">週日</string><string name="month_day_format">%1$d月%2$d日</string><string name="week_month_day_format">%1$s · %2$s</string><string name="reflection">感想</string><string name="content_desc_reflection">感想</string><string name="content_desc_settings">設置</string><string name="encouragement_master">🌟 你已經成為真正的願望大師！繼續保持這份熱情，你的能量會感染更多人！</string><string name="encouragement_amazing">🚀 哇！你的願望實現能力令人驚嘆！還有幾步就能達到大師級別了！</string><string name="encouragement_great">💫 太棒了！你已經走在願望實現的快車道上！每一個成就都在為下一個奠定基礎！</string><string name="encouragement_good_start">🌱 很棒的開始！你的願望種子正在茁壯成長，保持這份堅持和熱情！</string><string name="encouragement_progress">✨ 每一步都是進步！你已經開始了願望實現的旅程，相信自己的力量！</string><string name="encouragement_ready">🎯 願望成就系統已為你準備好！開始你的第一個願望，解鎖專屬於你的成就徽章！</string><string name="notification_category_task_management">任務管理</string><string name="notification_category_task_management_desc">管理任務相關的提醒通知</string><string name="notification_item_task_reminders">任務提醒</string><string name="notification_item_task_reminders_desc">開啟任務到期前的提醒</string><string name="notification_item_deadline_reminders">截止日期提醒</string><string name="notification_item_deadline_reminders_desc">任務即將到期時通知</string><string name="notification_item_overdue_reminders">逾期任務提醒</string><string name="notification_item_overdue_reminders_desc">提醒已過期的重要任務</string><string name="ai_suggestion_demo_presentation">準備明天的演示文稿</string><string name="search_suggestion_work_result">工作成果</string><string name="search_suggestion_meditation_method">冥想方法</string><string name="search_suggestion_philosophy">哲學</string><string name="search_suggestion_running">跑步</string><string name="search_suggestion_work_progress">工作進展</string><string name="search_suggestion_yoga">瑜伽</string><string name="search_suggestion_efficiency">效率</string><string name="search_suggestion_understand">理解</string><string name="search_suggestion_study">學習</string><string name="ai_suggestion_monthly_content">基於您近一個月的數據分析，為您提供以下建議：\n\n1. 📈 效率趨勢：您的工作效率在週三和週四最高\n2. 🎯 目標達成：建議將大目標拆分為更小的里程碑\n3. 💪 習慣養成：您在堅持習慣方面表現優秀，可以嘗試增加新的挑戰\n\n%s，繼續加油！</string><string name="focus_mode_enter">進入專注模式</string><string name="search_suggestion_history">歷史</string><string name="search_suggestion_study_method">學習方法</string><string name="search_suggestion_meditation">冥想</string><string name="search_suggestion_meditation_experience">冥想體驗</string><string name="ai_suggestion_smart_reminder_title">智能提醒</string><string name="search_suggestion_work_pressure">工作壓力</string><string name="search_suggestion_reading">讀書</string><string name="search_suggestion_self_help">自助</string><string name="ai_suggestion_smart_reminder_content">根據您的使用模式，AI助手為您推薦了一些優化建議，點擊查看詳情。</string><string name="search_suggestion_work">工作</string><string name="search_suggestion_breathing">呼吸</string><string name="search_suggestion_novel">小說</string><string name="search_suggestion_work_reflection">工作反思</string><string name="search_suggestion_fitness">健身</string><string name="ai_suggestion_weekly_content">根據您本週的任務完成情況，AI助手建議：\n\n1. 🎯 專注時段優化：建議在上午9-11點安排重要任務\n2. ⏰ 任務時間預估：可以適當增加15%的緩衝時間\n3. 🔄 工作節奏調整：每工作50分鐘休息10分鐘效果更佳\n\n繼續保持良好的工作習慣！</string><string name="search_suggestion_task">任務</string><string name="search_suggestion_reading_notes">讀書筆記</string><string name="search_suggestion_study_notes">學習筆記</string><string name="search_suggestion_reading_plan">讀書計劃</string><string name="search_suggestion_clarity">清晰</string><string name="search_suggestion_course">課程</string><string name="search_suggestion_psychology">心理</string><string name="ai_suggestion_demo_afternoon_meeting">下午3:00 會議</string><string name="search_suggestion_exercise_effect">運動效果</string><string name="search_suggestion_project">項目</string><string name="search_suggestion_relax">放鬆</string><string name="search_suggestion_exercise">運動</string><string name="search_suggestion_exercise_reflection">運動反思</string><string name="search_suggestion_swimming">游泳</string><string name="search_suggestion_exam">考試</string><string name="search_suggestion_master">掌握</string><string name="search_suggestion_meditation_effect">冥想效果</string><string name="search_suggestion_meeting">會議</string><string name="search_suggestion_notes">筆記</string><string name="ai_suggestion_monthly_title">月度工作模式分析</string><string name="ai_suggestion_demo_morning_review">上午9:00 復盤昨日工作</string><string name="search_suggestion_exercise_plan">運動計劃</string><string name="search_suggestion_introspection">內觀</string><string name="ai_suggestion_weekly_title">本週效率提升建議</string><string name="task_management">任務管理</string><string name="search_suggestion_team">團隊</string><string name="search_suggestion_review">復習</string><string name="search_suggestion_study_progress">學習進度</string><string name="search_suggestion_focus">專注</string><string name="search_suggestion_morning_run">晨跑</string><string name="search_suggestion_basketball">籃球</string><string name="search_suggestion_pressure">壓力</string><string name="ai_suggestion_demo_conflict_message">與任務\'項目討論\'存在時間衝突</string><string name="time_melody">時間旋律</string><string name="search_suggestion_study_plan">學習計劃</string><string name="focus_mode_exit">退出專注模式</string><string name="search_suggestion_reading_thoughts">讀書感悟</string><string name="search_suggestion_science">科學</string><string name="search_suggestions">搜索建議</string><string name="you_might_be_looking_for">您可能想找</string><string name="ai_settings">AI設置</string><string name="feature_data_analysis">數據統計分析</string><string name="wish_cloud">願望星雲</string><string name="core_features">核心功能</string><string name="emotion_type_calm">平靜</string><string name="restore">恢復</string><string name="feature_personalized_theme">個性化主題</string><string name="reminder_time">提醒時間</string><string name="sync_settings_desc">管理數據同步和雲存儲</string><string name="feature_time_tracking">時間自動追蹤</string><string name="add_wish">添加願望</string><string name="feature_goal_tracking_desc">設定目標並跟蹤進度，持續自我提升</string><string name="feature_ai_split_desc">自動將複雜任務拆解為可執行的子任務</string><string name="feature_time_tracking_desc">智能記錄專注時間，深度分析工作習慣</string><string name="feature_emotion_record">情緒記錄</string><string name="feature_goal_tracking">目標追蹤</string><string name="backup">備份</string><string name="feature_kanban_management_desc">類似Trello的任務看板，直觀管理工作流</string><string name="emotion_type_angry">生氣</string><string name="import_export">導入導出</string><string name="default_reminder_time">默認提醒時間</string><string name="feature_ai_split">AI智能拆分</string><string name="my_space">我的空間</string><string name="feature_personalized_theme_desc">多種主題色彩，打造專屬的視覺體驗</string><string name="emotion_type_joy">開心</string><string name="emotion_type_anxious">焦慮</string><string name="view_wish_pool">查看願望池</string><string name="emotion_type_sad">傷心</string><string name="feature_data_analysis_desc">可視化效率報告，洞察時間使用規律</string><string name="feature_calendar_sync_desc">無縫集成系統日曆，統一管理時間安排</string><string name="feature_emotion_record_desc">記錄每日心情變化，關注心理健康</string><string name="reminder_time_desc">設置通用的提醒提前時間</string><string name="not_set">未設置</string><string name="feature_calendar_sync">日曆同步</string><string name="sync_settings">同步設置</string><string name="about_desc">應用版本和信息</string><string name="data_management_desc">備份、恢復、導入導出和存儲清理</string><string name="feature_kanban_management">看板管理</string><string name="ai_settings_desc">自定義AI服務提供商</string><string name="no_wishes_yet">還沒有願望，點擊添加你的第一個願望吧！</string><string name="data_management">數據管理</string></file><file name="backup_rules" path="D:\development\Codes\MyApplication\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="D:\development\Codes\MyApplication\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="file_paths" path="D:\development\Codes\MyApplication\app\src\main\res\xml\file_paths.xml" qualifiers="" type="xml"/><file name="focus_timer_widget_info" path="D:\development\Codes\MyApplication\app\src\main\res\xml\focus_timer_widget_info.xml" qualifiers="" type="xml"/><file name="goal_progress_widget_info" path="D:\development\Codes\MyApplication\app\src\main\res\xml\goal_progress_widget_info.xml" qualifiers="" type="xml"/><file name="quick_timer_widget_info" path="D:\development\Codes\MyApplication\app\src\main\res\xml\quick_timer_widget_info.xml" qualifiers="" type="xml"/><file name="time_insight_widget_info" path="D:\development\Codes\MyApplication\app\src\main\res\xml\time_insight_widget_info.xml" qualifiers="" type="xml"/><file name="today_tasks_widget_info" path="D:\development\Codes\MyApplication\app\src\main\res\xml\today_tasks_widget_info.xml" qualifiers="" type="xml"/><file name="weekly_stats_widget_info" path="D:\development\Codes\MyApplication\app\src\main\res\xml\weekly_stats_widget_info.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\development\Codes\MyApplication\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\development\Codes\MyApplication\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\development\Codes\MyApplication\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\development\Codes\MyApplication\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>