package com.timeflow.app.ui.screen.task;

import android.content.Context;
import com.timeflow.app.data.repository.TaskRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class TaskDetailViewModel_Factory implements Factory<TaskDetailViewModel> {
  private final Provider<Context> contextProvider;

  private final Provider<TaskRepository> taskRepositoryProvider;

  public TaskDetailViewModel_Factory(Provider<Context> contextProvider,
      Provider<TaskRepository> taskRepositoryProvider) {
    this.contextProvider = contextProvider;
    this.taskRepositoryProvider = taskRepositoryProvider;
  }

  @Override
  public TaskDetailViewModel get() {
    return newInstance(contextProvider.get(), taskRepositoryProvider.get());
  }

  public static TaskDetailViewModel_Factory create(Provider<Context> contextProvider,
      Provider<TaskRepository> taskRepositoryProvider) {
    return new TaskDetailViewModel_Factory(contextProvider, taskRepositoryProvider);
  }

  public static TaskDetailViewModel newInstance(Context context, TaskRepository taskRepository) {
    return new TaskDetailViewModel(context, taskRepository);
  }
}
