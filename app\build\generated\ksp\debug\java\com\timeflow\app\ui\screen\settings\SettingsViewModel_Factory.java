package com.timeflow.app.ui.screen.settings;

import android.content.Context;
import com.timeflow.app.service.PaymentManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SettingsViewModel_Factory implements Factory<SettingsViewModel> {
  private final Provider<Context> contextProvider;

  private final Provider<PaymentManager> paymentManagerProvider;

  public SettingsViewModel_Factory(Provider<Context> contextProvider,
      Provider<PaymentManager> paymentManagerProvider) {
    this.contextProvider = contextProvider;
    this.paymentManagerProvider = paymentManagerProvider;
  }

  @Override
  public SettingsViewModel get() {
    return newInstance(contextProvider.get(), paymentManagerProvider.get());
  }

  public static SettingsViewModel_Factory create(Provider<Context> contextProvider,
      Provider<PaymentManager> paymentManagerProvider) {
    return new SettingsViewModel_Factory(contextProvider, paymentManagerProvider);
  }

  public static SettingsViewModel newInstance(Context context, PaymentManager paymentManager) {
    return new SettingsViewModel(context, paymentManager);
  }
}
