package com.timeflow.app.ui.task;

import com.timeflow.app.data.repository.TaskRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class KanbanViewModel_Factory implements Factory<KanbanViewModel> {
  private final Provider<TaskRepository> taskRepositoryProvider;

  public KanbanViewModel_Factory(Provider<TaskRepository> taskRepositoryProvider) {
    this.taskRepositoryProvider = taskRepositoryProvider;
  }

  @Override
  public KanbanViewModel get() {
    return newInstance(taskRepositoryProvider.get());
  }

  public static KanbanViewModel_Factory create(Provider<TaskRepository> taskRepositoryProvider) {
    return new KanbanViewModel_Factory(taskRepositoryProvider);
  }

  public static KanbanViewModel newInstance(TaskRepository taskRepository) {
    return new KanbanViewModel(taskRepository);
  }
}
