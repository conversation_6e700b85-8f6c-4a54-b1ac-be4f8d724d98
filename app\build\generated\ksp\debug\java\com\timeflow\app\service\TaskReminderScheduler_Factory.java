package com.timeflow.app.service;

import android.content.Context;
import com.timeflow.app.data.repository.TaskRepository;
import com.timeflow.app.utils.TimeFlowNotificationManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class TaskReminderScheduler_Factory implements Factory<TaskReminderScheduler> {
  private final Provider<Context> contextProvider;

  private final Provider<TaskRepository> taskRepositoryProvider;

  private final Provider<TimeFlowNotificationManager> notificationManagerProvider;

  public TaskReminderScheduler_Factory(Provider<Context> contextProvider,
      Provider<TaskRepository> taskRepositoryProvider,
      Provider<TimeFlowNotificationManager> notificationManagerProvider) {
    this.contextProvider = contextProvider;
    this.taskRepositoryProvider = taskRepositoryProvider;
    this.notificationManagerProvider = notificationManagerProvider;
  }

  @Override
  public TaskReminderScheduler get() {
    return newInstance(contextProvider.get(), taskRepositoryProvider.get(), notificationManagerProvider.get());
  }

  public static TaskReminderScheduler_Factory create(Provider<Context> contextProvider,
      Provider<TaskRepository> taskRepositoryProvider,
      Provider<TimeFlowNotificationManager> notificationManagerProvider) {
    return new TaskReminderScheduler_Factory(contextProvider, taskRepositoryProvider, notificationManagerProvider);
  }

  public static TaskReminderScheduler newInstance(Context context, TaskRepository taskRepository,
      TimeFlowNotificationManager notificationManager) {
    return new TaskReminderScheduler(context, taskRepository, notificationManager);
  }
}
