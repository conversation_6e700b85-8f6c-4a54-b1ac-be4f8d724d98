package com.timeflow.app.ui.settings;

import android.content.Context;
import androidx.datastore.core.DataStore;
import androidx.datastore.preferences.core.Preferences;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata({
    "dagger.hilt.android.qualifiers.ApplicationContext",
    "com.timeflow.app.di.DataStoreModule.ThemeDataStore"
})
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ThemeSettingsViewModel_Factory implements Factory<ThemeSettingsViewModel> {
  private final Provider<Context> contextProvider;

  private final Provider<DataStore<Preferences>> themeDataStoreProvider;

  public ThemeSettingsViewModel_Factory(Provider<Context> contextProvider,
      Provider<DataStore<Preferences>> themeDataStoreProvider) {
    this.contextProvider = contextProvider;
    this.themeDataStoreProvider = themeDataStoreProvider;
  }

  @Override
  public ThemeSettingsViewModel get() {
    return newInstance(contextProvider.get(), themeDataStoreProvider.get());
  }

  public static ThemeSettingsViewModel_Factory create(Provider<Context> contextProvider,
      Provider<DataStore<Preferences>> themeDataStoreProvider) {
    return new ThemeSettingsViewModel_Factory(contextProvider, themeDataStoreProvider);
  }

  public static ThemeSettingsViewModel newInstance(Context context,
      DataStore<Preferences> themeDataStore) {
    return new ThemeSettingsViewModel(context, themeDataStore);
  }
}
