package com.timeflow.app.data.repository;

import com.timeflow.app.data.dao.KanbanColumnDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class KanbanColumnRepositoryImpl_Factory implements Factory<KanbanColumnRepositoryImpl> {
  private final Provider<KanbanColumnDao> kanbanColumnDaoProvider;

  public KanbanColumnRepositoryImpl_Factory(Provider<KanbanColumnDao> kanbanColumnDaoProvider) {
    this.kanbanColumnDaoProvider = kanbanColumnDaoProvider;
  }

  @Override
  public KanbanColumnRepositoryImpl get() {
    return newInstance(kanbanColumnDaoProvider.get());
  }

  public static KanbanColumnRepositoryImpl_Factory create(
      Provider<KanbanColumnDao> kanbanColumnDaoProvider) {
    return new KanbanColumnRepositoryImpl_Factory(kanbanColumnDaoProvider);
  }

  public static KanbanColumnRepositoryImpl newInstance(KanbanColumnDao kanbanColumnDao) {
    return new KanbanColumnRepositoryImpl(kanbanColumnDao);
  }
}
