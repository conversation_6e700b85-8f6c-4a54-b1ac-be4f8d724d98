package com.timeflow.app.util;

import com.timeflow.app.data.dao.TaskDao;
import com.timeflow.app.data.repository.TaskRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DataConsistencyFixer_Factory implements Factory<DataConsistencyFixer> {
  private final Provider<TaskDao> taskDaoProvider;

  private final Provider<TaskRepository> taskRepositoryProvider;

  public DataConsistencyFixer_Factory(Provider<TaskDao> taskDaoProvider,
      Provider<TaskRepository> taskRepositoryProvider) {
    this.taskDaoProvider = taskDaoProvider;
    this.taskRepositoryProvider = taskRepositoryProvider;
  }

  @Override
  public DataConsistencyFixer get() {
    return newInstance(taskDaoProvider.get(), taskRepositoryProvider.get());
  }

  public static DataConsistencyFixer_Factory create(Provider<TaskDao> taskDaoProvider,
      Provider<TaskRepository> taskRepositoryProvider) {
    return new DataConsistencyFixer_Factory(taskDaoProvider, taskRepositoryProvider);
  }

  public static DataConsistencyFixer newInstance(TaskDao taskDao, TaskRepository taskRepository) {
    return new DataConsistencyFixer(taskDao, taskRepository);
  }
}
