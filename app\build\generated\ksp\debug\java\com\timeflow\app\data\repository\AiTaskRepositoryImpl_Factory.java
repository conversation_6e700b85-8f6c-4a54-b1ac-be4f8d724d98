package com.timeflow.app.data.repository;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AiTaskRepositoryImpl_Factory implements Factory<AiTaskRepositoryImpl> {
  private final Provider<TaskRepository> taskRepositoryProvider;

  private final Provider<Context> contextProvider;

  public AiTaskRepositoryImpl_Factory(Provider<TaskRepository> taskRepositoryProvider,
      Provider<Context> contextProvider) {
    this.taskRepositoryProvider = taskRepositoryProvider;
    this.contextProvider = contextProvider;
  }

  @Override
  public AiTaskRepositoryImpl get() {
    return newInstance(taskRepositoryProvider.get(), contextProvider.get());
  }

  public static AiTaskRepositoryImpl_Factory create(Provider<TaskRepository> taskRepositoryProvider,
      Provider<Context> contextProvider) {
    return new AiTaskRepositoryImpl_Factory(taskRepositoryProvider, contextProvider);
  }

  public static AiTaskRepositoryImpl newInstance(TaskRepository taskRepository, Context context) {
    return new AiTaskRepositoryImpl(taskRepository, context);
  }
}
