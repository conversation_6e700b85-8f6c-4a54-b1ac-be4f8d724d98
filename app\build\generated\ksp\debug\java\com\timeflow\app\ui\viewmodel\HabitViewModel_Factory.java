package com.timeflow.app.ui.viewmodel;

import android.content.Context;
import com.timeflow.app.data.repository.HabitRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class HabitViewModel_Factory implements Factory<HabitViewModel> {
  private final Provider<HabitRepository> habitRepositoryProvider;

  private final Provider<Context> contextProvider;

  public HabitViewModel_Factory(Provider<HabitRepository> habitRepositoryProvider,
      Provider<Context> contextProvider) {
    this.habitRepositoryProvider = habitRepositoryProvider;
    this.contextProvider = contextProvider;
  }

  @Override
  public HabitViewModel get() {
    return newInstance(habitRepositoryProvider.get(), contextProvider.get());
  }

  public static HabitViewModel_Factory create(Provider<HabitRepository> habitRepositoryProvider,
      Provider<Context> contextProvider) {
    return new HabitViewModel_Factory(habitRepositoryProvider, contextProvider);
  }

  public static HabitViewModel newInstance(HabitRepository habitRepository, Context context) {
    return new HabitViewModel(habitRepository, context);
  }
}
