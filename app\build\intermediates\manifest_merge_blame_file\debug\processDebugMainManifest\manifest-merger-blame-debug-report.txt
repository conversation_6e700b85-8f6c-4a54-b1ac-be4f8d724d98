1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.timeflow.app"
4    android:versionCode="8"
5    android:versionName="0.5.3" >
6
7    <uses-sdk
8        android:minSdkVersion="26"
9        android:targetSdkVersion="34" />
10
11    <!-- Calendar permissions -->
12    <uses-permission android:name="android.permission.READ_CALENDAR" />
12-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:6:5-72
12-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:6:22-69
13    <uses-permission android:name="android.permission.WRITE_CALENDAR" />
13-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:7:5-73
13-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:7:22-70
14
15    <!-- Network permissions -->
16    <uses-permission android:name="android.permission.INTERNET" />
16-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:10:5-67
16-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:10:22-64
17    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
17-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:11:5-79
17-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:11:22-76
18
19    <!-- Notification permissions -->
20    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
20-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:14:5-77
20-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:14:22-74
21    <uses-permission android:name="android.permission.VIBRATE" />
21-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:15:5-66
21-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:15:22-63
22
23    <!-- Background work permissions -->
24    <uses-permission android:name="android.permission.WAKE_LOCK" />
24-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:18:5-68
24-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:18:22-65
25    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
25-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:19:5-81
25-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:19:22-78
26
27    <!-- 🔧 新增：前台服务权限 -->
28    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
28-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:22:5-77
28-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:22:22-74
29    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_SPECIAL_USE" />
29-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:23:5-89
29-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:23:22-86
30
31    <!-- Alarm permissions for medication reminders -->
32    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
32-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:26:5-79
32-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:26:22-76
33    <uses-permission android:name="android.permission.USE_EXACT_ALARM" />
33-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:27:5-74
33-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:27:22-71
34
35    <!-- Storage permissions -->
36    <uses-permission
36-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:30:5-31:51
37        android:name="android.permission.READ_EXTERNAL_STORAGE"
37-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:30:22-77
38        android:maxSdkVersion="32" />
38-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:31:22-48
39    <uses-permission
39-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:32:5-33:51
40        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
40-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:32:22-78
41        android:maxSdkVersion="29" />
41-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:33:22-48
42    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
42-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:34:5-76
42-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:34:22-73
43
44    <permission
44-->[androidx.core:core:1.12.0] D:\development\Android\gradle\caches\8.11.1\transforms\443a6cfc0de72abe8b7a8ef4c7b7a3e3\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
45        android:name="com.timeflow.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
45-->[androidx.core:core:1.12.0] D:\development\Android\gradle\caches\8.11.1\transforms\443a6cfc0de72abe8b7a8ef4c7b7a3e3\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
46        android:protectionLevel="signature" />
46-->[androidx.core:core:1.12.0] D:\development\Android\gradle\caches\8.11.1\transforms\443a6cfc0de72abe8b7a8ef4c7b7a3e3\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
47
48    <uses-permission android:name="com.timeflow.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
48-->[androidx.core:core:1.12.0] D:\development\Android\gradle\caches\8.11.1\transforms\443a6cfc0de72abe8b7a8ef4c7b7a3e3\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
48-->[androidx.core:core:1.12.0] D:\development\Android\gradle\caches\8.11.1\transforms\443a6cfc0de72abe8b7a8ef4c7b7a3e3\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
49
50    <application
50-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:36:5-290:19
51        android:name="com.timeflow.app.TimeFlowApplication"
51-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:37:9-44
52        android:allowBackup="true"
52-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:38:9-35
53        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
53-->[androidx.core:core:1.12.0] D:\development\Android\gradle\caches\8.11.1\transforms\443a6cfc0de72abe8b7a8ef4c7b7a3e3\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
54        android:dataExtractionRules="@xml/data_extraction_rules"
54-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:39:9-65
55        android:debuggable="true"
56        android:enableOnBackInvokedCallback="true"
56-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:46:9-51
57        android:extractNativeLibs="false"
58        android:fullBackupContent="@xml/backup_rules"
58-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:40:9-54
59        android:hardwareAccelerated="@bool/enable_hardware_acceleration"
59-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:47:9-73
60        android:icon="@mipmap/ic_launcher"
60-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:41:9-43
61        android:label="@string/app_name"
61-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:42:9-41
62        android:roundIcon="@mipmap/ic_launcher_round"
62-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:43:9-54
63        android:supportsRtl="true"
63-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:44:9-35
64        android:testOnly="true"
65        android:theme="@style/Theme.TimeFlow.OptimizedAnimation" >
65-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:45:9-65
66
67        <!-- 主活动 - 默认启用硬件加速，但会在运行时根据设备能力进行调整 -->
68        <activity
68-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:51:9-69:20
69            android:name="com.timeflow.app.ui.MainActivity"
69-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:52:13-44
70            android:exported="true"
70-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:53:13-36
71            android:hardwareAccelerated="true"
71-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:54:13-47
72            android:launchMode="singleTop"
72-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:55:13-43
73            android:theme="@style/Theme.TimeFlow.OptimizedAnimation"
73-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:56:13-69
74            android:windowSoftInputMode="adjustResize" >
74-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:57:13-55
75            <intent-filter>
75-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:58:13-61:29
76                <action android:name="android.intent.action.MAIN" />
76-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:59:17-69
76-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:59:25-66
77
78                <category android:name="android.intent.category.LAUNCHER" />
78-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:60:17-77
78-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:60:27-74
79            </intent-filter>
80            <!-- 添加深度链接支持任务页面直接访问 -->
81            <intent-filter>
81-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:63:13-68:29
82                <action android:name="android.intent.action.VIEW" />
82-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:64:17-69
82-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:64:25-66
83
84                <category android:name="android.intent.category.DEFAULT" />
84-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:65:17-76
84-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:65:27-73
85                <category android:name="android.intent.category.BROWSABLE" />
85-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:66:17-78
85-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:66:27-75
86
87                <data
87-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:67:17-72
88                    android:host="tasks"
88-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:67:49-69
89                    android:scheme="timeflow" />
89-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:67:23-48
90            </intent-filter>
91        </activity>
92
93        <!-- 习惯提醒广播接收器 -->
94        <receiver
94-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:72:9-74:40
95            android:name="com.timeflow.app.receiver.HabitAlarmReceiver"
95-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:73:13-56
96            android:exported="false" />
96-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:74:13-37
97
98        <!-- 任务提醒广播接收器 -->
99        <receiver
99-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:77:9-79:40
100            android:name="com.timeflow.app.receiver.TaskAlarmReceiver"
100-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:78:13-55
101            android:exported="false" />
101-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:79:13-37
102
103        <!-- 每日回顾闹钟接收器 -->
104        <receiver
104-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:82:9-84:40
105            android:name="com.timeflow.app.receiver.DailyReviewAlarmReceiver"
105-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:83:13-62
106            android:exported="false" />
106-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:84:13-37
107
108        <!-- 用药提醒广播接收器 -->
109        <receiver
109-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:87:9-89:40
110            android:name="com.timeflow.app.service.MedicationReminderReceiver"
110-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:88:13-63
111            android:exported="false" />
111-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:89:13-37
112
113        <!-- 用药动作广播接收器 -->
114        <receiver
114-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:92:9-99:20
115            android:name="com.timeflow.app.service.MedicationActionReceiver"
115-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:93:13-61
116            android:exported="false" >
116-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:94:13-37
117            <intent-filter>
117-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:95:13-98:29
118                <action android:name="MEDICATION_TAKEN" />
118-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:96:17-59
118-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:96:25-56
119                <action android:name="MEDICATION_SNOOZE" />
119-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:97:17-60
119-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:97:25-57
120            </intent-filter>
121        </receiver>
122
123        <!-- 在应用启动时接收启动完成广播，重新设置闹钟 -->
124        <receiver
124-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:102:9-108:20
125            android:name="com.timeflow.app.receiver.BootCompletedReceiver"
125-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:103:13-59
126            android:exported="false" >
126-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:104:13-37
127            <intent-filter>
127-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:105:13-107:29
128                <action android:name="android.intent.action.BOOT_COMPLETED" />
128-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:106:17-79
128-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:106:25-76
129            </intent-filter>
130        </receiver>
131
132        <!-- 通知操作接收器 -->
133        <receiver
133-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:111:9-120:20
134            android:name="com.timeflow.app.utils.NotificationActionReceiver"
134-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:112:13-61
135            android:exported="false" >
135-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:113:13-37
136            <intent-filter>
136-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:114:13-119:29
137                <action android:name="COMPLETE_TASK" />
137-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:115:17-56
137-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:115:25-53
138                <action android:name="POSTPONE_TASK" />
138-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:116:17-56
138-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:116:25-53
139                <action android:name="COMPLETE_HABIT" />
139-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:117:17-57
139-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:117:25-54
140                <action android:name="REMIND_HABIT_LATER" />
140-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:118:17-61
140-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:118:25-58
141            </intent-filter>
142        </receiver>
143
144        <!-- 🔧 新增：专注计时操作接收器 -->
145        <receiver
145-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:123:9-133:20
146            android:name="com.timeflow.app.receiver.FocusTimerActionReceiver"
146-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:124:13-62
147            android:exported="false" >
147-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:125:13-37
148            <intent-filter>
148-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:126:13-132:29
149                <action android:name="START_TIMER" />
149-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:127:17-54
149-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:127:25-51
150                <action android:name="PAUSE_TIMER" />
150-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:128:17-54
150-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:128:25-51
151                <action android:name="RESUME_TIMER" />
151-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:129:17-55
151-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:129:25-52
152                <action android:name="STOP_TIMER" />
152-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:130:17-53
152-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:130:25-50
153                <action android:name="SHOW_APP" />
153-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:131:17-51
153-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:131:25-48
154            </intent-filter>
155        </receiver>
156
157        <!-- 🔧 新增：任务常驻通知操作接收器 -->
158        <receiver
158-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:136:9-146:20
159            android:name="com.timeflow.app.receiver.TaskPersistentNotificationActionReceiver"
159-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:137:13-78
160            android:exported="false" >
160-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:138:13-37
161            <intent-filter>
161-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:139:13-145:29
162                <action android:name="START_PERSISTENT" />
162-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:140:17-59
162-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:140:25-56
163                <action android:name="STOP_PERSISTENT" />
163-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:141:17-58
163-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:141:25-55
164                <action android:name="COMPLETE_TASK" />
164-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:115:17-56
164-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:115:25-53
165                <action android:name="SHOW_TASKS" />
165-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:143:17-53
165-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:143:25-50
166                <action android:name="REFRESH_TASKS" />
166-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:144:17-56
166-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:144:25-53
167            </intent-filter>
168        </receiver>
169
170        <!-- 通知测试服务 -->
171        <service
171-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:149:9-151:40
172            android:name="com.timeflow.app.service.NotificationTestService"
172-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:150:13-60
173            android:exported="false" />
173-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:151:13-37
174
175        <!-- 🔧 新增：专注计时前台服务 -->
176        <service
176-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:154:9-157:58
177            android:name="com.timeflow.app.service.FocusTimerService"
177-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:155:13-54
178            android:exported="false"
178-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:156:13-37
179            android:foregroundServiceType="specialUse" />
179-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:157:13-55
180
181        <!-- 🔧 新增：任务常驻通知前台服务 -->
182        <service
182-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:160:9-163:58
183            android:name="com.timeflow.app.service.TaskPersistentNotificationService"
183-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:161:13-70
184            android:exported="false"
184-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:162:13-37
185            android:foregroundServiceType="specialUse" />
185-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:163:13-55
186
187        <!-- WorkManager initialization -->
188        <provider
189            android:name="androidx.startup.InitializationProvider"
189-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:167:13-67
190            android:authorities="com.timeflow.app.androidx-startup"
190-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:168:13-68
191            android:exported="false" >
191-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:169:13-37
192            <meta-data
192-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:175:13-177:52
193                android:name="com.timeflow.app.di.WorkManagerInitializer"
193-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:176:17-74
194                android:value="androidx.startup" />
194-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:177:17-49
195            <meta-data
195-->[androidx.emoji2:emoji2:1.3.0] D:\development\Android\gradle\caches\8.11.1\transforms\f7bae21354e558eb0376dedde1961243\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
196                android:name="androidx.emoji2.text.EmojiCompatInitializer"
196-->[androidx.emoji2:emoji2:1.3.0] D:\development\Android\gradle\caches\8.11.1\transforms\f7bae21354e558eb0376dedde1961243\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
197                android:value="androidx.startup" />
197-->[androidx.emoji2:emoji2:1.3.0] D:\development\Android\gradle\caches\8.11.1\transforms\f7bae21354e558eb0376dedde1961243\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
198            <meta-data
198-->[androidx.lifecycle:lifecycle-process:2.7.0] D:\development\Android\gradle\caches\8.11.1\transforms\60fc766cbb27c715f12e8233d3d92850\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
199                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
199-->[androidx.lifecycle:lifecycle-process:2.7.0] D:\development\Android\gradle\caches\8.11.1\transforms\60fc766cbb27c715f12e8233d3d92850\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
200                android:value="androidx.startup" />
200-->[androidx.lifecycle:lifecycle-process:2.7.0] D:\development\Android\gradle\caches\8.11.1\transforms\60fc766cbb27c715f12e8233d3d92850\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
201            <meta-data
201-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\development\Android\gradle\caches\8.11.1\transforms\d955dd94fbac997219b01017148e4551\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
202                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
202-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\development\Android\gradle\caches\8.11.1\transforms\d955dd94fbac997219b01017148e4551\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
203                android:value="androidx.startup" />
203-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\development\Android\gradle\caches\8.11.1\transforms\d955dd94fbac997219b01017148e4551\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
204        </provider>
205
206        <!-- Notification channels for OPPO devices -->
207        <meta-data
207-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:181:9-183:35
208            android:name="android.max_aspect"
208-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:182:13-46
209            android:value="2.4" />
209-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:183:13-32
210
211        <!-- Automatic backup settings -->
212        <meta-data
212-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:186:9-188:82
213            android:name="com.google.android.backup.api_key"
213-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:187:13-61
214            android:value="AEdPqrEAAAAIqYVTTbHXb52YeDRQIqNiSUnJtpQEJCOLy7Qo0A" />
214-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:188:13-79
215
216        <!-- 添加 FileProvider -->
217        <provider
218            android:name="androidx.core.content.FileProvider"
218-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:192:13-62
219            android:authorities="com.timeflow.app.fileprovider"
219-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:193:13-64
220            android:exported="false"
220-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:194:13-37
221            android:grantUriPermissions="true" >
221-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:195:13-47
222            <meta-data
222-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:196:13-198:54
223                android:name="android.support.FILE_PROVIDER_PATHS"
223-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:197:17-67
224                android:resource="@xml/file_paths" />
224-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:198:17-51
225        </provider>
226
227        <!-- 小组件 -->
228        <!-- 时间洞察小组件 -->
229        <receiver
229-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:203:9-212:20
230            android:name="com.timeflow.app.widget.TimeInsightWidget"
230-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:204:13-53
231            android:exported="true" >
231-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:205:13-36
232            <intent-filter>
232-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:206:13-208:29
233                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
233-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:207:17-84
233-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:207:25-81
234            </intent-filter>
235
236            <meta-data
236-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:209:13-211:68
237                android:name="android.appwidget.provider"
237-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:210:17-58
238                android:resource="@xml/time_insight_widget_info" />
238-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:211:17-65
239        </receiver>
240
241        <!-- 今日待办小组件 -->
242        <receiver
242-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:215:9-224:20
243            android:name="com.timeflow.app.widget.TodayTasksWidget"
243-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:216:13-52
244            android:exported="true" >
244-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:217:13-36
245            <intent-filter>
245-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:206:13-208:29
246                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
246-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:207:17-84
246-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:207:25-81
247            </intent-filter>
248
249            <meta-data
249-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:209:13-211:68
250                android:name="android.appwidget.provider"
250-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:210:17-58
251                android:resource="@xml/today_tasks_widget_info" />
251-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:211:17-65
252        </receiver>
253
254        <!-- 快速计时小组件 -->
255        <receiver
255-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:227:9-238:20
256            android:name="com.timeflow.app.widget.QuickTimerWidget"
256-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:228:13-52
257            android:exported="true" >
257-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:229:13-36
258            <intent-filter>
258-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:230:13-234:29
259                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
259-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:207:17-84
259-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:207:25-81
260                <action android:name="com.timeflow.app.widget.START_TIMER" />
260-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:232:17-78
260-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:232:25-75
261                <action android:name="com.timeflow.app.widget.STOP_TIMER" />
261-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:233:17-77
261-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:233:25-74
262            </intent-filter>
263
264            <meta-data
264-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:209:13-211:68
265                android:name="android.appwidget.provider"
265-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:210:17-58
266                android:resource="@xml/quick_timer_widget_info" />
266-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:211:17-65
267        </receiver>
268
269        <!-- 专注计时器小组件 -->
270        <receiver
270-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:241:9-253:20
271            android:name="com.timeflow.app.widget.FocusTimerWidget"
271-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:242:13-52
272            android:exported="true" >
272-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:243:13-36
273            <intent-filter>
273-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:244:13-249:29
274                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
274-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:207:17-84
274-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:207:25-81
275                <action android:name="com.timeflow.app.widget.PLAY_PAUSE" />
275-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:246:17-77
275-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:246:25-74
276                <action android:name="com.timeflow.app.widget.STOP" />
276-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:247:17-71
276-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:247:25-68
277                <action android:name="com.timeflow.app.widget.REFRESH" />
277-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:248:17-74
277-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:248:25-71
278            </intent-filter>
279
280            <meta-data
280-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:209:13-211:68
281                android:name="android.appwidget.provider"
281-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:210:17-58
282                android:resource="@xml/focus_timer_widget_info" />
282-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:211:17-65
283        </receiver>
284
285        <!-- 周统计小组件 -->
286        <receiver
286-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:256:9-265:20
287            android:name="com.timeflow.app.widget.WeeklyStatsWidget"
287-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:257:13-53
288            android:exported="true" >
288-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:258:13-36
289            <intent-filter>
289-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:206:13-208:29
290                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
290-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:207:17-84
290-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:207:25-81
291            </intent-filter>
292
293            <meta-data
293-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:209:13-211:68
294                android:name="android.appwidget.provider"
294-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:210:17-58
295                android:resource="@xml/weekly_stats_widget_info" />
295-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:211:17-65
296        </receiver>
297
298        <!-- 目标进度小组件 -->
299        <receiver
299-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:268:9-277:20
300            android:name="com.timeflow.app.widget.GoalProgressWidget"
300-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:269:13-54
301            android:exported="true" >
301-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:270:13-36
302            <intent-filter>
302-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:206:13-208:29
303                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
303-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:207:17-84
303-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:207:25-81
304            </intent-filter>
305
306            <meta-data
306-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:209:13-211:68
307                android:name="android.appwidget.provider"
307-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:210:17-58
308                android:resource="@xml/goal_progress_widget_info" />
308-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:211:17-65
309        </receiver>
310
311        <!-- 🔧 循环任务初始化器 -->
312        <provider
313            android:name="androidx.startup.InitializationProvider"
313-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:167:13-67
314            android:authorities="com.timeflow.app.androidx-startup"
314-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:168:13-68
315            android:exported="false" >
315-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:169:13-37
316            <meta-data
316-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:285:13-287:52
317                android:name="com.timeflow.app.initializer.RecurringTaskInitializer"
317-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:286:17-85
318                android:value="androidx.startup" />
318-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:287:17-49
319        </provider>
320
321        <service
321-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:39:9-45:35
322            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
322-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:40:13-88
323            android:directBootAware="false"
323-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:41:13-44
324            android:enabled="@bool/enable_system_alarm_service_default"
324-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:42:13-72
325            android:exported="false" />
325-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:43:13-37
326        <service
326-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:46:9-52:35
327            android:name="androidx.work.impl.background.systemjob.SystemJobService"
327-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:47:13-84
328            android:directBootAware="false"
328-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:48:13-44
329            android:enabled="@bool/enable_system_job_service_default"
329-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:49:13-70
330            android:exported="true"
330-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:50:13-36
331            android:permission="android.permission.BIND_JOB_SERVICE" />
331-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:51:13-69
332        <service
332-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:53:9-59:35
333            android:name="androidx.work.impl.foreground.SystemForegroundService"
333-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:54:13-81
334            android:directBootAware="false"
334-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:55:13-44
335            android:enabled="@bool/enable_system_foreground_service_default"
335-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:56:13-77
336            android:exported="false" />
336-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:57:13-37
337
338        <receiver
338-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:61:9-66:35
339            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
339-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:62:13-88
340            android:directBootAware="false"
340-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:63:13-44
341            android:enabled="true"
341-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:64:13-35
342            android:exported="false" />
342-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:65:13-37
343        <receiver
343-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:67:9-77:20
344            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
344-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:68:13-106
345            android:directBootAware="false"
345-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:69:13-44
346            android:enabled="false"
346-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:70:13-36
347            android:exported="false" >
347-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:71:13-37
348            <intent-filter>
348-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:73:13-76:29
349                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
349-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:17-87
349-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:25-84
350                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
350-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:17-90
350-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:25-87
351            </intent-filter>
352        </receiver>
353        <receiver
353-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:78:9-88:20
354            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
354-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:79:13-104
355            android:directBootAware="false"
355-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:80:13-44
356            android:enabled="false"
356-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:81:13-36
357            android:exported="false" >
357-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:82:13-37
358            <intent-filter>
358-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:84:13-87:29
359                <action android:name="android.intent.action.BATTERY_OKAY" />
359-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:17-77
359-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:25-74
360                <action android:name="android.intent.action.BATTERY_LOW" />
360-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:17-76
360-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:25-73
361            </intent-filter>
362        </receiver>
363        <receiver
363-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:89:9-99:20
364            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
364-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:90:13-104
365            android:directBootAware="false"
365-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:91:13-44
366            android:enabled="false"
366-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:92:13-36
367            android:exported="false" >
367-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:93:13-37
368            <intent-filter>
368-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:95:13-98:29
369                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
369-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:17-83
369-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:25-80
370                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
370-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:17-82
370-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:25-79
371            </intent-filter>
372        </receiver>
373        <receiver
373-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:100:9-109:20
374            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
374-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:101:13-103
375            android:directBootAware="false"
375-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:102:13-44
376            android:enabled="false"
376-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:103:13-36
377            android:exported="false" >
377-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:104:13-37
378            <intent-filter>
378-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:106:13-108:29
379                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
379-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:17-79
379-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:25-76
380            </intent-filter>
381        </receiver>
382        <receiver
382-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:110:9-121:20
383            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
383-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:111:13-88
384            android:directBootAware="false"
384-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:112:13-44
385            android:enabled="false"
385-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:113:13-36
386            android:exported="false" >
386-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:114:13-37
387            <intent-filter>
387-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:116:13-120:29
388                <action android:name="android.intent.action.BOOT_COMPLETED" />
388-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:106:17-79
388-->D:\development\Codes\MyApplication\app\src\main\AndroidManifest.xml:106:25-76
389                <action android:name="android.intent.action.TIME_SET" />
389-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:17-73
389-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:25-70
390                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
390-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:17-81
390-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:25-78
391            </intent-filter>
392        </receiver>
393        <receiver
393-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:122:9-131:20
394            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
394-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:123:13-99
395            android:directBootAware="false"
395-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:124:13-44
396            android:enabled="@bool/enable_system_alarm_service_default"
396-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:125:13-72
397            android:exported="false" >
397-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:126:13-37
398            <intent-filter>
398-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:128:13-130:29
399                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
399-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:17-98
399-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:25-95
400            </intent-filter>
401        </receiver>
402        <receiver
402-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:132:9-142:20
403            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
403-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:133:13-78
404            android:directBootAware="false"
404-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:134:13-44
405            android:enabled="true"
405-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:135:13-35
406            android:exported="true"
406-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:136:13-36
407            android:permission="android.permission.DUMP" >
407-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:137:13-57
408            <intent-filter>
408-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:139:13-141:29
409                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
409-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:17-88
409-->[androidx.work:work-runtime:2.9.0] D:\development\Android\gradle\caches\8.11.1\transforms\75ac900f6bf42e90e2b331a4ef619a90\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:25-85
410            </intent-filter>
411        </receiver>
412
413        <activity
413-->[androidx.compose.ui:ui-tooling-android:1.6.2] D:\development\Android\gradle\caches\8.11.1\transforms\e0323427d6271e66aa0784cfd040b6fb\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
414            android:name="androidx.compose.ui.tooling.PreviewActivity"
414-->[androidx.compose.ui:ui-tooling-android:1.6.2] D:\development\Android\gradle\caches\8.11.1\transforms\e0323427d6271e66aa0784cfd040b6fb\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
415            android:exported="true" />
415-->[androidx.compose.ui:ui-tooling-android:1.6.2] D:\development\Android\gradle\caches\8.11.1\transforms\e0323427d6271e66aa0784cfd040b6fb\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
416        <activity
416-->[androidx.compose.ui:ui-test-manifest:1.6.2] D:\development\Android\gradle\caches\8.11.1\transforms\14c1da9b40958075b50036a34014f52c\transformed\ui-test-manifest-1.6.2\AndroidManifest.xml:23:9-25:39
417            android:name="androidx.activity.ComponentActivity"
417-->[androidx.compose.ui:ui-test-manifest:1.6.2] D:\development\Android\gradle\caches\8.11.1\transforms\14c1da9b40958075b50036a34014f52c\transformed\ui-test-manifest-1.6.2\AndroidManifest.xml:24:13-63
418            android:exported="true" />
418-->[androidx.compose.ui:ui-test-manifest:1.6.2] D:\development\Android\gradle\caches\8.11.1\transforms\14c1da9b40958075b50036a34014f52c\transformed\ui-test-manifest-1.6.2\AndroidManifest.xml:25:13-36
419
420        <uses-library
420-->[androidx.window:window:1.0.0] D:\development\Android\gradle\caches\8.11.1\transforms\ac812fd31ead2ff3fba2b67a79fdb627\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
421            android:name="androidx.window.extensions"
421-->[androidx.window:window:1.0.0] D:\development\Android\gradle\caches\8.11.1\transforms\ac812fd31ead2ff3fba2b67a79fdb627\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
422            android:required="false" />
422-->[androidx.window:window:1.0.0] D:\development\Android\gradle\caches\8.11.1\transforms\ac812fd31ead2ff3fba2b67a79fdb627\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
423        <uses-library
423-->[androidx.window:window:1.0.0] D:\development\Android\gradle\caches\8.11.1\transforms\ac812fd31ead2ff3fba2b67a79fdb627\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
424            android:name="androidx.window.sidecar"
424-->[androidx.window:window:1.0.0] D:\development\Android\gradle\caches\8.11.1\transforms\ac812fd31ead2ff3fba2b67a79fdb627\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
425            android:required="false" />
425-->[androidx.window:window:1.0.0] D:\development\Android\gradle\caches\8.11.1\transforms\ac812fd31ead2ff3fba2b67a79fdb627\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
426
427        <service
427-->[androidx.room:room-runtime:2.6.1] D:\development\Android\gradle\caches\8.11.1\transforms\83fec271716eb865a80add3efead4ed6\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
428            android:name="androidx.room.MultiInstanceInvalidationService"
428-->[androidx.room:room-runtime:2.6.1] D:\development\Android\gradle\caches\8.11.1\transforms\83fec271716eb865a80add3efead4ed6\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
429            android:directBootAware="true"
429-->[androidx.room:room-runtime:2.6.1] D:\development\Android\gradle\caches\8.11.1\transforms\83fec271716eb865a80add3efead4ed6\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
430            android:exported="false" />
430-->[androidx.room:room-runtime:2.6.1] D:\development\Android\gradle\caches\8.11.1\transforms\83fec271716eb865a80add3efead4ed6\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
431
432        <receiver
432-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\development\Android\gradle\caches\8.11.1\transforms\d955dd94fbac997219b01017148e4551\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
433            android:name="androidx.profileinstaller.ProfileInstallReceiver"
433-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\development\Android\gradle\caches\8.11.1\transforms\d955dd94fbac997219b01017148e4551\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
434            android:directBootAware="false"
434-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\development\Android\gradle\caches\8.11.1\transforms\d955dd94fbac997219b01017148e4551\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
435            android:enabled="true"
435-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\development\Android\gradle\caches\8.11.1\transforms\d955dd94fbac997219b01017148e4551\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
436            android:exported="true"
436-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\development\Android\gradle\caches\8.11.1\transforms\d955dd94fbac997219b01017148e4551\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
437            android:permission="android.permission.DUMP" >
437-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\development\Android\gradle\caches\8.11.1\transforms\d955dd94fbac997219b01017148e4551\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
438            <intent-filter>
438-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\development\Android\gradle\caches\8.11.1\transforms\d955dd94fbac997219b01017148e4551\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
439                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
439-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\development\Android\gradle\caches\8.11.1\transforms\d955dd94fbac997219b01017148e4551\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
439-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\development\Android\gradle\caches\8.11.1\transforms\d955dd94fbac997219b01017148e4551\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
440            </intent-filter>
441            <intent-filter>
441-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\development\Android\gradle\caches\8.11.1\transforms\d955dd94fbac997219b01017148e4551\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
442                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
442-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\development\Android\gradle\caches\8.11.1\transforms\d955dd94fbac997219b01017148e4551\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
442-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\development\Android\gradle\caches\8.11.1\transforms\d955dd94fbac997219b01017148e4551\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
443            </intent-filter>
444            <intent-filter>
444-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\development\Android\gradle\caches\8.11.1\transforms\d955dd94fbac997219b01017148e4551\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
445                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
445-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\development\Android\gradle\caches\8.11.1\transforms\d955dd94fbac997219b01017148e4551\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
445-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\development\Android\gradle\caches\8.11.1\transforms\d955dd94fbac997219b01017148e4551\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
446            </intent-filter>
447            <intent-filter>
447-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\development\Android\gradle\caches\8.11.1\transforms\d955dd94fbac997219b01017148e4551\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
448                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
448-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\development\Android\gradle\caches\8.11.1\transforms\d955dd94fbac997219b01017148e4551\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
448-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\development\Android\gradle\caches\8.11.1\transforms\d955dd94fbac997219b01017148e4551\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
449            </intent-filter>
450        </receiver>
451
452        <uses-library
452-->[com.amazonaws:aws-android-sdk-core:2.76.0] D:\development\Android\gradle\caches\8.11.1\transforms\7d417b4ddf552c8a183d7e185e1abe83\transformed\aws-android-sdk-core-2.76.0\AndroidManifest.xml:11:9-13:40
453            android:name="org.apache.http.legacy"
453-->[com.amazonaws:aws-android-sdk-core:2.76.0] D:\development\Android\gradle\caches\8.11.1\transforms\7d417b4ddf552c8a183d7e185e1abe83\transformed\aws-android-sdk-core-2.76.0\AndroidManifest.xml:12:13-50
454            android:required="false" />
454-->[com.amazonaws:aws-android-sdk-core:2.76.0] D:\development\Android\gradle\caches\8.11.1\transforms\7d417b4ddf552c8a183d7e185e1abe83\transformed\aws-android-sdk-core-2.76.0\AndroidManifest.xml:13:13-37
455    </application>
456
457</manifest>
