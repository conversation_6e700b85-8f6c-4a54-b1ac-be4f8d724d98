package com.timeflow.app.ui.screen.profile;

import com.timeflow.app.ui.screen.reflection.ReflectionRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class EmotionStatisticsViewModel_Factory implements Factory<EmotionStatisticsViewModel> {
  private final Provider<ReflectionRepository> reflectionRepositoryProvider;

  public EmotionStatisticsViewModel_Factory(
      Provider<ReflectionRepository> reflectionRepositoryProvider) {
    this.reflectionRepositoryProvider = reflectionRepositoryProvider;
  }

  @Override
  public EmotionStatisticsViewModel get() {
    return newInstance(reflectionRepositoryProvider.get());
  }

  public static EmotionStatisticsViewModel_Factory create(
      Provider<ReflectionRepository> reflectionRepositoryProvider) {
    return new EmotionStatisticsViewModel_Factory(reflectionRepositoryProvider);
  }

  public static EmotionStatisticsViewModel newInstance(ReflectionRepository reflectionRepository) {
    return new EmotionStatisticsViewModel(reflectionRepository);
  }
}
