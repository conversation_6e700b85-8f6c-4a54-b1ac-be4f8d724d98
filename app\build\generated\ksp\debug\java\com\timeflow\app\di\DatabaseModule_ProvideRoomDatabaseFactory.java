package com.timeflow.app.di;

import androidx.room.RoomDatabase;
import com.timeflow.app.data.db.AppDatabase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DatabaseModule_ProvideRoomDatabaseFactory implements Factory<RoomDatabase> {
  private final Provider<AppDatabase> appDatabaseProvider;

  public DatabaseModule_ProvideRoomDatabaseFactory(Provider<AppDatabase> appDatabaseProvider) {
    this.appDatabaseProvider = appDatabaseProvider;
  }

  @Override
  public RoomDatabase get() {
    return provideRoomDatabase(appDatabaseProvider.get());
  }

  public static DatabaseModule_ProvideRoomDatabaseFactory create(
      Provider<AppDatabase> appDatabaseProvider) {
    return new DatabaseModule_ProvideRoomDatabaseFactory(appDatabaseProvider);
  }

  public static RoomDatabase provideRoomDatabase(AppDatabase appDatabase) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.provideRoomDatabase(appDatabase));
  }
}
