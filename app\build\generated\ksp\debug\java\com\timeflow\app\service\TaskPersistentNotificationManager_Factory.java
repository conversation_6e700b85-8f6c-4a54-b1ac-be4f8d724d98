package com.timeflow.app.service;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class TaskPersistentNotificationManager_Factory implements Factory<TaskPersistentNotificationManager> {
  private final Provider<Context> contextProvider;

  public TaskPersistentNotificationManager_Factory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public TaskPersistentNotificationManager get() {
    return newInstance(contextProvider.get());
  }

  public static TaskPersistentNotificationManager_Factory create(
      Provider<Context> contextProvider) {
    return new TaskPersistentNotificationManager_Factory(contextProvider);
  }

  public static TaskPersistentNotificationManager newInstance(Context context) {
    return new TaskPersistentNotificationManager(context);
  }
}
