package com.timeflow.app.ui.viewmodel;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class TimeFlowViewModel_Factory implements Factory<TimeFlowViewModel> {
  @Override
  public TimeFlowViewModel get() {
    return newInstance();
  }

  public static TimeFlowViewModel_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static TimeFlowViewModel newInstance() {
    return new TimeFlowViewModel();
  }

  private static final class InstanceHolder {
    private static final TimeFlowViewModel_Factory INSTANCE = new TimeFlowViewModel_Factory();
  }
}
