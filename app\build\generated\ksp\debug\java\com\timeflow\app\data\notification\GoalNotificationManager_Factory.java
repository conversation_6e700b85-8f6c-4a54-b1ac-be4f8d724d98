package com.timeflow.app.data.notification;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class GoalNotificationManager_Factory implements Factory<GoalNotificationManager> {
  private final Provider<Context> contextProvider;

  public GoalNotificationManager_Factory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public GoalNotificationManager get() {
    return newInstance(contextProvider.get());
  }

  public static GoalNotificationManager_Factory create(Provider<Context> contextProvider) {
    return new GoalNotificationManager_Factory(contextProvider);
  }

  public static GoalNotificationManager newInstance(Context context) {
    return new GoalNotificationManager(context);
  }
}
