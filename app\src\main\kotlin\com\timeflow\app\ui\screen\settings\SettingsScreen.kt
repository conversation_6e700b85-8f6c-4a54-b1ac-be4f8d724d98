package com.timeflow.app.ui.screen.settings

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.tween
import androidx.compose.animation.slideInHorizontally
import androidx.compose.animation.slideOutHorizontally
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material.icons.outlined.*
// 移除不存在的图标导入
import androidx.compose.material.icons.outlined.RestoreFromTrash
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavController
import com.timeflow.app.R
import com.timeflow.app.navigation.AppDestinations
import com.timeflow.app.ui.theme.*
import androidx.hilt.navigation.compose.hiltViewModel
import com.timeflow.app.ui.screen.calendar.CalendarViewModel
import android.app.Activity
import com.timeflow.app.utils.SystemBarManager
import android.util.Log
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import android.Manifest
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.provider.MediaStore
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.Image
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.lifecycle.lifecycleScope
import coil.compose.rememberAsyncImagePainter
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.ui.hapticfeedback.HapticFeedbackType
import androidx.compose.ui.platform.LocalHapticFeedback
import androidx.compose.foundation.combinedClickable
import coil.compose.AsyncImagePainter
import com.timeflow.app.utils.TimeFlowNotificationManager
import com.timeflow.app.ui.screen.settings.NotificationSettings
import com.timeflow.app.utils.SafeAsyncImage
import com.timeflow.app.utils.rememberSafeAsyncImagePainter
import android.os.Build
import com.timeflow.app.ui.task.components.common.event.EventBus
import com.timeflow.app.ui.task.components.common.event.AppEvent
import androidx.compose.ui.graphics.toArgb

/**
 * 设置页面
 * 全屏显示的设置界面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SettingsScreen(
    navController: NavController,
    onClose: () -> Unit = {},
    calendarViewModel: CalendarViewModel = hiltViewModel(),
    settingsViewModel: SettingsViewModel = hiltViewModel()
) {
    // 在SettingsScreen中直接声明依赖
    val context = LocalContext.current
    

    // 读取ViewModel中的状态
    val cardBackgroundUri by settingsViewModel.cardBackgroundUri.collectAsState()
    val avatarUri by settingsViewModel.avatarUri.collectAsState() // 读取头像URI状态
    val userInfo by settingsViewModel.userInfo.collectAsState() // 读取用户信息状态
    val isProcessingImage by settingsViewModel.isProcessingImage.collectAsState() // 读取图片处理状态
    val processingMessage by settingsViewModel.processingMessage.collectAsState() // 读取处理消息
    
    // 🔧 修复背景色管理 - 直接使用ThemeManager的主题设置
    var settingsBackgroundColor by remember { mutableStateOf(Color(0xFFF9F9F9)) } // 默认背景色
    
    // 🔧 在组件初始化时立即从ThemeManager获取当前主题设置
    LaunchedEffect(Unit) {
        // 从ThemeManager获取当前主题偏好
        val themePreference = com.timeflow.app.ui.theme.ThemeManager.userThemePreference.value
        val initialBackgroundColor = Color(themePreference.settingsPageColor.toArgb())
        settingsBackgroundColor = initialBackgroundColor
        
        Log.d("SettingsScreen", "🎨 初始化设置页背景色: ${initialBackgroundColor.toArgb().toString(16)}")
    }
    
    // 🔧 监听主题设置变化
    LaunchedEffect(Unit) {
        EventBus.events.collect { event ->
            when (event) {
                is AppEvent.PageBackgroundChanged -> {
                    if (event.pageName == "settings") {
                        try {
                            val colorInt = event.colorArgb.toInt()
                            val newColor = Color(colorInt)
                            
                            Log.d("SettingsScreen", "🔄 收到设置页背景色变更事件: ${event.colorArgb.toString(16)} -> ${newColor.toArgb().toString(16)}")
                            
                            settingsBackgroundColor = newColor
                            
                            Log.d("SettingsScreen", "✅ 设置页背景色已更新")
                        } catch (e: Exception) {
                            Log.e("SettingsScreen", "❌ 背景色转换失败: ${e.message}", e)
                        }
                    }
                }
                is AppEvent.ThemeSettingsChanged -> {
                    if (event.useUnifiedBackground) {
                        try {
                            val colorInt = event.colorArgb.toInt()
                            val newColor = Color(colorInt)
                            
                            Log.d("SettingsScreen", "🔄 收到统一背景色事件: ${event.colorArgb.toString(16)} -> ${newColor.toArgb().toString(16)}")
                            
                            settingsBackgroundColor = newColor
                            
                            Log.d("SettingsScreen", "✅ 统一背景色已应用到设置页")
                        } catch (e: Exception) {
                            Log.e("SettingsScreen", "❌ 统一背景色转换失败: ${e.message}", e)
                        }
                    }
                }
            }
        }
    }
    
    // 获取当前活动实例
    val activity = remember { context as? Activity }
    val lifecycleOwner = LocalLifecycleOwner.current
    
    // 添加头像选择状态标志 - 定义在使用之前
    var isSelectingAvatar by remember { mutableStateOf(false) }
    
    // 相册选择器
    val galleryLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.GetContent()
    ) { uri: Uri? ->
        uri?.let {
            // 根据当前选择类型保存URI
            if (isSelectingAvatar) {
                settingsViewModel.saveAvatarUri(it)
            } else {
                settingsViewModel.saveCardBackgroundUri(it)
            }
            // 重置选择标志
            isSelectingAvatar = false
        }
    }
    
    // 权限请求
    val permissionLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.RequestPermission()
    ) { isGranted: Boolean ->
        if (isGranted) {
            // 权限被授予，启动图片选择器
            galleryLauncher.launch("image/*")
        } else {
            // 权限被拒绝
            Log.d("SettingsScreen", "存储权限被拒绝")
        }
    }
    
    // 选择图片函数
    val selectImage = { isAvatar: Boolean ->
        when {
            // Android 10+不再需要外部存储权限来访问媒体
            android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.Q -> {
                galleryLauncher.launch("image/*")
            }
            // 低版本Android需要请求权限
            ContextCompat.checkSelfPermission(
                context,
                Manifest.permission.READ_EXTERNAL_STORAGE
            ) == PackageManager.PERMISSION_GRANTED -> {
                galleryLauncher.launch("image/*")
            }
            else -> {
                // 请求权限
                permissionLauncher.launch(Manifest.permission.READ_EXTERNAL_STORAGE)
            }
        }
    }
    
    // 使用更安全的状态栏实现
    SideEffect {
        activity?.let { 
            SystemBarManager.forceOpaqueStatusBar(it)  // 使用不透明黑色状态栏
        }
    }
    
    activity?.let { act ->
        DisposableEffect(key1 = Unit) {
            val window = act.window
            
            // 保存原始值以在dispose时恢复
            val originalStatusBarColor = window.statusBarColor
            
            // 应用不透明状态栏设置
            SystemBarManager.forceOpaqueStatusBar(act)
            
            onDispose {
                // 恢复原始状态栏颜色
                window.statusBarColor = originalStatusBarColor
                Log.d("SettingsScreen", "SettingsScreen disposed")
            }
        }
    }

    // 全屏显示设置页面
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(settingsBackgroundColor)
            .padding(top = SystemBarManager.getFixedStatusBarHeight()) // 添加固定高度顶部内边距
    ) {
        // 顶部标题和返回按钮
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp)
                .padding(vertical = 16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 返回按钮
            IconButton(
                onClick = onClose,
                modifier = Modifier
                    .size(36.dp)
                    .background(Color.LightGray.copy(alpha = 0.2f), CircleShape)
            ) {
                Icon(
                    imageVector = Icons.Filled.ArrowBack,
                    contentDescription = "返回",
                    tint = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.size(20.dp)
                )
            }
            
            Text(
                text = "设置",
                style = MaterialTheme.typography.headlineMedium,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.primary
            )
            
            // 占位空间，保持标题居中
            Box(modifier = Modifier.size(36.dp))
        }
        
        Spacer(modifier = Modifier.height(24.dp))
        
        // 设置项列表（支持滚动）
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f) // 占用剩余空间
                .verticalScroll(rememberScrollState()) // 添加垂直滚动
                .padding(horizontal = 16.dp)
        ) {
            // 个人信息卡片
            var showMenu by remember { mutableStateOf(false) }
            val hapticFeedback = LocalHapticFeedback.current
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 8.dp),
                shape = RoundedCornerShape(16.dp),
                colors = CardDefaults.cardColors(
                    containerColor = Color.White
                ),
                elevation = CardDefaults.cardElevation(
                    defaultElevation = 2.dp
                )
            ) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .combinedClickable(
                            onClick = { navController.navigate(AppDestinations.ACCOUNT_ROUTE) },
                            onLongClick = {
                                // 显示菜单
                                showMenu = true
                                // 触觉反馈
                                hapticFeedback.performHapticFeedback(HapticFeedbackType.LongPress)
                            }
                        )
                ) {
                    // 卡片背景图片（如果有）
                    if (cardBackgroundUri != null) {
                        // 图片加载状态
                        var isLoading by remember { mutableStateOf(true) }
                        
                        SafeAsyncImage(
                                model = cardBackgroundUri,
                            contentDescription = "卡片背景",
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(100.dp),
                            contentScale = ContentScale.Crop,
                            showLoadingIndicator = true,
                            onLoading = { isLoading = true },
                            onSuccess = { isLoading = false },
                            onError = { 
                                isLoading = false
                                // 可以在这里添加错误处理逻辑
                            }
                        )
                        
                        // 加载进度指示器
                        if (isLoading) {
                            Box(
                                modifier = Modifier.matchParentSize(),
                                contentAlignment = Alignment.Center
                            ) {
                                CircularProgressIndicator(
                                    modifier = Modifier.size(36.dp),
                                    color = MaterialTheme.colorScheme.primary
                                )
                            }
                        }
                        
                        // 添加渐变覆盖，使内容更加清晰可见
                        Box(
                            modifier = Modifier
                                .matchParentSize()
                                .background(
                                    Brush.verticalGradient(
                                        colors = listOf(
                                            Color(0x80FFFFFF),
                                            Color(0xB0FFFFFF)
                                        )
                                    )
                                )
                        )
                    } else {
                        // 没有背景时显示提示
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(100.dp)
                                .background(
                                    Brush.horizontalGradient(
                                        colors = listOf(
                                            Color(0xFFF8F8FF),
                                            Color(0xFFF0F0FF)
                                        )
                                    )
                                ),
                            contentAlignment = Alignment.Center
                        ) {
                            Column(
                                horizontalAlignment = Alignment.CenterHorizontally
                            ) {
                                Icon(
                                    imageVector = Icons.Filled.PhotoCamera,
                                    contentDescription = null,
                                    tint = MaterialTheme.colorScheme.primary.copy(alpha = 0.6f),
                                    modifier = Modifier.size(32.dp)
                                )
                                Spacer(modifier = Modifier.height(6.dp))
                                Text(
                                    text = "长按卡片设置背景图片",
                                    style = MaterialTheme.typography.bodySmall,
                                    color = Color.Gray.copy(alpha = 0.7f), // 🔧 调整为更浅的灰色
                                    fontWeight = FontWeight.Normal // 🔧 调整字重
                                )
                                Spacer(modifier = Modifier.height(2.dp))
                                Text(
                                    text = "点击头像更换头像",
                                    style = MaterialTheme.typography.bodySmall,
                                    color = Color.Gray.copy(alpha = 0.6f),
                                    fontSize = 11.sp
                                )
                            }
                        }
                    }
                    
                    // 卡片内容
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        // 用户头像
                        Box(
                            modifier = Modifier
                                .size(60.dp)
                                .clip(CircleShape)
                                .background(Color.LightGray.copy(alpha = 0.3f))
                                .clickable {
                                    // 设置为选择头像并启动选择器
                                    isSelectingAvatar = true
                                    selectImage(true)
                                }
                        ) {
                            if (avatarUri != null) {
                                // 显示用户选择的头像
                                SafeAsyncImage(
                                    model = avatarUri,
                                    contentDescription = "用户头像",
                                    modifier = Modifier.fillMaxSize(),
                                    contentScale = ContentScale.Crop,
                                    showLoadingIndicator = true
                                )
                            } else {
                                // 显示默认图标
                                Icon(
                                    imageVector = Icons.Outlined.Person,
                                    contentDescription = "头像",
                                    tint = MaterialTheme.colorScheme.primary,
                                    modifier = Modifier
                                        .size(40.dp)
                                        .align(Alignment.Center)
                                )
                            }
                        }
                        
                        Spacer(modifier = Modifier.width(16.dp))
                        
                        // 用户信息
                        Column(
                            modifier = Modifier.weight(1f),
                            verticalArrangement = Arrangement.spacedBy(8.dp) // 🔧 增加间距防止重叠
                        ) {
                            Text(
                                text = userInfo.nickname,
                                style = MaterialTheme.typography.titleMedium,
                                fontWeight = FontWeight.Bold,
                                color = if (userInfo.isLoggedIn) MaterialTheme.colorScheme.primary else Color.Gray,
                                maxLines = 1,
                                overflow = TextOverflow.Ellipsis // 🔧 添加省略号处理
                            )

                            // 🔧 只在已登录时显示会员状态
                            if (userInfo.isLoggedIn) {
                                Text(
                                    text = when (userInfo.membershipType) {
                                        com.timeflow.app.ui.screen.settings.MembershipType.LIFETIME -> "终身会员"
                                        com.timeflow.app.ui.screen.settings.MembershipType.MONTHLY -> "月度会员"
                                        else -> "普通用户"
                                    },
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = MaterialTheme.colorScheme.primary,
                                    maxLines = 1,
                                    overflow = TextOverflow.Ellipsis
                                )
                            }
                        }
                        
                        // 右侧箭头
                        Icon(
                            imageVector = Icons.Filled.ChevronRight,
                            contentDescription = null,
                            tint = MaterialTheme.colorScheme.primary,
                            modifier = Modifier.size(24.dp)
                        )
                    }
                }
                
                // 长按菜单
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(8.dp),
                    contentAlignment = Alignment.TopEnd
                ) {
                    DropdownMenu(
                        expanded = showMenu,
                        onDismissRequest = { showMenu = false }
                    ) {
                        DropdownMenuItem(
                            text = { Text("更换背景") },
                            onClick = {
                                isSelectingAvatar = false // 设置为选择背景
                                selectImage(false)
                                showMenu = false
                            },
                            leadingIcon = {
                                Icon(
                                    imageVector = Icons.Filled.Image,
                                    contentDescription = "更换背景"
                                )
                            }
                        )
                        
                        if (cardBackgroundUri != null) {
                            DropdownMenuItem(
                                text = { Text("移除背景") },
                                onClick = {
                                    settingsViewModel.saveCardBackgroundUri(null)
                                    showMenu = false
                                },
                                leadingIcon = {
                                    Icon(
                                        imageVector = Icons.Filled.Delete,
                                        contentDescription = "移除背景"
                                    )
                                }
                            )
                        }
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            SettingItem(
                icon = Icons.Outlined.Palette,
                title = "主题设置",
                subtitle = "自定义应用主题和颜色"
            ) {
                // 导航到主题设置
                navController.navigate(AppDestinations.THEME_SETTINGS_ROUTE)
            }

            SettingItem(
                icon = Icons.Outlined.Language,
                title = "语言设置",
                subtitle = "选择应用显示语言"
            ) {
                // 导航到语言设置
                navController.navigate(AppDestinations.LANGUAGE_SETTINGS_ROUTE)
            }
            

            
            SettingItem(
                icon = Icons.Outlined.Notifications,
                title = "通知设置",
                subtitle = "管理应用通知和提醒"
            ) {
                // 导航到通知设置
                navController.navigate("notification_settings")
            }
            
            SettingItem(
                icon = Icons.Outlined.Lock,
                title = "隐私设置",
                subtitle = "管理隐私和安全选项"
            ) {
                // 导航到隐私设置
                navController.navigate("privacy_settings")
            }
            
            SettingItem(
                icon = Icons.Outlined.Sync,
                title = stringResource(R.string.sync_settings),
                subtitle = stringResource(R.string.sync_settings_desc)
            ) {
                // 导航到同步设置
                navController.navigate(AppDestinations.SYNC_SETTINGS_ROUTE)
            }
            
            SettingItem(
                icon = Icons.Outlined.Storage,
                title = stringResource(R.string.data_management),
                subtitle = stringResource(R.string.data_management_desc)
            ) {
                // 导航到统一的数据管理页面
                navController.navigate(AppDestinations.DATA_MANAGEMENT_ROUTE)
            }
            
            // 添加AI设置选项
            SettingItem(
                icon = Icons.Outlined.SmartToy,
                title = stringResource(R.string.ai_settings),
                subtitle = stringResource(R.string.ai_settings_desc)
            ) {
                // 导航到AI设置页面
                navController.navigate(AppDestinations.AI_SETTINGS_ROUTE)
            }
            
            SettingItem(
                icon = Icons.Outlined.Info,
                title = stringResource(R.string.about),
                subtitle = stringResource(R.string.about_desc)
            ) {
                // 导航到关于页面
                navController.navigate("about")
            }
            
            // 底部内边距，确保最后一个设置项不被遮挡
            Spacer(modifier = Modifier.height(32.dp))
        }
        
        // 图片处理状态指示器
        if (isProcessingImage) {
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                shape = RoundedCornerShape(12.dp),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.primary.copy(alpha = 0.1f)
                ),
                elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(24.dp),
                        strokeWidth = 2.dp,
                        color = MaterialTheme.colorScheme.primary
                    )
                    
                    Spacer(modifier = Modifier.width(12.dp))
                    
                    Text(
                        text = processingMessage,
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.primary,
                        fontWeight = FontWeight.Medium
                    )
                }
            }
        }
    }
}

/**
 * 设置项组件
 */
@Composable
fun SettingItem(
    icon: ImageVector,
    title: String,
    subtitle: String,
    onClick: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable(onClick = onClick)
            .padding(vertical = 12.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 图标
        Icon(
            imageVector = icon,
            contentDescription = title,
            tint = MaterialTheme.colorScheme.primary,
            modifier = Modifier.size(24.dp)
        )
        
        Spacer(modifier = Modifier.width(16.dp))
        
        // 文本内容
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.bodyLarge,
                fontWeight = FontWeight.Medium,
                color = MaterialTheme.colorScheme.primary
            )
            
            Text(
                text = subtitle,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.primary,
                fontSize = 12.sp
            )
        }
        
        // 右侧箭头
        Icon(
            imageVector = Icons.Filled.ChevronRight,
            contentDescription = null,
            tint = MaterialTheme.colorScheme.primary,
            modifier = Modifier.size(20.dp)
        )
    }
    
    // 分隔线
    HorizontalDivider(
        color = LavenderAsh.copy(alpha = 0.3f),
        thickness = 0.5.dp,
        modifier = Modifier.padding(start = 40.dp)
    )
}