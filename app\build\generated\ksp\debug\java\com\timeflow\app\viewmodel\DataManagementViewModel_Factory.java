package com.timeflow.app.viewmodel;

import android.content.Context;
import com.timeflow.app.data.db.AppDatabase;
import com.timeflow.app.utils.DatabaseBackupManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DataManagementViewModel_Factory implements Factory<DataManagementViewModel> {
  private final Provider<Context> contextProvider;

  private final Provider<DatabaseBackupManager> backupManagerProvider;

  private final Provider<AppDatabase> databaseProvider;

  public DataManagementViewModel_Factory(Provider<Context> contextProvider,
      Provider<DatabaseBackupManager> backupManagerProvider,
      Provider<AppDatabase> databaseProvider) {
    this.contextProvider = contextProvider;
    this.backupManagerProvider = backupManagerProvider;
    this.databaseProvider = databaseProvider;
  }

  @Override
  public DataManagementViewModel get() {
    return newInstance(contextProvider.get(), backupManagerProvider.get(), databaseProvider.get());
  }

  public static DataManagementViewModel_Factory create(Provider<Context> contextProvider,
      Provider<DatabaseBackupManager> backupManagerProvider,
      Provider<AppDatabase> databaseProvider) {
    return new DataManagementViewModel_Factory(contextProvider, backupManagerProvider, databaseProvider);
  }

  public static DataManagementViewModel newInstance(Context context,
      DatabaseBackupManager backupManager, AppDatabase database) {
    return new DataManagementViewModel(context, backupManager, database);
  }
}
