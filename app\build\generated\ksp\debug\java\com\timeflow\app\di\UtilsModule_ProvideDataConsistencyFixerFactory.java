package com.timeflow.app.di;

import com.timeflow.app.data.dao.TaskDao;
import com.timeflow.app.data.repository.TaskRepository;
import com.timeflow.app.util.DataConsistencyFixer;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class UtilsModule_ProvideDataConsistencyFixerFactory implements Factory<DataConsistencyFixer> {
  private final Provider<TaskDao> taskDaoProvider;

  private final Provider<TaskRepository> taskRepositoryProvider;

  public UtilsModule_ProvideDataConsistencyFixerFactory(Provider<TaskDao> taskDaoProvider,
      Provider<TaskRepository> taskRepositoryProvider) {
    this.taskDaoProvider = taskDaoProvider;
    this.taskRepositoryProvider = taskRepositoryProvider;
  }

  @Override
  public DataConsistencyFixer get() {
    return provideDataConsistencyFixer(taskDaoProvider.get(), taskRepositoryProvider.get());
  }

  public static UtilsModule_ProvideDataConsistencyFixerFactory create(
      Provider<TaskDao> taskDaoProvider, Provider<TaskRepository> taskRepositoryProvider) {
    return new UtilsModule_ProvideDataConsistencyFixerFactory(taskDaoProvider, taskRepositoryProvider);
  }

  public static DataConsistencyFixer provideDataConsistencyFixer(TaskDao taskDao,
      TaskRepository taskRepository) {
    return Preconditions.checkNotNullFromProvides(UtilsModule.INSTANCE.provideDataConsistencyFixer(taskDao, taskRepository));
  }
}
