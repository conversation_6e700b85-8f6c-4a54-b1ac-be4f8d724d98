package com.timeflow.app.ui.screen.settings

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material.icons.outlined.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import com.timeflow.app.R
import com.timeflow.app.utils.SystemBarManager
import com.timeflow.app.BuildConfig
import com.timeflow.app.utils.TaskPersistentNotificationTestHelper
import androidx.hilt.navigation.compose.hiltViewModel

/**
 * 通知设置页面
 * 参考知名时间管理app的简洁设计，无卡片阴影，背景色与主题一致
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun NotificationSettingsScreen(
    navController: NavController,
    viewModel: NotificationSettingsViewModel = hiltViewModel()
) {
    val settings by viewModel.notificationSettings.collectAsState()
    val showTimePickerDialog by viewModel.showTimePickerDialog.collectAsState()

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(MaterialTheme.colorScheme.background)
            .padding(top = SystemBarManager.getFixedStatusBarHeight())
    ) {
        // 顶部标题栏 - 简洁设计
        TopAppBar(
            title = {
                Text(
                    text = "通知设置",
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Bold
                )
            },
            navigationIcon = {
                IconButton(
                    onClick = { navController.popBackStack() }
                ) {
                    Icon(
                        imageVector = Icons.Filled.ArrowBack,
                        contentDescription = "返回",
                        tint = MaterialTheme.colorScheme.onSurface
                    )
                }
            },
            colors = TopAppBarDefaults.topAppBarColors(
                containerColor = MaterialTheme.colorScheme.background
            )
        )
        
        LazyColumn(
            modifier = Modifier.fillMaxSize(),
            contentPadding = PaddingValues(horizontal = 16.dp, vertical = 8.dp),
            verticalArrangement = Arrangement.spacedBy(24.dp)
        ) {
            // 总通知开关 - 简洁设计
            item {
                SettingSection {
                    SwitchSettingItem(
                        icon = Icons.Outlined.Notifications,
                        title = stringResource(R.string.enable_notifications),
                        subtitle = stringResource(R.string.enable_notifications_desc),
                        checked = settings.notificationsEnabled,
                        onCheckedChange = viewModel::toggleNotifications,
                        iconTint = MaterialTheme.colorScheme.primary
                    )

                    if (!settings.notificationsEnabled) {
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = stringResource(R.string.notifications_disabled_hint),
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.outline,
                            modifier = Modifier.padding(start = 48.dp)
                        )
                    }

                    // 测试通知按钮 - 简化设计
                    if (settings.notificationsEnabled) {
                        Spacer(modifier = Modifier.height(16.dp))
                        OutlinedButton(
                            onClick = { viewModel.sendTestNotification() },
                            modifier = Modifier.fillMaxWidth()
                        ) {
                            Icon(
                                imageVector = Icons.Outlined.Send,
                                contentDescription = null,
                                modifier = Modifier.size(16.dp)
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text("发送测试通知")
                        }
                    }
                }
            }
            
            // 任务提醒设置 - 简洁分组
            if (settings.notificationsEnabled) {
                item {
                    SettingSection {
                        SettingSectionHeader(
                            title = stringResource(R.string.task_reminders),
                            subtitle = stringResource(R.string.task_reminders_desc)
                        )

                        SwitchSettingItem(
                            icon = Icons.Outlined.Task,
                            title = stringResource(R.string.task_reminders_enable),
                            subtitle = stringResource(R.string.task_reminders_enable_desc),
                            checked = settings.taskRemindersEnabled,
                            onCheckedChange = viewModel::toggleTaskReminders
                        )

                        SwitchSettingItem(
                            icon = Icons.Outlined.Schedule,
                            title = stringResource(R.string.deadline_reminders),
                            subtitle = stringResource(R.string.deadline_reminders_desc),
                            checked = settings.deadlineRemindersEnabled,
                            onCheckedChange = viewModel::toggleDeadlineReminders
                        )

                        SwitchSettingItem(
                            icon = Icons.Outlined.Warning,
                            title = stringResource(R.string.overdue_reminders),
                            subtitle = stringResource(R.string.overdue_reminders_desc),
                            checked = settings.overdueRemindersEnabled,
                            onCheckedChange = viewModel::toggleOverdueReminders
                        )

                        SwitchSettingItem(
                            icon = Icons.Outlined.Today,
                            title = stringResource(R.string.daily_review_notification),
                            subtitle = stringResource(R.string.daily_review_notification_desc),
                            checked = settings.dailyReviewEnabled,
                            onCheckedChange = viewModel::toggleDailyReview
                        )

                        if (settings.dailyReviewEnabled) {
                            ClickableSettingItem(
                                icon = Icons.Outlined.AccessTime,
                                title = "回顾时间",
                                subtitle = settings.dailyReviewTime,
                                onClick = { viewModel.showTimePicker("daily_review") }
                            )
                        }

                        SwitchSettingItem(
                            icon = Icons.Outlined.NotificationsActive,
                            title = "任务常驻通知",
                            subtitle = "在通知栏常驻显示今日主任务，不包含子待办",
                            checked = settings.taskPersistentNotificationEnabled,
                            onCheckedChange = viewModel::toggleTaskPersistentNotification
                        )
                    }
                }
                
                // 习惯提醒设置（简化版）
                item {
                    SettingSection {
                        SettingSectionHeader(
                            title = "习惯提醒",
                            subtitle = "管理习惯培养相关的提醒"
                        )

                        SwitchSettingItem(
                            icon = Icons.Outlined.Loop,
                            title = "习惯提醒",
                            subtitle = "按时提醒培养良好习惯",
                            checked = settings.habitRemindersEnabled,
                            onCheckedChange = viewModel::toggleHabitReminders
                        )
                    }
                }

                // 专注提醒设置（简化版）
                item {
                    SettingSection {
                        SettingSectionHeader(
                            title = "专注提醒",
                            subtitle = "管理专注时间相关的提醒"
                        )

                        SwitchSettingItem(
                            icon = Icons.Outlined.Timer,
                            title = "专注提醒",
                            subtitle = "专注时间开始和结束时通知",
                            checked = settings.focusRemindersEnabled,
                            onCheckedChange = viewModel::toggleFocusReminders
                        )

                        if (settings.focusRemindersEnabled) {
                            SwitchSettingItem(
                                icon = Icons.Outlined.Notifications,
                                title = "专注会话通知",
                                subtitle = "专注会话状态变化时通知",
                                checked = settings.focusSessionNotificationsEnabled,
                                onCheckedChange = viewModel::toggleFocusSessionNotifications
                            )
                        }
                    }
                }

                // 健康管理提醒设置（新增用药提醒）
                item {
                    SettingSection {
                        SettingSectionHeader(
                            title = "健康管理",
                            subtitle = "管理健康相关的提醒通知"
                        )

                        SwitchSettingItem(
                            icon = Icons.Outlined.MedicalServices,
                            title = "用药提醒",
                            subtitle = "按时提醒服用药物，保持健康",
                            checked = settings.medicationRemindersEnabled,
                            onCheckedChange = viewModel::toggleMedicationReminders,
                            iconTint = MaterialTheme.colorScheme.primary
                        )

                        if (settings.medicationRemindersEnabled) {
                            SwitchSettingItem(
                                icon = Icons.Outlined.VolumeUp,
                                title = "用药提醒声音",
                                subtitle = "用药提醒时播放声音",
                                checked = settings.medicationSoundEnabled,
                                onCheckedChange = viewModel::toggleMedicationSound
                            )

                            SwitchSettingItem(
                                icon = Icons.Outlined.Vibration,
                                title = "用药提醒震动",
                                subtitle = "用药提醒时设备震动",
                                checked = settings.medicationVibrationEnabled,
                                onCheckedChange = viewModel::toggleMedicationVibration
                            )

                            DropdownSettingItem(
                                icon = Icons.Outlined.Schedule,
                                title = "用药提醒提前时间",
                                subtitle = viewModel.getMedicationReminderTimeOptions()
                                    .find { it.first == settings.medicationAdvanceTime }?.second ?: "未设置",
                                options = viewModel.getMedicationReminderTimeOptions(),
                                selectedValue = settings.medicationAdvanceTime,
                                onOptionSelected = viewModel::setMedicationAdvanceTime
                            )
                        }
                    }
                }

                // 通知方式设置 - 简化版本
                item {
                    SettingSection {
                        SettingSectionHeader(
                            title = "通知方式",
                            subtitle = "选择通知的表现形式"
                        )

                        SwitchSettingItem(
                            icon = Icons.Outlined.VolumeUp,
                            title = "声音提醒",
                            subtitle = "播放通知声音",
                            checked = settings.soundEnabled,
                            onCheckedChange = viewModel::toggleSound
                        )

                        SwitchSettingItem(
                            icon = Icons.Outlined.Vibration,
                            title = "震动提醒",
                            subtitle = "设备震动反馈",
                            checked = settings.vibrationEnabled,
                            onCheckedChange = viewModel::toggleVibration
                        )
                    }
                }

                // 免打扰设置 - 简化版本
                item {
                    SettingSection {
                        SettingSectionHeader(
                            title = "免打扰",
                            subtitle = "设置静音时段，避免被通知打扰"
                        )

                        SwitchSettingItem(
                            icon = Icons.Outlined.DoNotDisturb,
                            title = "免打扰模式",
                            subtitle = if (settings.doNotDisturbEnabled)
                                "${settings.doNotDisturbStartTime} - ${settings.doNotDisturbEndTime}"
                            else "关闭",
                            checked = settings.doNotDisturbEnabled,
                            onCheckedChange = viewModel::toggleDoNotDisturb
                        )

                        if (settings.doNotDisturbEnabled) {
                            ClickableSettingItem(
                                icon = Icons.Outlined.Bedtime,
                                title = "开始时间",
                                subtitle = settings.doNotDisturbStartTime,
                                onClick = { viewModel.showTimePicker("dnd_start") }
                            )

                            ClickableSettingItem(
                                icon = Icons.Outlined.WbSunny,
                                title = "结束时间",
                                subtitle = settings.doNotDisturbEndTime,
                                onClick = { viewModel.showTimePicker("dnd_end") }
                            )
                        }
                    }
                }
                
                // 提醒时间设置（统一简化）
                item {
                    SettingSection {
                        SettingSectionHeader(
                            title = stringResource(R.string.reminder_time),
                            subtitle = stringResource(R.string.reminder_time_desc)
                        )

                        DropdownSettingItem(
                            icon = Icons.Outlined.Schedule,
                            title = stringResource(R.string.default_reminder_time),
                            subtitle = viewModel.getReminderTimeOptions()
                                .find { it.first == settings.defaultReminderTime }?.second ?: stringResource(R.string.not_set),
                            options = viewModel.getReminderTimeOptions(),
                            selectedValue = settings.defaultReminderTime,
                            onOptionSelected = viewModel::setDefaultReminderTime
                        )
                    }
                }

                // 调试功能（仅在DEBUG模式下显示）
                if (BuildConfig.DEBUG) {
                    item {
                        DebugSection()
                    }
                }
            }
        }

        // 时间选择器对话框
        showTimePickerDialog?.let { settingKey ->
            TimePickerDialog(
                settingKey = settingKey,
                currentTime = when (settingKey) {
                    "daily_review" -> settings.dailyReviewTime
                    "dnd_start" -> settings.doNotDisturbStartTime
                    "dnd_end" -> settings.doNotDisturbEndTime
                    else -> "12:00"
                },
                onTimeSelected = { time ->
                    when (settingKey) {
                        "daily_review" -> viewModel.setDailyReviewTime(time)
                        "dnd_start" -> viewModel.setDoNotDisturbStartTime(time)
                        "dnd_end" -> viewModel.setDoNotDisturbEndTime(time)
                    }
                    viewModel.hideTimePicker()
                },
                onDismiss = { viewModel.hideTimePicker() }
            )
        }
    }
}

/**
 * 设置分组容器 - 无卡片阴影，背景色与主题一致
 */
@Composable
fun SettingSection(
    content: @Composable () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(
                color = MaterialTheme.colorScheme.background,
                shape = RoundedCornerShape(16.dp)
            )
            .padding(16.dp)
    ) {
        content()
    }
}

/**
 * 设置分组标题
 */
@Composable
fun SettingSectionHeader(
    title: String,
    subtitle: String
) {
    Column(
        modifier = Modifier.padding(bottom = 16.dp)
    ) {
        Text(
            text = title,
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.primary
        )
        Text(
            text = subtitle,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.outline,
            modifier = Modifier.padding(top = 4.dp)
        )
    }
}

/**
 * 开关设置项
 */
@Composable
fun SwitchSettingItem(
    icon: ImageVector,
    title: String,
    subtitle: String,
    checked: Boolean,
    onCheckedChange: (Boolean) -> Unit,
    iconTint: Color = MaterialTheme.colorScheme.onSurfaceVariant
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = icon,
            contentDescription = null,
            tint = iconTint,
            modifier = Modifier.size(24.dp)
        )
        
        Spacer(modifier = Modifier.width(16.dp))
        
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.bodyLarge,
                fontWeight = FontWeight.Medium
            )
            Text(
                text = subtitle,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.outline,
                modifier = Modifier.padding(top = 2.dp)
            )
        }
        
        Switch(
            checked = checked,
            onCheckedChange = onCheckedChange,
            colors = SwitchDefaults.colors(
                checkedThumbColor = MaterialTheme.colorScheme.primary,
                checkedTrackColor = MaterialTheme.colorScheme.primary.copy(alpha = 0.3f)
            )
        )
    }
}

/**
 * 可点击设置项
 */
@Composable
fun ClickableSettingItem(
    icon: ImageVector,
    title: String,
    subtitle: String,
    onClick: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = icon,
            contentDescription = null,
            tint = MaterialTheme.colorScheme.onSurfaceVariant,
            modifier = Modifier.size(24.dp)
        )
        
        Spacer(modifier = Modifier.width(16.dp))
        
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.bodyLarge,
                fontWeight = FontWeight.Medium
            )
            Text(
                text = subtitle,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.primary,
                modifier = Modifier.padding(top = 2.dp)
            )
        }
        
        IconButton(onClick = onClick) {
            Icon(
                imageVector = Icons.Filled.ChevronRight,
                contentDescription = "设置",
                tint = MaterialTheme.colorScheme.outline
            )
        }
    }
}

/**
 * 下拉选择设置项
 */
@Composable
fun DropdownSettingItem(
    icon: ImageVector,
    title: String,
    subtitle: String,
    options: List<Pair<Int, String>>,
    selectedValue: Int,
    onOptionSelected: (Int) -> Unit
) {
    var expanded by remember { mutableStateOf(false) }
    
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = icon,
            contentDescription = null,
            tint = MaterialTheme.colorScheme.onSurfaceVariant,
            modifier = Modifier.size(24.dp)
        )
        
        Spacer(modifier = Modifier.width(16.dp))
        
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.bodyLarge,
                fontWeight = FontWeight.Medium
            )
            Text(
                text = subtitle,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.primary,
                modifier = Modifier.padding(top = 2.dp)
            )
        }
        
        Box {
            IconButton(onClick = { expanded = true }) {
                Icon(
                    imageVector = Icons.Filled.ChevronRight,
                    contentDescription = "选择",
                    tint = MaterialTheme.colorScheme.outline
                )
            }
            
            DropdownMenu(
                expanded = expanded,
                onDismissRequest = { expanded = false }
            ) {
                options.forEach { (value, label) ->
                    DropdownMenuItem(
                        text = { Text(label) },
                        onClick = {
                            onOptionSelected(value)
                            expanded = false
                        },
                        leadingIcon = if (value == selectedValue) {
                            { Icon(Icons.Filled.Check, contentDescription = null) }
                        } else null
                    )
                }
            }
        }
    }
}

/**
 * 时间选择器对话框
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TimePickerDialog(
    settingKey: String,
    currentTime: String,
    onTimeSelected: (String) -> Unit,
    onDismiss: () -> Unit
) {
    val timeState = rememberTimePickerState(
        initialHour = currentTime.split(":")[0].toIntOrNull() ?: 12,
        initialMinute = currentTime.split(":")[1].toIntOrNull() ?: 0,
        is24Hour = true
    )
    
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { 
            Text(
                text = when (settingKey) {
                    "daily_review" -> "设置每日回顾时间"
                    "dnd_start" -> "设置免打扰开始时间"
                    "dnd_end" -> "设置免打扰结束时间"
                    else -> "选择时间"
                },
                textAlign = TextAlign.Center,
                modifier = Modifier.fillMaxWidth()
            ) 
        },
        text = {
            TimePicker(
                state = timeState,
                modifier = Modifier.padding(16.dp)
            )
        },
        confirmButton = {
            TextButton(
                onClick = {
                    val hour = String.format("%02d", timeState.hour)
                    val minute = String.format("%02d", timeState.minute)
                    onTimeSelected("$hour:$minute")
                }
            ) {
                Text("确定")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("取消")
            }
        }
    )
}

/**
 * 调试功能区域（仅在DEBUG模式下显示）
 */
@Composable
fun DebugSection() {
    val context = LocalContext.current

    SettingSection {
        SettingSectionHeader(
            title = "🔧 调试功能",
            subtitle = "主任务常驻通知测试工具（仅开发模式，不含子待办）"
        )

        // 启动任务常驻通知测试
        ClickableSettingItem(
            icon = Icons.Outlined.PlayArrow,
            title = "启动任务常驻通知",
            subtitle = "测试启动任务常驻通知服务",
            onClick = {
                try {
                    val intent = android.content.Intent(context, com.timeflow.app.service.TaskPersistentNotificationService::class.java).apply {
                        action = com.timeflow.app.service.TaskPersistentNotificationService.ACTION_START_PERSISTENT
                    }
                    context.startForegroundService(intent)
                    android.util.Log.d("DebugSection", "✅ 测试启动任务常驻通知成功")
                } catch (e: Exception) {
                    android.util.Log.e("DebugSection", "❌ 测试启动任务常驻通知失败", e)
                }
            }
        )

        // 停止任务常驻通知测试
        ClickableSettingItem(
            icon = Icons.Outlined.Stop,
            title = "停止任务常驻通知",
            subtitle = "测试停止任务常驻通知服务",
            onClick = {
                try {
                    val intent = android.content.Intent(context, com.timeflow.app.service.TaskPersistentNotificationService::class.java).apply {
                        action = com.timeflow.app.service.TaskPersistentNotificationService.ACTION_STOP_PERSISTENT
                    }
                    context.startService(intent)
                    android.util.Log.d("DebugSection", "✅ 测试停止任务常驻通知成功")
                } catch (e: Exception) {
                    android.util.Log.e("DebugSection", "❌ 测试停止任务常驻通知失败", e)
                }
            }
        )

        // 刷新任务数据测试
        ClickableSettingItem(
            icon = Icons.Outlined.Refresh,
            title = "刷新任务数据",
            subtitle = "测试刷新任务常驻通知数据",
            onClick = {
                try {
                    val intent = android.content.Intent(context, com.timeflow.app.service.TaskPersistentNotificationService::class.java).apply {
                        action = com.timeflow.app.service.TaskPersistentNotificationService.ACTION_REFRESH_TASKS
                    }
                    context.startService(intent)
                    android.util.Log.d("DebugSection", "✅ 测试刷新任务数据成功")
                } catch (e: Exception) {
                    android.util.Log.e("DebugSection", "❌ 测试刷新任务数据失败", e)
                }
            }
        )
    }
}