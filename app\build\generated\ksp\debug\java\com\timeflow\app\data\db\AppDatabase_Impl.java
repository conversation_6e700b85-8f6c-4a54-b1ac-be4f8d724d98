package com.timeflow.app.data.db;

import androidx.annotation.NonNull;
import androidx.room.DatabaseConfiguration;
import androidx.room.InvalidationTracker;
import androidx.room.RoomDatabase;
import androidx.room.RoomOpenHelper;
import androidx.room.migration.AutoMigrationSpec;
import androidx.room.migration.Migration;
import androidx.room.util.DBUtil;
import androidx.room.util.TableInfo;
import androidx.sqlite.db.SupportSQLiteDatabase;
import androidx.sqlite.db.SupportSQLiteOpenHelper;
import com.timeflow.app.data.dao.AppUsageDao;
import com.timeflow.app.data.dao.AppUsageDao_Impl;
import com.timeflow.app.data.dao.CycleDao;
import com.timeflow.app.data.dao.CycleDao_Impl;
import com.timeflow.app.data.dao.EmotionRecordDao;
import com.timeflow.app.data.dao.EmotionRecordDao_Impl;
import com.timeflow.app.data.dao.GoalDao;
import com.timeflow.app.data.dao.GoalDao_Impl;
import com.timeflow.app.data.dao.GoalTemplateDao;
import com.timeflow.app.data.dao.GoalTemplateDao_Impl;
import com.timeflow.app.data.dao.HabitDao;
import com.timeflow.app.data.dao.HabitDao_Impl;
import com.timeflow.app.data.dao.KanbanBoardDao;
import com.timeflow.app.data.dao.KanbanBoardDao_Impl;
import com.timeflow.app.data.dao.KanbanColumnDao;
import com.timeflow.app.data.dao.KanbanColumnDao_Impl;
import com.timeflow.app.data.dao.MedicationRecordDao;
import com.timeflow.app.data.dao.MedicationRecordDao_Impl;
import com.timeflow.app.data.dao.ReflectionDao;
import com.timeflow.app.data.dao.ReflectionDao_Impl;
import com.timeflow.app.data.dao.TaskDao;
import com.timeflow.app.data.dao.TaskDao_Impl;
import com.timeflow.app.data.dao.TimeSessionDao;
import com.timeflow.app.data.dao.TimeSessionDao_Impl;
import com.timeflow.app.data.dao.WishDao;
import com.timeflow.app.data.dao.WishDao_Impl;
import java.lang.Class;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import javax.annotation.processing.Generated;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class AppDatabase_Impl extends AppDatabase {
  private volatile TaskDao _taskDao;

  private volatile KanbanBoardDao _kanbanBoardDao;

  private volatile KanbanColumnDao _kanbanColumnDao;

  private volatile AppUsageDao _appUsageDao;

  private volatile CycleDao _cycleDao;

  private volatile GoalDao _goalDao;

  private volatile GoalTemplateDao _goalTemplateDao;

  private volatile ReflectionDao _reflectionDao;

  private volatile TimeSessionDao _timeSessionDao;

  private volatile WishDao _wishDao;

  private volatile HabitDao _habitDao;

  private volatile MedicationRecordDao _medicationRecordDao;

  private volatile EmotionRecordDao _emotionRecordDao;

  @Override
  @NonNull
  protected SupportSQLiteOpenHelper createOpenHelper(@NonNull final DatabaseConfiguration config) {
    final SupportSQLiteOpenHelper.Callback _openCallback = new RoomOpenHelper(config, new RoomOpenHelper.Delegate(22) {
      @Override
      public void createAllTables(@NonNull final SupportSQLiteDatabase db) {
        db.execSQL("CREATE TABLE IF NOT EXISTS `tasks` (`id` TEXT NOT NULL, `title` TEXT NOT NULL, `description` TEXT NOT NULL, `due_date` INTEGER, `start_date` INTEGER, `priority` INTEGER NOT NULL DEFAULT 0, `created_at` INTEGER NOT NULL, `updated_at` INTEGER NOT NULL, `completed_at` INTEGER, `parent_task_id` TEXT, `depth` INTEGER NOT NULL DEFAULT 0, `status` TEXT NOT NULL DEFAULT '待办', `has_subtasks` INTEGER NOT NULL DEFAULT 0, `progress` REAL NOT NULL DEFAULT 0, `order_index` INTEGER NOT NULL DEFAULT 0, `projectId` TEXT, `parentId` TEXT, `reminderTime` INTEGER, `isRecurring` INTEGER NOT NULL, `recurringPattern` TEXT, `attachmentUrls` TEXT NOT NULL, `tagIds` TEXT NOT NULL, `assignedTo` TEXT, `estimatedTime` INTEGER, `actualTime` INTEGER, `sortOrder` INTEGER NOT NULL DEFAULT 0, `isStarred` INTEGER NOT NULL, `child_tasks_count` INTEGER NOT NULL DEFAULT 0, `completed_child_tasks_count` INTEGER NOT NULL DEFAULT 0, `column_id` TEXT, `ai_generated` INTEGER NOT NULL DEFAULT 0, `goal_id` TEXT, `date_manually_modified` INTEGER NOT NULL DEFAULT 0, `is_floating_task` INTEGER NOT NULL DEFAULT 0, `floating_week_start` INTEGER, `floating_week_end` INTEGER, `scheduled_date` INTEGER, `floating_task_order` INTEGER NOT NULL DEFAULT 0, PRIMARY KEY(`id`), FOREIGN KEY(`column_id`) REFERENCES `kanban_columns`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE , FOREIGN KEY(`parent_task_id`) REFERENCES `tasks`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE )");
        db.execSQL("CREATE INDEX IF NOT EXISTS `index_tasks_column_id` ON `tasks` (`column_id`)");
        db.execSQL("CREATE INDEX IF NOT EXISTS `index_tasks_parent_task_id` ON `tasks` (`parent_task_id`)");
        db.execSQL("CREATE INDEX IF NOT EXISTS `index_tasks_created_at` ON `tasks` (`created_at`)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `task_tags` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `taskId` TEXT NOT NULL, `name` TEXT NOT NULL, `color` TEXT, `createdAt` INTEGER NOT NULL, FOREIGN KEY(`taskId`) REFERENCES `tasks`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE )");
        db.execSQL("CREATE INDEX IF NOT EXISTS `index_task_tags_taskId` ON `task_tags` (`taskId`)");
        db.execSQL("CREATE INDEX IF NOT EXISTS `index_task_tags_name` ON `task_tags` (`name`)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `task_closure` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `ancestorId` TEXT NOT NULL, `descendantId` TEXT NOT NULL, `depth` INTEGER NOT NULL, FOREIGN KEY(`ancestorId`) REFERENCES `tasks`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE , FOREIGN KEY(`descendantId`) REFERENCES `tasks`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE )");
        db.execSQL("CREATE INDEX IF NOT EXISTS `index_task_closure_ancestorId` ON `task_closure` (`ancestorId`)");
        db.execSQL("CREATE INDEX IF NOT EXISTS `index_task_closure_descendantId` ON `task_closure` (`descendantId`)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `kanban_boards` (`id` TEXT NOT NULL, `title` TEXT NOT NULL, `description` TEXT, `color` TEXT, `icon` TEXT, `position` INTEGER NOT NULL, `created_at` INTEGER NOT NULL, `updated_at` INTEGER NOT NULL, PRIMARY KEY(`id`))");
        db.execSQL("CREATE TABLE IF NOT EXISTS `kanban_columns` (`id` TEXT NOT NULL, `board_id` TEXT NOT NULL, `title` TEXT NOT NULL, `description` TEXT, `position` INTEGER NOT NULL, `color` TEXT, `created_at` INTEGER NOT NULL, `updated_at` INTEGER NOT NULL, PRIMARY KEY(`id`), FOREIGN KEY(`board_id`) REFERENCES `kanban_boards`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE )");
        db.execSQL("CREATE INDEX IF NOT EXISTS `index_kanban_columns_board_id` ON `kanban_columns` (`board_id`)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `app_usage` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `packageName` TEXT NOT NULL, `date` TEXT NOT NULL, `durationMs` INTEGER NOT NULL, `launchCount` INTEGER NOT NULL, `category` TEXT NOT NULL, `isProductivity` INTEGER NOT NULL)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `cycle_records` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `startDate` TEXT NOT NULL, `endDate` TEXT, `startTime` TEXT, `endTime` TEXT, `cycleLength` INTEGER, `periodLength` INTEGER, `notes` TEXT, `createdAt` TEXT NOT NULL)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `symptoms` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `date` TEXT NOT NULL, `symptom_type` TEXT NOT NULL, `intensity` INTEGER NOT NULL, `notes` TEXT, `createdAt` TEXT NOT NULL)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `goals` (`id` TEXT NOT NULL, `title` TEXT NOT NULL, `description` TEXT NOT NULL, `startDate` INTEGER NOT NULL, `dueDate` INTEGER, `createdAt` INTEGER NOT NULL, `updatedAt` INTEGER NOT NULL, `completedAt` INTEGER, `progress` REAL NOT NULL, `priority` TEXT NOT NULL, `hasAiBreakdown` INTEGER NOT NULL, `hasAiAnalysis` INTEGER NOT NULL, `relatedTaskIds` TEXT NOT NULL, `aiRecommendationsJson` TEXT NOT NULL, `tags` TEXT NOT NULL, `status` TEXT NOT NULL, `bestTimeSlotsJson` TEXT NOT NULL, `metricsJson` TEXT NOT NULL, `reviewFrequency` TEXT NOT NULL, `categoryId` TEXT NOT NULL, PRIMARY KEY(`id`))");
        db.execSQL("CREATE TABLE IF NOT EXISTS `goal_subtasks` (`id` TEXT NOT NULL, `goalId` TEXT NOT NULL, `title` TEXT NOT NULL, `description` TEXT NOT NULL, `estimatedDurationDays` INTEGER NOT NULL, `completedAt` INTEGER, `createdAt` INTEGER NOT NULL, `aiRecommendation` TEXT, `status` TEXT NOT NULL, PRIMARY KEY(`id`))");
        db.execSQL("CREATE TABLE IF NOT EXISTS `goal_templates` (`id` TEXT NOT NULL, `name` TEXT NOT NULL, `description` TEXT NOT NULL, `category` TEXT NOT NULL, `categoryId` TEXT NOT NULL, `iconName` TEXT NOT NULL, `colorHex` TEXT NOT NULL, `usageCount` INTEGER NOT NULL, `lastUsed` INTEGER, `createdAt` INTEGER NOT NULL, `defaultTitle` TEXT NOT NULL, `defaultDescription` TEXT NOT NULL, `defaultPriority` TEXT NOT NULL, `defaultTags` TEXT NOT NULL, `defaultDurationDays` INTEGER, `goalType` TEXT NOT NULL, `defaultTargetValue` REAL, `defaultUnit` TEXT, `isRecurring` INTEGER NOT NULL, `recurringSettingsJson` TEXT NOT NULL, `reminderSettingsJson` TEXT NOT NULL, PRIMARY KEY(`id`))");
        db.execSQL("CREATE TABLE IF NOT EXISTS `goal_subtask_templates` (`id` TEXT NOT NULL, `templateId` TEXT NOT NULL, `title` TEXT NOT NULL, `description` TEXT NOT NULL, `estimatedDurationDays` INTEGER NOT NULL, `orderIndex` INTEGER NOT NULL, PRIMARY KEY(`id`), FOREIGN KEY(`templateId`) REFERENCES `goal_templates`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE )");
        db.execSQL("CREATE INDEX IF NOT EXISTS `index_goal_subtask_templates_templateId` ON `goal_subtask_templates` (`templateId`)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `reflections` (`id` TEXT NOT NULL, `title` TEXT NOT NULL, `content` TEXT NOT NULL, `rich_content_json` TEXT NOT NULL, `date` INTEGER NOT NULL, `rating` INTEGER NOT NULL, `tags_json` TEXT NOT NULL, `type` TEXT NOT NULL, `mood` TEXT NOT NULL, `plans_json` TEXT NOT NULL, `background_image` TEXT, `metrics_json` TEXT NOT NULL, `created_at` INTEGER NOT NULL, `updated_at` INTEGER NOT NULL, `task_id` TEXT, `task_title` TEXT, `is_from_task_completion` INTEGER NOT NULL, PRIMARY KEY(`id`))");
        db.execSQL("CREATE TABLE IF NOT EXISTS `time_sessions` (`id` TEXT NOT NULL, `taskId` TEXT NOT NULL, `taskName` TEXT NOT NULL, `startTime` INTEGER NOT NULL, `endTime` INTEGER, `duration` INTEGER NOT NULL, `timerType` TEXT NOT NULL, `isCompleted` INTEGER NOT NULL, `notes` TEXT NOT NULL, `createdAt` INTEGER NOT NULL, `updatedAt` INTEGER NOT NULL, `tags` TEXT NOT NULL, `focusRating` INTEGER, `productivityRating` INTEGER, PRIMARY KEY(`id`))");
        db.execSQL("CREATE TABLE IF NOT EXISTS `wishes` (`id` TEXT NOT NULL, `title` TEXT NOT NULL, `description` TEXT NOT NULL, `category` TEXT NOT NULL, `priority` INTEGER NOT NULL, `inspirationItems` TEXT NOT NULL, `imageUris` TEXT NOT NULL, `estimatedCost` REAL, `targetTimePeriod` TEXT NOT NULL, `tags` TEXT NOT NULL, `status` TEXT NOT NULL, `relatedGoalId` TEXT, `createdAt` INTEGER NOT NULL, `updatedAt` INTEGER NOT NULL, `achievedAt` INTEGER, `archivedAt` INTEGER, `isArchived` INTEGER NOT NULL, `difficulty` TEXT NOT NULL, `motivation` TEXT NOT NULL, `prerequisites` TEXT NOT NULL, `notes` TEXT NOT NULL, PRIMARY KEY(`id`))");
        db.execSQL("CREATE TABLE IF NOT EXISTS `habits` (`id` TEXT NOT NULL, `name` TEXT NOT NULL, `description` TEXT NOT NULL, `iconName` TEXT NOT NULL, `colorHex` TEXT NOT NULL, `category` TEXT NOT NULL, `customCategoryId` TEXT, `frequencyType` TEXT NOT NULL, `frequencyDays` TEXT NOT NULL, `targetCount` INTEGER NOT NULL, `reminderEnabled` INTEGER NOT NULL, `reminderTime` TEXT, `fixedTime` TEXT, `difficulty` TEXT NOT NULL, `relatedGoalId` TEXT, `currentStreak` INTEGER NOT NULL, `longestStreak` INTEGER NOT NULL, `totalCompletions` INTEGER NOT NULL, `createdAt` INTEGER NOT NULL, `updatedAt` INTEGER NOT NULL, `isActive` INTEGER NOT NULL, `customEmoji` TEXT NOT NULL, `notes` TEXT NOT NULL, `sortOrder` INTEGER NOT NULL, PRIMARY KEY(`id`), FOREIGN KEY(`relatedGoalId`) REFERENCES `goals`(`id`) ON UPDATE NO ACTION ON DELETE SET NULL )");
        db.execSQL("CREATE INDEX IF NOT EXISTS `index_habits_relatedGoalId` ON `habits` (`relatedGoalId`)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `habit_records` (`id` TEXT NOT NULL, `habitId` TEXT NOT NULL, `date` INTEGER NOT NULL, `completed` INTEGER NOT NULL, `completedAt` INTEGER, `completionCount` INTEGER NOT NULL, `notes` TEXT NOT NULL, `skipReason` TEXT NOT NULL, `mood` TEXT NOT NULL, `createdAt` INTEGER NOT NULL, `updatedAt` INTEGER NOT NULL, PRIMARY KEY(`id`), FOREIGN KEY(`habitId`) REFERENCES `habits`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE )");
        db.execSQL("CREATE INDEX IF NOT EXISTS `index_habit_records_habitId` ON `habit_records` (`habitId`)");
        db.execSQL("CREATE INDEX IF NOT EXISTS `index_habit_records_date` ON `habit_records` (`date`)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `habit_reminders` (`id` TEXT NOT NULL, `habitId` TEXT NOT NULL, `time` TEXT NOT NULL, `days` TEXT NOT NULL, `enabled` INTEGER NOT NULL, `message` TEXT NOT NULL, `createdAt` INTEGER NOT NULL, `updatedAt` INTEGER NOT NULL, PRIMARY KEY(`id`), FOREIGN KEY(`habitId`) REFERENCES `habits`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE )");
        db.execSQL("CREATE INDEX IF NOT EXISTS `index_habit_reminders_habitId` ON `habit_reminders` (`habitId`)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `medication_records` (`id` TEXT NOT NULL, `medicationId` TEXT NOT NULL, `recordDate` INTEGER NOT NULL, `completedAt` INTEGER NOT NULL, `status` TEXT NOT NULL, `notes` TEXT, PRIMARY KEY(`id`))");
        db.execSQL("CREATE INDEX IF NOT EXISTS `index_medication_records_medicationId_recordDate` ON `medication_records` (`medicationId`, `recordDate`)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `emotion_records` (`id` TEXT NOT NULL, `date` TEXT NOT NULL, `emotion_type` TEXT NOT NULL, `triggers` TEXT NOT NULL, `mindfulness_note` TEXT NOT NULL, `image_uri` TEXT, `audio_uri` TEXT, `is_detailed` INTEGER NOT NULL, `created_at` INTEGER NOT NULL, `updated_at` INTEGER NOT NULL, PRIMARY KEY(`id`))");
        db.execSQL("CREATE INDEX IF NOT EXISTS `index_emotion_records_date` ON `emotion_records` (`date`)");
        db.execSQL("CREATE INDEX IF NOT EXISTS `index_emotion_records_emotion_type` ON `emotion_records` (`emotion_type`)");
        db.execSQL("CREATE INDEX IF NOT EXISTS `index_emotion_records_is_detailed` ON `emotion_records` (`is_detailed`)");
        db.execSQL("CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)");
        db.execSQL("INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '325ce67406202f732be05a54a450810c')");
      }

      @Override
      public void dropAllTables(@NonNull final SupportSQLiteDatabase db) {
        db.execSQL("DROP TABLE IF EXISTS `tasks`");
        db.execSQL("DROP TABLE IF EXISTS `task_tags`");
        db.execSQL("DROP TABLE IF EXISTS `task_closure`");
        db.execSQL("DROP TABLE IF EXISTS `kanban_boards`");
        db.execSQL("DROP TABLE IF EXISTS `kanban_columns`");
        db.execSQL("DROP TABLE IF EXISTS `app_usage`");
        db.execSQL("DROP TABLE IF EXISTS `cycle_records`");
        db.execSQL("DROP TABLE IF EXISTS `symptoms`");
        db.execSQL("DROP TABLE IF EXISTS `goals`");
        db.execSQL("DROP TABLE IF EXISTS `goal_subtasks`");
        db.execSQL("DROP TABLE IF EXISTS `goal_templates`");
        db.execSQL("DROP TABLE IF EXISTS `goal_subtask_templates`");
        db.execSQL("DROP TABLE IF EXISTS `reflections`");
        db.execSQL("DROP TABLE IF EXISTS `time_sessions`");
        db.execSQL("DROP TABLE IF EXISTS `wishes`");
        db.execSQL("DROP TABLE IF EXISTS `habits`");
        db.execSQL("DROP TABLE IF EXISTS `habit_records`");
        db.execSQL("DROP TABLE IF EXISTS `habit_reminders`");
        db.execSQL("DROP TABLE IF EXISTS `medication_records`");
        db.execSQL("DROP TABLE IF EXISTS `emotion_records`");
        final List<? extends RoomDatabase.Callback> _callbacks = mCallbacks;
        if (_callbacks != null) {
          for (RoomDatabase.Callback _callback : _callbacks) {
            _callback.onDestructiveMigration(db);
          }
        }
      }

      @Override
      public void onCreate(@NonNull final SupportSQLiteDatabase db) {
        final List<? extends RoomDatabase.Callback> _callbacks = mCallbacks;
        if (_callbacks != null) {
          for (RoomDatabase.Callback _callback : _callbacks) {
            _callback.onCreate(db);
          }
        }
      }

      @Override
      public void onOpen(@NonNull final SupportSQLiteDatabase db) {
        mDatabase = db;
        db.execSQL("PRAGMA foreign_keys = ON");
        internalInitInvalidationTracker(db);
        final List<? extends RoomDatabase.Callback> _callbacks = mCallbacks;
        if (_callbacks != null) {
          for (RoomDatabase.Callback _callback : _callbacks) {
            _callback.onOpen(db);
          }
        }
      }

      @Override
      public void onPreMigrate(@NonNull final SupportSQLiteDatabase db) {
        DBUtil.dropFtsSyncTriggers(db);
      }

      @Override
      public void onPostMigrate(@NonNull final SupportSQLiteDatabase db) {
      }

      @Override
      @NonNull
      public RoomOpenHelper.ValidationResult onValidateSchema(
          @NonNull final SupportSQLiteDatabase db) {
        final HashMap<String, TableInfo.Column> _columnsTasks = new HashMap<String, TableInfo.Column>(38);
        _columnsTasks.put("id", new TableInfo.Column("id", "TEXT", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTasks.put("title", new TableInfo.Column("title", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTasks.put("description", new TableInfo.Column("description", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTasks.put("due_date", new TableInfo.Column("due_date", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTasks.put("start_date", new TableInfo.Column("start_date", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTasks.put("priority", new TableInfo.Column("priority", "INTEGER", true, 0, "0", TableInfo.CREATED_FROM_ENTITY));
        _columnsTasks.put("created_at", new TableInfo.Column("created_at", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTasks.put("updated_at", new TableInfo.Column("updated_at", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTasks.put("completed_at", new TableInfo.Column("completed_at", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTasks.put("parent_task_id", new TableInfo.Column("parent_task_id", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTasks.put("depth", new TableInfo.Column("depth", "INTEGER", true, 0, "0", TableInfo.CREATED_FROM_ENTITY));
        _columnsTasks.put("status", new TableInfo.Column("status", "TEXT", true, 0, "'待办'", TableInfo.CREATED_FROM_ENTITY));
        _columnsTasks.put("has_subtasks", new TableInfo.Column("has_subtasks", "INTEGER", true, 0, "0", TableInfo.CREATED_FROM_ENTITY));
        _columnsTasks.put("progress", new TableInfo.Column("progress", "REAL", true, 0, "0", TableInfo.CREATED_FROM_ENTITY));
        _columnsTasks.put("order_index", new TableInfo.Column("order_index", "INTEGER", true, 0, "0", TableInfo.CREATED_FROM_ENTITY));
        _columnsTasks.put("projectId", new TableInfo.Column("projectId", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTasks.put("parentId", new TableInfo.Column("parentId", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTasks.put("reminderTime", new TableInfo.Column("reminderTime", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTasks.put("isRecurring", new TableInfo.Column("isRecurring", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTasks.put("recurringPattern", new TableInfo.Column("recurringPattern", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTasks.put("attachmentUrls", new TableInfo.Column("attachmentUrls", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTasks.put("tagIds", new TableInfo.Column("tagIds", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTasks.put("assignedTo", new TableInfo.Column("assignedTo", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTasks.put("estimatedTime", new TableInfo.Column("estimatedTime", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTasks.put("actualTime", new TableInfo.Column("actualTime", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTasks.put("sortOrder", new TableInfo.Column("sortOrder", "INTEGER", true, 0, "0", TableInfo.CREATED_FROM_ENTITY));
        _columnsTasks.put("isStarred", new TableInfo.Column("isStarred", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTasks.put("child_tasks_count", new TableInfo.Column("child_tasks_count", "INTEGER", true, 0, "0", TableInfo.CREATED_FROM_ENTITY));
        _columnsTasks.put("completed_child_tasks_count", new TableInfo.Column("completed_child_tasks_count", "INTEGER", true, 0, "0", TableInfo.CREATED_FROM_ENTITY));
        _columnsTasks.put("column_id", new TableInfo.Column("column_id", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTasks.put("ai_generated", new TableInfo.Column("ai_generated", "INTEGER", true, 0, "0", TableInfo.CREATED_FROM_ENTITY));
        _columnsTasks.put("goal_id", new TableInfo.Column("goal_id", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTasks.put("date_manually_modified", new TableInfo.Column("date_manually_modified", "INTEGER", true, 0, "0", TableInfo.CREATED_FROM_ENTITY));
        _columnsTasks.put("is_floating_task", new TableInfo.Column("is_floating_task", "INTEGER", true, 0, "0", TableInfo.CREATED_FROM_ENTITY));
        _columnsTasks.put("floating_week_start", new TableInfo.Column("floating_week_start", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTasks.put("floating_week_end", new TableInfo.Column("floating_week_end", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTasks.put("scheduled_date", new TableInfo.Column("scheduled_date", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTasks.put("floating_task_order", new TableInfo.Column("floating_task_order", "INTEGER", true, 0, "0", TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysTasks = new HashSet<TableInfo.ForeignKey>(2);
        _foreignKeysTasks.add(new TableInfo.ForeignKey("kanban_columns", "CASCADE", "NO ACTION", Arrays.asList("column_id"), Arrays.asList("id")));
        _foreignKeysTasks.add(new TableInfo.ForeignKey("tasks", "CASCADE", "NO ACTION", Arrays.asList("parent_task_id"), Arrays.asList("id")));
        final HashSet<TableInfo.Index> _indicesTasks = new HashSet<TableInfo.Index>(3);
        _indicesTasks.add(new TableInfo.Index("index_tasks_column_id", false, Arrays.asList("column_id"), Arrays.asList("ASC")));
        _indicesTasks.add(new TableInfo.Index("index_tasks_parent_task_id", false, Arrays.asList("parent_task_id"), Arrays.asList("ASC")));
        _indicesTasks.add(new TableInfo.Index("index_tasks_created_at", false, Arrays.asList("created_at"), Arrays.asList("ASC")));
        final TableInfo _infoTasks = new TableInfo("tasks", _columnsTasks, _foreignKeysTasks, _indicesTasks);
        final TableInfo _existingTasks = TableInfo.read(db, "tasks");
        if (!_infoTasks.equals(_existingTasks)) {
          return new RoomOpenHelper.ValidationResult(false, "tasks(com.timeflow.app.data.entity.Task).\n"
                  + " Expected:\n" + _infoTasks + "\n"
                  + " Found:\n" + _existingTasks);
        }
        final HashMap<String, TableInfo.Column> _columnsTaskTags = new HashMap<String, TableInfo.Column>(5);
        _columnsTaskTags.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTaskTags.put("taskId", new TableInfo.Column("taskId", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTaskTags.put("name", new TableInfo.Column("name", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTaskTags.put("color", new TableInfo.Column("color", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTaskTags.put("createdAt", new TableInfo.Column("createdAt", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysTaskTags = new HashSet<TableInfo.ForeignKey>(1);
        _foreignKeysTaskTags.add(new TableInfo.ForeignKey("tasks", "CASCADE", "NO ACTION", Arrays.asList("taskId"), Arrays.asList("id")));
        final HashSet<TableInfo.Index> _indicesTaskTags = new HashSet<TableInfo.Index>(2);
        _indicesTaskTags.add(new TableInfo.Index("index_task_tags_taskId", false, Arrays.asList("taskId"), Arrays.asList("ASC")));
        _indicesTaskTags.add(new TableInfo.Index("index_task_tags_name", false, Arrays.asList("name"), Arrays.asList("ASC")));
        final TableInfo _infoTaskTags = new TableInfo("task_tags", _columnsTaskTags, _foreignKeysTaskTags, _indicesTaskTags);
        final TableInfo _existingTaskTags = TableInfo.read(db, "task_tags");
        if (!_infoTaskTags.equals(_existingTaskTags)) {
          return new RoomOpenHelper.ValidationResult(false, "task_tags(com.timeflow.app.data.entity.TaskTag).\n"
                  + " Expected:\n" + _infoTaskTags + "\n"
                  + " Found:\n" + _existingTaskTags);
        }
        final HashMap<String, TableInfo.Column> _columnsTaskClosure = new HashMap<String, TableInfo.Column>(4);
        _columnsTaskClosure.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTaskClosure.put("ancestorId", new TableInfo.Column("ancestorId", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTaskClosure.put("descendantId", new TableInfo.Column("descendantId", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTaskClosure.put("depth", new TableInfo.Column("depth", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysTaskClosure = new HashSet<TableInfo.ForeignKey>(2);
        _foreignKeysTaskClosure.add(new TableInfo.ForeignKey("tasks", "CASCADE", "NO ACTION", Arrays.asList("ancestorId"), Arrays.asList("id")));
        _foreignKeysTaskClosure.add(new TableInfo.ForeignKey("tasks", "CASCADE", "NO ACTION", Arrays.asList("descendantId"), Arrays.asList("id")));
        final HashSet<TableInfo.Index> _indicesTaskClosure = new HashSet<TableInfo.Index>(2);
        _indicesTaskClosure.add(new TableInfo.Index("index_task_closure_ancestorId", false, Arrays.asList("ancestorId"), Arrays.asList("ASC")));
        _indicesTaskClosure.add(new TableInfo.Index("index_task_closure_descendantId", false, Arrays.asList("descendantId"), Arrays.asList("ASC")));
        final TableInfo _infoTaskClosure = new TableInfo("task_closure", _columnsTaskClosure, _foreignKeysTaskClosure, _indicesTaskClosure);
        final TableInfo _existingTaskClosure = TableInfo.read(db, "task_closure");
        if (!_infoTaskClosure.equals(_existingTaskClosure)) {
          return new RoomOpenHelper.ValidationResult(false, "task_closure(com.timeflow.app.data.entity.TaskClosure).\n"
                  + " Expected:\n" + _infoTaskClosure + "\n"
                  + " Found:\n" + _existingTaskClosure);
        }
        final HashMap<String, TableInfo.Column> _columnsKanbanBoards = new HashMap<String, TableInfo.Column>(8);
        _columnsKanbanBoards.put("id", new TableInfo.Column("id", "TEXT", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsKanbanBoards.put("title", new TableInfo.Column("title", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsKanbanBoards.put("description", new TableInfo.Column("description", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsKanbanBoards.put("color", new TableInfo.Column("color", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsKanbanBoards.put("icon", new TableInfo.Column("icon", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsKanbanBoards.put("position", new TableInfo.Column("position", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsKanbanBoards.put("created_at", new TableInfo.Column("created_at", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsKanbanBoards.put("updated_at", new TableInfo.Column("updated_at", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysKanbanBoards = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesKanbanBoards = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoKanbanBoards = new TableInfo("kanban_boards", _columnsKanbanBoards, _foreignKeysKanbanBoards, _indicesKanbanBoards);
        final TableInfo _existingKanbanBoards = TableInfo.read(db, "kanban_boards");
        if (!_infoKanbanBoards.equals(_existingKanbanBoards)) {
          return new RoomOpenHelper.ValidationResult(false, "kanban_boards(com.timeflow.app.data.entity.KanbanBoard).\n"
                  + " Expected:\n" + _infoKanbanBoards + "\n"
                  + " Found:\n" + _existingKanbanBoards);
        }
        final HashMap<String, TableInfo.Column> _columnsKanbanColumns = new HashMap<String, TableInfo.Column>(8);
        _columnsKanbanColumns.put("id", new TableInfo.Column("id", "TEXT", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsKanbanColumns.put("board_id", new TableInfo.Column("board_id", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsKanbanColumns.put("title", new TableInfo.Column("title", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsKanbanColumns.put("description", new TableInfo.Column("description", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsKanbanColumns.put("position", new TableInfo.Column("position", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsKanbanColumns.put("color", new TableInfo.Column("color", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsKanbanColumns.put("created_at", new TableInfo.Column("created_at", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsKanbanColumns.put("updated_at", new TableInfo.Column("updated_at", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysKanbanColumns = new HashSet<TableInfo.ForeignKey>(1);
        _foreignKeysKanbanColumns.add(new TableInfo.ForeignKey("kanban_boards", "CASCADE", "NO ACTION", Arrays.asList("board_id"), Arrays.asList("id")));
        final HashSet<TableInfo.Index> _indicesKanbanColumns = new HashSet<TableInfo.Index>(1);
        _indicesKanbanColumns.add(new TableInfo.Index("index_kanban_columns_board_id", false, Arrays.asList("board_id"), Arrays.asList("ASC")));
        final TableInfo _infoKanbanColumns = new TableInfo("kanban_columns", _columnsKanbanColumns, _foreignKeysKanbanColumns, _indicesKanbanColumns);
        final TableInfo _existingKanbanColumns = TableInfo.read(db, "kanban_columns");
        if (!_infoKanbanColumns.equals(_existingKanbanColumns)) {
          return new RoomOpenHelper.ValidationResult(false, "kanban_columns(com.timeflow.app.data.entity.KanbanColumn).\n"
                  + " Expected:\n" + _infoKanbanColumns + "\n"
                  + " Found:\n" + _existingKanbanColumns);
        }
        final HashMap<String, TableInfo.Column> _columnsAppUsage = new HashMap<String, TableInfo.Column>(7);
        _columnsAppUsage.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAppUsage.put("packageName", new TableInfo.Column("packageName", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAppUsage.put("date", new TableInfo.Column("date", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAppUsage.put("durationMs", new TableInfo.Column("durationMs", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAppUsage.put("launchCount", new TableInfo.Column("launchCount", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAppUsage.put("category", new TableInfo.Column("category", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAppUsage.put("isProductivity", new TableInfo.Column("isProductivity", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysAppUsage = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesAppUsage = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoAppUsage = new TableInfo("app_usage", _columnsAppUsage, _foreignKeysAppUsage, _indicesAppUsage);
        final TableInfo _existingAppUsage = TableInfo.read(db, "app_usage");
        if (!_infoAppUsage.equals(_existingAppUsage)) {
          return new RoomOpenHelper.ValidationResult(false, "app_usage(com.timeflow.app.data.entity.AppUsageEntity).\n"
                  + " Expected:\n" + _infoAppUsage + "\n"
                  + " Found:\n" + _existingAppUsage);
        }
        final HashMap<String, TableInfo.Column> _columnsCycleRecords = new HashMap<String, TableInfo.Column>(9);
        _columnsCycleRecords.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCycleRecords.put("startDate", new TableInfo.Column("startDate", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCycleRecords.put("endDate", new TableInfo.Column("endDate", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCycleRecords.put("startTime", new TableInfo.Column("startTime", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCycleRecords.put("endTime", new TableInfo.Column("endTime", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCycleRecords.put("cycleLength", new TableInfo.Column("cycleLength", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCycleRecords.put("periodLength", new TableInfo.Column("periodLength", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCycleRecords.put("notes", new TableInfo.Column("notes", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsCycleRecords.put("createdAt", new TableInfo.Column("createdAt", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysCycleRecords = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesCycleRecords = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoCycleRecords = new TableInfo("cycle_records", _columnsCycleRecords, _foreignKeysCycleRecords, _indicesCycleRecords);
        final TableInfo _existingCycleRecords = TableInfo.read(db, "cycle_records");
        if (!_infoCycleRecords.equals(_existingCycleRecords)) {
          return new RoomOpenHelper.ValidationResult(false, "cycle_records(com.timeflow.app.data.entity.CycleRecord).\n"
                  + " Expected:\n" + _infoCycleRecords + "\n"
                  + " Found:\n" + _existingCycleRecords);
        }
        final HashMap<String, TableInfo.Column> _columnsSymptoms = new HashMap<String, TableInfo.Column>(6);
        _columnsSymptoms.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSymptoms.put("date", new TableInfo.Column("date", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSymptoms.put("symptom_type", new TableInfo.Column("symptom_type", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSymptoms.put("intensity", new TableInfo.Column("intensity", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSymptoms.put("notes", new TableInfo.Column("notes", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSymptoms.put("createdAt", new TableInfo.Column("createdAt", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysSymptoms = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesSymptoms = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoSymptoms = new TableInfo("symptoms", _columnsSymptoms, _foreignKeysSymptoms, _indicesSymptoms);
        final TableInfo _existingSymptoms = TableInfo.read(db, "symptoms");
        if (!_infoSymptoms.equals(_existingSymptoms)) {
          return new RoomOpenHelper.ValidationResult(false, "symptoms(com.timeflow.app.data.entity.SymptomRecord).\n"
                  + " Expected:\n" + _infoSymptoms + "\n"
                  + " Found:\n" + _existingSymptoms);
        }
        final HashMap<String, TableInfo.Column> _columnsGoals = new HashMap<String, TableInfo.Column>(20);
        _columnsGoals.put("id", new TableInfo.Column("id", "TEXT", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsGoals.put("title", new TableInfo.Column("title", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsGoals.put("description", new TableInfo.Column("description", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsGoals.put("startDate", new TableInfo.Column("startDate", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsGoals.put("dueDate", new TableInfo.Column("dueDate", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsGoals.put("createdAt", new TableInfo.Column("createdAt", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsGoals.put("updatedAt", new TableInfo.Column("updatedAt", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsGoals.put("completedAt", new TableInfo.Column("completedAt", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsGoals.put("progress", new TableInfo.Column("progress", "REAL", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsGoals.put("priority", new TableInfo.Column("priority", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsGoals.put("hasAiBreakdown", new TableInfo.Column("hasAiBreakdown", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsGoals.put("hasAiAnalysis", new TableInfo.Column("hasAiAnalysis", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsGoals.put("relatedTaskIds", new TableInfo.Column("relatedTaskIds", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsGoals.put("aiRecommendationsJson", new TableInfo.Column("aiRecommendationsJson", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsGoals.put("tags", new TableInfo.Column("tags", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsGoals.put("status", new TableInfo.Column("status", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsGoals.put("bestTimeSlotsJson", new TableInfo.Column("bestTimeSlotsJson", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsGoals.put("metricsJson", new TableInfo.Column("metricsJson", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsGoals.put("reviewFrequency", new TableInfo.Column("reviewFrequency", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsGoals.put("categoryId", new TableInfo.Column("categoryId", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysGoals = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesGoals = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoGoals = new TableInfo("goals", _columnsGoals, _foreignKeysGoals, _indicesGoals);
        final TableInfo _existingGoals = TableInfo.read(db, "goals");
        if (!_infoGoals.equals(_existingGoals)) {
          return new RoomOpenHelper.ValidationResult(false, "goals(com.timeflow.app.data.entity.Goal).\n"
                  + " Expected:\n" + _infoGoals + "\n"
                  + " Found:\n" + _existingGoals);
        }
        final HashMap<String, TableInfo.Column> _columnsGoalSubtasks = new HashMap<String, TableInfo.Column>(9);
        _columnsGoalSubtasks.put("id", new TableInfo.Column("id", "TEXT", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsGoalSubtasks.put("goalId", new TableInfo.Column("goalId", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsGoalSubtasks.put("title", new TableInfo.Column("title", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsGoalSubtasks.put("description", new TableInfo.Column("description", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsGoalSubtasks.put("estimatedDurationDays", new TableInfo.Column("estimatedDurationDays", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsGoalSubtasks.put("completedAt", new TableInfo.Column("completedAt", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsGoalSubtasks.put("createdAt", new TableInfo.Column("createdAt", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsGoalSubtasks.put("aiRecommendation", new TableInfo.Column("aiRecommendation", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsGoalSubtasks.put("status", new TableInfo.Column("status", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysGoalSubtasks = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesGoalSubtasks = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoGoalSubtasks = new TableInfo("goal_subtasks", _columnsGoalSubtasks, _foreignKeysGoalSubtasks, _indicesGoalSubtasks);
        final TableInfo _existingGoalSubtasks = TableInfo.read(db, "goal_subtasks");
        if (!_infoGoalSubtasks.equals(_existingGoalSubtasks)) {
          return new RoomOpenHelper.ValidationResult(false, "goal_subtasks(com.timeflow.app.data.entity.GoalSubTask).\n"
                  + " Expected:\n" + _infoGoalSubtasks + "\n"
                  + " Found:\n" + _existingGoalSubtasks);
        }
        final HashMap<String, TableInfo.Column> _columnsGoalTemplates = new HashMap<String, TableInfo.Column>(21);
        _columnsGoalTemplates.put("id", new TableInfo.Column("id", "TEXT", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsGoalTemplates.put("name", new TableInfo.Column("name", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsGoalTemplates.put("description", new TableInfo.Column("description", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsGoalTemplates.put("category", new TableInfo.Column("category", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsGoalTemplates.put("categoryId", new TableInfo.Column("categoryId", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsGoalTemplates.put("iconName", new TableInfo.Column("iconName", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsGoalTemplates.put("colorHex", new TableInfo.Column("colorHex", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsGoalTemplates.put("usageCount", new TableInfo.Column("usageCount", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsGoalTemplates.put("lastUsed", new TableInfo.Column("lastUsed", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsGoalTemplates.put("createdAt", new TableInfo.Column("createdAt", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsGoalTemplates.put("defaultTitle", new TableInfo.Column("defaultTitle", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsGoalTemplates.put("defaultDescription", new TableInfo.Column("defaultDescription", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsGoalTemplates.put("defaultPriority", new TableInfo.Column("defaultPriority", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsGoalTemplates.put("defaultTags", new TableInfo.Column("defaultTags", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsGoalTemplates.put("defaultDurationDays", new TableInfo.Column("defaultDurationDays", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsGoalTemplates.put("goalType", new TableInfo.Column("goalType", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsGoalTemplates.put("defaultTargetValue", new TableInfo.Column("defaultTargetValue", "REAL", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsGoalTemplates.put("defaultUnit", new TableInfo.Column("defaultUnit", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsGoalTemplates.put("isRecurring", new TableInfo.Column("isRecurring", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsGoalTemplates.put("recurringSettingsJson", new TableInfo.Column("recurringSettingsJson", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsGoalTemplates.put("reminderSettingsJson", new TableInfo.Column("reminderSettingsJson", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysGoalTemplates = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesGoalTemplates = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoGoalTemplates = new TableInfo("goal_templates", _columnsGoalTemplates, _foreignKeysGoalTemplates, _indicesGoalTemplates);
        final TableInfo _existingGoalTemplates = TableInfo.read(db, "goal_templates");
        if (!_infoGoalTemplates.equals(_existingGoalTemplates)) {
          return new RoomOpenHelper.ValidationResult(false, "goal_templates(com.timeflow.app.data.entity.GoalTemplate).\n"
                  + " Expected:\n" + _infoGoalTemplates + "\n"
                  + " Found:\n" + _existingGoalTemplates);
        }
        final HashMap<String, TableInfo.Column> _columnsGoalSubtaskTemplates = new HashMap<String, TableInfo.Column>(6);
        _columnsGoalSubtaskTemplates.put("id", new TableInfo.Column("id", "TEXT", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsGoalSubtaskTemplates.put("templateId", new TableInfo.Column("templateId", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsGoalSubtaskTemplates.put("title", new TableInfo.Column("title", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsGoalSubtaskTemplates.put("description", new TableInfo.Column("description", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsGoalSubtaskTemplates.put("estimatedDurationDays", new TableInfo.Column("estimatedDurationDays", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsGoalSubtaskTemplates.put("orderIndex", new TableInfo.Column("orderIndex", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysGoalSubtaskTemplates = new HashSet<TableInfo.ForeignKey>(1);
        _foreignKeysGoalSubtaskTemplates.add(new TableInfo.ForeignKey("goal_templates", "CASCADE", "NO ACTION", Arrays.asList("templateId"), Arrays.asList("id")));
        final HashSet<TableInfo.Index> _indicesGoalSubtaskTemplates = new HashSet<TableInfo.Index>(1);
        _indicesGoalSubtaskTemplates.add(new TableInfo.Index("index_goal_subtask_templates_templateId", false, Arrays.asList("templateId"), Arrays.asList("ASC")));
        final TableInfo _infoGoalSubtaskTemplates = new TableInfo("goal_subtask_templates", _columnsGoalSubtaskTemplates, _foreignKeysGoalSubtaskTemplates, _indicesGoalSubtaskTemplates);
        final TableInfo _existingGoalSubtaskTemplates = TableInfo.read(db, "goal_subtask_templates");
        if (!_infoGoalSubtaskTemplates.equals(_existingGoalSubtaskTemplates)) {
          return new RoomOpenHelper.ValidationResult(false, "goal_subtask_templates(com.timeflow.app.data.entity.GoalSubTaskTemplate).\n"
                  + " Expected:\n" + _infoGoalSubtaskTemplates + "\n"
                  + " Found:\n" + _existingGoalSubtaskTemplates);
        }
        final HashMap<String, TableInfo.Column> _columnsReflections = new HashMap<String, TableInfo.Column>(17);
        _columnsReflections.put("id", new TableInfo.Column("id", "TEXT", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsReflections.put("title", new TableInfo.Column("title", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsReflections.put("content", new TableInfo.Column("content", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsReflections.put("rich_content_json", new TableInfo.Column("rich_content_json", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsReflections.put("date", new TableInfo.Column("date", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsReflections.put("rating", new TableInfo.Column("rating", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsReflections.put("tags_json", new TableInfo.Column("tags_json", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsReflections.put("type", new TableInfo.Column("type", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsReflections.put("mood", new TableInfo.Column("mood", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsReflections.put("plans_json", new TableInfo.Column("plans_json", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsReflections.put("background_image", new TableInfo.Column("background_image", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsReflections.put("metrics_json", new TableInfo.Column("metrics_json", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsReflections.put("created_at", new TableInfo.Column("created_at", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsReflections.put("updated_at", new TableInfo.Column("updated_at", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsReflections.put("task_id", new TableInfo.Column("task_id", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsReflections.put("task_title", new TableInfo.Column("task_title", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsReflections.put("is_from_task_completion", new TableInfo.Column("is_from_task_completion", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysReflections = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesReflections = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoReflections = new TableInfo("reflections", _columnsReflections, _foreignKeysReflections, _indicesReflections);
        final TableInfo _existingReflections = TableInfo.read(db, "reflections");
        if (!_infoReflections.equals(_existingReflections)) {
          return new RoomOpenHelper.ValidationResult(false, "reflections(com.timeflow.app.data.entity.ReflectionEntity).\n"
                  + " Expected:\n" + _infoReflections + "\n"
                  + " Found:\n" + _existingReflections);
        }
        final HashMap<String, TableInfo.Column> _columnsTimeSessions = new HashMap<String, TableInfo.Column>(14);
        _columnsTimeSessions.put("id", new TableInfo.Column("id", "TEXT", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTimeSessions.put("taskId", new TableInfo.Column("taskId", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTimeSessions.put("taskName", new TableInfo.Column("taskName", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTimeSessions.put("startTime", new TableInfo.Column("startTime", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTimeSessions.put("endTime", new TableInfo.Column("endTime", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTimeSessions.put("duration", new TableInfo.Column("duration", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTimeSessions.put("timerType", new TableInfo.Column("timerType", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTimeSessions.put("isCompleted", new TableInfo.Column("isCompleted", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTimeSessions.put("notes", new TableInfo.Column("notes", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTimeSessions.put("createdAt", new TableInfo.Column("createdAt", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTimeSessions.put("updatedAt", new TableInfo.Column("updatedAt", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTimeSessions.put("tags", new TableInfo.Column("tags", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTimeSessions.put("focusRating", new TableInfo.Column("focusRating", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTimeSessions.put("productivityRating", new TableInfo.Column("productivityRating", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysTimeSessions = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesTimeSessions = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoTimeSessions = new TableInfo("time_sessions", _columnsTimeSessions, _foreignKeysTimeSessions, _indicesTimeSessions);
        final TableInfo _existingTimeSessions = TableInfo.read(db, "time_sessions");
        if (!_infoTimeSessions.equals(_existingTimeSessions)) {
          return new RoomOpenHelper.ValidationResult(false, "time_sessions(com.timeflow.app.data.model.TimeSession).\n"
                  + " Expected:\n" + _infoTimeSessions + "\n"
                  + " Found:\n" + _existingTimeSessions);
        }
        final HashMap<String, TableInfo.Column> _columnsWishes = new HashMap<String, TableInfo.Column>(21);
        _columnsWishes.put("id", new TableInfo.Column("id", "TEXT", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWishes.put("title", new TableInfo.Column("title", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWishes.put("description", new TableInfo.Column("description", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWishes.put("category", new TableInfo.Column("category", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWishes.put("priority", new TableInfo.Column("priority", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWishes.put("inspirationItems", new TableInfo.Column("inspirationItems", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWishes.put("imageUris", new TableInfo.Column("imageUris", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWishes.put("estimatedCost", new TableInfo.Column("estimatedCost", "REAL", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWishes.put("targetTimePeriod", new TableInfo.Column("targetTimePeriod", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWishes.put("tags", new TableInfo.Column("tags", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWishes.put("status", new TableInfo.Column("status", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWishes.put("relatedGoalId", new TableInfo.Column("relatedGoalId", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWishes.put("createdAt", new TableInfo.Column("createdAt", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWishes.put("updatedAt", new TableInfo.Column("updatedAt", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWishes.put("achievedAt", new TableInfo.Column("achievedAt", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWishes.put("archivedAt", new TableInfo.Column("archivedAt", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWishes.put("isArchived", new TableInfo.Column("isArchived", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWishes.put("difficulty", new TableInfo.Column("difficulty", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWishes.put("motivation", new TableInfo.Column("motivation", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWishes.put("prerequisites", new TableInfo.Column("prerequisites", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWishes.put("notes", new TableInfo.Column("notes", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysWishes = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesWishes = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoWishes = new TableInfo("wishes", _columnsWishes, _foreignKeysWishes, _indicesWishes);
        final TableInfo _existingWishes = TableInfo.read(db, "wishes");
        if (!_infoWishes.equals(_existingWishes)) {
          return new RoomOpenHelper.ValidationResult(false, "wishes(com.timeflow.app.data.entity.Wish).\n"
                  + " Expected:\n" + _infoWishes + "\n"
                  + " Found:\n" + _existingWishes);
        }
        final HashMap<String, TableInfo.Column> _columnsHabits = new HashMap<String, TableInfo.Column>(24);
        _columnsHabits.put("id", new TableInfo.Column("id", "TEXT", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsHabits.put("name", new TableInfo.Column("name", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsHabits.put("description", new TableInfo.Column("description", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsHabits.put("iconName", new TableInfo.Column("iconName", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsHabits.put("colorHex", new TableInfo.Column("colorHex", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsHabits.put("category", new TableInfo.Column("category", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsHabits.put("customCategoryId", new TableInfo.Column("customCategoryId", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsHabits.put("frequencyType", new TableInfo.Column("frequencyType", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsHabits.put("frequencyDays", new TableInfo.Column("frequencyDays", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsHabits.put("targetCount", new TableInfo.Column("targetCount", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsHabits.put("reminderEnabled", new TableInfo.Column("reminderEnabled", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsHabits.put("reminderTime", new TableInfo.Column("reminderTime", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsHabits.put("fixedTime", new TableInfo.Column("fixedTime", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsHabits.put("difficulty", new TableInfo.Column("difficulty", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsHabits.put("relatedGoalId", new TableInfo.Column("relatedGoalId", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsHabits.put("currentStreak", new TableInfo.Column("currentStreak", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsHabits.put("longestStreak", new TableInfo.Column("longestStreak", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsHabits.put("totalCompletions", new TableInfo.Column("totalCompletions", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsHabits.put("createdAt", new TableInfo.Column("createdAt", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsHabits.put("updatedAt", new TableInfo.Column("updatedAt", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsHabits.put("isActive", new TableInfo.Column("isActive", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsHabits.put("customEmoji", new TableInfo.Column("customEmoji", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsHabits.put("notes", new TableInfo.Column("notes", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsHabits.put("sortOrder", new TableInfo.Column("sortOrder", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysHabits = new HashSet<TableInfo.ForeignKey>(1);
        _foreignKeysHabits.add(new TableInfo.ForeignKey("goals", "SET NULL", "NO ACTION", Arrays.asList("relatedGoalId"), Arrays.asList("id")));
        final HashSet<TableInfo.Index> _indicesHabits = new HashSet<TableInfo.Index>(1);
        _indicesHabits.add(new TableInfo.Index("index_habits_relatedGoalId", false, Arrays.asList("relatedGoalId"), Arrays.asList("ASC")));
        final TableInfo _infoHabits = new TableInfo("habits", _columnsHabits, _foreignKeysHabits, _indicesHabits);
        final TableInfo _existingHabits = TableInfo.read(db, "habits");
        if (!_infoHabits.equals(_existingHabits)) {
          return new RoomOpenHelper.ValidationResult(false, "habits(com.timeflow.app.data.entity.Habit).\n"
                  + " Expected:\n" + _infoHabits + "\n"
                  + " Found:\n" + _existingHabits);
        }
        final HashMap<String, TableInfo.Column> _columnsHabitRecords = new HashMap<String, TableInfo.Column>(11);
        _columnsHabitRecords.put("id", new TableInfo.Column("id", "TEXT", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsHabitRecords.put("habitId", new TableInfo.Column("habitId", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsHabitRecords.put("date", new TableInfo.Column("date", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsHabitRecords.put("completed", new TableInfo.Column("completed", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsHabitRecords.put("completedAt", new TableInfo.Column("completedAt", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsHabitRecords.put("completionCount", new TableInfo.Column("completionCount", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsHabitRecords.put("notes", new TableInfo.Column("notes", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsHabitRecords.put("skipReason", new TableInfo.Column("skipReason", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsHabitRecords.put("mood", new TableInfo.Column("mood", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsHabitRecords.put("createdAt", new TableInfo.Column("createdAt", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsHabitRecords.put("updatedAt", new TableInfo.Column("updatedAt", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysHabitRecords = new HashSet<TableInfo.ForeignKey>(1);
        _foreignKeysHabitRecords.add(new TableInfo.ForeignKey("habits", "CASCADE", "NO ACTION", Arrays.asList("habitId"), Arrays.asList("id")));
        final HashSet<TableInfo.Index> _indicesHabitRecords = new HashSet<TableInfo.Index>(2);
        _indicesHabitRecords.add(new TableInfo.Index("index_habit_records_habitId", false, Arrays.asList("habitId"), Arrays.asList("ASC")));
        _indicesHabitRecords.add(new TableInfo.Index("index_habit_records_date", false, Arrays.asList("date"), Arrays.asList("ASC")));
        final TableInfo _infoHabitRecords = new TableInfo("habit_records", _columnsHabitRecords, _foreignKeysHabitRecords, _indicesHabitRecords);
        final TableInfo _existingHabitRecords = TableInfo.read(db, "habit_records");
        if (!_infoHabitRecords.equals(_existingHabitRecords)) {
          return new RoomOpenHelper.ValidationResult(false, "habit_records(com.timeflow.app.data.entity.HabitRecord).\n"
                  + " Expected:\n" + _infoHabitRecords + "\n"
                  + " Found:\n" + _existingHabitRecords);
        }
        final HashMap<String, TableInfo.Column> _columnsHabitReminders = new HashMap<String, TableInfo.Column>(8);
        _columnsHabitReminders.put("id", new TableInfo.Column("id", "TEXT", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsHabitReminders.put("habitId", new TableInfo.Column("habitId", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsHabitReminders.put("time", new TableInfo.Column("time", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsHabitReminders.put("days", new TableInfo.Column("days", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsHabitReminders.put("enabled", new TableInfo.Column("enabled", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsHabitReminders.put("message", new TableInfo.Column("message", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsHabitReminders.put("createdAt", new TableInfo.Column("createdAt", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsHabitReminders.put("updatedAt", new TableInfo.Column("updatedAt", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysHabitReminders = new HashSet<TableInfo.ForeignKey>(1);
        _foreignKeysHabitReminders.add(new TableInfo.ForeignKey("habits", "CASCADE", "NO ACTION", Arrays.asList("habitId"), Arrays.asList("id")));
        final HashSet<TableInfo.Index> _indicesHabitReminders = new HashSet<TableInfo.Index>(1);
        _indicesHabitReminders.add(new TableInfo.Index("index_habit_reminders_habitId", false, Arrays.asList("habitId"), Arrays.asList("ASC")));
        final TableInfo _infoHabitReminders = new TableInfo("habit_reminders", _columnsHabitReminders, _foreignKeysHabitReminders, _indicesHabitReminders);
        final TableInfo _existingHabitReminders = TableInfo.read(db, "habit_reminders");
        if (!_infoHabitReminders.equals(_existingHabitReminders)) {
          return new RoomOpenHelper.ValidationResult(false, "habit_reminders(com.timeflow.app.data.entity.HabitReminder).\n"
                  + " Expected:\n" + _infoHabitReminders + "\n"
                  + " Found:\n" + _existingHabitReminders);
        }
        final HashMap<String, TableInfo.Column> _columnsMedicationRecords = new HashMap<String, TableInfo.Column>(6);
        _columnsMedicationRecords.put("id", new TableInfo.Column("id", "TEXT", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsMedicationRecords.put("medicationId", new TableInfo.Column("medicationId", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsMedicationRecords.put("recordDate", new TableInfo.Column("recordDate", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsMedicationRecords.put("completedAt", new TableInfo.Column("completedAt", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsMedicationRecords.put("status", new TableInfo.Column("status", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsMedicationRecords.put("notes", new TableInfo.Column("notes", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysMedicationRecords = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesMedicationRecords = new HashSet<TableInfo.Index>(1);
        _indicesMedicationRecords.add(new TableInfo.Index("index_medication_records_medicationId_recordDate", false, Arrays.asList("medicationId", "recordDate"), Arrays.asList("ASC", "ASC")));
        final TableInfo _infoMedicationRecords = new TableInfo("medication_records", _columnsMedicationRecords, _foreignKeysMedicationRecords, _indicesMedicationRecords);
        final TableInfo _existingMedicationRecords = TableInfo.read(db, "medication_records");
        if (!_infoMedicationRecords.equals(_existingMedicationRecords)) {
          return new RoomOpenHelper.ValidationResult(false, "medication_records(com.timeflow.app.data.entity.MedicationRecord).\n"
                  + " Expected:\n" + _infoMedicationRecords + "\n"
                  + " Found:\n" + _existingMedicationRecords);
        }
        final HashMap<String, TableInfo.Column> _columnsEmotionRecords = new HashMap<String, TableInfo.Column>(10);
        _columnsEmotionRecords.put("id", new TableInfo.Column("id", "TEXT", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsEmotionRecords.put("date", new TableInfo.Column("date", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsEmotionRecords.put("emotion_type", new TableInfo.Column("emotion_type", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsEmotionRecords.put("triggers", new TableInfo.Column("triggers", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsEmotionRecords.put("mindfulness_note", new TableInfo.Column("mindfulness_note", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsEmotionRecords.put("image_uri", new TableInfo.Column("image_uri", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsEmotionRecords.put("audio_uri", new TableInfo.Column("audio_uri", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsEmotionRecords.put("is_detailed", new TableInfo.Column("is_detailed", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsEmotionRecords.put("created_at", new TableInfo.Column("created_at", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsEmotionRecords.put("updated_at", new TableInfo.Column("updated_at", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysEmotionRecords = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesEmotionRecords = new HashSet<TableInfo.Index>(3);
        _indicesEmotionRecords.add(new TableInfo.Index("index_emotion_records_date", false, Arrays.asList("date"), Arrays.asList("ASC")));
        _indicesEmotionRecords.add(new TableInfo.Index("index_emotion_records_emotion_type", false, Arrays.asList("emotion_type"), Arrays.asList("ASC")));
        _indicesEmotionRecords.add(new TableInfo.Index("index_emotion_records_is_detailed", false, Arrays.asList("is_detailed"), Arrays.asList("ASC")));
        final TableInfo _infoEmotionRecords = new TableInfo("emotion_records", _columnsEmotionRecords, _foreignKeysEmotionRecords, _indicesEmotionRecords);
        final TableInfo _existingEmotionRecords = TableInfo.read(db, "emotion_records");
        if (!_infoEmotionRecords.equals(_existingEmotionRecords)) {
          return new RoomOpenHelper.ValidationResult(false, "emotion_records(com.timeflow.app.data.entity.EmotionRecordEntity).\n"
                  + " Expected:\n" + _infoEmotionRecords + "\n"
                  + " Found:\n" + _existingEmotionRecords);
        }
        return new RoomOpenHelper.ValidationResult(true, null);
      }
    }, "325ce67406202f732be05a54a450810c", "a3bc057221218a851150b7ee0a954248");
    final SupportSQLiteOpenHelper.Configuration _sqliteConfig = SupportSQLiteOpenHelper.Configuration.builder(config.context).name(config.name).callback(_openCallback).build();
    final SupportSQLiteOpenHelper _helper = config.sqliteOpenHelperFactory.create(_sqliteConfig);
    return _helper;
  }

  @Override
  @NonNull
  protected InvalidationTracker createInvalidationTracker() {
    final HashMap<String, String> _shadowTablesMap = new HashMap<String, String>(0);
    final HashMap<String, Set<String>> _viewTables = new HashMap<String, Set<String>>(0);
    return new InvalidationTracker(this, _shadowTablesMap, _viewTables, "tasks","task_tags","task_closure","kanban_boards","kanban_columns","app_usage","cycle_records","symptoms","goals","goal_subtasks","goal_templates","goal_subtask_templates","reflections","time_sessions","wishes","habits","habit_records","habit_reminders","medication_records","emotion_records");
  }

  @Override
  public void clearAllTables() {
    super.assertNotMainThread();
    final SupportSQLiteDatabase _db = super.getOpenHelper().getWritableDatabase();
    final boolean _supportsDeferForeignKeys = android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.LOLLIPOP;
    try {
      if (!_supportsDeferForeignKeys) {
        _db.execSQL("PRAGMA foreign_keys = FALSE");
      }
      super.beginTransaction();
      if (_supportsDeferForeignKeys) {
        _db.execSQL("PRAGMA defer_foreign_keys = TRUE");
      }
      _db.execSQL("DELETE FROM `tasks`");
      _db.execSQL("DELETE FROM `task_tags`");
      _db.execSQL("DELETE FROM `task_closure`");
      _db.execSQL("DELETE FROM `kanban_boards`");
      _db.execSQL("DELETE FROM `kanban_columns`");
      _db.execSQL("DELETE FROM `app_usage`");
      _db.execSQL("DELETE FROM `cycle_records`");
      _db.execSQL("DELETE FROM `symptoms`");
      _db.execSQL("DELETE FROM `goals`");
      _db.execSQL("DELETE FROM `goal_subtasks`");
      _db.execSQL("DELETE FROM `goal_templates`");
      _db.execSQL("DELETE FROM `goal_subtask_templates`");
      _db.execSQL("DELETE FROM `reflections`");
      _db.execSQL("DELETE FROM `time_sessions`");
      _db.execSQL("DELETE FROM `wishes`");
      _db.execSQL("DELETE FROM `habits`");
      _db.execSQL("DELETE FROM `habit_records`");
      _db.execSQL("DELETE FROM `habit_reminders`");
      _db.execSQL("DELETE FROM `medication_records`");
      _db.execSQL("DELETE FROM `emotion_records`");
      super.setTransactionSuccessful();
    } finally {
      super.endTransaction();
      if (!_supportsDeferForeignKeys) {
        _db.execSQL("PRAGMA foreign_keys = TRUE");
      }
      _db.query("PRAGMA wal_checkpoint(FULL)").close();
      if (!_db.inTransaction()) {
        _db.execSQL("VACUUM");
      }
    }
  }

  @Override
  @NonNull
  protected Map<Class<?>, List<Class<?>>> getRequiredTypeConverters() {
    final HashMap<Class<?>, List<Class<?>>> _typeConvertersMap = new HashMap<Class<?>, List<Class<?>>>();
    _typeConvertersMap.put(TaskDao.class, TaskDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(KanbanBoardDao.class, KanbanBoardDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(KanbanColumnDao.class, KanbanColumnDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(AppUsageDao.class, AppUsageDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(CycleDao.class, CycleDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(GoalDao.class, GoalDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(GoalTemplateDao.class, GoalTemplateDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(ReflectionDao.class, ReflectionDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(TimeSessionDao.class, TimeSessionDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(WishDao.class, WishDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(HabitDao.class, HabitDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(MedicationRecordDao.class, MedicationRecordDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(EmotionRecordDao.class, EmotionRecordDao_Impl.getRequiredConverters());
    return _typeConvertersMap;
  }

  @Override
  @NonNull
  public Set<Class<? extends AutoMigrationSpec>> getRequiredAutoMigrationSpecs() {
    final HashSet<Class<? extends AutoMigrationSpec>> _autoMigrationSpecsSet = new HashSet<Class<? extends AutoMigrationSpec>>();
    return _autoMigrationSpecsSet;
  }

  @Override
  @NonNull
  public List<Migration> getAutoMigrations(
      @NonNull final Map<Class<? extends AutoMigrationSpec>, AutoMigrationSpec> autoMigrationSpecs) {
    final List<Migration> _autoMigrations = new ArrayList<Migration>();
    return _autoMigrations;
  }

  @Override
  public TaskDao taskDao() {
    if (_taskDao != null) {
      return _taskDao;
    } else {
      synchronized(this) {
        if(_taskDao == null) {
          _taskDao = new TaskDao_Impl(this);
        }
        return _taskDao;
      }
    }
  }

  @Override
  public KanbanBoardDao kanbanBoardDao() {
    if (_kanbanBoardDao != null) {
      return _kanbanBoardDao;
    } else {
      synchronized(this) {
        if(_kanbanBoardDao == null) {
          _kanbanBoardDao = new KanbanBoardDao_Impl(this);
        }
        return _kanbanBoardDao;
      }
    }
  }

  @Override
  public KanbanColumnDao kanbanColumnDao() {
    if (_kanbanColumnDao != null) {
      return _kanbanColumnDao;
    } else {
      synchronized(this) {
        if(_kanbanColumnDao == null) {
          _kanbanColumnDao = new KanbanColumnDao_Impl(this);
        }
        return _kanbanColumnDao;
      }
    }
  }

  @Override
  public AppUsageDao appUsageDao() {
    if (_appUsageDao != null) {
      return _appUsageDao;
    } else {
      synchronized(this) {
        if(_appUsageDao == null) {
          _appUsageDao = new AppUsageDao_Impl(this);
        }
        return _appUsageDao;
      }
    }
  }

  @Override
  public CycleDao cycleDao() {
    if (_cycleDao != null) {
      return _cycleDao;
    } else {
      synchronized(this) {
        if(_cycleDao == null) {
          _cycleDao = new CycleDao_Impl(this);
        }
        return _cycleDao;
      }
    }
  }

  @Override
  public GoalDao goalDao() {
    if (_goalDao != null) {
      return _goalDao;
    } else {
      synchronized(this) {
        if(_goalDao == null) {
          _goalDao = new GoalDao_Impl(this);
        }
        return _goalDao;
      }
    }
  }

  @Override
  public GoalTemplateDao goalTemplateDao() {
    if (_goalTemplateDao != null) {
      return _goalTemplateDao;
    } else {
      synchronized(this) {
        if(_goalTemplateDao == null) {
          _goalTemplateDao = new GoalTemplateDao_Impl(this);
        }
        return _goalTemplateDao;
      }
    }
  }

  @Override
  public ReflectionDao reflectionDao() {
    if (_reflectionDao != null) {
      return _reflectionDao;
    } else {
      synchronized(this) {
        if(_reflectionDao == null) {
          _reflectionDao = new ReflectionDao_Impl(this);
        }
        return _reflectionDao;
      }
    }
  }

  @Override
  public TimeSessionDao timeSessionDao() {
    if (_timeSessionDao != null) {
      return _timeSessionDao;
    } else {
      synchronized(this) {
        if(_timeSessionDao == null) {
          _timeSessionDao = new TimeSessionDao_Impl(this);
        }
        return _timeSessionDao;
      }
    }
  }

  @Override
  public WishDao wishDao() {
    if (_wishDao != null) {
      return _wishDao;
    } else {
      synchronized(this) {
        if(_wishDao == null) {
          _wishDao = new WishDao_Impl(this);
        }
        return _wishDao;
      }
    }
  }

  @Override
  public HabitDao habitDao() {
    if (_habitDao != null) {
      return _habitDao;
    } else {
      synchronized(this) {
        if(_habitDao == null) {
          _habitDao = new HabitDao_Impl(this);
        }
        return _habitDao;
      }
    }
  }

  @Override
  public MedicationRecordDao medicationRecordDao() {
    if (_medicationRecordDao != null) {
      return _medicationRecordDao;
    } else {
      synchronized(this) {
        if(_medicationRecordDao == null) {
          _medicationRecordDao = new MedicationRecordDao_Impl(this);
        }
        return _medicationRecordDao;
      }
    }
  }

  @Override
  public EmotionRecordDao emotionRecordDao() {
    if (_emotionRecordDao != null) {
      return _emotionRecordDao;
    } else {
      synchronized(this) {
        if(_emotionRecordDao == null) {
          _emotionRecordDao = new EmotionRecordDao_Impl(this);
        }
        return _emotionRecordDao;
      }
    }
  }
}
