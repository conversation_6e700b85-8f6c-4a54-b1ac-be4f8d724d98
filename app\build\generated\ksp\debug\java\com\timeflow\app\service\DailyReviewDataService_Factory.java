package com.timeflow.app.service;

import android.content.Context;
import com.timeflow.app.data.repository.GoalRepository;
import com.timeflow.app.data.repository.HabitRepository;
import com.timeflow.app.data.repository.TaskRepository;
import com.timeflow.app.data.repository.TimeSessionRepository;
import com.timeflow.app.ui.screen.reflection.ReflectionRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DailyReviewDataService_Factory implements Factory<DailyReviewDataService> {
  private final Provider<Context> contextProvider;

  private final Provider<TaskRepository> taskRepositoryProvider;

  private final Provider<HabitRepository> habitRepositoryProvider;

  private final Provider<TimeSessionRepository> timeSessionRepositoryProvider;

  private final Provider<ReflectionRepository> reflectionRepositoryProvider;

  private final Provider<GoalRepository> goalRepositoryProvider;

  public DailyReviewDataService_Factory(Provider<Context> contextProvider,
      Provider<TaskRepository> taskRepositoryProvider,
      Provider<HabitRepository> habitRepositoryProvider,
      Provider<TimeSessionRepository> timeSessionRepositoryProvider,
      Provider<ReflectionRepository> reflectionRepositoryProvider,
      Provider<GoalRepository> goalRepositoryProvider) {
    this.contextProvider = contextProvider;
    this.taskRepositoryProvider = taskRepositoryProvider;
    this.habitRepositoryProvider = habitRepositoryProvider;
    this.timeSessionRepositoryProvider = timeSessionRepositoryProvider;
    this.reflectionRepositoryProvider = reflectionRepositoryProvider;
    this.goalRepositoryProvider = goalRepositoryProvider;
  }

  @Override
  public DailyReviewDataService get() {
    return newInstance(contextProvider.get(), taskRepositoryProvider.get(), habitRepositoryProvider.get(), timeSessionRepositoryProvider.get(), reflectionRepositoryProvider.get(), goalRepositoryProvider.get());
  }

  public static DailyReviewDataService_Factory create(Provider<Context> contextProvider,
      Provider<TaskRepository> taskRepositoryProvider,
      Provider<HabitRepository> habitRepositoryProvider,
      Provider<TimeSessionRepository> timeSessionRepositoryProvider,
      Provider<ReflectionRepository> reflectionRepositoryProvider,
      Provider<GoalRepository> goalRepositoryProvider) {
    return new DailyReviewDataService_Factory(contextProvider, taskRepositoryProvider, habitRepositoryProvider, timeSessionRepositoryProvider, reflectionRepositoryProvider, goalRepositoryProvider);
  }

  public static DailyReviewDataService newInstance(Context context, TaskRepository taskRepository,
      HabitRepository habitRepository, TimeSessionRepository timeSessionRepository,
      ReflectionRepository reflectionRepository, GoalRepository goalRepository) {
    return new DailyReviewDataService(context, taskRepository, habitRepository, timeSessionRepository, reflectionRepository, goalRepository);
  }
}
