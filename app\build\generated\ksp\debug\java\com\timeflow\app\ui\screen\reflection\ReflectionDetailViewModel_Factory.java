package com.timeflow.app.ui.screen.reflection;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ReflectionDetailViewModel_Factory implements Factory<ReflectionDetailViewModel> {
  private final Provider<ReflectionRepository> reflectionRepositoryProvider;

  public ReflectionDetailViewModel_Factory(
      Provider<ReflectionRepository> reflectionRepositoryProvider) {
    this.reflectionRepositoryProvider = reflectionRepositoryProvider;
  }

  @Override
  public ReflectionDetailViewModel get() {
    return newInstance(reflectionRepositoryProvider.get());
  }

  public static ReflectionDetailViewModel_Factory create(
      Provider<ReflectionRepository> reflectionRepositoryProvider) {
    return new ReflectionDetailViewModel_Factory(reflectionRepositoryProvider);
  }

  public static ReflectionDetailViewModel newInstance(ReflectionRepository reflectionRepository) {
    return new ReflectionDetailViewModel(reflectionRepository);
  }
}
